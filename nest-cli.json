{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "monorepo": true, "root": "apps/core", "sourceRoot": "apps/core/src", "compilerOptions": {"webpack": true, "tsConfigPath": "apps/core/tsconfig.app.json", "deleteOutDir": true}, "projects": {"core": {"type": "application", "root": "apps/core", "entryFile": "main", "sourceRoot": "apps/core/src", "compilerOptions": {"tsConfigPath": "apps/core/tsconfig.app.json"}}, "communication": {"type": "application", "root": "apps/communication", "entryFile": "main", "sourceRoot": "apps/communication/src", "compilerOptions": {"tsConfigPath": "apps/communication/tsconfig.app.json"}}, "document-engine": {"type": "application", "root": "apps/document-engine", "entryFile": "main", "sourceRoot": "apps/document-engine/src", "compilerOptions": {"tsConfigPath": "apps/document-engine/tsconfig.app.json"}}, "auth": {"type": "application", "root": "apps/auth", "entryFile": "main", "sourceRoot": "apps/auth/src", "compilerOptions": {"tsConfigPath": "apps/auth/tsconfig.app.json", "assets": [{"include": "../../scripts/data/**/*", "outDir": "dist/scripts/data"}]}}, "case-management": {"type": "application", "root": "apps/case-management", "entryFile": "main", "sourceRoot": "apps/case-management/src", "compilerOptions": {"tsConfigPath": "apps/case-management/tsconfig.app.json"}}, "quote-engine": {"type": "application", "root": "apps/quote-engine", "entryFile": "main", "sourceRoot": "apps/quote-engine/src", "compilerOptions": {"tsConfigPath": "apps/quote-engine/tsconfig.app.json"}}, "task-management": {"type": "application", "root": "apps/task-management", "entryFile": "main", "sourceRoot": "apps/task-management/src", "compilerOptions": {"tsConfigPath": "apps/task-management/tsconfig.app.json"}}, "common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}}}