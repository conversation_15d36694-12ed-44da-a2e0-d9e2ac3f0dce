import * as bcrypt from 'bcrypt';

/**
 * Utility functions for password hashing and verification
 */
export class PasswordUtil {
    /**
     * Default salt rounds for bcrypt
     */
    private static readonly SALT_ROUNDS = 10;

    /**
     * Hashes a password using bcrypt
     * @param password Plain text password
     * @returns Hashed password
     */
    static async hashPassword(password: string): Promise<string> {
        return bcrypt.hash(password, this.SALT_ROUNDS);
    }

    /**
     * Verifies a password against a hash
     * @param password Plain text password
     * @param hash Hashed password
     * @returns True if password matches the hash
     */
    static async verifyPassword(password: string, hash: string): Promise<boolean> {
        return bcrypt.compare(password, hash);
    }
}
