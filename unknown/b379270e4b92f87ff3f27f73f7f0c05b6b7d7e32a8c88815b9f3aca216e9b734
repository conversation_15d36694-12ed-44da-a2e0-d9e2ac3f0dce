import { HttpStatusCode } from '../enums/http-status.enum';
import { BaseException, ExceptionMetadata } from './base.exception';

/**
 * Exception for forbidden access errors
 */
export class ForbiddenException extends BaseException {
    /**
     * Creates a new ForbiddenException
     * @param message Error message
     * @param errorCode Optional error code
     * @param meta Optional additional metadata
     */
    constructor(message = 'Forbidden', errorCode?: string, meta?: ExceptionMetadata) {
        super(message, HttpStatusCode.FORBIDDEN, errorCode, meta);
    }
}
