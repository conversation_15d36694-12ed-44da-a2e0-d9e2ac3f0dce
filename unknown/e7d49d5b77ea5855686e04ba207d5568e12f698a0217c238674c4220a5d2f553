import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { Logger } from '@nestjs/common';

// Load environment variables
dotenv.config({
    path: path.resolve(process.cwd(), '.env'),
});

const cleanDatabase = async () => {
    // Create a connection to the database
    const dataSource = new DataSource({
        type: 'postgres',
        host: process.env.POSTGRES_HOST,
        port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
        username: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: process.env.POSTGRES_DB,
        schema: 'public'
    });

    try {
        await dataSource.initialize();
        Logger.log('Connected to database');

        // Drop all tenant schemas
        const schemas = await dataSource.query(
            `SELECT nspname FROM pg_namespace WHERE nspname LIKE 'tenant_%'`
        );

        for (const schema of schemas) {
            const schemaName = schema.nspname;
            Logger.log(`Dropping schema ${schemaName}`);
            await dataSource.query(`DROP SCHEMA IF EXISTS "${schemaName}" CASCADE`);
        }

        // Drop problematic tables if they exist
        const tablesToDrop = [
            'user_roles',
            'user_profiles',
            'tenant_roles',
            'user_roles'
        ];

        for (const table of tablesToDrop) {
            Logger.log(`Dropping table ${table} if exists`);
            await dataSource.query(`DROP TABLE IF EXISTS "${table}" CASCADE`);
        }

        // Get all constraints
        const constraints = await dataSource.query(`
            SELECT tc.table_schema, tc.constraint_name, tc.table_name
            FROM information_schema.table_constraints tc
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND (tc.table_name LIKE '%user%' OR tc.table_name LIKE '%role%' OR tc.table_name LIKE '%tenant%')
        `);

        // Drop foreign key constraints
        for (const constraint of constraints) {
            const { table_schema, constraint_name, table_name } = constraint;
            Logger.log(`Dropping constraint ${constraint_name} from ${table_schema}.${table_name}`);
            await dataSource.query(`
                ALTER TABLE "${table_schema}"."${table_name}" 
                DROP CONSTRAINT IF EXISTS "${constraint_name}" CASCADE
            `);
        }

        Logger.log('Successfully cleaned database');
    } catch (error) {
        Logger.error('Error cleaning database:', error);
        throw error;
    } finally {
        if (dataSource.isInitialized) {
            await dataSource.destroy();
        }
    }
};

// Run the script
cleanDatabase()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error('Script failed:', error);
        process.exit(1);
    }); 