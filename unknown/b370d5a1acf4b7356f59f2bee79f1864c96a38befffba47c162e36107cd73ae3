/**
 * Raw multi-tenancy environment variables interface
 * This represents the raw, possibly undefined environment variables
 */
export interface RawMultiTenancyEnv {
    MULTI_TENANCY_MAX_CONNECTIONS?: string;
    MULTI_TENANCY_CONNECTION_TTL?: string;
    MULTI_TENANCY_AUTO_CREATE_SCHEMA?: string;
    MULTI_TENANCY_AUTO_RUN_MIGRATIONS?: string;
    MULTI_TENANCY_MIGRATIONS_DIR?: string;
}

/**
 * Multi-tenancy configuration interface
 * This represents the fully validated configuration
 */
export interface MultiTenancyConfig {
    maxConnections: number;
    connectionTtl: number;
    autoCreateSchema: boolean;
    autoRunMigrations: boolean;
    migrationsDir?: string;
}
