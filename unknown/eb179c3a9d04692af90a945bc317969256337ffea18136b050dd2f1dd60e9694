import {
    sanitizeTenantId,
    isValidSchemaName,
    getSchemaNameFromTenantId,
    getTenantIdFromSchemaName
} from '../sanitize-tenant-id.util';

describe('sanitizeTenantId', () => {
    it('should sanitize valid tenant IDs', () => {
        expect(sanitizeTenantId('test')).toBe('test');
        expect(sanitizeTenantId('test-tenant')).toBe('test_tenant'); // Hyphens are replaced with underscores
        expect(sanitizeTenantId('test_tenant')).toBe('test_tenant');
        expect(sanitizeTenantId('TEST')).toBe('test');
        expect(sanitizeTenantId('Test123')).toBe('test123');
    });

    it('should handle tenant IDs starting with numbers', () => {
        expect(sanitizeTenantId('123test')).toBe('123test');
        expect(sanitizeTenantId('1test')).toBe('1test');
    });

    it('should handle tenant IDs starting with hyphens', () => {
        expect(sanitizeTenantId('-test')).toBe('_test'); // Hyphen is replaced with underscore
    });

    it('should throw error for empty tenant IDs', () => {
        expect(() => sanitizeTenantId('')).toThrow('Tenant ID cannot be empty');
    });

    it('should throw error for tenant IDs with invalid characters', () => {
        expect(() => sanitizeTenantId('test;drop table')).toThrow(
            'Tenant ID contains invalid characters'
        );
        expect(() => sanitizeTenantId('test space')).toThrow(
            'Tenant ID contains invalid characters'
        );
        expect(() => sanitizeTenantId('test.tenant')).toThrow(
            'Tenant ID contains invalid characters'
        );
        expect(() => sanitizeTenantId('test/tenant')).toThrow(
            'Tenant ID contains invalid characters'
        );
        expect(() => sanitizeTenantId('test\\tenant')).toThrow(
            'Tenant ID contains invalid characters'
        );
    });

    it('should throw error for tenant IDs that are too long', () => {
        const longTenantId = 'a'.repeat(51);
        expect(() => sanitizeTenantId(longTenantId)).toThrow('Tenant ID is too long');
    });
});

describe('isValidSchemaName', () => {
    it('should validate schema names', () => {
        expect(isValidSchemaName('tenant_test')).toBe(true);
        expect(isValidSchemaName('_test')).toBe(true);
        expect(isValidSchemaName('test123')).toBe(true);
    });

    it('should reject invalid schema names', () => {
        expect(isValidSchemaName('')).toBe(false);
        expect(isValidSchemaName('1test')).toBe(false);
        expect(isValidSchemaName('test-tenant')).toBe(false);
        expect(isValidSchemaName('test.tenant')).toBe(false);
        expect(isValidSchemaName('test space')).toBe(false);
    });

    it('should reject schema names that are too long', () => {
        const longSchemaName = 'a'.repeat(64);
        expect(isValidSchemaName(longSchemaName)).toBe(false);
    });
});

describe('getSchemaNameFromTenantId', () => {
    it('should generate schema names from tenant IDs', () => {
        expect(getSchemaNameFromTenantId('test')).toBe('tenant_test');
        expect(getSchemaNameFromTenantId('test-tenant')).toBe('tenant_test_tenant'); // Hyphens are replaced with underscores
        expect(getSchemaNameFromTenantId('TEST')).toBe('tenant_test');
    });

    it('should handle tenant IDs starting with numbers', () => {
        expect(getSchemaNameFromTenantId('123test')).toBe('tenant_x123test'); // 'x' is added before numbers
    });
});

describe('getTenantIdFromSchemaName', () => {
    it('should extract tenant IDs from schema names', () => {
        expect(getTenantIdFromSchemaName('tenant_test')).toBe('test');
        expect(getTenantIdFromSchemaName('tenant_test_tenant')).toBe('test_tenant');
        expect(getTenantIdFromSchemaName('tenant_x123test')).toBe('123test');
    });

    it('should return null for non-tenant schema names', () => {
        expect(getTenantIdFromSchemaName('public')).toBeNull();
        expect(getTenantIdFromSchemaName('test')).toBeNull();
    });
});
