import { Injectable, Logger } from '@nestjs/common';
import { CaseAuditRepository } from '../repositories/case-audit.repository';
import { Request } from 'express';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';

/**
 * Service for logging audit events for case operations
 */
@Injectable()
export class CaseAuditService {
    private readonly logger = new Logger(CaseAuditService.name);

    constructor(private readonly caseAuditRepository: CaseAuditRepository) {}

    /**
     * Logs a case creation event
     */
    async logCaseCreation(
        caseId: string,
        userId: string,
        userName: string,
        request: Request,
        details?: Record<string, any>
    ): Promise<void> {
        await this.logAction(
            caseId,
            CaseAuditAction.CREATED,
            userId,
            userName,
            this.getIpAddress(request),
            details
        );
    }

    /**
     * Logs a case update event
     */
    async logCaseUpdate(
        caseId: string,
        userId: string,
        userName: string,
        request: Request,
        oldValues: Record<string, any>,
        newValues: Record<string, any>
    ): Promise<void> {
        // Only log fields that actually changed
        const changes: Record<string, { from: any; to: any }> = {};

        for (const key in newValues) {
            if (oldValues[key] !== newValues[key]) {
                changes[key] = {
                    from: oldValues[key],
                    to: newValues[key]
                };
            }
        }

        if (Object.keys(changes).length === 0) {
            this.logger.debug(`No changes detected for case ${caseId}, skipping audit log`);
            return;
        }

        await this.logAction(
            caseId,
            CaseAuditAction.UPDATED,
            userId,
            userName,
            this.getIpAddress(request),
            { changes }
        );
    }

    /**
     * Logs a case status change event
     */
    async logStatusChange(
        caseId: string,
        userId: string,
        userName: string,
        request: Request,
        oldStatus: string,
        newStatus: string
    ): Promise<void> {
        await this.logAction(
            caseId,
            CaseAuditAction.STATUS_CHANGED,
            userId,
            userName,
            this.getIpAddress(request),
            {
                oldStatus,
                newStatus
            }
        );
    }

    /**
     * Logs a case assignment event
     */
    async logAssignment(
        caseId: string,
        userId: string,
        userName: string,
        request: Request,
        assignedTo: string,
        assignedToName: string
    ): Promise<void> {
        await this.logAction(
            caseId,
            CaseAuditAction.ASSIGNED,
            userId,
            userName,
            this.getIpAddress(request),
            {
                assignedTo,
                assignedToName
            }
        );
    }

    /**
     * Logs a case unassignment event
     */
    async logUnassignment(
        caseId: string,
        userId: string,
        userName: string,
        request: Request,
        unassignedFrom: string,
        unassignedFromName: string
    ): Promise<void> {
        await this.logAction(
            caseId,
            CaseAuditAction.UNASSIGNED,
            userId,
            userName,
            this.getIpAddress(request),
            {
                unassignedFrom,
                unassignedFromName
            }
        );
    }

    /**
     * Logs a note addition event
     */
    async logNoteAdded(
        caseId: string,
        userId: string,
        userName: string,
        request: Request,
        noteId: string
    ): Promise<void> {
        await this.logAction(
            caseId,
            CaseAuditAction.NOTE_ADDED,
            userId,
            userName,
            this.getIpAddress(request),
            {
                noteId
            }
        );
    }

    /**
     * Logs an attachment addition event
     */
    async logAttachmentAdded(
        caseId: string,
        userId: string,
        userName: string,
        request: Request,
        attachmentId: string,
        filename: string
    ): Promise<void> {
        await this.logAction(
            caseId,
            CaseAuditAction.ATTACHMENT_ADDED,
            userId,
            userName,
            this.getIpAddress(request),
            {
                attachmentId,
                filename
            }
        );
    }

    /**
     * Logs an attachment removal event
     */
    async logAttachmentRemoved(
        caseId: string,
        userId: string,
        userName: string,
        request: Request,
        attachmentId: string,
        filename: string
    ): Promise<void> {
        await this.logAction(
            caseId,
            CaseAuditAction.ATTACHMENT_REMOVED,
            userId,
            userName,
            this.getIpAddress(request),
            {
                attachmentId,
                filename
            }
        );
    }

    /**
     * Generic method to log an action
     */
    async logAction(
        caseId: string,
        action: CaseAuditAction,
        performedBy: string,
        performedByName: string,
        ipAddress: string,
        details?: Record<string, any>
    ): Promise<void> {
        try {
            await this.caseAuditRepository.logAction(
                caseId,
                action,
                performedBy,
                performedByName,
                ipAddress,
                details
            );

            this.logger.debug(
                `Audit log created for case ${caseId}: ${action} by ${performedByName} (${performedBy})`
            );
        } catch (error) {
            this.logger.error(
                `Failed to create audit log for case ${caseId}: ${error.message}`,
                error.stack
            );
            // Don't throw the error to avoid disrupting the main operation
        }
    }

    /**
     * Gets the audit trail for a case
     */
    async getCaseAuditTrail(caseId: string, limit: number = 100): Promise<any[]> {
        return this.caseAuditRepository.findByCaseId(caseId, limit);
    }

    /**
     * Extracts the IP address from the request
     */
    private getIpAddress(request: Request): string {
        return (
            (request.headers['x-forwarded-for'] as string) ||
            request.socket.remoteAddress ||
            'unknown'
        );
    }
}
