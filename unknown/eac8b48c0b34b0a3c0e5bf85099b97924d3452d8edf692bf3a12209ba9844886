import { Injectable, Logger } from '@nestjs/common';
import {
    Repository,
    EntityTarget,
    FindOptionsWhere,
    FindOneOptions,
    FindManyOptions,
    DeepPartial,
    SaveOptions,
    RemoveOptions,
    DataSource
} from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { TenantContextService } from './tenant-context.service';
import { TenantConnectionService } from './tenant-connection.service';

/**
 * Base repository class for tenant-scoped entities
 * Automatically uses the correct schema based on the current tenant
 *
 * @template T The entity type
 */
@Injectable()
export abstract class BaseTenantRepository<T extends object> {
    protected readonly logger = new Logger(BaseTenantRepository.name);
    protected repository: Repository<T>;
    protected entityTarget: EntityTarget<T>;

    constructor(
        entity: EntityTarget<T>,
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService,
        protected readonly isPublicEntity: boolean = false
    ) {
        this.entityTarget = entity;
    }

    /**
     * Gets the repository for the current tenant
     * This method is called before each operation to ensure we're using the correct schema
     *
     * @returns The repository for the current tenant
     */
    protected async getTenantRepository(): Promise<Repository<T>> {
        let dataSource: DataSource;

        // 1. Get the right connection based on context
        if (this.isPublicEntity) {
            dataSource = this.tenantConnectionService.getPublicDataSource();
            this.logger.debug(`Using public connection for ${this.entityTarget?.['name']}`);
        } else if (this.tenantContextService?.hasTenant()) {
            const tenantId = this.tenantContextService.getTenantId();
            if (!tenantId) {
                throw new Error('Tenant ID is not set');
            }

            dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
            this.logger.debug(`Using tenant connection for ${tenantId}`);
        } else {
            // Default to public if no tenant context
            dataSource = this.tenantConnectionService.getPublicDataSource();
            this.logger.debug(`No tenant context, defaulting to public connection`);
        }

        // 2. Set schema context if needed for tenant
        if (!this.isPublicEntity && this.tenantContextService?.hasTenant()) {
            const schema = this.tenantContextService.getTenantSchema();
            // Need to ensure schema is set for each query
            await dataSource.query(`SET search_path TO "${schema}"`);
        }

        return dataSource.getRepository(this.entityTarget);
    }

    /**
     * Finds entities that match the given conditions
     *
     * @param conditions The conditions to match
     * @returns An array of matching entities
     */
    async find(options?: FindManyOptions<T>): Promise<T[]> {
        const repository = await this.getTenantRepository();
        return repository.find(options);
    }

    /**
     * Finds entities that match the given where conditions
     *
     * @param where The where conditions
     * @returns An array of matching entities
     */
    async findBy(where: FindOptionsWhere<T> | FindOptionsWhere<T>[]): Promise<T[]> {
        const repository = await this.getTenantRepository();
        return repository.findBy(where);
    }

    /**
     * Finds the first entity that matches the given conditions
     *
     * @param options The find options
     * @returns The matching entity or null
     */
    async findOne(options: FindOneOptions<T>): Promise<T | null> {
        const repository = await this.getTenantRepository();
        return repository.findOne(options);
    }

    /**
     * Finds the first entity that matches the given where conditions
     *
     * @param where The where conditions
     * @returns The matching entity or null
     */
    async findOneBy(where: FindOptionsWhere<T> | FindOptionsWhere<T>[]): Promise<T | null> {
        const repository = await this.getTenantRepository();
        return repository.findOneBy(where);
    }

    /**
     * Finds an entity by its ID
     *
     * @param id The entity ID
     * @returns The matching entity or null
     */
    async findOneById(id: string | number): Promise<T | null> {
        const repository = await this.getTenantRepository();
        return repository.findOneBy({ id } as unknown as FindOptionsWhere<T>);
    }

    /**
     * Creates a new entity instance
     *
     * @param entityLike The entity-like object
     * @returns The created entity
     */
    async create(entityLike: DeepPartial<T>): Promise<T> {
        const repository = await this.getTenantRepository();
        return repository.create(entityLike);
    }

    /**
     * Saves an entity
     *
     * @param entity The entity to save
     * @param options Save options
     * @returns The saved entity
     */
    async save(entity: DeepPartial<T>, options?: SaveOptions): Promise<T> {
        const repository = await this.getTenantRepository();
        return repository.save(entity, options);
    }

    /**
     * Updates an entity
     *
     * @param id The entity ID
     * @param partialEntity The partial entity with updated fields
     * @returns The updated entity
     */
    async update(id: string | number, partialEntity: DeepPartial<T>): Promise<T | null> {
        const repository = await this.getTenantRepository();
        await repository.update(id, partialEntity as QueryDeepPartialEntity<T>);
        return this.findOneById(id);
    }

    /**
     * Removes an entity
     *
     * @param entity The entity to remove
     * @param options Remove options
     * @returns The removed entity
     */
    async remove(entity: T, options?: RemoveOptions): Promise<T> {
        const repository = await this.getTenantRepository();
        return repository.remove(entity, options);
    }

    /**
     * Removes an entity by its ID
     *
     * @param id The entity ID
     * @returns True if the entity was removed, false otherwise
     */
    async removeById(id: string | number): Promise<boolean> {
        const repository = await this.getTenantRepository();
        const result = await repository.delete(id);
        return result.affected !== null && result.affected !== undefined && result.affected > 0;
    }

    /**
     * Counts entities that match the given conditions
     *
     * @param options The find options
     * @returns The count
     */
    async count(options?: FindManyOptions<T>): Promise<number> {
        const repository = await this.getTenantRepository();
        return repository.count(options);
    }

    /**
     * Executes a raw SQL query in the tenant's schema
     *
     * @param query The SQL query
     * @param parameters The query parameters
     * @returns The query result
     */
    async query(query: string, parameters?: any[]): Promise<any> {
        const tenantId = this.tenantContextService.getTenantId();
        const dataSource = await this.tenantConnectionService.getTenantDataSource(
            tenantId || 'public'
        );

        // The schema is already set in the data source options, so we can execute the query directly
        return dataSource.query(query, parameters);
    }

    /**
     * Gets the DataSource for the current tenant
     *
     * @returns The DataSource for the current tenant
     */
    async getDataSource(): Promise<DataSource> {
        const tenantId = this.tenantContextService.getTenantId();
        return this.tenantConnectionService.getTenantDataSource(tenantId || 'public');
    }
}
