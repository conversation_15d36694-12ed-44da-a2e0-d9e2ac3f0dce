import { Controller, Get } from '@nestjs/common';
import { HealthService } from '@app/common/health';
import { HealthCheckResult } from '@nestjs/terminus';

@Controller('health')
export class MicroHealthController {
    constructor(private readonly healthService: HealthService) {}

    @Get()
    getHealth(): { status: string; details: Record<string, any> } {
        return this.healthService.getStatus();
    }

    @Get('db')
    checkDb(): Promise<HealthCheckResult> {
        return this.healthService.checkDbHealth();
    }
}
