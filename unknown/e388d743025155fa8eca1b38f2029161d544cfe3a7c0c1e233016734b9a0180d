import { HttpStatusCode } from '../enums/http-status.enum';
import { BaseException, ExceptionMetadata } from './base.exception';

/**
 * Exception for internal server errors
 */
export class InternalServerException extends BaseException {
    /**
     * Creates a new InternalServerException
     * @param message Error message
     * @param errorCode Optional error code
     * @param meta Optional additional metadata
     */
    constructor(message = 'Internal server error', errorCode?: string, meta?: ExceptionMetadata) {
        super(message, HttpStatusCode.INTERNAL_SERVER_ERROR, errorCode, meta);
    }
}
