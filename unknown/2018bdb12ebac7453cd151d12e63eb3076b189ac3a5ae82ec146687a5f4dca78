import { IsString, <PERSON><PERSON>otE<PERSON>y, <PERSON><PERSON>ption<PERSON>, IsBoolean } from 'class-validator';

/**
 * DTO for creating a new role
 */
export class CreateRoleDto {
    /**
     * Role name
     */
    @IsString()
    @IsNotEmpty()
    name: string;

    /**
     * Role description
     */
    @IsString()
    @IsOptional()
    description?: string;

    /**
     * Whether the role is composite
     */
    @IsBoolean()
    @IsOptional()
    composite?: boolean;

    /**
     * Realm to create the role in
     */
    @IsString()
    @IsNotEmpty()
    realm: string;

    /**
     * Admin password for the realm admin
     * Only needed when using a realm-specific admin token
     */
    @IsString()
    @IsOptional()
    adminPassword?: string;
}
