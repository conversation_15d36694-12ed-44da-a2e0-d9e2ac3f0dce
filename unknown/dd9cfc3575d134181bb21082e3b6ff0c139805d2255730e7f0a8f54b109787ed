/**
 * Raw application environment variables interface
 * This represents the raw, possibly undefined environment variables
 */
export interface RawAppEnv {
    NODE_ENV?: string;
    API_GLOBAL_PREFIX?: string;
    API_RATE_LIMIT_WINDOW_MS?: string;
    API_RATE_LIMIT_MAX?: string;
    CORS_ENABLED?: string;
    CORS_ORIGIN?: string;
    LOG_LEVEL?: string;
    CORE_PORT?: string;
}

/**
 * Application configuration interface
 * This represents the fully validated configuration
 */
export interface AppConfig {
    nodeEnv: string;
    isProduction: boolean;
    isDevelopment: boolean;
    isTest: boolean;

    // API configuration
    apiGlobalPrefix: string;
    rateLimitWindowMs: number;
    rateLimitMax: number;

    // CORS configuration
    corsEnabled: boolean;
    corsOrigin: string;

    // Logging
    logLevel: string;

    // Port
    port: number;
}
