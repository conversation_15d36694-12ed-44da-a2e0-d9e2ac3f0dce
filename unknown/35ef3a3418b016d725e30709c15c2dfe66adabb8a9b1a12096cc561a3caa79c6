import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

/**
 * DTO for assigning roles to a user
 */
export class AssignRoleDto {
    /**
     * Array of role names to assign
     */
    @IsArray()
    @IsNotEmpty()
    roles: string[];

    /**
     * Realm where the user and roles exist
     */
    @IsString()
    @IsNotEmpty()
    realm: string;

    /**
     * Admin password for the realm admin
     * Only needed when using a realm-specific admin token
     */
    @IsString()
    @IsOptional()
    adminPassword?: string;
}
