import { Module, DynamicModule, Provider, Global } from '@nestjs/common';
import { TenantContextService } from './tenant-context.service';
import { TenantConnectionService } from './tenant-connection.service';
import { TenantGuard } from './tenant.guard';
import { MultiTenancyOptions } from './interfaces/tenant.interface';
import { APP_GUARD } from '@nestjs/core';
import { TenantMigrationService } from './migrations/tenant-migration.service';
import { ConfigModule } from '../config/config.module';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

/**
 * Module token for multi-tenancy options
 */
export const MULTI_TENANCY_OPTIONS = 'MULTI_TENANCY_OPTIONS';

/**
 * Module for multi-tenancy support
 * Provides services for tenant context, connection management, and guards
 */
@Global()
@Module({
    providers: [
        {
            provide: 'DATA_SOURCE',
            useFactory: (configService: ConfigService) => {
                const options = {
                    type: 'postgres',
                    host: configService.get('POSTGRES_HOST'),
                    port: parseInt(configService.get('POSTGRES_PORT') || '5432'),
                    username: configService.get('POSTGRES_USER'),
                    password: configService.get('POSTGRES_PASSWORD'),
                    database: configService.get('POSTGRES_DB'),
                    ssl: configService.get('POSTGRES_SSL') === 'true'
                };
                return new DataSource(options as PostgresConnectionOptions);
            },
            inject: [ConfigService]
        }
        // ...other providers
    ],
    exports: ['DATA_SOURCE']
})
export class MultiTenancyModule {
    /**
     * Registers the multi-tenancy module with the provided options
     * @param options Configuration options for the multi-tenancy module
     * @returns The configured module
     */
    static forRoot(options: MultiTenancyOptions = {}): DynamicModule {
        const optionsProvider: Provider = {
            provide: MULTI_TENANCY_OPTIONS,
            useValue: {
                maxConnections: options.maxConnections || 50,
                connectionTtl: options.connectionTtl || 3600000, // 1 hour
                autoCreateSchema: options.autoCreateSchema !== false, // Default to true
                autoRunMigrations: options.autoRunMigrations || false,
                migrationsDir: options.migrationsDir
            }
        };

        return {
            module: MultiTenancyModule,
            imports: [ConfigModule],
            providers: [
                optionsProvider,
                TenantContextService,
                TenantConnectionService,
                TenantGuard,
                TenantMigrationService
            ],
            exports: [
                TenantContextService,
                TenantConnectionService,
                TenantGuard,
                TenantMigrationService
            ]
        };
    }

    /**
     * Registers the multi-tenancy module asynchronously
     * @param options Async options for the multi-tenancy module
     * @returns The configured module
     */
    static forRootAsync(options: {
        imports?: any[];
        useFactory: (...args: any[]) => MultiTenancyOptions | Promise<MultiTenancyOptions>;
        inject?: any[];
    }): DynamicModule {
        const optionsProvider: Provider = {
            provide: MULTI_TENANCY_OPTIONS,
            useFactory: options.useFactory,
            inject: options.inject || []
        };

        return {
            module: MultiTenancyModule,
            imports: [...(options.imports || []), ConfigModule],
            providers: [
                optionsProvider,
                TenantContextService,
                TenantConnectionService,
                TenantGuard,
                TenantMigrationService,
                {
                    provide: APP_GUARD,
                    useClass: TenantGuard
                }
            ],
            exports: [
                TenantContextService,
                TenantConnectionService,
                TenantGuard,
                TenantMigrationService
            ]
        };
    }
}
