import { Column, <PERSON><PERSON><PERSON>, ManyTo<PERSON>any, PrimaryGeneratedColumn, JoinTable } from 'typeorm';
import { SystemUser } from './system-user.entity';

@Entity({ schema: 'public', name: 'tenants' })
export class Tenant {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ unique: true })
    realm: string;

    @Column({ name: 'display_name', nullable: true })
    displayName?: string;

    @Column({ name: 'admin_username', nullable: true })
    adminUsername: string;

    @Column({ name: 'admin_email', unique: true, nullable: true })
    adminEmail: string;

    @Column({ name: 'admin_first_name', nullable: true })
    adminFirstName: string;

    @Column({ name: 'admin_last_name', nullable: true })
    adminLastName: string;

    @Column({ name: 'registration_allowed', default: false })
    registrationAllowed: boolean;

    @Column({ name: 'verify_email', default: true })
    verifyEmail: boolean;

    @Column({ name: 'remember_me', default: true })
    rememberMe: boolean;

    @Column({ name: 'dedicated_realm_admin', default: true })
    dedicatedRealmAdmin: boolean;

    @Column({ name: 'client_id', nullable: true })
    clientId: string;

    @Column({ name: 'client_secret', nullable: true })
    clientSecret: string;

    @Column({ default: true })
    enabled: boolean;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @ManyToMany(() => SystemUser, (user) => user.tenants)
    @JoinTable({
        name: 'user_tenants',
        schema: 'public',
        joinColumn: {
            name: 'tenant_id',
            referencedColumnName: 'id'
        },
        inverseJoinColumn: {
            name: 'user_id',
            referencedColumnName: 'id'
        }
    })
    users: SystemUser[];
}
