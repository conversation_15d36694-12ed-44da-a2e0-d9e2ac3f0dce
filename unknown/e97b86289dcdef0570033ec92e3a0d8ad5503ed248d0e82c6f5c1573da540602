#!/bin/bash
set -e

MAX_RETRIES=30
RETRY_INTERVAL=5

echo "Waiting for Keycloak to be ready..."

for i in $(seq 1 $MAX_RETRIES); do
  if curl -s http://keycloak:8080/ > /dev/null; then
    echo "Keycloak is ready!"
    exit 0
  fi
  
  echo "Keycloak not ready yet (attempt $i/$MAX_RETRIES). Retrying in $RETRY_INTERVAL seconds..."
  sleep $RETRY_INTERVAL
done

echo "Failed to connect to Keycloak after $MAX_RETRIES attempts"
exit 1 