services:
  # Test PostgreSQL database for our application
  postgres-test:
    image: postgres:15-alpine
    container_name: tk-lpm-postgres-test
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: tk_lpm_test
      # Additional PostgreSQL configurations
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5434:5432"  # Using 5434 externally to avoid conflict
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      # Mount the SQL initialization scripts for the test database
      - ./initdb/test-setup.sql:/docker-entrypoint-initdb.d/00-test-setup.sql
      - ./initdb/test-migration.sql:/docker-entrypoint-initdb.d/01-test-migration.sql
    networks:
      - test-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d tk_lpm_test"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    command: ["postgres", "-c", "max_connections=200"]

  # Test Keycloak instance - simplified dev mode with H2 database for testing
  keycloak-test:
    image: quay.io/keycloak/keycloak:22.0.1
    container_name: tk-lpm-keycloak-test
    restart: unless-stopped
    environment:
      # Use dev mode with H2 database for testing (much faster startup)
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
      # Basic settings
      KC_HTTP_ENABLED: "true"
      KC_HEALTH_ENABLED: "true"
      # Performance optimizations for testing
      KC_METRICS_ENABLED: "false"
      KC_LOG_LEVEL: "warn"
      # Java options
      JAVA_OPTS: "-Xms256m -Xmx512m"
    ports:
      - "8090:8080"  # Using 8090 externally to avoid conflict
    networks:
      - test-network
    command: 
      - start-dev
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 30s

networks:
  test-network:
    driver: bridge

volumes:
  postgres_test_data:
    driver: local 