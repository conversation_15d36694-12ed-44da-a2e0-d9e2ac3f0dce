module.exports = {
  displayName: 'document-engine',
  preset: '../../jest.preset.js',
  rootDir: '../../',
  testMatch: ['<rootDir>/apps/document-engine/**/*.spec.ts'],
  moduleNameMapper: {
    '^@app/common(.*)$': '<rootDir>/libs/common/src$1'
  },
  setupFilesAfterEnv: ['<rootDir>/test/jest-setup.ts'],
  coverageDirectory: '<rootDir>/coverage/apps/document-engine',
  testEnvironment: 'node',
  // Pass tests if no tests are found
  testPathIgnorePatterns: ['/node_modules/'],
  passWithNoTests: true
};
