import { Injectable } from '@nestjs/common';

/**
 * Utility class for token-related operations
 */
@Injectable()
export class TokenUtils {
    /**
     * Generate suggested token name based on field and entity
     */
    generateSuggestedTokenName(fieldName: string, entityName: string): string {
        // Special cases for common fields
        if (fieldName === 'name' && entityName === 'client') {
            return 'clientName';
        }
        if (fieldName === 'name' && entityName === 'lawyer') {
            return 'lawyerName';
        }

        // For property-related fields accessed via case, use descriptive names
        const propertyFieldMappings: Record<string, string> = {
            propertyAddress: 'propertyAddress',
            purchasePrice: 'purchasePrice',
            vendorSolicitorName: 'vendorSolicitor',
            vendorSolicitorFirm: 'vendorSolicitorFirm',
            assignedLawyerName: 'assignedLawyer'
        };

        if (propertyFieldMappings[fieldName]) {
            return propertyFieldMappings[fieldName];
        }

        // Default: use the field name as-is
        return fieldName;
    }

    /**
     * Categorize fields for better organization
     */
    categorizeField(fieldName: string, entityName: string): string {
        // Date fields
        if (fieldName.includes('Date') || fieldName.includes('date')) {
            return 'dates';
        }

        // Financial fields
        if (
            fieldName.includes('Price') ||
            fieldName.includes('Amount') ||
            fieldName.includes('price') ||
            fieldName.includes('amount')
        ) {
            return 'financial';
        }

        // Contact fields
        if (
            fieldName.includes('email') ||
            fieldName.includes('phone') ||
            fieldName.includes('Email') ||
            fieldName.includes('Phone')
        ) {
            return 'contact';
        }

        // Address fields
        if (fieldName.includes('address') || fieldName.includes('Address')) {
            return 'address';
        }

        // Solicitor/legal fields
        if (
            fieldName.includes('solicitor') ||
            fieldName.includes('Solicitor') ||
            fieldName.includes('lawyer') ||
            fieldName.includes('Lawyer')
        ) {
            return 'legal';
        }

        // Property fields
        if (
            fieldName.includes('property') ||
            fieldName.includes('Property') ||
            fieldName.includes('tenure') ||
            fieldName.includes('bedrooms')
        ) {
            return 'property';
        }

        // System fields
        if (entityName === 'system' || fieldName.includes('current')) {
            return 'system';
        }

        // Default to entity name
        return entityName;
    }

    /**
     * Get usage context for fields
     */
    getFieldUsageContext(fieldName: string): string {
        const contextMap: Record<string, string> = {
            caseNumber: 'Used in all documents for case identification',
            title: 'Case description or title for correspondence',
            exchangeDate: 'Critical for exchange confirmation documents',
            completionDate: 'Used in completion notices and client communications',
            propertyAddress: 'Essential for all property-related documents',
            purchasePrice: 'Required in contracts and financial communications',
            clientName: 'Used in all client-facing documents',
            email: 'For correspondence and contact details',
            vendorSolicitorName: 'Required for solicitor correspondence',
            vendorSolicitorFirm: 'For solicitor firm identification in documents',
            assignedLawyerName: 'Used in case-related communications',
            bedrooms: 'Property description in sales/purchase documents',
            tenure: 'Property legal status in contracts',
            currentDate: 'System-generated current date for document timestamps',
            currentUser: 'Information about the user generating the document'
        };

        return contextMap[fieldName] || `Used for ${fieldName} data in documents`;
    }

    /**
     * Generate entity token suggestions
     */
    generateEntityTokenSuggestions(entityName: string): Array<{
        field: string;
        suggestedTokenName: string;
        category: string;
        usageContext: string;
        priority: 'high' | 'medium' | 'low';
    }> {
        const suggestions: Array<{
            field: string;
            suggestedTokenName: string;
            category: string;
            usageContext: string;
            priority: 'high' | 'medium' | 'low';
        }> = [];

        // Define common fields for each entity with priorities
        const entityFields: Record<
            string,
            Array<{ field: string; priority: 'high' | 'medium' | 'low' }>
        > = {
            case: [
                { field: 'caseNumber', priority: 'high' },
                { field: 'title', priority: 'high' },
                { field: 'exchangeDate', priority: 'medium' },
                { field: 'completionDate', priority: 'medium' }
            ],
            client: [
                { field: 'name', priority: 'high' },
                { field: 'email', priority: 'high' },
                { field: 'phone', priority: 'medium' },
                { field: 'address', priority: 'medium' }
            ],
            property: [
                { field: 'propertyAddress', priority: 'high' },
                { field: 'purchasePrice', priority: 'high' },
                { field: 'bedrooms', priority: 'low' },
                { field: 'tenure', priority: 'medium' }
            ]
        };

        const fields = entityFields[entityName] || [];

        for (const { field, priority } of fields) {
            suggestions.push({
                field,
                suggestedTokenName: this.generateSuggestedTokenName(field, entityName),
                category: this.categorizeField(field, entityName),
                usageContext: this.getFieldUsageContext(field),
                priority
            });
        }

        return suggestions;
    }
}
