import { Injectable } from '@nestjs/common';

/**
 * Utility class for template analysis operations
 */
@Injectable()
export class TemplateAnalysisUtils {
    /**
     * Generate token suggestions based on template analysis
     */
    generateTokenSuggestions(analysis: any): string[] {
        const suggestions: string[] = [];

        if (analysis.invalidTokens.length > 0) {
            suggestions.push('Remove or replace invalid tokens with supported alternatives');
        }

        if (analysis.missingRequiredTokens.length > 0) {
            suggestions.push('Add missing required tokens to ensure template functionality');
        }

        if (analysis.uniqueTokens.length === 0) {
            suggestions.push('Consider adding dynamic tokens to make the template more flexible');
        }

        if (analysis.validTokens.length > 0 && analysis.invalidTokens.length === 0) {
            suggestions.push('Template tokens are valid and ready for upload');
        }

        return suggestions;
    }

    /**
     * Generate token recommendations based on usage statistics
     */
    generateTokenRecommendations(stats: any[], customTokens: any[]): string[] {
        const recommendations: string[] = [];

        const unusedTokens = customTokens.filter((t) => t.usageCount === 0);

        if (unusedTokens.length > 0) {
            recommendations.push(
                `Consider reviewing ${unusedTokens.length} unused tokens for cleanup or promotion`
            );
        }

        const highUsageTokens = stats.filter((t) => t.usageCount > 10);
        if (highUsageTokens.length > 0) {
            recommendations.push(
                `${highUsageTokens.length} tokens are heavily used - consider creating variations or related tokens`
            );
        }

        const recentTokens = customTokens.filter(
            (t) => new Date(t.createdAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        );
        if (recentTokens.length > 0) {
            recommendations.push(
                `${recentTokens.length} tokens were created recently - monitor their usage patterns`
            );
        }

        if (recommendations.length === 0) {
            recommendations.push('Token usage looks healthy - no immediate recommendations');
        }

        return recommendations;
    }

    /**
     * Analyze template complexity
     */
    analyzeTemplateComplexity(tokens: string[]): {
        complexity: 'simple' | 'moderate' | 'complex';
        score: number;
        factors: string[];
    } {
        const factors: string[] = [];
        let score = 0;

        // Base complexity on token count
        score += tokens.length;

        if (tokens.length > 20) {
            factors.push('High token count');
            score += 10;
        }

        // Check for nested tokens
        const nestedTokens = tokens.filter((token) => token.includes('.'));
        if (nestedTokens.length > 0) {
            factors.push('Contains nested tokens');
            score += nestedTokens.length * 2;
        }

        // Check for conditional tokens
        const conditionalTokens = tokens.filter(
            (token) => token.includes('if') || token.includes('else') || token.includes('?')
        );
        if (conditionalTokens.length > 0) {
            factors.push('Contains conditional logic');
            score += conditionalTokens.length * 3;
        }

        // Check for array/loop tokens
        const arrayTokens = tokens.filter(
            (token) => token.includes('#each') || token.includes('[') || token.includes('forEach')
        );
        if (arrayTokens.length > 0) {
            factors.push('Contains array/loop operations');
            score += arrayTokens.length * 5;
        }

        let complexity: 'simple' | 'moderate' | 'complex';
        if (score <= 10) {
            complexity = 'simple';
        } else if (score <= 25) {
            complexity = 'moderate';
        } else {
            complexity = 'complex';
        }

        return { complexity, score, factors };
    }

    /**
     * Generate template health score
     */
    generateTemplateHealthScore(analysis: any): {
        score: number;
        grade: 'A' | 'B' | 'C' | 'D' | 'F';
        issues: string[];
    } {
        let score = 100;
        const issues: string[] = [];

        // Deduct points for invalid tokens
        if (analysis.invalidTokens.length > 0) {
            const deduction = analysis.invalidTokens.length * 10;
            score -= deduction;
            issues.push(`${analysis.invalidTokens.length} invalid tokens (-${deduction} points)`);
        }

        // Deduct points for missing required tokens
        if (analysis.missingRequiredTokens?.length > 0) {
            const deduction = analysis.missingRequiredTokens.length * 15;
            score -= deduction;
            issues.push(
                `${analysis.missingRequiredTokens.length} missing required tokens (-${deduction} points)`
            );
        }

        // Deduct points for unused tokens in template
        if (analysis.unusedTokens?.length > 0) {
            const deduction = analysis.unusedTokens.length * 5;
            score -= deduction;
            issues.push(`${analysis.unusedTokens.length} unused tokens (-${deduction} points)`);
        }

        // Bonus points for good practices
        if (analysis.validTokens.length > 5) {
            score += 5;
            issues.push('Good token variety (+5 points)');
        }

        // Ensure score doesn't go below 0
        score = Math.max(0, score);

        let grade: 'A' | 'B' | 'C' | 'D' | 'F';
        if (score >= 90) grade = 'A';
        else if (score >= 80) grade = 'B';
        else if (score >= 70) grade = 'C';
        else if (score >= 60) grade = 'D';
        else grade = 'F';

        return { score, grade, issues };
    }
}
