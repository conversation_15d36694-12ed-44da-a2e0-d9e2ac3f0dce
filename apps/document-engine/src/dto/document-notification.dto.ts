import { IsString, IsBoolean, IsOptional, IsArray, IsEmail, IsEnum } from 'class-validator';
import { DocumentNotificationType } from '@app/common/enums/document-notification-types.enum';
import {
    DocumentActionType,
    DocumentOperationType
} from '@app/common/enums/document-action-types.enum';

export class DocumentUploadNotificationDto {
    @IsOptional()
    @IsEnum(DocumentNotificationType)
    type?: DocumentNotificationType = DocumentNotificationType.DOCUMENT_UPLOADED;

    @IsString()
    documentName: string;

    @IsString()
    caseId: string;

    @IsOptional()
    @IsString()
    caseNumber?: string;

    @IsOptional()
    @IsString()
    folderId?: string;

    @IsOptional()
    @IsBoolean()
    notifyUpload?: boolean = true;

    @IsOptional()
    @IsArray()
    @IsEmail({}, { each: true })
    additionalRecipients?: string[];
}

export class DocumentGenerationNotificationDto {
    @IsOptional()
    @IsEnum(DocumentNotificationType)
    type?: DocumentNotificationType = DocumentNotificationType.DOCUMENT_GENERATED;

    @IsString()
    templateId: string;

    @IsString()
    caseId: string;

    @IsOptional()
    @IsString()
    caseNumber?: string;

    @IsOptional()
    @IsString()
    outputFileName?: string;

    @IsOptional()
    @IsBoolean()
    notifyGeneration?: boolean = true;

    @IsOptional()
    @IsArray()
    @IsEmail({}, { each: true })
    additionalRecipients?: string[];
}

export class DocumentShareNotificationDto {
    @IsOptional()
    @IsEnum(DocumentNotificationType)
    type?: DocumentNotificationType = DocumentNotificationType.DOCUMENT_SHARED;

    @IsString()
    documentId: string;

    @IsArray()
    @IsEmail({}, { each: true })
    sharedWithEmails: string[];

    @IsArray()
    @IsString({ each: true })
    sharedWithUserIds: string[];

    @IsOptional()
    @IsString()
    shareMessage?: string;

    @IsOptional()
    @IsString()
    caseNumber?: string;
}

export class DocumentDeletionNotificationDto {
    @IsOptional()
    @IsEnum(DocumentNotificationType)
    type?: DocumentNotificationType = DocumentNotificationType.DOCUMENT_DELETED;

    @IsString()
    documentId: string;

    @IsOptional()
    @IsString()
    caseNumber?: string;

    @IsOptional()
    @IsBoolean()
    notifyDeletion?: boolean = true;
}

export class BatchDocumentNotificationDto {
    @IsOptional()
    @IsEnum([
        DocumentNotificationType.BATCH_DOCUMENTS_UPLOADED,
        DocumentNotificationType.BATCH_DOCUMENTS_DELETED,
        DocumentNotificationType.BATCH_DOCUMENTS_SHARED
    ])
    type?: DocumentNotificationType;

    @IsArray()
    @IsString({ each: true })
    documentIds: string[];

    @IsEnum(DocumentOperationType)
    operation: DocumentOperationType;

    @IsString()
    caseId: string;

    @IsOptional()
    @IsString()
    caseNumber?: string;

    @IsOptional()
    @IsArray()
    @IsEmail({}, { each: true })
    additionalRecipients?: string[];
}

export class DocumentActionReminderDto {
    @IsOptional()
    @IsEnum([
        DocumentNotificationType.DOCUMENT_REVIEW_REMINDER,
        DocumentNotificationType.DOCUMENT_APPROVAL_REMINDER,
        DocumentNotificationType.DOCUMENT_SIGNATURE_REMINDER
    ])
    type?: DocumentNotificationType;

    @IsString()
    documentId: string;

    @IsEnum(DocumentActionType)
    actionType: DocumentActionType;

    @IsOptional()
    @IsString()
    dueDate?: string; // ISO date string

    @IsOptional()
    @IsString()
    documentUrl?: string;

    @IsOptional()
    @IsString()
    caseNumber?: string;
}
