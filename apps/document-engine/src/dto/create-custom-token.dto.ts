import { IsString, IsOptional, <PERSON><PERSON>rray, IsEnum, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { TokenDataType } from '@app/common/typeorm/entities/tenant/custom-token.entity';

class TransformationConfigDto {
    @IsOptional()
    @IsString()
    format?: string;

    @IsOptional()
    @IsString()
    prefix?: string;

    @IsOptional()
    @IsString()
    suffix?: string;

    @IsOptional()
    uppercase?: boolean;

    @IsOptional()
    lowercase?: boolean;

    @IsOptional()
    truncate?: number;

    @IsOptional()
    defaultValue?: any;
}

class ValidationConfigDto {
    @IsOptional()
    required?: boolean;

    @IsOptional()
    minLength?: number;

    @IsOptional()
    maxLength?: number;

    @IsOptional()
    @IsString()
    pattern?: string;

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    allowedValues?: string[];
}

export class CreateCustomTokenDto {
    @IsString()
    tokenName: string;

    @IsOptional()
    @IsString()
    description?: string;

    @IsEnum(TokenDataType)
    dataType: TokenDataType;

    @IsString()
    entityName: string;

    @IsString()
    fieldPath: string;

    @IsOptional()
    @ValidateNested()
    @Type(() => TransformationConfigDto)
    transformationConfig?: TransformationConfigDto;

    @IsOptional()
    @ValidateNested()
    @Type(() => ValidationConfigDto)
    validationConfig?: ValidationConfigDto;

    @IsOptional()
    @IsString()
    category?: string;

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    tags?: string[];

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    compatibleTemplateTypes?: string[];

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    compatibleCaseTypes?: string[];
}
