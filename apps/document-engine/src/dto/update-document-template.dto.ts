import { IsString, IsOptional, IsEnum, IsBoolean, IsArray } from 'class-validator';
import { DocumentTemplateCategory, DocumentGenerationTrigger } from '@app/common/typeorm/entities';

export class UpdateDocumentTemplateDto {
    @IsOptional()
    @IsString()
    name?: string;

    @IsOptional()
    @IsString()
    description?: string;

    @IsOptional()
    @IsEnum(DocumentTemplateCategory)
    category?: DocumentTemplateCategory;

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    requiredTokens?: string[];

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    optionalTokens?: string[];

    @IsOptional()
    @IsArray()
    @IsEnum(DocumentGenerationTrigger, { each: true })
    generationTriggers?: DocumentGenerationTrigger[];

    @IsOptional()
    @IsBoolean()
    autoAttachToCase?: boolean;

    @IsOptional()
    @IsBoolean()
    autoEmailToClient?: boolean;

    @IsOptional()
    @IsString()
    emailTemplateType?: string;

    @IsOptional()
    @IsString()
    outputFileNameTemplate?: string;
}
