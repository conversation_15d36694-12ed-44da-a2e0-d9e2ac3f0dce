import { IsString, IsOptional, IsEnum, IsBoolean, IsArray } from 'class-validator';
import { Transform } from 'class-transformer';
import {
    DocumentTemplateType,
    DocumentTemplateCategory,
    DocumentGenerationTrigger,
    PartyType,
    CaseType
} from '@app/common/typeorm/entities';

export class CreateDocumentTemplateDto {
    @IsString()
    name: string;

    @IsOptional()
    @IsString()
    description?: string;

    @IsEnum(DocumentTemplateType)
    templateType: DocumentTemplateType;

    @IsOptional()
    @IsEnum(DocumentTemplateCategory)
    category?: DocumentTemplateCategory;

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    requiredTokens?: string[];

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    optionalTokens?: string[];

    @IsOptional()
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    @IsArray()
    @IsEnum(DocumentGenerationTrigger, { each: true })
    generationTriggers?: DocumentGenerationTrigger[];

    @IsOptional()
    @IsBoolean()
    autoAttachToCase?: boolean;

    @IsOptional()
    @IsBoolean()
    autoEmailToClient?: boolean;

    @IsOptional()
    @IsString()
    emailTemplateType?: string;

    @IsOptional()
    @IsString()
    outputFileNameTemplate?: string;

    @IsOptional()
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    @IsArray()
    @IsEnum(PartyType, { each: true })
    partyTypes?: PartyType[];

    @IsOptional()
    @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
    @IsArray()
    @IsEnum(CaseType, { each: true })
    allowedCaseTypes?: CaseType[];
}
