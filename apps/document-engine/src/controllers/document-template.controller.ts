import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    UploadedFile,
    UseGuards,
    UseInterceptors,
    ValidationPipe,
    BadRequestException
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { DocumentTemplateService } from '../document/services/document-template.service';
import { TemplateValidationService } from '../document/services/template-validation.service';
import { TokenExtractionService } from '../document/services/token-extraction.service';
import { TokenManagementService } from '../document/services/token-management.service';
import {
    DocumentGenerationService,
    GenerationRequest
} from '../document/services/document-generation.service';
import {
    DocumentTemplateType,
    DocumentTemplateCategory,
    DocumentTemplateStatus,
    CaseType,
    PartyType
} from '@app/common/typeorm/entities';
import { CreateDocumentTemplateDto, UpdateDocumentTemplateDto } from '../dto';
import { DocxFileValidationPipe } from '../validation/file-validation.pipe';
import { TemplateAnalysisUtils } from '../utils/template-analysis.utils';
import { TenantGuard, TenantContextService } from '@app/common/multi-tenancy';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { Request } from 'express';
import { ApiResponseUtil } from '@app/common/api-response';

interface MulterFile {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    size: number;
    destination: string;
    filename: string;
    path: string;
    buffer: Buffer;
}

@Controller('document-templates')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class DocumentTemplateController {
    constructor(
        private readonly documentTemplateService: DocumentTemplateService,
        private readonly templateValidationService: TemplateValidationService,
        private readonly tokenExtractionService: TokenExtractionService,
        private readonly tokenManagementService: TokenManagementService,
        private readonly documentGenerationService: DocumentGenerationService,
        private readonly templateAnalysisUtils: TemplateAnalysisUtils,
        private readonly tenantContextService: TenantContextService
    ) {}

    @Post()
    @UseInterceptors(FileInterceptor('file'))
    async createTemplate(
        @UploadedFile(DocxFileValidationPipe) file: MulterFile,
        @Body(new ValidationPipe({ transform: true })) data: CreateDocumentTemplateDto,
        @Req() request: Request
    ) {
        const user = request['user'];

        try {
            // MANDATORY: Validate all tokens before creating template
            const tokenValidation = await this.templateValidationService.validateTemplateTokens(
                file.buffer,
                data.category,
                data.allowedCaseTypes
            );

            // FAIL if any tokens are invalid
            if (!tokenValidation.isValid) {
                return ApiResponseUtil.badRequest(
                    'Template validation failed: Invalid tokens found',
                    {
                        filename: file.originalname,
                        validation: tokenValidation,
                        details: {
                            invalidTokens: tokenValidation.invalidTokens,
                            errors: tokenValidation.errors,
                            suggestions: [
                                'Create missing custom tokens via POST /tokens',
                                'Use valid system tokens: ' +
                                    (
                                        await this.templateValidationService.getAllAvailableTokens()
                                    ).join(', '),
                                'Check GET /tokens/tokenizable-fields for available fields'
                            ]
                        }
                    }
                );
            }

            // Only create template if all tokens are valid
            const template = await this.documentTemplateService.createTemplate({
                ...data,
                fileBuffer: file.buffer,
                filename: file.originalname,
                createdBy: user.systemUserId
            });

            return ApiResponseUtil.created(
                {
                    ...template,
                    tokenValidation: {
                        totalTokens: tokenValidation.foundTokens.length,
                        systemTokens: tokenValidation.systemTokens.length,
                        customTokens: tokenValidation.customTokens.length,
                        validTokens: tokenValidation.validTokens.length
                    }
                },
                'Document template created successfully with valid tokens'
            );
        } catch (error) {
            // Handle template validation errors with proper format
            if (error instanceof BadRequestException) {
                const response = error.getResponse();
                if (typeof response === 'object' && response !== null && 'details' in response) {
                    return ApiResponseUtil.badRequest(
                        error.message || 'Template validation failed',
                        response as Record<string, any>
                    );
                }
            }
            throw new BadRequestException(error.message);
        }
    }

    @Get()
    async getTemplates(
        @Query('page') page?: string,
        @Query('limit') limit?: string,
        @Query('sortBy') sortBy?: string,
        @Query('order') order?: 'ASC' | 'DESC',
        @Query('search') search?: string,
        @Query('templateType') templateType?: DocumentTemplateType,
        @Query('category') category?: DocumentTemplateCategory,
        @Query('status') status?: DocumentTemplateStatus,
        @Query('isActive') isActive?: string,
        @Query('createdBy') createdBy?: string
    ) {
        const result = await this.documentTemplateService.getTemplatesWithFilters({
            page: page ? parseInt(page, 10) : 1,
            limit: limit ? parseInt(limit, 10) : 10,
            sortBy: sortBy || 'createdAt',
            order: order || 'DESC',
            search,
            templateType,
            category,
            status,
            isActive: isActive ? isActive === 'true' : undefined,
            createdBy
        });

        return ApiResponseUtil.ok(result, 'Templates retrieved successfully');
    }

    @Get('search')
    async searchTemplates(
        @Query('q') searchTerm: string,
        @Query('includeInactive') includeInactive?: string,
        @Query('includeArchived') includeArchived?: string,
        @Query('limit') limit?: string
    ) {
        const templates = await this.documentTemplateService.searchTemplates(searchTerm, {
            includeInactive: includeInactive === 'true',
            includeArchived: includeArchived === 'true',
            limit: limit ? parseInt(limit, 10) : 10
        });

        return ApiResponseUtil.ok(templates, 'Templates searched successfully');
    }

    @Get('types')
    getTemplateTypes() {
        const types = Object.values(DocumentTemplateType).map((type) => ({
            value: type,
            label: type
                .replace(/_/g, ' ')
                .toLowerCase()
                .replace(/\b\w/g, (l) => l.toUpperCase())
        }));
        return ApiResponseUtil.ok({ types }, 'Template types retrieved successfully');
    }

    @Get('categories')
    getTemplateCategories() {
        const categories = Object.values(DocumentTemplateCategory).map((category) => ({
            value: category,
            label: category
                .replace(/_/g, ' ')
                .toLowerCase()
                .replace(/\b\w/g, (l) => l.toUpperCase())
        }));
        return ApiResponseUtil.ok({ categories }, 'Template categories retrieved successfully');
    }

    @Get('parties')
    getTemplateParties() {
        try {
            // Get all available party types from the enum
            const partyTypes = Object.values(PartyType);

            // Categorize parties into always visible and must be added
            const alwaysVisibleParties = [
                PartyType.CLIENT,
                PartyType.LAND_REGISTRY,
                PartyType.ATTENDANCE_NOTES_BILLING
            ];

            const conditionalParties = [
                PartyType.SOLICITOR,
                PartyType.AGENT_SOLICITOR,
                PartyType.BANK_BUILDING_SOCIETY_SOLICITOR,
                PartyType.INDIVIDUAL_SOLICITOR,
                PartyType.LANDLORD_SOLICITOR,
                PartyType.MANAGEMENT_COMPANY,
                PartyType.OTHER
            ];

            // Create detailed party information
            const partyDetails = partyTypes.map((partyType) => {
                const isAlwaysVisible = alwaysVisibleParties.includes(partyType);
                return {
                    type: partyType,
                    displayName: this.getPartyDisplayName(partyType),
                    description: this.getPartyDescription(partyType),
                    category: isAlwaysVisible ? 'always_visible' : 'conditional',
                    isAlwaysVisible,
                    requiresPartySetup: !isAlwaysVisible
                };
            });

            // Group by category
            const categorizedParties = {
                alwaysVisible: partyDetails.filter((p) => p.isAlwaysVisible),
                conditional: partyDetails.filter((p) => !p.isAlwaysVisible)
            };

            const response = {
                parties: partyDetails,
                categorized: categorizedParties,
                summary: {
                    totalPartyTypes: partyTypes.length,
                    alwaysVisibleCount: alwaysVisibleParties.length,
                    conditionalCount: conditionalParties.length
                },
                usage: {
                    description:
                        'Template parties define which parties/entities can be associated with document templates',
                    categories: {
                        alwaysVisible:
                            'These parties are always available and visible in templates without requiring setup',
                        conditional:
                            'These parties must be added to a case before they can be used in templates'
                    },
                    examples: {
                        templateAssignment:
                            'When creating a template, specify which party types are relevant for that template',
                        documentGeneration:
                            'During document generation, only relevant parties for the template will be available'
                    }
                }
            };

            return ApiResponseUtil.ok(response, 'Template parties retrieved successfully');
        } catch (error) {
            throw new BadRequestException(`Failed to retrieve template parties: ${error.message}`);
        }
    }

    @Get(':id')
    async getTemplate(@Param('id') id: string) {
        const template = await this.documentTemplateService.getTemplateById(id);
        if (!template) {
            return ApiResponseUtil.notFound('Template not found');
        }
        return ApiResponseUtil.ok(template, 'Template retrieved successfully');
    }

    @Get(':id/download')
    async downloadTemplate(@Param('id') id: string) {
        const downloadInfo = await this.documentTemplateService.getTemplateDownloadUrl(id);
        return ApiResponseUtil.ok(downloadInfo, 'Download URL generated successfully');
    }

    @Put(':id')
    async updateTemplate(
        @Param('id') id: string,
        @Body(new ValidationPipe({ transform: true })) data: UpdateDocumentTemplateDto,
        @Req() request: Request
    ) {
        if (!id?.trim()) {
            throw new BadRequestException('Template ID is required');
        }

        const user = request['user'];

        try {
            const template = await this.documentTemplateService.updateTemplate(id.trim(), {
                ...data,
                updatedBy: user.systemUserId
            });

            if (!template) {
                return ApiResponseUtil.notFound('Template not found');
            }

            return ApiResponseUtil.ok(template, 'Template updated successfully');
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    @Put(':id/file')
    @UseInterceptors(FileInterceptor('file'))
    async replaceTemplateFile(
        @Param('id') id: string,
        @UploadedFile() file: MulterFile,
        @Req() request: Request
    ) {
        const user = request['user'];

        const template = await this.documentTemplateService.replaceTemplateFile(id, {
            fileBuffer: file.buffer,
            filename: file.originalname,
            updatedBy: user.systemUserId
        });

        return ApiResponseUtil.ok(template, 'Template file replaced successfully');
    }

    @Post(':id/activate')
    async activateTemplate(@Param('id') id: string, @Req() request: Request) {
        const user = request['user'];
        const template = await this.documentTemplateService.activateTemplate(id, user.systemUserId);

        if (!template) {
            return ApiResponseUtil.notFound('Template not found');
        }

        return ApiResponseUtil.ok(template, 'Template activated successfully');
    }

    @Post(':id/deactivate')
    async deactivateTemplate(@Param('id') id: string, @Req() request: Request) {
        const user = request['user'];
        const template = await this.documentTemplateService.deactivateTemplate(
            id,
            user.systemUserId
        );

        if (!template) {
            return ApiResponseUtil.notFound('Template not found');
        }

        return ApiResponseUtil.ok(template, 'Template deactivated successfully');
    }

    @Post(':id/archive')
    async archiveTemplate(@Param('id') id: string, @Req() request: Request) {
        const user = request['user'];
        const result = await this.documentTemplateService.archiveTemplate(id, user.systemUserId);

        if (!result) {
            return ApiResponseUtil.notFound('Template not found');
        }

        return ApiResponseUtil.ok({ success: true }, 'Template archived successfully');
    }

    @Post(':id/restore')
    async restoreTemplate(@Param('id') id: string, @Req() request: Request) {
        const user = request['user'];
        const result = await this.documentTemplateService.restoreTemplate(id, user.systemUserId);

        if (!result) {
            return ApiResponseUtil.notFound('Template not found');
        }

        return ApiResponseUtil.ok({ success: true }, 'Template restored successfully');
    }

    @Delete(':id')
    async deleteTemplate(@Param('id') id: string) {
        const result = await this.documentTemplateService.deleteTemplate(id);
        return ApiResponseUtil.ok({ success: result }, 'Template deleted successfully');
    }

    @Get('case-types/:caseType/available-tokens')
    getAvailableTokensForCaseType(@Param('caseType') caseType: CaseType) {
        const tokens = this.templateValidationService.getAvailableTokensForCaseType(caseType);
        return ApiResponseUtil.ok({ tokens }, 'Available tokens retrieved successfully');
    }

    @Get('categories/:category/required-tokens')
    getRequiredTokensForCategory(@Param('category') category: DocumentTemplateCategory) {
        const tokens = this.templateValidationService.getRequiredSystemTokensForCategory(category);
        return ApiResponseUtil.ok({ tokens }, 'Required tokens retrieved successfully');
    }

    @Get('available-tokens')
    async getAvailableTokens(@Query('allowedCaseTypes') allowedCaseTypes?: string) {
        try {
            // Parse allowed case types from query string
            const caseTypes: CaseType[] = allowedCaseTypes ? JSON.parse(allowedCaseTypes) : [];

            // Get all available tokens using the enhanced validation service
            const allTokens = await this.templateValidationService.getAllAvailableTokens();

            // Get system and custom tokens separately
            const systemTokens = await this.tokenManagementService.getSystemTokens();
            const customTokens = await this.tokenManagementService.getCustomTokens();

            return ApiResponseUtil.ok(
                {
                    allowedCaseTypes: caseTypes,
                    totalTokens: allTokens.length,
                    systemTokens: systemTokens.length,
                    customTokens: customTokens.length,
                    tokens: allTokens.sort(),
                    breakdown: {
                        system: systemTokens.map((t) => ({
                            name: t.tokenName,
                            description: t.description
                        })),
                        custom: customTokens.map((t) => ({
                            name: t.tokenName,
                            description: t.description,
                            category: t.category
                        }))
                    }
                },
                'Available tokens retrieved successfully'
            );
        } catch (error) {
            throw new BadRequestException(`Failed to get available tokens: ${error.message}`);
        }
    }

    @Get('validate-for-case-type/:caseType')
    async validateTemplatesForCaseType(@Param('caseType') caseType: CaseType) {
        const templates = await this.documentTemplateService.getTemplatesWithFilters({
            page: 1,
            limit: 100,
            isActive: true
        });

        const validTemplates = templates.templates.filter(
            (template) =>
                !template.allowedCaseTypes.length || template.allowedCaseTypes.includes(caseType)
        );

        return ApiResponseUtil.ok(
            {
                caseType,
                templates: validTemplates,
                count: validTemplates.length
            },
            `Templates for case type ${caseType} retrieved successfully`
        );
    }

    @Post('validate-tokens')
    @UseInterceptors(FileInterceptor('file'))
    async validateTemplateTokens(
        @UploadedFile(DocxFileValidationPipe) file: MulterFile,
        @Body('category') category: DocumentTemplateCategory,
        @Body('allowedCaseTypes') allowedCaseTypes: string
    ) {
        try {
            // Parse allowed case types from string
            const caseTypes: CaseType[] = allowedCaseTypes ? JSON.parse(allowedCaseTypes) : [];

            // Validate template tokens using the enhanced validation service
            const validation = await this.templateValidationService.validateTemplateTokens(
                file.buffer,
                category,
                caseTypes
            );

            return ApiResponseUtil.ok(
                {
                    filename: file.originalname,
                    category,
                    allowedCaseTypes: caseTypes,
                    validation: {
                        isValid: validation.isValid,
                        foundTokens: validation.foundTokens,
                        systemTokens: validation.systemTokens,
                        customTokens: validation.customTokens,
                        validTokens: validation.validTokens,
                        invalidTokens: validation.invalidTokens,
                        errors: validation.errors,
                        warnings: validation.warnings,
                        tokenDetails: validation.tokenDetails
                    },
                    recommendations: {
                        canUpload: validation.isValid,
                        issues: validation.errors,
                        suggestions: validation.warnings
                    }
                },
                validation.isValid ? 'Template validation successful' : 'Template validation failed'
            );
        } catch (error) {
            throw new BadRequestException(`Template validation failed: ${error.message}`);
        }
    }

    @Post('analyze-tokens')
    @UseInterceptors(FileInterceptor('file'))
    async analyzeTemplateTokens(
        @UploadedFile(DocxFileValidationPipe) file: MulterFile,
        @Body('category') category: DocumentTemplateCategory,
        @Body('allowedCaseTypes') allowedCaseTypes: string
    ) {
        if (!category) {
            throw new BadRequestException('Template category is required');
        }

        try {
            // Parse allowed case types from string
            const caseTypes: CaseType[] = allowedCaseTypes ? JSON.parse(allowedCaseTypes) : [];

            // Extract and validate tokens
            const tokenAnalysis = await this.tokenExtractionService.validateTemplateTokens(
                file.buffer,
                category,
                caseTypes
            );

            // Get token statistics
            const statistics = this.tokenExtractionService.getTokenStatistics(tokenAnalysis);

            return ApiResponseUtil.ok(
                {
                    filename: file.originalname,
                    category,
                    allowedCaseTypes: caseTypes,
                    analysis: tokenAnalysis,
                    statistics,
                    recommendations: {
                        isReadyForUpload: statistics.isValid,
                        issues: tokenAnalysis.validationErrors,
                        suggestions:
                            this.templateAnalysisUtils.generateTokenSuggestions(tokenAnalysis)
                    }
                },
                'Template token analysis completed successfully'
            );
        } catch (error) {
            throw new BadRequestException(`Token analysis failed: ${error.message}`);
        }
    }

    @Post('generate')
    async generateDocument(@Body() request: GenerationRequest, @Req() req: Request) {
        try {
            const user = req['user'];
            const tenantMetadata = this.tenantContextService.getTenantMetadata();

            // Add user and tenant information to the request
            const enhancedRequest = {
                ...request,
                userId: user?.systemUserId,
                userName:
                    user?.name || `${user?.given_name || ''} ${user?.family_name || ''}`.trim(),
                userEmail: user?.email,
                tenantId: this.tenantContextService.getTenantId(),
                tenantName: tenantMetadata?.displayName || 'Organization',
                notifyGeneration: request.notifyGeneration !== false // Default to true
            };

            const result = await this.documentGenerationService.generateDocument(enhancedRequest);

            if (!result.success) {
                return ApiResponseUtil.badRequest('Document generation failed', {
                    errors: result.errors,
                    ...result.metadata
                });
            }

            return ApiResponseUtil.ok(
                {
                    success: true,
                    filename: result.filename,
                    attachmentId: result.attachmentId,
                    documentUrl: result.documentUrl,
                    metadata: result.metadata
                },
                'Document generated successfully'
            );
        } catch (error) {
            return ApiResponseUtil.internalServerError('Document generation failed', {
                error: error.message
            });
        }
    }

    @Post('generate-enhanced')
    async generateDocumentEnhanced(
        @Body()
        request: {
            templateId: string;
            caseId: string;
            customData?: Record<string, any>;
            outputFileName?: string;
            autoAttachToCase?: boolean;
            autoEmailToClient?: boolean;
            notifyGeneration?: boolean;
        },
        @Req() req: Request
    ) {
        try {
            const user = req['user'];
            const tenantMetadata = this.tenantContextService.getTenantMetadata();

            const enhancedRequest = {
                ...request,
                userId: user?.systemUserId,
                userName:
                    user?.name || `${user?.given_name || ''} ${user?.family_name || ''}`.trim(),
                userEmail: user?.email,
                tenantId: this.tenantContextService.getTenantId(),
                tenantName: tenantMetadata?.displayName || 'Organization',
                notifyGeneration: request.notifyGeneration !== false // Default to true
            };

            const result = await this.documentGenerationService.generateDocument(enhancedRequest);

            if (!result.success) {
                return ApiResponseUtil.badRequest('Enhanced document generation failed', {
                    errors: result.errors,
                    warnings: result.warnings,
                    tokenResolutionDetails: result.tokenResolutionDetails,
                    ...result.metadata
                });
            }

            return ApiResponseUtil.ok(
                {
                    success: true,
                    filename: result.filename,
                    documentUrl: result.documentUrl,
                    attachmentId: result.attachmentId,
                    tokenResolutionDetails: result.tokenResolutionDetails,
                    metadata: result.metadata
                },
                'Document generated successfully with enhanced token resolution'
            );
        } catch (error) {
            return ApiResponseUtil.internalServerError('Enhanced document generation failed', {
                error: error.message
            });
        }
    }

    @Get('token-usage-report')
    async getTokenUsageReport() {
        try {
            const stats = await this.tokenManagementService.getTokenUsageStats();
            const systemTokens = await this.tokenManagementService.getSystemTokens();
            const customTokens = await this.tokenManagementService.getCustomTokens();

            return ApiResponseUtil.ok(
                {
                    summary: {
                        totalTokens: systemTokens.length + customTokens.length,
                        systemTokens: systemTokens.length,
                        customTokens: customTokens.length,
                        activeCustomTokens: customTokens.filter((t) => t.isActive).length
                    },
                    usage: stats,
                    recommendations: this.templateAnalysisUtils.generateTokenRecommendations(
                        stats,
                        customTokens
                    )
                },
                'Token usage report generated successfully'
            );
        } catch (error) {
            throw new BadRequestException(
                `Failed to generate token usage report: ${error.message}`
            );
        }
    }

    @Get('enhanced-available-tokens')
    async getEnhancedAvailableTokens(
        @Query('caseType') caseType?: CaseType,
        @Query('category') category?: string,
        @Query('includeUsageStats') includeUsageStats?: string
    ) {
        try {
            let tokens;
            if (caseType) {
                tokens =
                    await this.templateValidationService.getAvailableTokensForCaseType(caseType);
            } else {
                tokens = await this.templateValidationService.getAllAvailableTokens();
            }

            const systemTokens = await this.tokenManagementService.getSystemTokens();
            const customTokens = await this.tokenManagementService.getCustomTokens();

            const response: any = {
                totalTokens: tokens.length,
                tokens: tokens.sort(),
                categorized: {
                    system: systemTokens.map((t) => ({
                        name: t.tokenName,
                        description: t.description,
                        dataType: t.dataType,
                        category: t.category
                    })),
                    custom: customTokens.map((t) => ({
                        name: t.tokenName,
                        description: t.description,
                        dataType: t.dataType,
                        category: t.category,
                        entityName: t.entityName,
                        fieldPath: t.fieldPath,
                        usageCount: t.usageCount
                    }))
                }
            };

            if (includeUsageStats === 'true') {
                const stats = await this.tokenManagementService.getTokenUsageStats();
                response.usageStats = stats;
            }

            return ApiResponseUtil.ok(response, 'Enhanced available tokens retrieved successfully');
        } catch (error) {
            throw new BadRequestException(
                `Failed to get enhanced available tokens: ${error.message}`
            );
        }
    }

    private getPartyDisplayName(partyType: PartyType): string {
        const displayNames: Record<PartyType, string> = {
            [PartyType.CLIENT]: 'Client',
            [PartyType.LAND_REGISTRY]: 'Land Registry',
            [PartyType.ATTENDANCE_NOTES_BILLING]: 'Attendance Notes Billing',
            [PartyType.SOLICITOR]: 'Solicitor',
            [PartyType.AGENT_SOLICITOR]: 'Agent Solicitor',
            [PartyType.BANK_BUILDING_SOCIETY_SOLICITOR]: 'Bank/Building Society Solicitor',
            [PartyType.INDIVIDUAL_SOLICITOR]: 'Individual Solicitor',
            [PartyType.LANDLORD_SOLICITOR]: 'Landlord Solicitor',
            [PartyType.MANAGEMENT_COMPANY]: 'Management Company',
            [PartyType.OTHER]: 'Other'
        };
        return displayNames[partyType] || partyType;
    }

    private getPartyDescription(partyType: PartyType): string {
        const descriptions: Record<PartyType, string> = {
            [PartyType.CLIENT]: 'The primary client for the case',
            [PartyType.LAND_REGISTRY]: 'UK Land Registry office',
            [PartyType.ATTENDANCE_NOTES_BILLING]: 'Party for attendance notes and billing purposes',
            [PartyType.SOLICITOR]: 'General solicitor representing another party',
            [PartyType.AGENT_SOLICITOR]: 'Solicitor acting as an agent',
            [PartyType.BANK_BUILDING_SOCIETY_SOLICITOR]:
                'Solicitor representing a bank or building society',
            [PartyType.INDIVIDUAL_SOLICITOR]: 'Solicitor representing an individual',
            [PartyType.LANDLORD_SOLICITOR]: 'Solicitor representing the landlord',
            [PartyType.MANAGEMENT_COMPANY]: 'Property management company',
            [PartyType.OTHER]: 'Any other party type not covered by specific categories'
        };
        return descriptions[partyType] || `${partyType} party type`;
    }
}
