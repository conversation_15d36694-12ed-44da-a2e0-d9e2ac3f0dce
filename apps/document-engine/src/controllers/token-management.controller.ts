import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    UseGuards,
    ValidationPipe,
    BadRequestException
} from '@nestjs/common';
import { TokenManagementService } from '../document/services/token-management.service';
import { TokenUtils } from '../utils/token.utils';
import { CreateCustomTokenDto } from '../dto/create-custom-token.dto';
import { UpdateCustomTokenDto } from '../dto/update-custom-token.dto';
import { BulkCreateTokensDto } from '../dto/bulk-create-tokens.dto';
import { ValidateTokenNameDto } from '../dto/validate-token-name.dto';
import { TenantGuard } from '@app/common/multi-tenancy';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { Request } from 'express';
import { ApiResponseUtil } from '@app/common/api-response';
import { TokenType, TokenStatus } from '@app/common/typeorm/entities/tenant/custom-token.entity';
import {
    getSystemTokensByCategory,
    SYSTEM_TOKEN_DEFINITIONS
} from '../config/system-tokens.config';

@Controller('tokens')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class TokenManagementController {
    constructor(
        private readonly tokenManagementService: TokenManagementService,
        private readonly tokenUtils: TokenUtils
    ) {}

    @Post()
    async createCustomToken(
        @Body(new ValidationPipe({ transform: true })) createTokenDto: CreateCustomTokenDto,
        @Req() request: Request
    ) {
        try {
            const user = request['user'];
            const token = await this.tokenManagementService.createCustomToken(
                createTokenDto,
                user.systemUserId
            );

            return ApiResponseUtil.created(token, 'Custom token created successfully');
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    @Post('bulk')
    async bulkCreateTokens(
        @Body(new ValidationPipe({ transform: true })) bulkCreateDto: BulkCreateTokensDto,
        @Req() request: Request
    ) {
        try {
            const user = request['user'];
            const result = await this.tokenManagementService.bulkCreateTokens(
                bulkCreateDto.tokens,
                user.systemUserId
            );

            return ApiResponseUtil.created(result, 'Bulk token creation completed');
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    @Get()
    async getTokens(
        @Query('page') page?: string,
        @Query('limit') limit?: string,
        @Query('search') search?: string,
        @Query('tokenType') tokenType?: TokenType,
        @Query('entityName') entityName?: string,
        @Query('category') category?: string,
        @Query('status') status?: TokenStatus,
        @Query('isActive') isActive?: string,
        @Query('sortBy') sortBy?: string,
        @Query('order') order?: 'ASC' | 'DESC'
    ) {
        const filters = {
            page: page ? parseInt(page, 10) : 1,
            limit: limit ? parseInt(limit, 10) : 10,
            search,
            tokenType,
            entityName,
            category,
            status,
            isActive: isActive ? isActive === 'true' : undefined,
            sortBy: sortBy || 'tokenName',
            order: order || 'ASC'
        };

        const result = await this.tokenManagementService.searchTokens(filters);
        return ApiResponseUtil.ok(result, 'Tokens retrieved successfully');
    }

    @Get('system')
    async getSystemTokens() {
        const tokens = await this.tokenManagementService.getSystemTokens();
        return ApiResponseUtil.ok({ tokens }, 'System tokens retrieved successfully');
    }

    @Get('custom')
    async getCustomTokens() {
        const tokens = await this.tokenManagementService.getCustomTokens();

        // Group tokens by entity
        const tokensByEntity = tokens.reduce(
            (acc, token) => {
                if (!acc[token.entityName]) {
                    acc[token.entityName] = [];
                }
                acc[token.entityName].push(token);
                return acc;
            },
            {} as Record<string, typeof tokens>
        );

        return ApiResponseUtil.ok(
            {
                tokens,
                tokensByEntity,
                summary: {
                    total: tokens.length,
                    byEntity: Object.keys(tokensByEntity).map((entity) => ({
                        entity,
                        count: tokensByEntity[entity].length
                    }))
                }
            },
            'Custom tokens retrieved successfully'
        );
    }

    @Get('by-entity/:entityName')
    async getTokensByEntity(@Param('entityName') entityName: string) {
        const tokens = await this.tokenManagementService.searchTokens({
            entityName,
            isActive: true
        });

        if (tokens.tokens.length === 0) {
            return ApiResponseUtil.ok(
                {
                    entityName,
                    tokens: [],
                    suggestions: this.tokenUtils.generateEntityTokenSuggestions(entityName)
                },
                `No tokens found for entity '${entityName}'`
            );
        }

        return ApiResponseUtil.ok(
            {
                entityName,
                tokens: tokens.tokens,
                count: tokens.total
            },
            `Tokens for entity '${entityName}' retrieved successfully`
        );
    }

    @Get('entities')
    getAvailableEntities() {
        const entities = this.tokenManagementService.getAvailableEntities();
        return ApiResponseUtil.ok({ entities }, 'Available entities retrieved successfully');
    }

    @Get('tokenizable-fields')
    async getTokenizableFields() {
        const entities = this.tokenManagementService.getAvailableEntities();

        // Organize fields by their source entity
        const tokenizableEntities = entities.map((entity) => ({
            entityName: entity.name,
            entityDisplayName: entity.displayName,
            entityDescription: entity.description,
            fields: entity.fields.map((field) => ({
                fieldName: field.name,
                fieldPath: field.path,
                displayName: field.displayName,
                description: field.description,
                dataType: field.dataType.toUpperCase(),
                nullable: field.nullable,
                example: field.example,
                // Usage notes specific to this field
                usageNotes: this.tokenUtils.getFieldUsageContext(field.name)
            }))
        }));

        // Get existing tokens to show which fields are already tokenized
        const existingTokens = await this.tokenManagementService.getAllActiveTokens();
        const usedFieldPaths = new Set(
            existingTokens.map((token) => `${token.entityName}:${token.fieldPath}`)
        );

        // Mark fields as available or already used
        tokenizableEntities.forEach((entity) => {
            entity.fields.forEach((field) => {
                const fieldKey = `${entity.entityName}:${field.fieldPath}`;
                (field as any).isAvailable = !usedFieldPaths.has(fieldKey);
                if (!usedFieldPaths.has(fieldKey)) {
                    (field as any).status = 'available';
                } else {
                    (field as any).status = 'already_tokenized';
                    const existingToken = existingTokens.find(
                        (t) => t.entityName === entity.entityName && t.fieldPath === field.fieldPath
                    );
                    (field as any).existingTokenName = existingToken?.tokenName;
                }
            });
        });

        return ApiResponseUtil.ok(
            {
                tokenizableEntities,
                summary: {
                    totalEntities: tokenizableEntities.length,
                    totalFields: tokenizableEntities.reduce(
                        (sum, entity) => sum + entity.fields.length,
                        0
                    ),
                    availableFields: tokenizableEntities.reduce(
                        (sum, entity) =>
                            sum + entity.fields.filter((f) => (f as any).isAvailable).length,
                        0
                    ),
                    alreadyTokenized: tokenizableEntities.reduce(
                        (sum, entity) =>
                            sum + entity.fields.filter((f) => !(f as any).isAvailable).length,
                        0
                    )
                },
                instructions: {
                    tokenCreation:
                        'Each field can only be tokenized once. Select an available field and create a token with any unique name.',
                    example: {
                        tokenName: 'caseNumber', // Your chosen unique name
                        entityName: 'case', // From the entity
                        fieldPath: 'caseNumber', // From the field
                        dataType: 'STRING', // From the field
                        description: 'Unique case identifier' // Your description
                    },
                    note: 'Token names must be unique across the entire system and can only be used once.'
                }
            },
            'Tokenizable fields organized by entity retrieved successfully'
        );
    }

    @Get('usage-stats')
    async getTokenUsageStats() {
        const stats = await this.tokenManagementService.getTokenUsageStats();
        return ApiResponseUtil.ok({ stats }, 'Token usage statistics retrieved successfully');
    }

    @Get('categories')
    async getTokenCategories() {
        try {
            // Get system token categories
            const systemTokensByCategory = getSystemTokensByCategory();

            // Get custom tokens and group by category - handle table not existing gracefully
            let customTokens: any[] = [];
            try {
                customTokens = await this.tokenManagementService.getAllActiveTokens();
            } catch {
                // If custom_tokens table doesn't exist, just return system tokens
            }
            const customTokensByCategory = customTokens.reduce(
                (acc, token) => {
                    const category = token.category || 'Uncategorized';
                    if (!acc[category]) {
                        acc[category] = [];
                    }
                    acc[category].push({
                        id: token.id,
                        name: token.tokenName,
                        description: token.description,
                        dataType: token.dataType,
                        entityName: token.entityName,
                        fieldPath: token.fieldPath,
                        status: token.status,
                        isActive: token.isActive,
                        usageCount: token.usageCount || 0,
                        type: 'custom'
                    });
                    return acc;
                },
                {} as Record<string, any[]>
            );

            // Define category descriptions and priorities
            const categoryMetadata: Record<
                string,
                { description: string; priority: number; icon?: string }
            > = {
                'Date/Time': {
                    description: 'Current date, time, and temporal system tokens',
                    priority: 1,
                    icon: 'calendar'
                },
                'User Context': {
                    description: 'Information about the current user generating the document',
                    priority: 2,
                    icon: 'user'
                },
                'System Metadata': {
                    description: 'Document generation metadata and system information',
                    priority: 3,
                    icon: 'system'
                },
                case: {
                    description: 'Legal case information and details',
                    priority: 4,
                    icon: 'briefcase'
                },
                client: {
                    description: 'Client personal information and contact details',
                    priority: 5,
                    icon: 'person'
                },
                property: {
                    description: 'Property details including address and characteristics',
                    priority: 6,
                    icon: 'home'
                },
                financial: {
                    description: 'Financial information including prices and costs',
                    priority: 7,
                    icon: 'currency'
                },
                Uncategorized: {
                    description: 'Custom tokens without assigned categories',
                    priority: 999,
                    icon: 'tag'
                }
            };

            // Combine all categories
            const allCategories = new Set([
                ...Object.keys(systemTokensByCategory),
                ...Object.keys(customTokensByCategory)
            ]);

            const categorizedTokens = Array.from(allCategories).map((category) => {
                const systemTokens = (systemTokensByCategory[category] || []).map((token) => ({
                    ...token,
                    type: 'system',
                    usage: 'system-generated'
                }));

                const customTokens = customTokensByCategory[category] || [];
                const metadata = categoryMetadata[category] || {
                    description: `Custom tokens in the ${category} category`,
                    priority: 100,
                    icon: 'tag'
                };

                return {
                    category,
                    description: metadata.description,
                    priority: metadata.priority,
                    icon: metadata.icon,
                    systemTokens,
                    customTokens,
                    systemTokenCount: systemTokens.length,
                    customTokenCount: customTokens.length,
                    totalTokens: systemTokens.length + customTokens.length,
                    hasSystemTokens: systemTokens.length > 0,
                    hasCustomTokens: customTokens.length > 0
                };
            });

            // Sort categories by priority, then by total token count
            categorizedTokens.sort((a, b) => {
                if (a.priority !== b.priority) {
                    return a.priority - b.priority;
                }
                return b.totalTokens - a.totalTokens;
            });

            const summary = {
                totalCategories: categorizedTokens.length,
                totalSystemTokens: SYSTEM_TOKEN_DEFINITIONS.length,
                totalCustomTokens: customTokens.length,
                totalTokens: SYSTEM_TOKEN_DEFINITIONS.length + customTokens.length,
                categoriesWithSystemTokens: Object.keys(systemTokensByCategory).length,
                categoriesWithCustomTokens: Object.keys(customTokensByCategory).length,
                mostPopularCategory:
                    categorizedTokens.length > 0 ? categorizedTokens[0].category : null,
                systemOnlyCategories: categorizedTokens.filter(
                    (c) => c.hasSystemTokens && !c.hasCustomTokens
                ).length,
                customOnlyCategories: categorizedTokens.filter(
                    (c) => !c.hasSystemTokens && c.hasCustomTokens
                ).length,
                mixedCategories: categorizedTokens.filter(
                    (c) => c.hasSystemTokens && c.hasCustomTokens
                ).length
            };

            return ApiResponseUtil.ok(
                {
                    categories: categorizedTokens,
                    summary,
                    usage: {
                        description:
                            'Use category information to organize tokens in UI and help users find relevant tokens',
                        examples: {
                            dateTime:
                                'For current date/time in documents: {currentDate}, {currentDateTime}',
                            userContext:
                                'For user information: {currentUser.name}, {currentUser.email}',
                            caseInfo:
                                'For case details: Create custom tokens for case.caseNumber, case.type',
                            clientInfo:
                                'For client details: Create custom tokens for client.name, client.email'
                        }
                    }
                },
                'Token categories with detailed metadata retrieved successfully'
            );
        } catch (error) {
            throw new BadRequestException(`Failed to retrieve token categories: ${error.message}`);
        }
    }

    @Get(':id')
    async getTokenById(@Param('id') id: string) {
        const token = await this.tokenManagementService.getTokenById(id);
        if (!token) {
            return ApiResponseUtil.notFound('Token not found');
        }
        return ApiResponseUtil.ok(token, 'Token retrieved successfully');
    }

    @Put(':id')
    async updateCustomToken(
        @Param('id') id: string,
        @Body(new ValidationPipe({ transform: true })) updateTokenDto: UpdateCustomTokenDto,
        @Req() request: Request
    ) {
        try {
            const user = request['user'];
            const token = await this.tokenManagementService.updateCustomToken(
                id,
                updateTokenDto,
                user.systemUserId
            );

            return ApiResponseUtil.ok(token, 'Token updated successfully');
        } catch (error) {
            if (error.message.includes('not found')) {
                return ApiResponseUtil.notFound(error.message);
            }
            throw new BadRequestException(error.message);
        }
    }

    @Delete(':id')
    async deleteCustomToken(@Param('id') id: string, @Req() request: Request) {
        try {
            const user = request['user'];
            const result = await this.tokenManagementService.deleteCustomToken(
                id,
                user.systemUserId
            );

            if (!result) {
                return ApiResponseUtil.notFound('Token not found');
            }

            return ApiResponseUtil.ok({ success: true }, 'Token deleted successfully');
        } catch (error) {
            if (error.message.includes('not found')) {
                return ApiResponseUtil.notFound(error.message);
            }
            throw new BadRequestException(error.message);
        }
    }

    @Post(':tokenName/resolve')
    async resolveTokenValue(@Param('tokenName') tokenName: string, @Body() context: any) {
        try {
            const result = await this.tokenManagementService.resolveTokenValue(tokenName, context);

            return ApiResponseUtil.ok(result, 'Token value resolved');
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    @Get('by-name/:tokenName')
    async getTokenByName(@Param('tokenName') tokenName: string) {
        const token = await this.tokenManagementService.getTokenByName(tokenName);
        if (!token) {
            return ApiResponseUtil.notFound('Token not found');
        }
        return ApiResponseUtil.ok(token, 'Token retrieved successfully');
    }

    @Post('validate-name')
    async validateTokenName(
        @Body(new ValidationPipe({ transform: true })) dto: ValidateTokenNameDto
    ) {
        const existingToken = await this.tokenManagementService.getTokenByName(dto.tokenName);
        const isAvailable = !existingToken;

        return ApiResponseUtil.ok(
            {
                tokenName: dto.tokenName.trim(),
                isAvailable,
                existingToken: existingToken
                    ? {
                          id: existingToken.id,
                          tokenType: existingToken.tokenType,
                          status: existingToken.status
                      }
                    : null
            },
            isAvailable ? 'Token name is available' : 'Token name is already taken'
        );
    }

    @Get('entity/:entityName/fields')
    getEntityFields(@Param('entityName') entityName: string) {
        try {
            const entities = this.tokenManagementService.getAvailableEntities();
            const entity = entities.find((e) => e.name === entityName);

            if (!entity) {
                return ApiResponseUtil.notFound(`Entity '${entityName}' not found`);
            }

            return ApiResponseUtil.ok(
                {
                    entity: entity.name,
                    displayName: entity.displayName,
                    fields: entity.fields,
                    relations: entity.relations
                },
                'Entity fields retrieved successfully'
            );
        } catch (error) {
            throw new BadRequestException(error.message);
        }
    }

    @Get('data-types/list')
    getDataTypes() {
        const dataTypes = [
            { value: 'STRING', label: 'Text' },
            { value: 'NUMBER', label: 'Number' },
            { value: 'DATE', label: 'Date' },
            { value: 'BOOLEAN', label: 'True/False' },
            { value: 'CURRENCY', label: 'Currency' },
            { value: 'EMAIL', label: 'Email Address' },
            { value: 'PHONE', label: 'Phone Number' },
            { value: 'ADDRESS', label: 'Address' }
        ];

        return ApiResponseUtil.ok({ dataTypes }, 'Data types retrieved successfully');
    }

    @Get('examples/client-name')
    getClientNameTokenExample() {
        const example = {
            tokenName: 'clientName',
            description: 'Client full name',
            dataType: 'STRING',
            entityName: 'client',
            fieldPath: 'name',
            category: 'client',
            transformationConfig: {
                uppercase: false
            },
            validationConfig: {
                required: true,
                minLength: 2,
                maxLength: 100
            }
        };

        return ApiResponseUtil.ok(
            {
                example,
                instructions: [
                    '1. POST /tokens with the above payload to create the token',
                    '2. The token maps to the "name" field from the "client" entity',
                    '3. Use {clientName} in templates to display client name',
                    '4. System resolves client relationship from case context during generation',
                    '5. Token name "clientName" is unique and can never be used again'
                ],
                note: 'Each field can only be tokenized once. Check GET /tokens/tokenizable-fields for availability.'
            },
            'Example for creating a client name token'
        );
    }

    @Get('examples/formatted-price')
    getFormattedPriceTokenExample() {
        const example = {
            tokenName: 'purchasePrice',
            description: 'Property purchase price with automatic currency formatting',
            dataType: 'CURRENCY',
            entityName: 'case',
            fieldPath: 'property.purchasePrice',
            category: 'financial',
            transformationConfig: {
                format: 'currency',
                currency: 'GBP',
                prefix: '£'
            },
            validationConfig: {
                required: false
            },
            compatibleTemplateTypes: ['CONTRACT_REQUEST', 'PURCHASE_COMMUNICATION']
        };

        return ApiResponseUtil.ok(
            {
                example,
                instructions: [
                    '1. This creates a currency token that auto-formats as £123,456.00',
                    '2. Maps to "property.purchasePrice" field from the case entity',
                    '3. Use {purchasePrice} in templates (simple, unique token name)',
                    '4. System resolves property relationship through case context during generation',
                    '5. Once created, no other token can use the property.purchasePrice field'
                ],
                warning:
                    'This field can only be tokenized once. If someone else creates this token first, you cannot create another token for the same field.',
                tip: 'Check GET /tokens/tokenizable-fields to see which fields are still available'
            },
            'Example for creating a formatted price token'
        );
    }

    @Post('seed-common')
    async seedCommonTokens(@Req() request: Request) {
        try {
            const user = request['user'];

            // List of common tokens to create
            const commonTokens = [
                {
                    tokenName: 'case.caseNumber',
                    description: 'Unique case identifier',
                    dataType: 'STRING',
                    entityName: 'case',
                    fieldPath: 'caseNumber',
                    category: 'case'
                },
                {
                    tokenName: 'client.name',
                    description: 'Client full name',
                    dataType: 'STRING',
                    entityName: 'client',
                    fieldPath: 'name',
                    category: 'client'
                },
                {
                    tokenName: 'case.propertyAddress',
                    description: 'Complete property address',
                    dataType: 'ADDRESS',
                    entityName: 'case',
                    fieldPath: 'property.fullAddress',
                    category: 'property'
                }
            ];

            const result = await this.tokenManagementService.bulkCreateTokens(
                commonTokens,
                user.systemUserId
            );

            return ApiResponseUtil.ok(result, 'Common tokens seeded successfully');
        } catch (error) {
            throw new BadRequestException(`Failed to seed common tokens: ${error.message}`);
        }
    }

    @Post('validate-token-name')
    async validateTokenNameDetailed(@Body() request: { tokenName: string; entityName: string }) {
        try {
            // This will throw an error if invalid
            const service = this.tokenManagementService as any;
            service.validateTokenName(request.tokenName, request.entityName);

            // Check if already exists
            const existingToken = await this.tokenManagementService.getTokenByName(
                request.tokenName
            );

            return ApiResponseUtil.ok(
                {
                    tokenName: request.tokenName,
                    entityName: request.entityName,
                    isValid: true,
                    isUnique: !existingToken,
                    existingToken: existingToken
                        ? {
                              id: existingToken.id,
                              description: existingToken.description,
                              status: existingToken.status
                          }
                        : null
                },
                'Token name validation successful'
            );
        } catch (error) {
            return ApiResponseUtil.badRequest('Token name validation failed', {
                tokenName: request.tokenName,
                entityName: request.entityName,
                isValid: false,
                error: error.message
            });
        }
    }

    @Post('validate-field-path')
    async validateFieldPath(@Body() request: { entityName: string; fieldPath: string }) {
        try {
            const service = this.tokenManagementService as any;
            await service.validateEntityFieldPath(request.entityName, request.fieldPath);

            return ApiResponseUtil.ok(
                {
                    entityName: request.entityName,
                    fieldPath: request.fieldPath,
                    isValid: true,
                    isResolvable: true
                },
                'Field path validation successful'
            );
        } catch (error) {
            return ApiResponseUtil.badRequest('Field path validation failed', {
                entityName: request.entityName,
                fieldPath: request.fieldPath,
                isValid: false,
                isResolvable: false,
                error: error.message
            });
        }
    }

    @Get('suggestions/:entityName')
    getEntityTokenSuggestions(@Param('entityName') entityName: string) {
        const suggestions = this.tokenUtils.generateEntityTokenSuggestions(entityName);
        return ApiResponseUtil.ok(
            {
                entityName,
                suggestions
            },
            `Token suggestions for entity '${entityName}' retrieved successfully`
        );
    }
}
