import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CommonModule } from '@app/common';
import { HealthModule } from './health/health.module';
import { DocumentController } from './controllers/document.controller';
import { DocumentModule } from './document/document.module';
import { DocumentFolderController } from './controllers/document-folder.controller';
import { DocumentTemplateController } from './controllers/document-template.controller';
import { AuthModule } from '@app/common/auth/auth.module';
import { TokenManagementController } from './controllers/token-management.controller';
import { BullProviderModule } from '@app/common/bull/bull.module';

@Module({
    imports: [
        CommonModule,
        ConfigModule.forRoot({
            isGlobal: true
        }),
        HealthModule,
        DocumentModule,
        AuthModule,
        BullProviderModule
    ],
    controllers: [
        DocumentController,
        DocumentFolderController,
        DocumentTemplateController,
        TokenManagementController
    ],
    providers: []
})
export class AppModule {}
