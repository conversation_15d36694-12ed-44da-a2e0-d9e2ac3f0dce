import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';

interface FileValidationOptions {
    required?: boolean;
    allowedExtensions?: string[];
    maxSize?: number; // in bytes
    allowedMimeTypes?: string[];
}

interface MulterFile {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    size: number;
    destination: string;
    filename: string;
    path: string;
    buffer: <PERSON><PERSON>er;
}

@Injectable()
export class FileValidationPipe implements PipeTransform {
    constructor(private readonly options: FileValidationOptions = {}) {}

    transform(file: MulterFile): MulterFile {
        // Check if file is required
        if (this.options.required !== false && !file) {
            throw new BadRequestException('File is required');
        }

        // If file is not provided and not required, return null
        if (!file) {
            return file;
        }

        // Validate file extension
        if (this.options.allowedExtensions && this.options.allowedExtensions.length > 0) {
            const fileExt = file.originalname.toLowerCase().split('.').pop();
            if (!fileExt || !this.options.allowedExtensions.includes(fileExt)) {
                throw new BadRequestException(
                    `Invalid file extension. Allowed extensions: ${this.options.allowedExtensions.join(', ')}`
                );
            }
        }

        // Validate file size
        if (this.options.maxSize && file.size > this.options.maxSize) {
            throw new BadRequestException(
                `File size exceeds limit. Maximum size: ${this.options.maxSize} bytes`
            );
        }

        // Validate MIME type
        if (this.options.allowedMimeTypes && this.options.allowedMimeTypes.length > 0) {
            if (!this.options.allowedMimeTypes.includes(file.mimetype)) {
                throw new BadRequestException(
                    `Invalid file type. Allowed types: ${this.options.allowedMimeTypes.join(', ')}`
                );
            }
        }

        return file;
    }
}

// Pre-configured pipes for common use cases
export const DocxFileValidationPipe = new FileValidationPipe({
    required: true,
    allowedExtensions: ['docx'],
    allowedMimeTypes: ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    maxSize: 10 * 1024 * 1024 // 10MB
});
