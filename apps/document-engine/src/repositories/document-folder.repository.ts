import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { IsNull, Repository } from 'typeorm';
import { DocumentFolder } from '@app/common/typeorm/entities';

@Injectable()
export class DocumentFolderRepository extends BaseTenantRepository<DocumentFolder> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(DocumentFolder, tenantContextService, tenantConnectionService);
    }

    /**
     * Find folders by case ID
     */
    async findByCaseId(caseId: string): Promise<DocumentFolder[]> {
        return this.find({
            where: { caseId },
            order: { name: 'ASC' }
        });
    }

    /**
     * Find top-level folders for a case (no parent folder)
     */
    async findRootFoldersByCaseId(caseId: string): Promise<DocumentFolder[]> {
        return this.find({
            where: {
                caseId,
                parentFolderId: IsNull()
            },
            order: { name: 'ASC' }
        });
    }

    /**
     * Find child folders of a parent folder
     */
    async findChildFolders(parentFolderId: string): Promise<DocumentFolder[]> {
        return this.find({
            where: { parentFolderId },
            order: { name: 'ASC' }
        });
    }

    /**
     * Find folder by ID and case ID (security check)
     */
    async findByCaseIdAndId(caseId: string, folderId: string): Promise<DocumentFolder | null> {
        return this.findOne({
            where: {
                id: folderId,
                caseId
            }
        });
    }

    /**
     * Search folders by name within a case
     */
    async searchInCase(
        caseId: string,
        searchTerm: string,
        limit: number = 10
    ): Promise<DocumentFolder[]> {
        if (!searchTerm || searchTerm.length < 2) {
            return [];
        }

        const repository = await this.getTenantRepository();
        const queryBuilder = repository
            .createQueryBuilder('folder')
            .where('folder.case_id = :caseId', { caseId })
            .andWhere('folder.name ILIKE :search OR folder.description ILIKE :search', {
                search: `%${searchTerm}%`
            })
            .orderBy('folder.name', 'ASC')
            .take(limit);

        return queryBuilder.getMany();
    }

    /**
     * Get folder path (breadcrumb)
     */
    async getFolderPath(folderId: string): Promise<DocumentFolder[]> {
        const path: DocumentFolder[] = [];
        let currentFolder = await this.findOneBy({ id: folderId });

        while (currentFolder) {
            path.unshift(currentFolder);
            if (currentFolder.parentFolderId) {
                currentFolder = await this.findOneBy({ id: currentFolder.parentFolderId });
            } else {
                break;
            }
        }

        return path;
    }

    /**
     * Count folders by case ID
     */
    async countByCaseId(caseId: string): Promise<number> {
        return this.count({ where: { caseId } });
    }

    /**
     * Count documents by folder ID
     */
    async countByFolderId(folderId: string): Promise<number> {
        // This should count documents in this folder
        // We'll need to import DocumentRepository or use a raw query
        const repository = await this.getTenantRepository();
        const result = await repository
            .createQueryBuilder('folder')
            .leftJoin('documents', 'doc', 'doc.folder_id = folder.id')
            .select('COUNT(doc.id)', 'count')
            .where('folder.id = :folderId', { folderId })
            .getRawOne();

        return parseInt(result?.count || '0', 10);
    }

    /**
     * Gets the tenant repository - public method to allow access from services
     */
    async getRepository(): Promise<Repository<DocumentFolder>> {
        return this.getTenantRepository();
    }
}
