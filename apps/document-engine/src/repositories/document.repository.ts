import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { Document } from '@app/common/typeorm/entities';

@Injectable()
export class DocumentRepository extends BaseTenantRepository<Document> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(Document, tenantContextService, tenantConnectionService);
    }

    /**
     * Find documents by case ID
     */
    async findByCaseId(caseId: string): Promise<Document[]> {
        return this.find({
            where: { caseId },
            order: { updatedAt: 'DESC' }
        });
    }

    /**
     * Find documents by folder ID
     */
    async findByFolderId(folderId: string): Promise<Document[]> {
        return this.find({
            where: { folderId },
            order: { updatedAt: 'DESC' }
        });
    }

    /**
     * Find root documents in a case (not in any folder)
     */
    async findRootDocumentsByCaseId(caseId: string): Promise<Document[]> {
        return this.find({
            where: {
                caseId,
                folderId: null as any
            },
            order: { updatedAt: 'DESC' }
        });
    }

    /**
     * Search for documents by term
     */
    async search(
        searchTerm: string,
        folderId?: string,
        caseId?: string,
        limit: number = 10
    ): Promise<Document[]> {
        if (!searchTerm || searchTerm.length < 2) {
            return [];
        }

        const repository = await this.getTenantRepository();
        const queryBuilder = repository
            .createQueryBuilder('doc')
            .where('doc.name ILIKE :search OR doc.description ILIKE :search', {
                search: `%${searchTerm}%`
            });

        if (folderId) {
            queryBuilder.andWhere('doc.folder_id = :folderId', { folderId });
        }

        if (caseId) {
            queryBuilder.andWhere('doc.case_id = :caseId', { caseId });
        }

        queryBuilder.orderBy('doc.updated_at', 'DESC').take(limit);

        return queryBuilder.getMany();
    }

    /**
     * Count documents by case ID
     */
    async countByCaseId(caseId: string): Promise<number> {
        return this.count({ where: { caseId } });
    }

    /**
     * Count documents by folder ID
     */
    async countByFolderId(folderId: string): Promise<number> {
        return this.count({ where: { folderId } });
    }
}
