import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { DocumentAudit } from '@app/common/typeorm/entities';

@Injectable()
export class DocumentAuditRepository extends BaseTenantRepository<DocumentAudit> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(DocumentAudit, tenantContextService, tenantConnectionService);
    }

    /**
     * Find audit entries for a document
     */
    async findByDocumentId(documentId: string): Promise<DocumentAudit[]> {
        return this.find({
            where: { documentId },
            order: { performedAt: 'DESC' }
        });
    }

    /**
     * Find audit entries by various criteria
     */
    async findAuditEntries(options: {
        documentId?: string;
        performedBy?: string;
        actionType?: string;
        fromDate?: Date;
        toDate?: Date;
        limit?: number;
        offset?: number;
    }): Promise<{ entries: DocumentAudit[]; total: number }> {
        const repository = await this.getTenantRepository();
        const queryBuilder = repository.createQueryBuilder('audit');

        // Add conditions
        if (options.documentId) {
            queryBuilder.andWhere('audit.document_id = :documentId', {
                documentId: options.documentId
            });
        }

        if (options.performedBy) {
            queryBuilder.andWhere('audit.performed_by = :performedBy', {
                performedBy: options.performedBy
            });
        }

        if (options.actionType) {
            queryBuilder.andWhere('audit.action_type = :actionType', {
                actionType: options.actionType
            });
        }

        if (options.fromDate) {
            queryBuilder.andWhere('audit.performed_at >= :fromDate', {
                fromDate: options.fromDate
            });
        }

        if (options.toDate) {
            queryBuilder.andWhere('audit.performed_at <= :toDate', { toDate: options.toDate });
        }

        // Get total count
        const total = await queryBuilder.getCount();

        // Add pagination
        queryBuilder.orderBy('audit.performed_at', 'DESC');

        if (options.limit) {
            queryBuilder.take(options.limit);
        }

        if (options.offset) {
            queryBuilder.skip(options.offset);
        }

        const entries = await queryBuilder.getMany();

        return { entries, total };
    }
}
