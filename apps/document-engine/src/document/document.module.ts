import { Module } from '@nestjs/common';
import { documentRepositories } from '../repositories';
import { documentServices } from './services';
import { CommonModule } from '@app/common';
import { BullProviderModule } from '@app/common/bull/bull.module';
import { DocumentNotificationService } from './services/document-notification.service';

@Module({
    imports: [CommonModule, BullProviderModule],
    providers: [...documentRepositories, ...documentServices, DocumentNotificationService],
    exports: [...documentRepositories, ...documentServices, DocumentNotificationService]
})
export class DocumentModule {}
