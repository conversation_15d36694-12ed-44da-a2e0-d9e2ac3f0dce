import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DocumentAudit } from '@app/common/typeorm/entities';
import { DocumentAuditRepository } from '../../repositories/document-audit.repository';

/**
 * Service for tracking document operations for audit purposes
 */
@Injectable()
export class DocumentAuditService {
    private readonly logger = new Logger(DocumentAuditService.name);

    constructor(private readonly documentAuditRepo: DocumentAuditRepository) {}

    /**
     * Create a new audit entry
     */
    async createAuditEntry(data: {
        documentId?: string;
        documentVersionId?: string;
        actionType: string;
        actionDetails?: Record<string, any>;
        performedBy: string;
        ipAddress?: string;
        userAgent?: string;
    }): Promise<DocumentAudit> {
        try {
            const auditEntry = await this.documentAuditRepo.create({
                documentId: data.documentId,
                documentVersionId: data.documentVersionId,
                actionType: data.actionType,
                actionDetails: data.actionDetails || {},
                performedBy: data.performedBy,
                performedAt: new Date(),
                ipAddress: data.ipAddress || '',
                userAgent: data.userAgent || ''
            });

            return this.documentAuditRepo.save(auditEntry);
        } catch (error) {
            this.logger.error(`Error creating audit entry: ${error.message}`, error.stack);
            throw new Error(`Failed to create audit entry: ${error.message}`);
        }
    }

    /**
     * Find audit entries for a document
     */
    async findByDocumentId(documentId: string): Promise<DocumentAudit[]> {
        try {
            return this.documentAuditRepo.findByDocumentId(documentId);
        } catch (error) {
            this.logger.error(`Error finding audit entries: ${error.message}`, error.stack);
            throw new NotFoundException(`Failed to find audit entries: ${error.message}`);
        }
    }

    /**
     * Find audit entries by various criteria
     */
    async findAuditEntries(options: {
        documentId?: string;
        performedBy?: string;
        actionType?: string;
        fromDate?: Date;
        toDate?: Date;
        limit?: number;
        offset?: number;
    }): Promise<{ entries: DocumentAudit[]; total: number }> {
        try {
            return this.documentAuditRepo.findAuditEntries(options);
        } catch (error) {
            this.logger.error(`Error finding audit entries: ${error.message}`, error.stack);
            throw new NotFoundException(`Failed to find audit entries: ${error.message}`);
        }
    }
}
