import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import {
    DocumentTemplate,
    DocumentTemplateCategory,
    DocumentTemplateStatus,
    DocumentGenerationTrigger,
    CaseType
} from '@app/common/typeorm/entities';
import { Case } from '@app/common/typeorm/entities/tenant/case.entity';
import { CustomTokenRepository } from '@app/common/repositories/custom-token.repository';
import { BusinessDayCalculatorService } from '@app/common/services/business-day-calculator.service';
import * as Piz<PERSON>ip from 'pizzip';
import Docxtemplater from 'docxtemplater';

export interface EnhancedTokenValidationResult {
    isValid: boolean;
    foundTokens: string[];
    systemTokens: string[];
    customTokens: string[];
    validTokens: string[];
    invalidTokens: string[];
    missingTokens: string[];
    errors: string[];
    warnings: string[];
    tokenDetails: Array<{
        tokenName: string;
        tokenType: 'system' | 'custom' | 'unknown';
        isValid: boolean;
        error?: string;
    }>;
}

export interface TemplateValidationContext {
    case: Case;
    template: DocumentTemplate;
    userId: string;
    triggerType?: DocumentGenerationTrigger;
}

@Injectable()
export class TemplateValidationService {
    private readonly logger = new Logger(TemplateValidationService.name);

    constructor(
        private readonly businessDayCalculator: BusinessDayCalculatorService,
        private readonly customTokenRepository: CustomTokenRepository
    ) {}

    /**
     * Validate template tokens during upload - supports both system and custom tokens
     */
    async validateTemplateTokens(
        templateBuffer: Buffer,
        category?: DocumentTemplateCategory,
        allowedCaseTypes?: CaseType[]
    ): Promise<EnhancedTokenValidationResult> {
        const result: EnhancedTokenValidationResult = {
            isValid: true,
            foundTokens: [],
            systemTokens: [],
            customTokens: [],
            validTokens: [],
            invalidTokens: [],
            missingTokens: [],
            errors: [],
            warnings: [],
            tokenDetails: []
        };

        try {
            // Extract tokens from the template using docxtemplater
            const zip = new PizZip(templateBuffer);
            const doc = new Docxtemplater(zip, {
                paragraphLoop: true,
                linebreaks: true
            });

            // Get all tokens from the template
            const fullText = doc.getFullText();
            const foundTokens = fullText.match(/\{[^}]+\}/g) || [];

            // Clean token names (remove braces)
            const cleanTokens = foundTokens.map((token) =>
                token.replace(/^\{/, '').replace(/\}$/, '').trim()
            );

            result.foundTokens = [...new Set(cleanTokens)]; // Remove duplicates

            // Get system tokens
            const systemTokens = this.getAllSystemTokens();

            // Get custom tokens from database
            const customTokens = await this.customTokenRepository.findAllActive();
            const customTokenNames = customTokens.map((t) => t.tokenName);

            // Validate each token
            for (const tokenName of result.foundTokens) {
                const tokenDetail = {
                    tokenName,
                    tokenType: 'unknown' as 'system' | 'custom' | 'unknown',
                    isValid: false,
                    error: undefined as string | undefined
                };

                if (systemTokens.includes(tokenName)) {
                    // System token
                    tokenDetail.tokenType = 'system';
                    tokenDetail.isValid = true;
                    result.systemTokens.push(tokenName);
                    result.validTokens.push(tokenName);
                } else if (customTokenNames.includes(tokenName)) {
                    // Custom token - validate compatibility
                    const customToken = customTokens.find((t) => t.tokenName === tokenName);
                    if (customToken) {
                        const isCompatible = this.validateTokenCompatibility(
                            customToken,
                            category,
                            allowedCaseTypes
                        );

                        if (isCompatible.isValid) {
                            tokenDetail.tokenType = 'custom';
                            tokenDetail.isValid = true;
                            result.customTokens.push(tokenName);
                            result.validTokens.push(tokenName);
                        } else {
                            tokenDetail.error = isCompatible.error;
                            result.invalidTokens.push(tokenName);
                            result.errors.push(
                                `Custom token '${tokenName}': ${isCompatible.error}`
                            );
                        }

                        if (isCompatible.warnings.length > 0) {
                            result.warnings.push(
                                ...isCompatible.warnings.map(
                                    (w) => `Custom token '${tokenName}': ${w}`
                                )
                            );
                        }
                    }
                } else {
                    // Unknown token
                    tokenDetail.error = 'Token not found in system or custom tokens';
                    result.invalidTokens.push(tokenName);
                    result.errors.push(`Unknown token: ${tokenName}`);
                }

                result.tokenDetails.push(tokenDetail);
            }

            // Check category-specific requirements for system tokens
            if (category && result.foundTokens.length > 0) {
                const requiredSystemTokens = this.getRequiredSystemTokensForCategory(category);
                const foundSystemTokens = result.systemTokens;
                const missingRequired = requiredSystemTokens.filter(
                    (required) => !foundSystemTokens.includes(required)
                );

                if (missingRequired.length > 0) {
                    result.warnings.push(
                        `Missing recommended system tokens for ${category}: ${missingRequired.join(', ')}`
                    );
                    result.missingTokens.push(...missingRequired);
                }
            }

            result.isValid = result.invalidTokens.length === 0;

            this.logger.debug(
                `Enhanced template validation result: ${JSON.stringify(
                    {
                        totalTokens: result.foundTokens.length,
                        systemTokens: result.systemTokens.length,
                        customTokens: result.customTokens.length,
                        validTokens: result.validTokens.length,
                        invalidTokens: result.invalidTokens.length,
                        isValid: result.isValid
                    },
                    null,
                    2
                )}`
            );

            return result;
        } catch (error) {
            result.isValid = false;
            result.errors.push(`Failed to parse template: ${error.message}`);
            this.logger.error(`Enhanced template validation error: ${error.message}`);
            return result;
        }
    }

    /**
     * Validate if a template can be used for a specific case
     */
    async validateTemplateForCase(
        template: DocumentTemplate,
        caseData: Case,
        userId: string,
        triggerType?: DocumentGenerationTrigger
    ): Promise<EnhancedTokenValidationResult> {
        const context: TemplateValidationContext = {
            case: caseData,
            template,
            userId,
            triggerType
        };

        // Check if template is active
        if (template.status !== DocumentTemplateStatus.ACTIVE || !template.isActive) {
            throw new BadRequestException('Template is not active and cannot be used');
        }

        // Check case type compatibility
        this.validateCaseTypeCompatibility(template, caseData);

        // Create a mock template buffer validation result
        const result: EnhancedTokenValidationResult = {
            isValid: true,
            foundTokens: [...template.requiredTokens, ...template.optionalTokens],
            systemTokens: [],
            customTokens: [],
            validTokens: [],
            invalidTokens: [],
            missingTokens: [],
            errors: [],
            warnings: [],
            tokenDetails: []
        };

        // Validate each token in the template against the case data
        const allTokens = [...template.requiredTokens, ...template.optionalTokens];
        const systemTokens = this.getAllSystemTokens();
        const customTokens = await this.customTokenRepository.findAllActive();

        for (const tokenName of allTokens) {
            const tokenDetail = {
                tokenName,
                tokenType: 'unknown' as 'system' | 'custom' | 'unknown',
                isValid: false,
                error: undefined as string | undefined
            };

            if (systemTokens.includes(tokenName)) {
                // System token - validate against case data
                const value = await this.getSystemTokenValue(tokenName, caseData);
                if (value !== null && value !== undefined && value !== '') {
                    tokenDetail.tokenType = 'system';
                    tokenDetail.isValid = true;
                    result.systemTokens.push(tokenName);
                    result.validTokens.push(tokenName);
                } else if (template.requiredTokens.includes(tokenName)) {
                    tokenDetail.error = 'Required system token has no value';
                    result.missingTokens.push(tokenName);
                    result.errors.push(`Missing required system token: ${tokenName}`);
                }
            } else {
                // Check if it's a custom token
                const customToken = customTokens.find((t) => t.tokenName === tokenName);
                if (customToken) {
                    tokenDetail.tokenType = 'custom';
                    tokenDetail.isValid = true;
                    result.customTokens.push(tokenName);
                    result.validTokens.push(tokenName);
                } else {
                    tokenDetail.error = 'Token not found';
                    result.invalidTokens.push(tokenName);
                    result.errors.push(`Unknown token: ${tokenName}`);
                }
            }

            result.tokenDetails.push(tokenDetail);
        }

        // Validate conveyancing-specific requirements
        const conveyancingValidation = this.validateConveyancingRequirements(context);
        result.missingTokens.push(...conveyancingValidation.missingTokens);
        result.invalidTokens.push(...conveyancingValidation.invalidTokens);
        result.warnings.push(...conveyancingValidation.warnings);

        result.isValid = result.invalidTokens.length === 0 && result.missingTokens.length === 0;

        return result;
    }

    /**
     * Get all available tokens (both system and custom)
     */
    async getAllAvailableTokens(): Promise<string[]> {
        const systemTokens = this.getAllSystemTokens();
        const customTokens = await this.customTokenRepository.findAllActive();
        const customTokenNames = customTokens.map((t) => t.tokenName);

        return [...systemTokens, ...customTokenNames].sort();
    }

    /**
     * Get tokens available for specific case type
     */
    async getAvailableTokensForCaseType(caseType: CaseType): Promise<string[]> {
        const systemTokens = this.getAllSystemTokens();
        const customTokens = await this.customTokenRepository.findCompatibleWithCaseType([
            caseType
        ]);
        const customTokenNames = customTokens.map((t) => t.tokenName);

        return [...systemTokens, ...customTokenNames].sort();
    }

    /**
     * Get required tokens for specific template categories (system tokens only)
     */
    getRequiredSystemTokensForCategory(category: DocumentTemplateCategory): string[] {
        switch (category) {
            case DocumentTemplateCategory.CONTRACT_REQUEST:
                return [
                    'case.caseNumber',
                    'case.propertyAddress',
                    'case.purchasePrice',
                    'case.purchasePriceFormatted',
                    'client.name',
                    'case.vendorSolicitorName'
                ];

            case DocumentTemplateCategory.EXCHANGE_CONFIRMATION:
                return [
                    'case.caseNumber',
                    'case.propertyAddress',
                    'case.exchangeDate',
                    'case.exchangeDateFormatted',
                    'case.completionDate',
                    'case.completionDateFormatted',
                    'client.name'
                ];

            default:
                return ['case.caseNumber', 'client.name'];
        }
    }

    /**
     * Private helper methods
     */

    private validateTokenCompatibility(
        customToken: any,
        category?: DocumentTemplateCategory,
        allowedCaseTypes?: CaseType[]
    ): { isValid: boolean; error?: string; warnings: string[] } {
        const result = {
            isValid: true,
            error: undefined as string | undefined,
            warnings: [] as string[]
        };

        // Check if token is active
        if (!customToken.isActive || customToken.status !== 'ACTIVE') {
            result.isValid = false;
            result.error = 'Token is not active';
            return result;
        }

        // Check template type compatibility
        if (category && customToken.compatibleTemplateTypes.length > 0) {
            const categoryString = category.toString();
            if (!customToken.compatibleTemplateTypes.includes(categoryString)) {
                result.warnings.push(
                    `Token may not be compatible with template category: ${category}`
                );
            }
        }

        // Check case type compatibility
        if (
            allowedCaseTypes &&
            allowedCaseTypes.length > 0 &&
            customToken.compatibleCaseTypes.length > 0
        ) {
            const hasCompatibleCaseType = allowedCaseTypes.some((caseType) =>
                customToken.compatibleCaseTypes.includes(caseType.toString())
            );

            if (!hasCompatibleCaseType) {
                result.warnings.push(
                    `Token may not be compatible with case types: ${allowedCaseTypes.join(', ')}`
                );
            }
        }

        return result;
    }

    private validateCaseTypeCompatibility(template: DocumentTemplate, caseData: Case): void {
        if (template.allowedCaseTypes && template.allowedCaseTypes.length > 0) {
            if (!template.allowedCaseTypes.includes(caseData.type)) {
                throw new BadRequestException(
                    `Template "${template.name}" cannot be used for case type "${caseData.type}". ` +
                        `Allowed case types: ${template.allowedCaseTypes.join(', ')}`
                );
            }
        }
    }

    private validateConveyancingRequirements(context: TemplateValidationContext): {
        missingTokens: string[];
        invalidTokens: string[];
        warnings: string[];
    } {
        const result = {
            missingTokens: [] as string[],
            invalidTokens: [] as string[],
            warnings: [] as string[]
        };

        const { template, case: caseData } = context;

        // Validate based on template category
        switch (template.category) {
            case DocumentTemplateCategory.CONTRACT_REQUEST:
                this.validateContractRequestRequirements(caseData, result);
                break;
            case DocumentTemplateCategory.EXCHANGE_CONFIRMATION:
                this.validateExchangeConfirmationRequirements(caseData, result);
                break;
            case DocumentTemplateCategory.SOLICITOR_CORRESPONDENCE:
                this.validateSolicitorCorrespondenceRequirements(caseData, result);
                break;
        }

        return result;
    }

    private validateContractRequestRequirements(caseData: Case, result: any): void {
        if (!caseData.property?.fullAddress) {
            result.missingTokens.push('case.propertyAddress');
        }

        if (!caseData.property?.purchasePrice && !caseData.property?.agreedPrice) {
            result.missingTokens.push('case.purchasePrice');
        }

        if (!caseData.client) {
            result.missingTokens.push('case.client');
        }

        if (!caseData.property?.vendorSolicitorName || !caseData.property?.vendorSolicitorFirm) {
            result.warnings.push('Vendor solicitor details are incomplete');
        }

        if (!caseData.property) {
            result.warnings.push('Property information is not linked to this case');
        }
    }

    private validateExchangeConfirmationRequirements(caseData: Case, result: any): void {
        if (!caseData.exchangeDate) {
            result.missingTokens.push('case.exchangeDate');
        }

        if (!caseData.completionDate) {
            result.missingTokens.push('case.completionDate');
        }

        if (!caseData.property?.fullAddress) {
            result.missingTokens.push('case.propertyAddress');
        }

        if (!caseData.client) {
            result.missingTokens.push('case.client');
        }

        if (!caseData.property) {
            result.warnings.push('Property information is not linked to this case');
        }

        if (caseData.exchangeDate && caseData.completionDate) {
            if (caseData.exchangeDate >= caseData.completionDate) {
                result.invalidTokens.push('case.completionDate');
                result.warnings.push('Completion date must be after exchange date');
            }

            // Validate working days between exchange and completion
            const workingDaysBetween = this.businessDayCalculator.getWorkingDaysBetween(
                caseData.exchangeDate,
                caseData.completionDate
            );

            if (workingDaysBetween < 7) {
                result.warnings.push(
                    `Only ${workingDaysBetween} working days between exchange and completion. ` +
                        'Consider allowing at least 7 working days for completion.'
                );
            }

            // Check if completion date falls on a working day
            if (!this.businessDayCalculator.isWorkingDay(caseData.completionDate)) {
                result.warnings.push(
                    `Completion date (${this.businessDayCalculator.formatUKDate(caseData.completionDate)}) ` +
                        'falls on a non-working day (weekend or bank holiday)'
                );
            }
        }

        if (
            caseData.type === CaseType.PURCHASE &&
            !caseData.conveyancingMetadata?.buildingInsuranceProvider
        ) {
            result.warnings.push('Building insurance provider not specified');
        }
    }

    private validateSolicitorCorrespondenceRequirements(caseData: Case, result: any): void {
        if (!caseData.property?.vendorSolicitorName || !caseData.property?.vendorSolicitorFirm) {
            result.missingTokens.push('case.vendorSolicitor');
        }

        if (
            !caseData.property?.vendorSolicitorContact &&
            !caseData.property?.vendorSolicitorReference
        ) {
            result.warnings.push('Vendor solicitor contact details are incomplete');
        }

        if (!caseData.property) {
            result.warnings.push('Property information is not linked to this case');
        }
    }

    /**
     * Get suggested working day alternatives for a given date
     */
    getSuggestedWorkingDates(date: Date): {
        nextWorkingDay: string;
        previousWorkingDay: string;
        formattedOriginal: string;
    } {
        return {
            nextWorkingDay: this.businessDayCalculator.formatUKDateWithOrdinal(
                this.businessDayCalculator.getNextWorkingDay(date)
            ),
            previousWorkingDay: this.businessDayCalculator.formatUKDateWithOrdinal(
                this.businessDayCalculator.getPreviousWorkingDay(date)
            ),
            formattedOriginal: this.businessDayCalculator.formatUKDateWithOrdinal(date)
        };
    }

    /**
     * Calculate working days between two dates
     */
    calculateWorkingDaysBetween(startDate: Date, endDate: Date): number {
        return this.businessDayCalculator.getWorkingDaysBetween(startDate, endDate);
    }

    /**
     * Check if a date is a working day
     */
    isWorkingDay(date: Date): boolean {
        return this.businessDayCalculator.isWorkingDay(date);
    }

    private getSystemTokenValue(
        token: string,
        caseData: Case,
        context?: {
            userId?: string;
            userName?: string;
            userEmail?: string;
            documentId?: string;
            templateVersion?: string;
        }
    ): Promise<any> {
        const now = new Date();

        const tokenMap: Record<string, any> = {
            // System-generated date/time tokens
            currentDate: now.toLocaleDateString('en-GB'),
            currentDateTime: now.toLocaleString('en-GB'),
            currentYear: now.getFullYear(),
            currentMonth: now.toLocaleDateString('en-GB', { month: 'long' }),
            currentDay: now.getDate(),

            // Current user tokens
            'currentUser.name': context?.userName || null,
            'currentUser.email': context?.userEmail || null,
            'currentUser.id': context?.userId || null,

            // System metadata tokens
            generationTimestamp: now.toLocaleString('en-GB'),
            generatedBy: context?.userName || context?.userId || 'System',
            documentId: context?.documentId || `DOC-${Date.now()}`,
            templateVersion: context?.templateVersion || '1.0'
        };

        return tokenMap[token] || null;
    }

    /**
     * Get all TRUE system tokens (only system-generated data)
     */
    private getAllSystemTokens(): string[] {
        return [
            // System-generated date/time tokens
            'currentDate',
            'currentDateTime',
            'currentYear',
            'currentMonth',
            'currentDay',

            // Current user tokens
            'currentUser.name',
            'currentUser.email',
            'currentUser.id',

            // System metadata tokens
            'generationTimestamp',
            'generatedBy',
            'documentId',
            'templateVersion'
        ];
    }
}
