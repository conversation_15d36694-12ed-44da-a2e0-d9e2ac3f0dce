import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { CustomTokenRepository } from '@app/common/repositories/custom-token.repository';
import { SYSTEM_TOKEN_DEFINITIONS } from '../../config/system-tokens.config';
import {
    CustomToken,
    TokenType,
    TokenStatus,
    TokenDataType
} from '@app/common/typeorm/entities/tenant/custom-token.entity';
import {
    CreateTokenRequest,
    UpdateTokenRequest,
    TokenResolutionContext,
    TokenResolutionResult,
    AvailableEntity,
    TokenUsageStats,
    BulkTokenOperationResult
} from '@app/common/interfaces/token.interfaces';

@Injectable()
export class TokenManagementService {
    private readonly logger = new Logger(TokenManagementService.name);

    constructor(private readonly customTokenRepository: CustomTokenRepository) {}

    /**
     * Create a new custom token
     */
    async createCustomToken(request: CreateTokenRequest, createdBy: string): Promise<CustomToken> {
        // Validate token name format and uniqueness
        this.validateTokenName(request.tokenName);

        const existingToken = await this.customTokenRepository.findByName(request.tokenName);
        if (existingToken) {
            throw new BadRequestException(`Token with name '${request.tokenName}' already exists`);
        }

        // Check if this field has already been tokenized (each field can only be used once)
        const existingFieldToken = await this.customTokenRepository.findByEntityAndFieldPath(
            request.entityName,
            request.fieldPath
        );
        if (existingFieldToken) {
            throw new BadRequestException(
                `Field '${request.fieldPath}' in entity '${request.entityName}' has already been tokenized as '${existingFieldToken.tokenName}'. Each field can only be tokenized once.`
            );
        }

        // Validate entity and field path for resolvability
        this.validateEntityFieldPath(request.entityName, request.fieldPath);

        const tokenData = {
            tokenName: request.tokenName,
            description: request.description,
            tokenType: TokenType.CUSTOM,
            dataType: this.mapStringToTokenDataType(request.dataType),
            entityName: request.entityName,
            fieldPath: request.fieldPath,
            transformationConfig: request.transformationConfig || {},
            validationConfig: request.validationConfig || {},
            category: request.category,
            tags: request.tags || [],
            compatibleTemplateTypes: request.compatibleTemplateTypes || [],
            compatibleCaseTypes: request.compatibleCaseTypes || [],
            status: TokenStatus.ACTIVE,
            isActive: true,
            usageCount: 0,
            createdBy,
            lastModifiedBy: createdBy
        };

        const token = await this.customTokenRepository.create(tokenData);
        this.logger.log(`Created custom token: ${token.tokenName} (${token.id})`);

        return token;
    }

    /**
     * Update an existing custom token
     */
    async updateCustomToken(
        tokenId: string,
        request: UpdateTokenRequest,
        updatedBy: string
    ): Promise<CustomToken> {
        const token = await this.customTokenRepository.findById(tokenId);
        if (!token) {
            throw new NotFoundException(`Token with ID ${tokenId} not found`);
        }

        if (token.isSystemToken) {
            throw new BadRequestException('System tokens cannot be modified');
        }

        // Validate entity and field path if provided
        if (request.entityName && request.fieldPath) {
            this.validateEntityFieldPath(request.entityName, request.fieldPath);
        }

        const updateData: Partial<CustomToken> = {
            description: request.description,
            entityName: request.entityName,
            fieldPath: request.fieldPath,
            transformationConfig: request.transformationConfig,
            validationConfig: request.validationConfig,
            category: request.category,
            tags: request.tags,
            compatibleTemplateTypes: request.compatibleTemplateTypes,
            compatibleCaseTypes: request.compatibleCaseTypes,
            isActive: request.isActive,
            lastModifiedBy: updatedBy,
            updatedAt: new Date()
        };

        // Ensure status is typed correctly if provided
        if (request.status && typeof request.status === 'string') {
            updateData.status = request.status as TokenStatus;
        }

        const updatedToken = await this.customTokenRepository.update(tokenId, updateData);
        this.logger.log(`Updated custom token: ${token.tokenName} (${tokenId})`);

        if (!updatedToken) {
            throw new Error('Failed to update token');
        }

        return updatedToken;
    }

    /**
     * Delete a custom token
     */
    async deleteCustomToken(tokenId: string, deletedBy: string): Promise<boolean> {
        const token = await this.customTokenRepository.findById(tokenId);
        if (!token) {
            throw new NotFoundException(`Token with ID ${tokenId} not found`);
        }

        if (token.isSystemToken) {
            throw new BadRequestException('System tokens cannot be deleted');
        }

        const result = await this.customTokenRepository.softDelete(tokenId, deletedBy);
        this.logger.log(`Deleted custom token: ${token.tokenName} (${tokenId})`);

        return result;
    }

    /**
     * Get token by ID
     */
    async getTokenById(tokenId: string): Promise<CustomToken | null> {
        return this.customTokenRepository.findById(tokenId);
    }

    /**
     * Get token by name
     */
    async getTokenByName(tokenName: string): Promise<CustomToken | null> {
        return this.customTokenRepository.findByName(tokenName);
    }

    /**
     * Get all active tokens
     */
    async getAllActiveTokens(): Promise<CustomToken[]> {
        return this.customTokenRepository.findAllActive();
    }

    /**
     * Get system tokens (both database-stored and hardcoded system tokens)
     */
    async getSystemTokens(): Promise<CustomToken[]> {
        // Get database-stored system tokens
        const databaseSystemTokens = await this.customTokenRepository.findSystemTokens();

        // Get hardcoded system tokens from document generation service
        const hardcodedSystemTokens = this.getHardcodedSystemTokens();

        // Combine both types
        return [...databaseSystemTokens, ...hardcodedSystemTokens];
    }

    /**
     * Get hardcoded system tokens (from document generation service)
     */
    private getHardcodedSystemTokens(): CustomToken[] {
        const systemTokenDefinitions = SYSTEM_TOKEN_DEFINITIONS;

        return systemTokenDefinitions.map((token) => {
            // Create a CustomToken-like object for hardcoded system tokens
            const customToken =
                new (require('@app/common/typeorm/entities/tenant/custom-token.entity').CustomToken)();
            customToken.tokenName = token.name;
            customToken.description = token.description;
            customToken.tokenType =
                require('@app/common/typeorm/entities/tenant/custom-token.entity').TokenType.SYSTEM;
            customToken.dataType = this.inferDataTypeFromTokenName(token.name);
            customToken.status =
                require('@app/common/typeorm/entities/tenant/custom-token.entity').TokenStatus.ACTIVE;
            customToken.isActive = true;
            customToken.category = token.category;
            customToken.entityName = 'system';
            customToken.fieldPath = token.name;
            customToken.transformationConfig = {};
            customToken.validationConfig = {};
            customToken.tags = ['system', 'hardcoded'];
            customToken.usageCount = 0;
            customToken.compatibleTemplateTypes = [];
            customToken.compatibleCaseTypes = [];
            customToken.metadata = {
                source: 'hardcoded',
                location: 'system'
            };
            customToken.createdBy = 'system';
            customToken.lastModifiedBy = 'system';
            customToken.createdAt = new Date();
            customToken.updatedAt = new Date();
            customToken.id = `system-${token.name.replace(/\./g, '-')}`;

            return customToken;
        });
    }

    /**
     * Infer data type from token name for hardcoded system tokens
     */
    private inferDataTypeFromTokenName(tokenName: string): any {
        const TokenDataType =
            require('@app/common/typeorm/entities/tenant/custom-token.entity').TokenDataType;

        if (tokenName.includes('email') || tokenName.includes('Email')) {
            return TokenDataType.EMAIL;
        }
        if (
            tokenName.includes('Date') ||
            tokenName.includes('DateTime') ||
            tokenName.includes('Timestamp')
        ) {
            return TokenDataType.DATE;
        }
        if (tokenName.includes('Year') || tokenName.includes('Day')) {
            return TokenDataType.NUMBER;
        }
        if (tokenName.includes('Id') || tokenName.includes('ID')) {
            return TokenDataType.STRING;
        }

        return TokenDataType.STRING; // Default to string
    }

    /**
     * Get custom tokens
     */
    async getCustomTokens(): Promise<CustomToken[]> {
        return this.customTokenRepository.findCustomTokens();
    }

    /**
     * Search tokens with filters (includes both database and hardcoded system tokens)
     */
    async searchTokens(filters: {
        page?: number;
        limit?: number;
        search?: string;
        tokenType?: TokenType;
        entityName?: string;
        category?: string;
        status?: TokenStatus;
        isActive?: boolean;
        sortBy?: string;
        order?: 'ASC' | 'DESC';
    }): Promise<{ tokens: CustomToken[]; total: number }> {
        const TokenType =
            require('@app/common/typeorm/entities/tenant/custom-token.entity').TokenType;

        // Get database tokens
        const [databaseTokens, databaseTotal] =
            await this.customTokenRepository.findWithFilters(filters);

        // If filtering specifically for custom tokens, return only database results
        if (filters.tokenType === TokenType.CUSTOM) {
            return { tokens: databaseTokens, total: databaseTotal };
        }

        // Get hardcoded system tokens
        const hardcodedSystemTokens = this.getHardcodedSystemTokens();

        // If filtering specifically for system tokens, return only system tokens
        if (filters.tokenType === TokenType.SYSTEM) {
            let filteredSystemTokens = hardcodedSystemTokens;

            // Apply search filter to system tokens if provided
            if (filters.search) {
                const searchLower = filters.search.toLowerCase();
                filteredSystemTokens = hardcodedSystemTokens.filter(
                    (token) =>
                        token.tokenName.toLowerCase().includes(searchLower) ||
                        token.description.toLowerCase().includes(searchLower) ||
                        (token.category && token.category.toLowerCase().includes(searchLower))
                );
            }

            // Apply category filter if provided
            if (filters.category) {
                filteredSystemTokens = filteredSystemTokens.filter(
                    (token) => token.category === filters.category
                );
            }

            // Apply sorting to system tokens
            if (filters.sortBy) {
                filteredSystemTokens.sort((a, b) => {
                    const aValue = a[filters.sortBy as keyof CustomToken];
                    const bValue = b[filters.sortBy as keyof CustomToken];
                    const aStr =
                        aValue == null
                            ? ''
                            : typeof aValue === 'string'
                              ? aValue
                              : typeof aValue === 'number'
                                ? aValue.toString()
                                : '';
                    const bStr =
                        bValue == null
                            ? ''
                            : typeof bValue === 'string'
                              ? bValue
                              : typeof bValue === 'number'
                                ? bValue.toString()
                                : '';
                    const comparison = aStr.localeCompare(bStr);
                    return filters.order === 'DESC' ? -comparison : comparison;
                });
            }

            // Apply pagination to system tokens
            const startIndex = ((filters.page || 1) - 1) * (filters.limit || 10);
            const endIndex = startIndex + (filters.limit || 10);
            const paginatedSystemTokens = filteredSystemTokens.slice(startIndex, endIndex);

            return { tokens: paginatedSystemTokens, total: filteredSystemTokens.length };
        }

        // If no specific token type filter, include both database and system tokens
        let allTokens = [...databaseTokens, ...hardcodedSystemTokens];

        // Apply search filter to system tokens if provided
        if (filters.search) {
            const searchLower = filters.search.toLowerCase();
            const filteredSystemTokens = hardcodedSystemTokens.filter(
                (token) =>
                    token.tokenName.toLowerCase().includes(searchLower) ||
                    token.description.toLowerCase().includes(searchLower) ||
                    (token.category && token.category.toLowerCase().includes(searchLower))
            );

            allTokens = [...databaseTokens, ...filteredSystemTokens];
        }

        // Apply category filter if provided
        if (filters.category) {
            const filteredSystemTokens = hardcodedSystemTokens.filter(
                (token) => token.category === filters.category
            );
            allTokens = [...databaseTokens, ...filteredSystemTokens];
        }

        // Apply sorting to combined tokens
        if (filters.sortBy) {
            allTokens.sort((a, b) => {
                const aValue = a[filters.sortBy as keyof CustomToken];
                const bValue = b[filters.sortBy as keyof CustomToken];
                const aStr =
                    aValue == null
                        ? ''
                        : typeof aValue === 'string'
                          ? aValue
                          : typeof aValue === 'number'
                            ? aValue.toString()
                            : '';
                const bStr =
                    bValue == null
                        ? ''
                        : typeof bValue === 'string'
                          ? bValue
                          : typeof bValue === 'number'
                            ? bValue.toString()
                            : '';
                const comparison = aStr.localeCompare(bStr);
                return filters.order === 'DESC' ? -comparison : comparison;
            });
        }

        // Apply pagination to combined tokens
        const startIndex = ((filters.page || 1) - 1) * (filters.limit || 10);
        const endIndex = startIndex + (filters.limit || 10);
        const paginatedTokens = allTokens.slice(startIndex, endIndex);

        return { tokens: paginatedTokens, total: allTokens.length };
    }

    /**
     * Resolve token value from context
     */
    async resolveTokenValue(
        tokenName: string,
        context: TokenResolutionContext
    ): Promise<TokenResolutionResult> {
        const token = await this.customTokenRepository.findByName(tokenName);
        if (!token) {
            return {
                value: null,
                success: false,
                error: `Token '${tokenName}' not found`,
                dataType: 'unknown',
                transformed: false
            };
        }

        if (!token.isActive || token.status !== TokenStatus.ACTIVE) {
            return {
                value: null,
                success: false,
                error: `Token '${tokenName}' is not active`,
                dataType: token.dataType,
                transformed: false
            };
        }

        try {
            // Get raw value from entity and field path
            const rawValue = this.extractValueFromContext(
                context,
                token.entityName,
                token.fieldPath
            );

            // Apply transformations if configured
            const transformedValue = this.applyTransformations(
                rawValue,
                token.transformationConfig,
                token.dataType
            );

            // Validate the value
            const isValid = this.validateTokenValue(transformedValue, token.validationConfig);
            if (!isValid) {
                return {
                    value: token.transformationConfig.defaultValue || null,
                    success: false,
                    error: `Token value failed validation`,
                    dataType: token.dataType,
                    transformed: true
                };
            }

            // Update usage statistics
            await this.customTokenRepository.incrementUsage(token.id);

            return {
                value: transformedValue,
                success: true,
                dataType: token.dataType,
                transformed: rawValue !== transformedValue
            };
        } catch (error) {
            this.logger.error(`Error resolving token '${tokenName}': ${error.message}`);
            return {
                value: token.transformationConfig.defaultValue || null,
                success: false,
                error: error.message,
                dataType: token.dataType,
                transformed: false
            };
        }
    }

    /**
     * Get available entities for token creation - SIMPLIFIED VERSION
     */
    getAvailableEntities(): AvailableEntity[] {
        return [
            {
                name: 'case',
                displayName: 'Case',
                description: 'Case information',
                fields: [
                    {
                        name: 'caseNumber',
                        path: 'caseNumber',
                        displayName: 'Case Number',
                        dataType: 'string',
                        nullable: false,
                        description: 'Case number',
                        example: 'CAS-2025-001'
                    },
                    {
                        name: 'title',
                        path: 'title',
                        displayName: 'Case Title',
                        dataType: 'string',
                        nullable: true,
                        description: 'Case title',
                        example: 'Property Purchase'
                    },
                    {
                        name: 'type',
                        path: 'type',
                        displayName: 'Case Type',
                        dataType: 'string',
                        nullable: false,
                        description: 'PURCHASE, SALE, etc.',
                        example: 'PURCHASE'
                    },
                    {
                        name: 'priorityLevel',
                        path: 'priorityLevel',
                        displayName: 'Priority Level',
                        dataType: 'string',
                        nullable: true,
                        description: 'Case priority level',
                        example: 'HIGH'
                    },
                    {
                        name: 'exchangeDate',
                        path: 'exchangeDate',
                        displayName: 'Exchange Date',
                        dataType: 'date',
                        nullable: true,
                        description: 'Exchange date',
                        example: '2025-01-15'
                    },
                    {
                        name: 'completionDate',
                        path: 'completionDate',
                        displayName: 'Completion Date',
                        dataType: 'date',
                        nullable: true,
                        description: 'Completion date',
                        example: '2025-02-15'
                    },
                    {
                        name: 'propertyAddress',
                        path: 'property.fullAddress',
                        displayName: 'Property Address',
                        dataType: 'address',
                        nullable: true,
                        description: 'Property address',
                        example: '123 Main St, London'
                    },
                    {
                        name: 'purchasePrice',
                        path: 'property.purchasePrice',
                        displayName: 'Purchase Price',
                        dataType: 'currency',
                        nullable: true,
                        description: 'Purchase price',
                        example: 500000
                    },
                    {
                        name: 'vendorSolicitorName',
                        path: 'property.vendorSolicitorName',
                        displayName: 'Vendor Solicitor',
                        dataType: 'string',
                        nullable: true,
                        description: 'Vendor solicitor name',
                        example: 'Jane Doe'
                    },
                    {
                        name: 'vendorSolicitorFirm',
                        path: 'property.vendorSolicitorFirm',
                        displayName: 'Vendor Solicitor Firm',
                        dataType: 'string',
                        nullable: true,
                        description: 'Vendor solicitor firm',
                        example: 'Doe & Associates'
                    },
                    {
                        name: 'assignedLawyerName',
                        path: 'assignedLawyer.name',
                        displayName: 'Assigned Lawyer',
                        dataType: 'string',
                        nullable: true,
                        description: 'Assigned lawyer name',
                        example: 'Sarah Johnson'
                    }
                ],
                relations: []
            },
            {
                name: 'client',
                displayName: 'Client',
                description: 'Client information',
                fields: [
                    {
                        name: 'name',
                        path: 'name',
                        displayName: 'Full Name',
                        dataType: 'string',
                        nullable: false,
                        description: 'Client name',
                        example: 'John Smith'
                    },
                    {
                        name: 'firstName',
                        path: 'firstName',
                        displayName: 'First Name',
                        dataType: 'string',
                        nullable: true,
                        description: 'First name',
                        example: 'John'
                    },
                    {
                        name: 'lastName',
                        path: 'lastName',
                        displayName: 'Last Name',
                        dataType: 'string',
                        nullable: true,
                        description: 'Last name',
                        example: 'Smith'
                    },
                    {
                        name: 'email',
                        path: 'email',
                        displayName: 'Email',
                        dataType: 'email',
                        nullable: true,
                        description: 'Email address',
                        example: '<EMAIL>'
                    },
                    {
                        name: 'phone',
                        path: 'phone',
                        displayName: 'Phone',
                        dataType: 'phone',
                        nullable: true,
                        description: 'Phone number',
                        example: '+44 20 1234 5678'
                    },
                    {
                        name: 'address',
                        path: 'address',
                        displayName: 'Address',
                        dataType: 'address',
                        nullable: true,
                        description: 'Client address',
                        example: '123 Client St, London'
                    }
                ],
                relations: []
            },
            {
                name: 'property',
                displayName: 'Property',
                description: 'Property information',
                fields: [
                    {
                        name: 'fullAddress',
                        path: 'fullAddress',
                        displayName: 'Address',
                        dataType: 'address',
                        nullable: false,
                        description: 'Property address',
                        example: '123 Property St, London'
                    },
                    {
                        name: 'purchasePrice',
                        path: 'purchasePrice',
                        displayName: 'Purchase Price',
                        dataType: 'currency',
                        nullable: true,
                        description: 'Purchase price',
                        example: 500000
                    },
                    {
                        name: 'askingPrice',
                        path: 'askingPrice',
                        displayName: 'Asking Price',
                        dataType: 'currency',
                        nullable: true,
                        description: 'Asking price',
                        example: 520000
                    },
                    {
                        name: 'propertyType',
                        path: 'propertyType',
                        displayName: 'Property Type',
                        dataType: 'string',
                        nullable: true,
                        description: 'House, Flat, etc.',
                        example: 'Terraced House'
                    },
                    {
                        name: 'tenure',
                        path: 'tenure',
                        displayName: 'Tenure',
                        dataType: 'string',
                        nullable: true,
                        description: 'Freehold/Leasehold',
                        example: 'Freehold'
                    },
                    {
                        name: 'bedrooms',
                        path: 'bedrooms',
                        displayName: 'Bedrooms',
                        dataType: 'number',
                        nullable: true,
                        description: 'Number of bedrooms',
                        example: 3
                    },
                    {
                        name: 'vendorSolicitorName',
                        path: 'vendorSolicitorName',
                        displayName: 'Vendor Solicitor',
                        dataType: 'string',
                        nullable: true,
                        description: 'Vendor solicitor name',
                        example: 'Jane Doe'
                    },
                    {
                        name: 'vendorSolicitorFirm',
                        path: 'vendorSolicitorFirm',
                        displayName: 'Vendor Firm',
                        dataType: 'string',
                        nullable: true,
                        description: 'Vendor solicitor firm',
                        example: 'Doe & Associates'
                    }
                ],
                relations: []
            },
            {
                name: 'lawyer',
                displayName: 'Lawyer',
                description: 'Lawyer information',
                fields: [
                    {
                        name: 'name',
                        path: 'name',
                        displayName: 'Full Name',
                        dataType: 'string',
                        nullable: false,
                        description: 'Lawyer name',
                        example: 'Sarah Johnson'
                    },
                    {
                        name: 'firstName',
                        path: 'firstName',
                        displayName: 'First Name',
                        dataType: 'string',
                        nullable: true,
                        description: 'First name',
                        example: 'Sarah'
                    },
                    {
                        name: 'lastName',
                        path: 'lastName',
                        displayName: 'Last Name',
                        dataType: 'string',
                        nullable: true,
                        description: 'Last name',
                        example: 'Johnson'
                    },
                    {
                        name: 'email',
                        path: 'email',
                        displayName: 'Email',
                        dataType: 'email',
                        nullable: true,
                        description: 'Email address',
                        example: '<EMAIL>'
                    },
                    {
                        name: 'department',
                        path: 'department',
                        displayName: 'Department',
                        dataType: 'string',
                        nullable: true,
                        description: 'Department',
                        example: 'Conveyancing'
                    }
                ],
                relations: []
            },
            {
                name: 'system',
                displayName: 'System',
                description: 'System information (auto-populated)',
                fields: [
                    {
                        name: 'currentDate',
                        path: 'currentDate',
                        displayName: 'Current Date',
                        dataType: 'date',
                        nullable: false,
                        description: "Today's date",
                        example: '15/01/2025'
                    },
                    {
                        name: 'currentDateTime',
                        path: 'currentDateTime',
                        displayName: 'Current Date & Time',
                        dataType: 'date',
                        nullable: false,
                        description: 'Current date and time',
                        example: '15/01/2025 14:30'
                    },
                    {
                        name: 'currentUserName',
                        path: 'currentUser.name',
                        displayName: 'Current User',
                        dataType: 'string',
                        nullable: false,
                        description: 'Logged in user',
                        example: 'Sarah Johnson'
                    },
                    {
                        name: 'currentUserEmail',
                        path: 'currentUser.email',
                        displayName: 'Current User Email',
                        dataType: 'email',
                        nullable: false,
                        description: 'Logged in user email',
                        example: '<EMAIL>'
                    }
                ],
                relations: []
            }
        ];
    }

    /**
     * Get token usage statistics
     */
    async getTokenUsageStats(): Promise<TokenUsageStats[]> {
        const stats = await this.customTokenRepository.getUsageStats();
        return stats.map((stat) => ({
            tokenId: stat.id,
            tokenName: stat.tokenName,
            usageCount: stat.usageCount,
            lastUsedAt: stat.lastUsedAt,
            templatesUsing: [], // This would need to be populated from template analysis
            usageTrend: 'stable' // This would need historical data analysis
        }));
    }

    /**
     * Bulk operations for tokens
     */
    async bulkCreateTokens(
        requests: CreateTokenRequest[],
        createdBy: string
    ): Promise<BulkTokenOperationResult> {
        const results: BulkTokenOperationResult = {
            successful: 0,
            failed: 0,
            results: []
        };

        for (const request of requests) {
            try {
                await this.createCustomToken(request, createdBy);
                results.successful++;
                results.results.push({
                    tokenName: request.tokenName,
                    success: true
                });
            } catch (error) {
                results.failed++;
                results.results.push({
                    tokenName: request.tokenName,
                    success: false,
                    error: error.message
                });
            }
        }

        return results;
    }

    /**
     * Private helper methods
     */

    private validateTokenName(tokenName: string): void {
        // Token name can be any descriptive name (no entity prefix required)
        if (!tokenName || tokenName.trim().length === 0) {
            throw new BadRequestException('Token name is required');
        }

        // Validate token name doesn't contain invalid characters
        const validTokenPattern = /^[a-zA-Z][a-zA-Z0-9_]*$/;
        if (!validTokenPattern.test(tokenName)) {
            throw new BadRequestException(
                `Token name '${tokenName}' contains invalid characters. Use only letters, numbers, and underscores. No dots allowed.`
            );
        }

        // Token names should be descriptive but simple
        if (tokenName.length < 2) {
            throw new BadRequestException('Token name must be at least 2 characters long');
        }

        if (tokenName.length > 50) {
            throw new BadRequestException('Token name must be no more than 50 characters long');
        }
    }

    private validateEntityFieldPath(entityName: string, fieldPath: string): void {
        const availableEntities = this.getAvailableEntities();
        const entity = availableEntities.find((e) => e.name === entityName);

        if (!entity) {
            const availableEntityNames = availableEntities.map((e) => e.name).join(', ');
            throw new BadRequestException(
                `Entity '${entityName}' is not available. Available entities: ${availableEntityNames}`
            );
        }

        if (!fieldPath || fieldPath.trim().length === 0) {
            throw new BadRequestException('Field path cannot be empty');
        }

        // Validate field path is resolvable
        this.validateFieldPathResolvability(entity, fieldPath);
    }

    private validateFieldPathResolvability(entity: AvailableEntity, fieldPath: string): void {
        // Simplified validation - check if the field path exists in the entity's available fields
        const field = entity.fields.find((f) => f.path === fieldPath);

        if (!field) {
            const availableFieldPaths = entity.fields
                .map((f) => `${f.name} (${f.path})`)
                .join(', ');
            throw new BadRequestException(
                `Field path '${fieldPath}' is not available for entity '${entity.name}'. ` +
                    `Available field paths: ${availableFieldPaths}`
            );
        }

        // Field exists and is valid
        this.logger.debug(
            `Field path '${fieldPath}' validated successfully for entity '${entity.name}'`
        );
    }

    private mapStringToTokenDataType(dataTypeString: string): TokenDataType {
        const mapping: Record<string, TokenDataType> = {
            string: TokenDataType.STRING,
            number: TokenDataType.NUMBER,
            date: TokenDataType.DATE,
            boolean: TokenDataType.BOOLEAN,
            currency: TokenDataType.CURRENCY,
            email: TokenDataType.EMAIL,
            phone: TokenDataType.PHONE,
            address: TokenDataType.ADDRESS
        };

        return mapping[dataTypeString.toLowerCase()] || TokenDataType.STRING;
    }

    private extractValueFromContext(
        context: TokenResolutionContext,
        entityName: string,
        fieldPath: string
    ): any {
        const entity = context[entityName as keyof TokenResolutionContext];
        if (!entity) {
            return null;
        }

        // Navigate field path (e.g., "property.address.street")
        const pathParts = fieldPath.split('.');
        let value = entity;

        for (const part of pathParts) {
            if (value && typeof value === 'object' && part in value) {
                value = value[part];
            } else {
                return null;
            }
        }

        return value;
    }

    private applyTransformations(
        value: any,
        transformationConfig: any,
        dataType: TokenDataType
    ): any {
        if (value === null || value === undefined) {
            return transformationConfig.defaultValue || null;
        }

        let transformed = value;

        // Apply prefix/suffix
        if (transformationConfig.prefix || transformationConfig.suffix) {
            transformed = `${transformationConfig.prefix || ''}${transformed}${transformationConfig.suffix || ''}`;
        }

        // Apply case transformations
        if (typeof transformed === 'string') {
            if (transformationConfig.uppercase) {
                transformed = transformed.toUpperCase();
            } else if (transformationConfig.lowercase) {
                transformed = transformed.toLowerCase();
            }

            // Apply truncation
            if (
                transformationConfig.truncate &&
                transformed.length > transformationConfig.truncate
            ) {
                transformed = transformed.substring(0, transformationConfig.truncate);
            }
        }

        // Apply data type specific formatting
        switch (dataType) {
            case TokenDataType.CURRENCY:
                if (typeof value === 'number') {
                    transformed = new Intl.NumberFormat('en-GB', {
                        style: 'currency',
                        currency: 'GBP'
                    }).format(value);
                }
                break;

            case TokenDataType.DATE:
                if (value instanceof Date || typeof value === 'string') {
                    const date = value instanceof Date ? value : new Date(value);
                    const format = transformationConfig.format || 'en-GB';
                    transformed = date.toLocaleDateString(format);
                }
                break;

            case TokenDataType.PHONE:
                // Apply phone number formatting if configured
                break;
        }

        return transformed;
    }

    private validateTokenValue(value: any, validationConfig: any): boolean {
        if (validationConfig.required && (value === null || value === undefined || value === '')) {
            return false;
        }

        if (typeof value === 'string') {
            if (validationConfig.minLength && value.length < validationConfig.minLength) {
                return false;
            }

            if (validationConfig.maxLength && value.length > validationConfig.maxLength) {
                return false;
            }

            if (validationConfig.pattern) {
                const regex = new RegExp(validationConfig.pattern);
                if (!regex.test(value)) {
                    return false;
                }
            }
        }

        if (validationConfig.allowedValues && validationConfig.allowedValues.length > 0) {
            if (!validationConfig.allowedValues.includes(value)) {
                return false;
            }
        }

        return true;
    }
}
