import { Injectable, Logger, Optional } from '@nestjs/common';
import { TokenManagementService } from './token-management.service';
import { TemplateValidationService } from './template-validation.service';
import { DocumentTemplateService } from './document-template.service';
import { S3StorageService } from './s3-storage.service';
import { DocumentNotificationService } from './document-notification.service';
import { TokenResolutionContext } from '@app/common/interfaces/token.interfaces';
import { DocumentTemplate } from '@app/common/typeorm/entities/tenant/document-template.entity';
import { Case } from '@app/common/typeorm/entities/tenant/case.entity';
import { CaseRepository } from '@app/common/repositories/case.repository';
import { TenantContextService } from '@app/common/multi-tenancy/tenant-context.service';
import * as PizZip from 'pizzip';
import Docxtemplater from 'docxtemplater';

export interface GenerationRequest {
    templateId: string;
    caseId: string;
    customData?: Record<string, any>;
    userId?: string;
    userName?: string;
    userEmail?: string;
    outputFileName?: string;
    autoAttachToCase?: boolean;
    autoEmailToClient?: boolean;
    notifyGeneration?: boolean;
    tenantId?: string;
    tenantName?: string;
}

export interface GenerationResult {
    success: boolean;
    filename?: string;
    documentUrl?: string;
    attachmentId?: string;
    errors: string[];
    warnings: string[];
    tokenResolutionDetails: Array<{
        tokenName: string;
        resolved: boolean;
        value: any;
        source: 'system' | 'custom' | 'fallback';
        error?: string;
    }>;
    metadata: Record<string, any>;
}

@Injectable()
export class DocumentGenerationService {
    private readonly logger = new Logger(DocumentGenerationService.name);

    constructor(
        private readonly tokenManagementService: TokenManagementService,
        private readonly templateValidationService: TemplateValidationService,
        private readonly documentTemplateService: DocumentTemplateService,
        private readonly s3StorageService: S3StorageService,
        private readonly caseRepository: CaseRepository,
        @Optional() private readonly notificationService: DocumentNotificationService,
        @Optional() private readonly tenantContextService: TenantContextService
    ) {}

    /**
     * Generate document with strict token resolution
     * FAILS if any token cannot be resolved
     */
    async generateDocument(request: GenerationRequest): Promise<GenerationResult> {
        const startTime = Date.now();
        const result: GenerationResult = {
            success: false,
            errors: [],
            warnings: [],
            tokenResolutionDetails: [],
            metadata: {}
        };

        try {
            // Get template
            const template = await this.documentTemplateService.getTemplateById(request.templateId);
            if (!template) {
                result.errors.push(`Template with ID ${request.templateId} not found`);
                return result;
            }

            // Get case data (this would typically come from your case service)
            const caseData = await this.getCaseData(request.caseId);
            if (!caseData) {
                result.errors.push(`Case with ID ${request.caseId} not found`);
                return result;
            }

            // Validate template for case (this method should be updated to match current interface)
            const validation = await this.templateValidationService.validateTemplateForCase(
                template,
                caseData,
                request.userId || 'system'
            );

            if (!validation.isValid) {
                result.errors.push(...validation.invalidTokens.map((t) => `Invalid token: ${t}`));
                result.warnings.push(...validation.warnings);
                return result;
            }

            // Download template file from S3
            const templateBuffer = await this.downloadTemplateFile(template.s3Key);

            // Create token resolution context
            const context: TokenResolutionContext = {
                case: caseData,
                client: caseData.client,
                property: caseData.property,
                user: {
                    name: request.userName,
                    email: request.userEmail,
                    id: request.userId
                },
                customEntities: request.customData || {},
                timestamp: new Date()
            };

            // Extract tokens from template
            const foundTokens = this.extractTokensFromTemplate(templateBuffer);

            if (foundTokens.length === 0) {
                result.warnings.push('No tokens found in template');
            } else {
                this.logger.debug(
                    `Found ${foundTokens.length} tokens in template: ${foundTokens.join(', ')}`
                );
            }

            // Pre-validate all tokens exist (either as system tokens or custom tokens in database)
            const preValidationResult = await this.preValidateTokens(foundTokens);
            if (!preValidationResult.isValid) {
                result.errors.push(...preValidationResult.errors);
                return result;
            }

            // Generate document with token resolution
            const generationResult = await this.generateDocumentWithTokens(
                templateBuffer,
                template,
                context,
                foundTokens
            );

            if (!generationResult.success) {
                result.errors.push(...generationResult.errors);
                return result;
            }

            // Upload generated document to S3
            const outputFileName =
                request.outputFileName ||
                `${template.name}_${caseData.caseNumber}_${Date.now()}.docx`;

            // Upload to S3 using existing uploadFile method
            const uploadResult = await this.s3StorageService.uploadFile(
                request.caseId,
                null, // folderId
                outputFileName,
                generationResult.documentBuffer!,
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            );

            // Update result
            result.success = true;
            result.filename = outputFileName;
            result.documentUrl = uploadResult.url;
            result.tokenResolutionDetails = generationResult.tokenResolutionDetails;
            result.warnings.push(...validation.warnings);
            result.metadata = {
                templateId: template.id,
                templateName: template.name,
                caseId: request.caseId,
                caseNumber: caseData.caseNumber,
                generationTimeMs: Date.now() - startTime,
                tokensResolved: generationResult.tokenResolutionDetails.filter((t) => t.resolved)
                    .length,
                totalTokens: generationResult.tokenResolutionDetails.length,
                s3Key: uploadResult.s3Key,
                s3Bucket: uploadResult.s3Bucket
            };

            this.logger.log(
                `Document generated successfully: ${outputFileName} (${Date.now() - startTime}ms)`
            );

            // Send notification if enabled and service is available
            if (request.notifyGeneration && this.notificationService) {
                try {
                    const tenantId = request.tenantId || this.getTenantId();
                    if (tenantId && request.userEmail && request.userName && request.tenantName) {
                        await this.notificationService.notifyDocumentGenerated({
                            tenantId,
                            tenantName: request.tenantName,
                            userId: request.userId || 'system',
                            userEmail: request.userEmail,
                            userName: request.userName,
                            template,
                            caseId: request.caseId,
                            caseNumber: caseData.caseNumber
                        });
                    }
                } catch (error) {
                    this.logger.error('Failed to send generation notification', error);
                    // Don't fail the generation if notification fails
                }
            }

            // Send document to client if enabled and client data is available
            if (request.autoEmailToClient && this.notificationService && caseData.client) {
                try {
                    const tenantId = request.tenantId || this.getTenantId();
                    if (tenantId && request.tenantName && caseData.client.email) {
                        const mockDocument = {
                            id: result.metadata.documentId || 'generated-doc',
                            name: result.filename
                        };

                        await this.notificationService.notifyDocumentSentToClient({
                            tenantId,
                            tenantName: request.tenantName,
                            userId: request.userId || 'system',
                            userEmail: request.userEmail || '',
                            userName: request.userName || 'System',
                            document: mockDocument as any,
                            caseId: request.caseId,
                            caseNumber: caseData.caseNumber,
                            clientEmail: caseData.client.email,
                            clientName: caseData.client.name || 'Valued Client',
                            documentUrl: result.documentUrl || '',
                            clientMessage: `Dear ${caseData.client.name || 'Valued Client'},\n\nPlease find your requested document "${result.filename}" attached.\n\nIf you have any questions, please don't hesitate to contact us.\n\nBest regards,\n${request.tenantName}`
                        });

                        this.logger.log(
                            `Document automatically sent to client: ${caseData.client.email}`
                        );
                    }
                } catch (error) {
                    this.logger.error('Failed to send document to client', error);
                    // Don't fail the generation if client email fails
                }
            }

            return result;
        } catch (error) {
            this.logger.error(`Document generation failed: ${error.message}`, error.stack);
            result.errors.push(`Document generation failed: ${error.message}`);

            // Send failure notification if enabled and service is available
            if (request.notifyGeneration && this.notificationService) {
                try {
                    const tenantId = request.tenantId || this.getTenantId();
                    if (tenantId && request.userEmail && request.userName && request.tenantName) {
                        await this.notificationService.notifyDocumentGenerationFailed({
                            tenantId,
                            tenantName: request.tenantName,
                            userId: request.userId || 'system',
                            userEmail: request.userEmail,
                            userName: request.userName,
                            templateName: 'Unknown Template',
                            caseId: request.caseId,
                            errorMessage: error.message
                        });
                    }
                } catch (notificationError) {
                    this.logger.error(
                        'Failed to send generation failure notification',
                        notificationError
                    );
                }
            }

            return result;
        }
    }

    /**
     * Generate document with token resolution
     */
    private async generateDocumentWithTokens(
        templateBuffer: Buffer,
        template: DocumentTemplate,
        context: TokenResolutionContext,
        foundTokens: string[]
    ): Promise<{
        success: boolean;
        documentBuffer?: Buffer;
        errors: string[];
        tokenResolutionDetails: Array<{
            tokenName: string;
            resolved: boolean;
            value: any;
            source: 'system' | 'custom' | 'fallback';
            error?: string;
        }>;
    }> {
        const result = {
            success: false,
            documentBuffer: undefined as Buffer | undefined,
            errors: [] as string[],
            tokenResolutionDetails: [] as Array<{
                tokenName: string;
                resolved: boolean;
                value: any;
                source: 'system' | 'custom' | 'fallback';
                error?: string;
            }>
        };

        try {
            const tokenData: Record<string, any> = {};
            const systemTokens = this.getSystemTokens();
            const unresolvedTokens: string[] = [];

            for (const tokenName of foundTokens) {
                const resolutionDetail = {
                    tokenName,
                    resolved: false,
                    value: null,
                    source: 'fallback' as 'system' | 'custom' | 'fallback',
                    error: undefined as string | undefined
                };

                try {
                    if (systemTokens.includes(tokenName)) {
                        // Resolve system token
                        const systemValue = this.resolveSystemToken(tokenName, context);
                        if (systemValue !== null && systemValue !== undefined) {
                            tokenData[tokenName] = systemValue;
                            resolutionDetail.resolved = true;
                            resolutionDetail.value = systemValue;
                            resolutionDetail.source = 'system';
                        } else {
                            resolutionDetail.error = 'System token resolved to null/undefined';
                            unresolvedTokens.push(tokenName);
                        }
                    } else {
                        // Try to resolve as custom token
                        const customResolution =
                            await this.tokenManagementService.resolveTokenValue(tokenName, context);

                        if (
                            customResolution.success &&
                            customResolution.value !== null &&
                            customResolution.value !== undefined
                        ) {
                            tokenData[tokenName] = customResolution.value;
                            resolutionDetail.resolved = true;
                            resolutionDetail.value = customResolution.value;
                            resolutionDetail.source = 'custom';
                        } else {
                            resolutionDetail.error =
                                customResolution.error || 'Custom token resolution failed';
                            unresolvedTokens.push(tokenName);
                        }
                    }
                } catch (error) {
                    resolutionDetail.error = error.message;
                    unresolvedTokens.push(tokenName);
                }

                result.tokenResolutionDetails.push(resolutionDetail);
            }

            // STRICT VALIDATION: Fail if any tokens are unresolved
            if (unresolvedTokens.length > 0) {
                result.errors.push(
                    `Document generation failed: ${unresolvedTokens.length} unresolved token(s) found: ${unresolvedTokens.join(', ')}`
                );
                result.errors.push(
                    'All tokens must be resolved before document generation can proceed.'
                );

                // Add detailed error for each unresolved token
                unresolvedTokens.forEach((tokenName) => {
                    const detail = result.tokenResolutionDetails.find(
                        (t) => t.tokenName === tokenName
                    );
                    if (detail?.error) {
                        result.errors.push(`- {${tokenName}}: ${detail.error}`);
                    }
                });

                return result;
            }

            // Generate document using docxtemplater
            const zip = new PizZip(templateBuffer);
            const doc = new Docxtemplater(zip, {
                paragraphLoop: true,
                linebreaks: true,
                delimiters: {
                    start: '{',
                    end: '}'
                }
            });

            // Set the template data
            doc.setData(tokenData);

            try {
                // Render the document
                doc.render();
            } catch (error) {
                // Handle template rendering errors
                result.errors.push(`Template rendering failed: ${error.message}`);
                return result;
            }

            // Get the generated document buffer
            const generatedBuffer = doc.getZip().generate({
                type: 'nodebuffer',
                compression: 'DEFLATE'
            });

            result.success = true;
            result.documentBuffer = generatedBuffer;

            this.logger.debug(
                `Document generated with ${result.tokenResolutionDetails.length} tokens resolved`
            );

            return result;
        } catch (error) {
            result.errors.push(`Document generation error: ${error.message}`);
            this.logger.error(`Document generation error: ${error.message}`, error.stack);
            return result;
        }
    }

    /**
     * Resolve system token value (only TRUE system tokens)
     */
    private resolveSystemToken(tokenName: string, context: TokenResolutionContext): any {
        const { user, timestamp } = context;
        const now = timestamp || new Date();

        const tokenMap: Record<string, any> = {
            // System-generated date/time tokens
            currentDate: now.toLocaleDateString('en-GB'),
            currentDateTime: now.toLocaleString('en-GB'),
            currentYear: now.getFullYear(),
            currentMonth: now.toLocaleDateString('en-GB', { month: 'long' }),
            currentDay: now.getDate(),

            // Current user tokens
            'currentUser.name': user?.name,
            'currentUser.email': user?.email,
            'currentUser.id': user?.id,

            // System metadata tokens
            generationTimestamp: now.toLocaleString('en-GB'),
            generatedBy: user?.name || user?.id || 'System',
            documentId: `DOC-${Date.now()}`,
            templateVersion: '1.0'
        };

        return tokenMap[tokenName] || null;
    }

    /**
     * Get list of TRUE system tokens (only system-generated data)
     */
    private getSystemTokens(): string[] {
        return [
            // System-generated date/time tokens
            'currentDate',
            'currentDateTime',
            'currentYear',
            'currentMonth',
            'currentDay',

            // Current user tokens
            'currentUser.name',
            'currentUser.email',
            'currentUser.id',

            // System metadata tokens
            'generationTimestamp',
            'generatedBy',
            'documentId',
            'templateVersion'
        ];
    }

    /**
     * Pre-validate that all tokens in the template are either system tokens or exist as custom tokens
     */
    private async preValidateTokens(foundTokens: string[]): Promise<{
        isValid: boolean;
        errors: string[];
        unknownTokens: string[];
    }> {
        const result = {
            isValid: true,
            errors: [] as string[],
            unknownTokens: [] as string[]
        };

        const systemTokens = this.getSystemTokens();
        const unknownTokens: string[] = [];

        for (const tokenName of foundTokens) {
            if (!systemTokens.includes(tokenName)) {
                // Check if it exists as a custom token
                const customToken = await this.tokenManagementService.getTokenByName(tokenName);
                if (!customToken || !customToken.isActive) {
                    unknownTokens.push(tokenName);
                }
            }
        }

        if (unknownTokens.length > 0) {
            result.isValid = false;
            result.unknownTokens = unknownTokens;
            result.errors.push(
                `Template contains ${unknownTokens.length} unknown/inactive token(s): ${unknownTokens.join(', ')}`
            );
            result.errors.push(
                'All tokens must be either system tokens or active custom tokens created via the token management system.'
            );

            // Provide helpful guidance
            unknownTokens.forEach((tokenName) => {
                result.errors.push(
                    `- {${tokenName}}: Create this token via POST /tokens or use a system token`
                );
            });

            result.errors.push('Available system tokens: ' + systemTokens.join(', '));
        }

        return result;
    }

    /**
     * Extract tokens from template buffer
     */
    private extractTokensFromTemplate(templateBuffer: Buffer): string[] {
        try {
            const zip = new PizZip(templateBuffer);
            const doc = new Docxtemplater(zip, {
                paragraphLoop: true,
                linebreaks: true,
                delimiters: {
                    start: '{',
                    end: '}'
                }
            });

            // Get all tokens from the template
            const fullText = doc.getFullText();
            const foundTokens = fullText.match(/\{[^}]+\}/g) || [];

            // Clean token names (remove braces)
            const cleanTokens = foundTokens.map((token) =>
                token.replace(/^{/, '').replace(/}$/, '').trim()
            );

            return [...new Set(cleanTokens)]; // Remove duplicates
        } catch (error) {
            this.logger.error(`Failed to extract tokens from template: ${error.message}`);
            return [];
        }
    }

    /**
     * Get case data with related entities for document generation
     */
    private async getCaseData(caseId: string): Promise<Case | null> {
        try {
            const caseData = await this.caseRepository.findCaseById(caseId, true);

            if (!caseData) {
                this.logger.warn(`Case not found with ID: ${caseId}`);
                return null;
            }

            this.logger.debug(
                `Retrieved case data for ID: ${caseId}, caseNumber: ${caseData.caseNumber}`
            );
            return caseData;
        } catch (error) {
            this.logger.error(`Failed to retrieve case data for ID: ${caseId}`, error.stack);
            return null;
        }
    }

    /**
     * Test token resolution for debugging - shows which tokens would fail
     */
    async testTokenResolution(
        tokenNames: string[],
        context: TokenResolutionContext
    ): Promise<
        Array<{
            tokenName: string;
            resolved: boolean;
            value: any;
            source: 'system' | 'custom' | 'unknown';
            error?: string;
        }>
    > {
        const results: Array<{
            tokenName: string;
            resolved: boolean;
            value: any;
            source: 'system' | 'custom' | 'unknown';
            error?: string;
        }> = [];
        const systemTokens = this.getSystemTokens();

        for (const tokenName of tokenNames) {
            const result: {
                tokenName: string;
                resolved: boolean;
                value: any;
                source: 'system' | 'custom' | 'unknown';
                error?: string;
            } = {
                tokenName,
                resolved: false,
                value: null,
                source: 'unknown',
                error: undefined
            };

            try {
                if (systemTokens.includes(tokenName)) {
                    const value = this.resolveSystemToken(tokenName, context);
                    if (value !== null && value !== undefined) {
                        result.resolved = true;
                        result.value = value;
                        result.source = 'system';
                    } else {
                        result.error = 'System token resolved to null/undefined';
                    }
                } else {
                    const customResolution = await this.tokenManagementService.resolveTokenValue(
                        tokenName,
                        context
                    );

                    if (customResolution.success) {
                        result.resolved = true;
                        result.value = customResolution.value;
                        result.source = 'custom';
                    } else {
                        result.error = customResolution.error;
                    }
                }
            } catch (error) {
                result.error = error.message;
            }

            results.push(result);
        }

        return results;
    }

    /**
     * Download template file from S3
     */
    private async downloadTemplateFile(s3Key: string): Promise<Buffer> {
        try {
            this.logger.debug(`Downloading template from S3: ${s3Key}`);
            const templateBuffer = await this.s3StorageService.downloadFileAsBuffer(s3Key);

            if (!templateBuffer || templateBuffer.length === 0) {
                throw new Error('Downloaded template file is empty');
            }

            this.logger.debug(
                `Successfully downloaded template: ${s3Key} (${templateBuffer.length} bytes)`
            );
            return templateBuffer;
        } catch (error) {
            this.logger.error(`Failed to download template from S3: ${error.message}`, error.stack);
            throw new Error(`Failed to download template: ${error.message}`);
        }
    }

    /**
     * Get tenant ID from context
     */
    private getTenantId(): string | null {
        try {
            if (this.tenantContextService) {
                return this.tenantContextService.getTenantId();
            }
            return null;
        } catch {
            this.logger.warn('Could not get tenant ID from context');
            return null;
        }
    }
}
