import { Injectable } from '@nestjs/common';
import { DocumentFolderRepository } from '../../repositories/document-folder.repository';
import { DocumentFolder } from '@app/common/typeorm/entities';

@Injectable()
export class DocumentFolderService {
    constructor(public readonly folderRepo: DocumentFolderRepository) {}

    /**
     * Create a new folder
     */
    async createFolder(data: {
        name: string;
        description?: string;
        caseId: string;
        parentFolderId?: string;
        createdBy: string;
    }): Promise<DocumentFolder> {
        // Generate path
        let path = `/${data.name}`;
        if (data.parentFolderId) {
            const parentFolder = await this.getFolderById(data.parentFolderId);
            if (parentFolder && parentFolder.path) {
                path = `${parentFolder.path}/${data.name}`;
            }
        }

        const folder = await this.folderRepo.create({
            name: data.name,
            description: data.description,
            caseId: data.caseId,
            parentFolderId: data.parentFolderId,
            path,
            createdBy: data.createdBy,
            createdAt: new Date(),
            updatedAt: new Date()
        });

        return this.folderRepo.save(folder);
    }

    /**
     * Get all folders for a case
     */
    async findAllByCase(caseId: string): Promise<DocumentFolder[]> {
        return this.folderRepo.findByCaseId(caseId);
    }

    /**
     * Get root folders for a case
     */
    async findRootFolders(caseId: string): Promise<DocumentFolder[]> {
        return this.folderRepo.findRootFoldersByCaseId(caseId);
    }

    /**
     * Get child folders
     */
    async findChildFolders(parentFolderId: string): Promise<DocumentFolder[]> {
        return this.folderRepo.findChildFolders(parentFolderId);
    }

    /**
     * Get a folder by ID
     */
    async getFolderById(id: string): Promise<DocumentFolder | null> {
        return this.folderRepo.findOneBy({ id });
    }

    /**
     * Update a folder
     */
    async updateFolder(
        id: string,
        data: {
            name?: string;
            description?: string;
            parentFolderId?: string;
            updatedBy: string;
        }
    ): Promise<DocumentFolder | null> {
        const folder = await this.getFolderById(id);
        if (!folder) {
            return null;
        }

        let updatePath = false;
        if (data.name && data.name !== folder.name) {
            folder.name = data.name;
            updatePath = true;
        }

        if (data.description !== undefined) {
            folder.description = data.description;
        }

        if (data.parentFolderId !== undefined) {
            folder.parentFolderId = data.parentFolderId;
            updatePath = true;
        }

        // Update path if name or parent changed
        if (updatePath) {
            if (folder.parentFolderId) {
                const parentFolder = await this.getFolderById(folder.parentFolderId);
                if (parentFolder && parentFolder.path) {
                    folder.path = `${parentFolder.path}/${folder.name}`;
                } else {
                    folder.path = `/${folder.name}`;
                }
            } else {
                folder.path = `/${folder.name}`;
            }

            // TODO: Update paths of all child folders recursively
        }

        folder.updatedAt = new Date();
        return this.folderRepo.save(folder);
    }

    /**
     * Delete a folder
     */
    async deleteFolder(id: string): Promise<boolean> {
        const folder = await this.getFolderById(id);
        if (!folder) {
            return false;
        }

        // Check if folder has child folders
        const childFolders = await this.findChildFolders(id);
        if (childFolders.length > 0) {
            throw new Error('Cannot delete folder that contains subfolders');
        }

        // Check if folder has documents
        const documentCount = await this.folderRepo.countByFolderId(id);
        if (documentCount > 0) {
            throw new Error('Cannot delete folder that contains documents');
        }

        return this.folderRepo.removeById(id);
    }

    /**
     * Search folders within a case
     */
    async searchInCase(
        caseId: string,
        searchTerm: string,
        limit: number = 10
    ): Promise<DocumentFolder[]> {
        return this.folderRepo.searchInCase(caseId, searchTerm, limit);
    }

    /**
     * Get folder hierarchy for a case
     */
    async getFolderHierarchy(caseId: string): Promise<DocumentFolder[]> {
        // Get all folders for the case
        const allFolders = await this.findAllByCase(caseId);

        // Build hierarchy (root folders with their children)
        const folderMap = new Map<string, DocumentFolder & { children: DocumentFolder[] }>();
        const rootFolders: (DocumentFolder & { children: DocumentFolder[] })[] = [];

        // Initialize all folders with children array
        allFolders.forEach((folder) => {
            folderMap.set(folder.id, { ...folder, children: [] });
        });

        // Build the hierarchy
        allFolders.forEach((folder) => {
            const folderWithChildren = folderMap.get(folder.id)!;

            if (folder.parentFolderId) {
                const parent = folderMap.get(folder.parentFolderId);
                if (parent) {
                    parent.children.push(folderWithChildren);
                }
            } else {
                rootFolders.push(folderWithChildren);
            }
        });

        return rootFolders as DocumentFolder[];
    }
}
