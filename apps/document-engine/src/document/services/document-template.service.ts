import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { DocumentTemplateRepository } from '@app/common/repositories/document-template.repository';
import { TemplatePartyAssociationRepository } from '@app/common/repositories/template-party-association.repository';
import { S3StorageService } from './s3-storage.service';
import { TokenExtractionService } from './token-extraction.service';
import { TemplateValidationService } from './template-validation.service';
import {
    DocumentTemplate,
    DocumentTemplateType,
    DocumentTemplateCategory,
    DocumentTemplateStatus,
    DocumentGenerationTrigger,
    CaseType,
    PartyType
} from '@app/common/typeorm/entities';

@Injectable()
export class DocumentTemplateService {
    private readonly logger = new Logger(DocumentTemplateService.name);

    constructor(
        private readonly documentTemplateRepo: DocumentTemplateRepository,
        private readonly templatePartyAssociationRepo: TemplatePartyAssociationRepository,
        private readonly s3StorageService: S3StorageService,
        private readonly tokenExtractionService: TokenExtractionService,
        private readonly templateValidationService: TemplateValidationService
    ) {}

    /**
     * Create a new document template
     */
    async createTemplate(data: {
        name: string;
        description?: string;
        templateType: DocumentTemplateType;
        category?: DocumentTemplateCategory;
        fileBuffer: Buffer;
        filename: string;
        allowedCaseTypes?: CaseType[];
        requiredTokens?: string[];
        optionalTokens?: string[];
        generationTriggers?: DocumentGenerationTrigger[];
        autoAttachToCase?: boolean;
        autoEmailToClient?: boolean;
        emailTemplateType?: string;
        outputFileNameTemplate?: string;
        partyTypes?: PartyType[];
        createdBy: string;
    }): Promise<DocumentTemplate> {
        // Validate input
        if (!data.name?.trim()) {
            throw new Error('Template name is required');
        }
        if (!data.fileBuffer || data.fileBuffer.length === 0) {
            throw new Error('Template file is required');
        }
        if (!data.filename?.trim()) {
            throw new Error('Filename is required');
        }

        // Check if template with same name already exists
        const existingTemplate = await this.documentTemplateRepo.findByName(data.name.trim());
        if (existingTemplate) {
            throw new Error(`Template with name '${data.name}' already exists`);
        }

        try {
            // FIRST: Validate template tokens before processing
            const tokenValidation = await this.templateValidationService.validateTemplateTokens(
                data.fileBuffer,
                data.category,
                data.allowedCaseTypes
            );

            // Reject template if it contains unknown tokens
            if (!tokenValidation.isValid) {
                throw new BadRequestException({
                    message: 'Template contains invalid tokens and cannot be uploaded',
                    details: {
                        foundTokens: tokenValidation.foundTokens,
                        validTokens: tokenValidation.validTokens,
                        invalidTokens: tokenValidation.invalidTokens,
                        errors: tokenValidation.errors,
                        warnings: tokenValidation.warnings
                    }
                });
            }

            // Log validation results
            this.logger.debug(
                `Template validation successful: ${tokenValidation.foundTokens.length} tokens found`
            );
            if (tokenValidation.warnings.length > 0) {
                this.logger.warn(`Template warnings: ${tokenValidation.warnings.join('; ')}`);
            }

            // Extract tokens from template using validation service results
            // Use the tokens found by validateTemplateTokens since it already parsed the template
            const detectedTokens = tokenValidation.foundTokens || [];

            // Categorize tokens into required and optional based on template category
            let requiredTokens: string[] = [];
            let optionalTokens: string[] = [];

            if (data.category) {
                // Get category-specific required tokens
                requiredTokens = this.templateValidationService.getRequiredSystemTokensForCategory(
                    data.category
                );

                // All detected tokens that are not required are considered optional
                optionalTokens = detectedTokens.filter((token) => !requiredTokens.includes(token));

                // Only include required tokens that are actually in the template
                requiredTokens = requiredTokens.filter((token) => detectedTokens.includes(token));
            } else {
                // If no category, treat all detected tokens as optional
                optionalTokens = [...detectedTokens];
            }

            this.logger.log(
                `Token analysis for template '${data.name}': ` +
                    `${detectedTokens.length} detected, ${requiredTokens.length} required, ${optionalTokens.length} optional`
            );

            // Upload template file to S3 using template-specific method
            const uploadResult = await this.s3StorageService.uploadTemplateFile(
                data.templateType,
                data.filename,
                data.fileBuffer,
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            );

            // Create template record with extracted tokens
            const template = await this.documentTemplateRepo.create({
                name: data.name.trim(),
                description: data.description?.trim(),
                templateType: data.templateType,
                category: data.category,
                fileName: data.filename,
                filePath: uploadResult.s3Key,
                s3Key: uploadResult.s3Key,
                s3Bucket: uploadResult.s3Bucket,
                mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                sizeInBytes: Buffer.byteLength(data.fileBuffer).toString(),
                checksum: uploadResult.checksum,
                allowedCaseTypes: data.allowedCaseTypes || [],
                requiredTokens: requiredTokens,
                optionalTokens: optionalTokens,
                detectedTokens: detectedTokens,
                generationTriggers: data.generationTriggers || [DocumentGenerationTrigger.MANUAL],
                status: DocumentTemplateStatus.DRAFT,
                isActive: true,
                autoAttachToCase: data.autoAttachToCase || false,
                autoEmailToClient: data.autoEmailToClient || false,
                emailTemplateType: data.emailTemplateType,
                outputFileNameTemplate: data.outputFileNameTemplate,
                createdBy: data.createdBy,
                createdAt: new Date(),
                updatedAt: new Date(),
                lastModifiedBy: data.createdBy,
                usageCount: 0
            });

            const savedTemplate = await this.documentTemplateRepo.save(template);

            // Ensure template was saved with an ID
            if (!savedTemplate.id) {
                throw new Error('Template was not saved properly - no ID generated');
            }

            this.logger.debug(`Template saved with ID: ${savedTemplate.id}`);

            // Create party associations if provided
            let supportedParties: string[] = [];
            if (data.partyTypes && data.partyTypes.length > 0) {
                this.logger.debug(
                    `Creating party associations for template ${savedTemplate.id} with parties: ${data.partyTypes.join(', ')}`
                );

                // Use the helper method from the repository
                const savedAssociations = await this.templatePartyAssociationRepo.createForTemplate(
                    savedTemplate.id,
                    data.partyTypes
                );
                supportedParties = savedAssociations.map((pa) => pa.partyType);

                this.logger.debug(`Saved ${savedAssociations.length} party associations`);
            }

            // Add supportedParties field to response
            (savedTemplate as any).supportedParties = supportedParties;

            return savedTemplate;
        } catch (error) {
            this.logger.error(`Error creating template: ${error.message}`, error.stack);
            throw new Error(`Failed to create template: ${error.message}`);
        }
    }

    /**
     * Get template by ID
     */
    async getTemplateById(id: string): Promise<DocumentTemplate | null> {
        if (!id?.trim()) {
            throw new Error('Template ID is required');
        }
        return this.documentTemplateRepo.findByIdWithDetails(id.trim());
    }

    /**
     * Search templates
     */
    async searchTemplates(
        searchTerm: string,
        options?: {
            includeInactive?: boolean;
            includeArchived?: boolean;
            limit?: number;
        }
    ): Promise<DocumentTemplate[]> {
        if (!searchTerm?.trim() || searchTerm.trim().length < 2) {
            return [];
        }
        return this.documentTemplateRepo.quickSearch(searchTerm.trim(), options);
    }

    /**
     * Get templates with filters
     */
    async getTemplatesWithFilters(filter: {
        page?: number;
        limit?: number;
        sortBy?: string;
        order?: 'ASC' | 'DESC';
        search?: string;
        templateType?: DocumentTemplateType;
        category?: DocumentTemplateCategory;
        status?: DocumentTemplateStatus;
        isActive?: boolean;
        createdBy?: string;
    }): Promise<{ templates: DocumentTemplate[]; total: number }> {
        const [templates, total] = await this.documentTemplateRepo.findWithFilters(filter);
        return { templates, total };
    }

    /**
     * Update template
     */
    async updateTemplate(
        id: string,
        data: {
            name?: string;
            description?: string;
            category?: DocumentTemplateCategory;
            requiredTokens?: string[];
            optionalTokens?: string[];
            generationTriggers?: DocumentGenerationTrigger[];
            autoAttachToCase?: boolean;
            autoEmailToClient?: boolean;
            emailTemplateType?: string;
            outputFileNameTemplate?: string;
            updatedBy: string;
        }
    ): Promise<DocumentTemplate | null> {
        const template = await this.getTemplateById(id);
        if (!template) {
            return null;
        }

        // Update fields if provided
        if (data.name !== undefined) template.name = data.name;
        if (data.description !== undefined) template.description = data.description;
        if (data.category !== undefined) template.category = data.category;
        if (data.requiredTokens !== undefined) template.requiredTokens = data.requiredTokens;
        if (data.optionalTokens !== undefined) template.optionalTokens = data.optionalTokens;
        if (data.generationTriggers !== undefined)
            template.generationTriggers = data.generationTriggers;
        if (data.autoAttachToCase !== undefined) template.autoAttachToCase = data.autoAttachToCase;
        if (data.autoEmailToClient !== undefined)
            template.autoEmailToClient = data.autoEmailToClient;
        if (data.emailTemplateType !== undefined)
            template.emailTemplateType = data.emailTemplateType;
        if (data.outputFileNameTemplate !== undefined)
            template.outputFileNameTemplate = data.outputFileNameTemplate;

        template.updatedAt = new Date();
        template.lastModifiedBy = data.updatedBy;

        return this.documentTemplateRepo.save(template);
    }

    /**
     * Activate template
     */
    async activateTemplate(id: string, activatedBy: string): Promise<DocumentTemplate | null> {
        const template = await this.getTemplateById(id);
        if (!template) {
            return null;
        }

        template.status = DocumentTemplateStatus.ACTIVE;
        template.isActive = true;
        template.updatedAt = new Date();
        template.lastModifiedBy = activatedBy;

        return this.documentTemplateRepo.save(template);
    }

    /**
     * Deactivate template
     */
    async deactivateTemplate(id: string, deactivatedBy: string): Promise<DocumentTemplate | null> {
        const template = await this.getTemplateById(id);
        if (!template) {
            return null;
        }

        template.status = DocumentTemplateStatus.INACTIVE;
        template.isActive = false;
        template.updatedAt = new Date();
        template.lastModifiedBy = deactivatedBy;

        return this.documentTemplateRepo.save(template);
    }

    /**
     * Archive template
     */
    async archiveTemplate(id: string, archivedBy: string): Promise<boolean> {
        return this.documentTemplateRepo.archiveTemplate(id, archivedBy);
    }

    /**
     * Restore archived template
     */
    async restoreTemplate(id: string, restoredBy: string): Promise<boolean> {
        return this.documentTemplateRepo.restoreTemplate(id, restoredBy);
    }

    /**
     * Delete template
     */
    async deleteTemplate(id: string): Promise<boolean> {
        const template = await this.getTemplateById(id);
        if (!template) {
            return false;
        }

        try {
            // Delete from S3 first
            await this.s3StorageService.deleteFile(template.s3Key);

            // Delete from database
            await this.documentTemplateRepo.removeById(id);

            return true;
        } catch (error) {
            this.logger.error(`Error deleting template ${id}: ${error.message}`, error.stack);
            throw new Error(`Failed to delete template: ${error.message}`);
        }
    }

    /**
     * Get template download URL
     */
    async getTemplateDownloadUrl(id: string): Promise<{ url: string; filename: string }> {
        const template = await this.getTemplateById(id);
        if (!template) {
            throw new NotFoundException(`Template with ID ${id} not found`);
        }

        const url = await this.s3StorageService.getDownloadUrl(template.s3Key, template.fileName);

        return {
            url,
            filename: template.fileName
        };
    }

    /**
     * Replace template file
     */
    async replaceTemplateFile(
        id: string,
        data: {
            fileBuffer: Buffer;
            filename: string;
            updatedBy: string;
        }
    ): Promise<DocumentTemplate> {
        const template = await this.getTemplateById(id);
        if (!template) {
            throw new NotFoundException(`Template with ID ${id} not found`);
        }

        // Delete old file from S3
        await this.s3StorageService.deleteFile(template.s3Key);

        // Upload new file using template-specific method
        const uploadResult = await this.s3StorageService.uploadTemplateFile(
            template.templateType,
            data.filename,
            data.fileBuffer,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        );

        // Update template metadata
        template.fileName = data.filename;
        template.filePath = uploadResult.s3Key;
        template.s3Key = uploadResult.s3Key;
        template.s3Bucket = uploadResult.s3Bucket;
        template.sizeInBytes = Buffer.byteLength(data.fileBuffer).toString();
        template.checksum = uploadResult.checksum;
        template.updatedAt = new Date();
        template.lastModifiedBy = data.updatedBy;

        return this.documentTemplateRepo.save(template);
    }
}
