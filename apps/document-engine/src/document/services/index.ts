import { DocumentFolderService } from './document-folder.service';
import { S3StorageService } from './s3-storage.service';
import { DocumentService } from './document.service';
import { DocumentAuditService } from './document-audit.service';
import { DocumentTemplateService } from './document-template.service';
import { TemplateValidationService } from './template-validation.service';
import { TokenExtractionService } from './token-extraction.service';
import { DocumentGenerationService } from './document-generation.service';
import { TokenManagementService } from './token-management.service';
import { DocumentNotificationService } from './document-notification.service';
import { TokenUtils } from '../../utils/token.utils';
import { TemplateAnalysisUtils } from '../../utils/template-analysis.utils';

export {
    DocumentFolderService,
    S3StorageService,
    DocumentService,
    DocumentAuditService,
    DocumentTemplateService,
    TemplateValidationService,
    TokenExtractionService,
    DocumentGenerationService,
    TokenManagementService,
    DocumentNotificationService,
    TokenUtils,
    TemplateAnalysisUtils
};

export const documentServices = [
    DocumentFolderService,
    S3StorageService,
    DocumentService,
    DocumentAuditService,
    DocumentTemplateService,
    TemplateValidationService,
    TokenExtractionService,
    DocumentGenerationService,
    TokenManagementService,
    DocumentNotificationService,
    TokenUtils,
    TemplateAnalysisUtils
];
