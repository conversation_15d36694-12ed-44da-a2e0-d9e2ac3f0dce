import { Injectable, Logger } from '@nestjs/common';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { CommunicationJobData } from '@app/common/communication/interfaces/communication-job.interface';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import { QUEUE_PRIORITIES } from '@app/common/constants/queue.constants';
import { Document } from '@app/common/typeorm/entities';
import { DocumentTemplate } from '@app/common/typeorm/entities/tenant/document-template.entity';
import { DocumentNotificationType } from '@app/common/enums/document-notification-types.enum';
import { DocumentEventType } from '@app/common/enums/document-event-types.enum';
import {
    DocumentActionType,
    DocumentOperationType
} from '@app/common/enums/document-action-types.enum';

export interface DocumentNotificationContext {
    tenantId: string;
    tenantName: string;
    userId: string;
    userEmail: string;
    userName: string;
    document?: Document;
    template?: DocumentTemplate;
    caseId?: string;
    caseNumber?: string;
    additionalRecipients?: string[];
}

@Injectable()
export class DocumentNotificationService {
    private readonly logger = new Logger(DocumentNotificationService.name);

    constructor(private readonly messageProducer: MessageProducerService) {}

    /**
     * Send notification when a document is generated
     */
    async notifyDocumentGenerated(context: DocumentNotificationContext): Promise<void> {
        try {
            const jobData: CommunicationJobData = {
                tenantId: context.tenantId,
                userId: context.userId,
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: [context.userEmail, ...(context.additionalRecipients || [])],
                    notification: [context.userId]
                },
                variables: {
                    type: DocumentNotificationType.DOCUMENT_GENERATED,
                    tenantName: context.tenantName,
                    recipientName: context.userName,
                    documentName: context.document?.name || 'Document',
                    templateName: context.template?.name || 'Template',
                    caseNumber: context.caseNumber,
                    caseId: context.caseId,
                    generatedAt: new Date().toISOString(),
                    message: `Your document "${context.document?.name}" has been successfully generated.`
                },
                priority: QUEUE_PRIORITIES.NORMAL,
                caseId: context.caseId,
                metadata: {
                    documentId: context.document?.id,
                    templateId: context.template?.id,
                    eventType: DocumentEventType.DOCUMENT_GENERATED
                }
            };

            const result = await this.messageProducer.enqueueMessage(jobData);
            this.logger.log(
                `Document generation notification queued: ${result.jobId} for user ${context.userId}`
            );
        } catch (error) {
            this.logger.error('Failed to send document generation notification', error);
        }
    }

    /**
     * Send notification when a document is uploaded
     */
    async notifyDocumentUploaded(context: DocumentNotificationContext): Promise<void> {
        try {
            const jobData: CommunicationJobData = {
                tenantId: context.tenantId,
                userId: context.userId,
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: [context.userEmail, ...(context.additionalRecipients || [])],
                    notification: [context.userId]
                },
                variables: {
                    type: DocumentNotificationType.DOCUMENT_UPLOADED,
                    tenantName: context.tenantName,
                    recipientName: context.userName,
                    documentName: context.document?.name || 'Document',
                    caseNumber: context.caseNumber,
                    caseId: context.caseId,
                    uploadedAt: new Date().toISOString(),
                    message: `A new document "${context.document?.name}" has been uploaded to case ${context.caseNumber}.`
                },
                priority: QUEUE_PRIORITIES.NORMAL,
                caseId: context.caseId,
                metadata: {
                    documentId: context.document?.id,
                    eventType: DocumentEventType.DOCUMENT_UPLOADED
                }
            };

            const result = await this.messageProducer.enqueueMessage(jobData);
            this.logger.log(
                `Document upload notification queued: ${result.jobId} for user ${context.userId}`
            );
        } catch (error) {
            this.logger.error('Failed to send document upload notification', error);
        }
    }

    /**
     * Send notification when a document is shared
     */
    async notifyDocumentShared(
        context: DocumentNotificationContext & {
            sharedWithEmails: string[];
            sharedWithUserIds: string[];
            shareMessage?: string;
        }
    ): Promise<void> {
        try {
            const jobData: CommunicationJobData = {
                tenantId: context.tenantId,
                userId: context.userId,
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: context.sharedWithEmails,
                    notification: context.sharedWithUserIds
                },
                variables: {
                    type: DocumentNotificationType.DOCUMENT_SHARED,
                    tenantName: context.tenantName,
                    recipientName: 'Team Member',
                    documentName: context.document?.name || 'Document',
                    sharedBy: context.userName,
                    caseNumber: context.caseNumber,
                    caseId: context.caseId,
                    sharedAt: new Date().toISOString(),
                    shareMessage: context.shareMessage,
                    message:
                        context.shareMessage ||
                        `${context.userName} has shared the document "${context.document?.name}" with you.`
                },
                priority: QUEUE_PRIORITIES.NORMAL,
                caseId: context.caseId,
                metadata: {
                    documentId: context.document?.id,
                    sharedBy: context.userId,
                    eventType: DocumentEventType.DOCUMENT_SHARED
                }
            };

            const result = await this.messageProducer.enqueueMessage(jobData);
            this.logger.log(
                `Document share notification queued: ${result.jobId} for ${context.sharedWithEmails.length} recipients`
            );
        } catch (error) {
            this.logger.error('Failed to send document share notification', error);
        }
    }

    /**
     * Send notification when a document is deleted
     */
    async notifyDocumentDeleted(context: DocumentNotificationContext): Promise<void> {
        try {
            const jobData: CommunicationJobData = {
                tenantId: context.tenantId,
                userId: context.userId,
                channels: [COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    notification: [context.userId]
                },
                variables: {
                    type: DocumentNotificationType.DOCUMENT_DELETED,
                    tenantName: context.tenantName,
                    recipientName: context.userName,
                    documentName: context.document?.name || 'Document',
                    caseNumber: context.caseNumber,
                    caseId: context.caseId,
                    deletedAt: new Date().toISOString(),
                    message: `The document "${context.document?.name}" has been deleted from case ${context.caseNumber}.`
                },
                priority: QUEUE_PRIORITIES.LOW,
                caseId: context.caseId,
                metadata: {
                    documentId: context.document?.id,
                    eventType: DocumentEventType.DOCUMENT_DELETED
                }
            };

            const result = await this.messageProducer.enqueueMessage(jobData);
            this.logger.log(
                `Document deletion notification queued: ${result.jobId} for user ${context.userId}`
            );
        } catch (error) {
            this.logger.error('Failed to send document deletion notification', error);
        }
    }

    /**
     * Send notification when document generation fails
     */
    async notifyDocumentGenerationFailed(
        context: DocumentNotificationContext & {
            errorMessage: string;
            templateName: string;
        }
    ): Promise<void> {
        try {
            const jobData: CommunicationJobData = {
                tenantId: context.tenantId,
                userId: context.userId,
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: [context.userEmail],
                    notification: [context.userId]
                },
                variables: {
                    type: DocumentNotificationType.DOCUMENT_GENERATION_FAILED,
                    tenantName: context.tenantName,
                    recipientName: context.userName,
                    templateName: context.templateName,
                    caseNumber: context.caseNumber,
                    caseId: context.caseId,
                    failedAt: new Date().toISOString(),
                    errorMessage: context.errorMessage,
                    message: `Failed to generate document from template "${context.templateName}". Error: ${context.errorMessage}`
                },
                priority: QUEUE_PRIORITIES.HIGH,
                caseId: context.caseId,
                metadata: {
                    templateId: context.template?.id,
                    eventType: DocumentEventType.DOCUMENT_GENERATION_FAILED
                }
            };

            const result = await this.messageProducer.enqueueMessage(jobData);
            this.logger.log(
                `Document generation failure notification queued: ${result.jobId} for user ${context.userId}`
            );
        } catch (error) {
            this.logger.error('Failed to send document generation failure notification', error);
        }
    }

    /**
     * Send batch notification for multiple document operations
     */
    async notifyBatchDocumentOperation(
        context: DocumentNotificationContext & {
            operation: DocumentOperationType;
            documentNames: string[];
            documentCount: number;
        }
    ): Promise<void> {
        try {
            const operationMessages = {
                [DocumentOperationType.UPLOADED]: `${context.documentCount} documents have been uploaded to case ${context.caseNumber}`,
                [DocumentOperationType.DELETED]: `${context.documentCount} documents have been deleted from case ${context.caseNumber}`,
                [DocumentOperationType.SHARED]: `${context.documentCount} documents have been shared with you from case ${context.caseNumber}`
            };

            const typeMapping = {
                [DocumentOperationType.UPLOADED]:
                    DocumentNotificationType.BATCH_DOCUMENTS_UPLOADED as string,
                [DocumentOperationType.DELETED]:
                    DocumentNotificationType.BATCH_DOCUMENTS_DELETED as string,
                [DocumentOperationType.SHARED]:
                    DocumentNotificationType.BATCH_DOCUMENTS_SHARED as string
            };

            const jobData: CommunicationJobData = {
                tenantId: context.tenantId,
                userId: context.userId,
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: [context.userEmail, ...(context.additionalRecipients || [])],
                    notification: [context.userId]
                },
                variables: {
                    type: typeMapping[context.operation],
                    tenantName: context.tenantName,
                    recipientName: context.userName,
                    documentNames: context.documentNames,
                    documentCount: context.documentCount,
                    operation: context.operation,
                    caseNumber: context.caseNumber,
                    caseId: context.caseId,
                    operatedAt: new Date().toISOString(),
                    message: operationMessages[context.operation]
                } as any,
                priority: QUEUE_PRIORITIES.NORMAL,
                caseId: context.caseId,
                metadata: {
                    operation: context.operation,
                    documentCount: context.documentCount,
                    eventType: this.getBatchEventType(context.operation)
                }
            };

            const result = await this.messageProducer.enqueueMessage(jobData);
            this.logger.log(
                `Batch document ${context.operation} notification queued: ${result.jobId} for user ${context.userId}`
            );
        } catch (error) {
            this.logger.error(
                `Failed to send batch document ${context.operation} notification`,
                error
            );
        }
    }

    /**
     * Send reminder notification for pending document actions
     */
    async notifyDocumentActionReminder(
        context: DocumentNotificationContext & {
            actionType: DocumentActionType;
            dueDate?: Date;
            documentUrl?: string;
        }
    ): Promise<void> {
        try {
            const actionMessages = {
                [DocumentActionType.REVIEW]: 'requires your review',
                [DocumentActionType.APPROVAL]: 'is pending your approval',
                [DocumentActionType.SIGNATURE]: 'requires your signature'
            };

            const actionTypeMapping = {
                [DocumentActionType.REVIEW]:
                    DocumentNotificationType.DOCUMENT_REVIEW_REMINDER as string,
                [DocumentActionType.APPROVAL]:
                    DocumentNotificationType.DOCUMENT_APPROVAL_REMINDER as string,
                [DocumentActionType.SIGNATURE]:
                    DocumentNotificationType.DOCUMENT_SIGNATURE_REMINDER as string
            };

            const jobData: CommunicationJobData = {
                tenantId: context.tenantId,
                userId: context.userId,
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: [context.userEmail],
                    notification: [context.userId]
                },
                variables: {
                    type: actionTypeMapping[context.actionType],
                    tenantName: context.tenantName,
                    recipientName: context.userName,
                    documentName: context.document?.name || 'Document',
                    actionType: context.actionType,
                    caseNumber: context.caseNumber,
                    caseId: context.caseId,
                    dueDate: context.dueDate?.toISOString(),
                    documentUrl: context.documentUrl,
                    message: `Document "${context.document?.name}" ${actionMessages[context.actionType]}.`
                } as any,
                priority: QUEUE_PRIORITIES.HIGH,
                delay: context.dueDate ? context.dueDate.getTime() - Date.now() - 86400000 : 0, // 24 hours before due date
                caseId: context.caseId,
                metadata: {
                    documentId: context.document?.id,
                    actionType: context.actionType,
                    eventType: this.getActionReminderEventType(context.actionType)
                }
            };

            const result = await this.messageProducer.enqueueMessage(jobData);
            this.logger.log(
                `Document ${context.actionType} reminder notification queued: ${result.jobId} for user ${context.userId}`
            );
        } catch (error) {
            this.logger.error(
                `Failed to send document ${context.actionType} reminder notification`,
                error
            );
        }
    }

    /**
     * Get the appropriate batch event type based on operation
     */
    private getBatchEventType(operation: DocumentOperationType): DocumentEventType {
        const eventTypeMap = {
            [DocumentOperationType.UPLOADED]: DocumentEventType.DOCUMENT_BATCH_UPLOADED,
            [DocumentOperationType.DELETED]: DocumentEventType.DOCUMENT_BATCH_DELETED,
            [DocumentOperationType.SHARED]: DocumentEventType.DOCUMENT_BATCH_SHARED
        };
        return eventTypeMap[operation];
    }

    /**
     * Get the appropriate action reminder event type based on action type
     */
    private getActionReminderEventType(actionType: DocumentActionType): DocumentEventType {
        const eventTypeMap = {
            [DocumentActionType.REVIEW]: DocumentEventType.DOCUMENT_REVIEW_REMINDER,
            [DocumentActionType.APPROVAL]: DocumentEventType.DOCUMENT_APPROVAL_REMINDER,
            [DocumentActionType.SIGNATURE]: DocumentEventType.DOCUMENT_SIGNATURE_REMINDER
        };
        return eventTypeMap[actionType];
    }

    /**
     * Send document to client via email
     */
    async notifyDocumentSentToClient(
        context: DocumentNotificationContext & {
            clientEmail: string;
            clientName: string;
            documentUrl: string;
            clientMessage?: string;
        }
    ): Promise<void> {
        try {
            const jobData: CommunicationJobData = {
                tenantId: context.tenantId,
                userId: context.userId,
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: [context.clientEmail]
                },
                variables: {
                    type: DocumentNotificationType.DOCUMENT_SHARED,
                    tenantName: context.tenantName,
                    recipientName: context.clientName,
                    documentName: context.document?.name || 'Document',
                    sharedBy: context.userName,
                    caseNumber: context.caseNumber,
                    caseId: context.caseId,
                    sharedAt: new Date().toISOString(),
                    shareMessage:
                        context.clientMessage ||
                        `Please find your document "${context.document?.name}" attached.`,
                    documentUrl: context.documentUrl,
                    message:
                        context.clientMessage ||
                        `Hello ${context.clientName},\n\nPlease find your document "${context.document?.name}" from ${context.tenantName}.\n\nDocument: ${context.documentUrl}\n\nBest regards,\n${context.tenantName}`
                },
                priority: QUEUE_PRIORITIES.NORMAL,
                caseId: context.caseId,
                metadata: {
                    documentId: context.document?.id,
                    clientEmail: context.clientEmail,
                    eventType: DocumentEventType.DOCUMENT_SHARED
                }
            };

            const result = await this.messageProducer.enqueueMessage(jobData);
            this.logger.log(
                `Document sent to client notification queued: ${result.jobId} for client ${context.clientEmail}`
            );
        } catch (error) {
            this.logger.error('Failed to send document to client notification', error);
        }
    }
}
