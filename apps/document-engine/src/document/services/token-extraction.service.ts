import { Injectable, Logger } from '@nestjs/common';
import * as J<PERSON><PERSON><PERSON> from 'jszip';
import { DocumentTemplateCategory, CaseType } from '@app/common/typeorm/entities';
import { TemplateValidationService } from './template-validation.service';

export interface ExtractedToken {
    token: string;
    position: number;
    line: number;
    context: string;
}

export interface TokenExtractionResult {
    extractedTokens: ExtractedToken[];
    uniqueTokens: string[];
    totalTokenCount: number;
    validTokens: string[];
    invalidTokens: string[];
    missingRequiredTokens: string[];
    validationErrors: string[];
}

@Injectable()
export class TokenExtractionService {
    private readonly logger = new Logger(TokenExtractionService.name);
    private readonly TOKEN_PATTERN = /\{\{([^}]+)\}\}/g;

    constructor(private readonly templateValidationService: TemplateValidationService) {}

    /**
     * Extract tokens from DOCX file buffer
     */
    async extractTokensFromDocx(
        fileBuffer: Buffer,
        category: DocumentTemplateCategory,
        caseType?: CaseType
    ): Promise<TokenExtractionResult> {
        try {
            this.logger.debug(`Extracting tokens from DOCX file for category: ${category}`);

            const zip = new JSZip();
            const zipContent = await zip.loadAsync(fileBuffer);

            // Extract text content from document.xml
            const documentXml = zipContent.files['word/document.xml'];
            if (!documentXml) {
                throw new Error('Invalid DOCX file: document.xml not found');
            }

            const xmlContent = await documentXml.async('string');
            const textContent = this.extractTextFromXml(xmlContent);

            // Extract tokens from text content
            const extractedTokens = this.extractTokensFromText(textContent);

            // Validate tokens against available tokens
            const validationResult = await this.validateExtractedTokens(
                extractedTokens,
                category,
                caseType
            );

            this.logger.debug(`Extracted ${extractedTokens.length} tokens from DOCX`);

            return {
                extractedTokens,
                uniqueTokens: [...new Set(extractedTokens.map((t) => t.token))],
                totalTokenCount: extractedTokens.length,
                ...validationResult
            };
        } catch (error) {
            this.logger.error(`Error extracting tokens from DOCX: ${error.message}`);
            throw new Error(`Failed to extract tokens from DOCX file: ${error.message}`);
        }
    }

    /**
     * Extract text content from Word XML
     */
    private extractTextFromXml(xmlContent: string): string {
        // Remove XML tags but preserve text content
        // This is a simplified approach - a more robust solution would use proper XML parsing
        const textContent = xmlContent
            .replace(/<w:p[^>]*>/g, '\n') // Paragraphs as new lines
            .replace(/<w:br[^>]*>/g, '\n') // Line breaks
            .replace(/<w:t[^>]*>([^<]*)<\/w:t>/g, '$1') // Extract text runs
            .replace(/<[^>]*>/g, '') // Remove all other XML tags
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&amp;/g, '&')
            .replace(/&quot;/g, '"')
            .replace(/&apos;/g, "'");

        return textContent;
    }

    /**
     * Extract tokens from plain text content
     */
    private extractTokensFromText(textContent: string): ExtractedToken[] {
        const extractedTokens: ExtractedToken[] = [];
        const lines = textContent.split('\n');

        lines.forEach((line, lineIndex) => {
            let match;
            while ((match = this.TOKEN_PATTERN.exec(line)) !== null) {
                const token = match[1].trim();
                const position = match.index;
                const context = this.getTokenContext(line, position, 50);

                extractedTokens.push({
                    token,
                    position,
                    line: lineIndex + 1,
                    context
                });
            }
            // Reset regex lastIndex for next line
            this.TOKEN_PATTERN.lastIndex = 0;
        });

        return extractedTokens;
    }

    /**
     * Get context around token for debugging
     */
    private getTokenContext(text: string, position: number, contextLength: number): string {
        const start = Math.max(0, position - contextLength);
        const end = Math.min(text.length, position + contextLength);
        const context = text.substring(start, end);

        return context.trim();
    }

    /**
     * Validate extracted tokens against available tokens
     *
     * STRICT VALIDATION: Rejects ALL unknown tokens to prevent runtime errors
     */
    private async validateExtractedTokens(
        extractedTokens: ExtractedToken[],
        category: DocumentTemplateCategory,
        caseType?: CaseType
    ): Promise<{
        validTokens: string[];
        invalidTokens: string[];
        missingRequiredTokens: string[];
        validationErrors: string[];
    }> {
        const uniqueTokens = [...new Set(extractedTokens.map((t) => t.token))];

        // Get available tokens for the category/case type
        const availableTokens = caseType
            ? await this.templateValidationService.getAvailableTokensForCaseType(caseType)
            : await this.getAllAvailableTokens();

        const requiredTokens =
            this.templateValidationService.getRequiredSystemTokensForCategory(category);

        // STRICT VALIDATION: Only allow known tokens
        const validTokens = uniqueTokens.filter((token) => availableTokens.includes(token));
        const invalidTokens = uniqueTokens.filter((token) => !availableTokens.includes(token));
        const missingRequiredTokens = requiredTokens.filter(
            (token) => !uniqueTokens.includes(token)
        );

        // Categorize invalid tokens for better error messages
        const firmTokens = invalidTokens.filter((token) => token.startsWith('firm.'));
        const unknownTokens = invalidTokens.filter((token) => !token.startsWith('firm.'));

        // Generate detailed validation errors
        const validationErrors: string[] = [];

        if (firmTokens.length > 0) {
            validationErrors.push(
                `Firm tokens are not allowed: ${firmTokens.join(', ')}. ` +
                    `Firm details should be pre-filled in your tenant-specific template.`
            );
        }

        if (unknownTokens.length > 0) {
            validationErrors.push(
                `Unknown/unsupported tokens found: ${unknownTokens.join(', ')}. ` +
                    `These tokens cannot be fulfilled during document generation.`
            );
        }

        if (invalidTokens.length > 0) {
            validationErrors.push(
                `Available tokens for this template: ${availableTokens.sort().join(', ')}`
            );
        }

        if (missingRequiredTokens.length > 0) {
            validationErrors.push(
                `Missing required tokens for category '${category}': ${missingRequiredTokens.join(', ')}`
            );
        }

        // Log detailed validation results
        this.logger.debug(`Token validation results:
            Total extracted: ${uniqueTokens.length}
            Valid tokens: ${validTokens.join(', ')}
            Firm tokens (rejected): ${firmTokens.join(', ')}
            Unknown tokens (rejected): ${unknownTokens.join(', ')}
            Missing required tokens: ${missingRequiredTokens.join(', ')}
            Category: ${category}
            Case type: ${caseType || 'any'}
        `);

        return {
            validTokens,
            invalidTokens,
            missingRequiredTokens,
            validationErrors
        };
    }

    /**
     * Get all available tokens across all case types
     */
    private async getAllAvailableTokens(): Promise<string[]> {
        const allTokens = new Set<string>();

        // Add tokens from all case types
        for (const caseType of Object.values(CaseType)) {
            const tokens =
                await this.templateValidationService.getAvailableTokensForCaseType(caseType);
            tokens.forEach((token) => allTokens.add(token));
        }

        return Array.from(allTokens);
    }

    /**
     * Validate template tokens during upload
     */
    async validateTemplateTokens(
        fileBuffer: Buffer,
        category: DocumentTemplateCategory,
        allowedCaseTypes: CaseType[]
    ): Promise<TokenExtractionResult> {
        // If multiple case types are allowed, validate against all of them
        if (allowedCaseTypes.length === 1) {
            return this.extractTokensFromDocx(fileBuffer, category, allowedCaseTypes[0]);
        }

        // For multiple case types, validate against all available tokens
        const result = await this.extractTokensFromDocx(fileBuffer, category);

        // Additional validation for multiple case types
        if (allowedCaseTypes.length > 1) {
            const allAllowedTokens = new Set<string>();
            for (const caseType of allowedCaseTypes) {
                const tokens =
                    await this.templateValidationService.getAvailableTokensForCaseType(caseType);
                tokens.forEach((token) => allAllowedTokens.add(token));
            }

            const allowedTokensArray = Array.from(allAllowedTokens);
            const validTokens = result.uniqueTokens.filter((token) =>
                allowedTokensArray.includes(token)
            );
            const invalidTokens = result.uniqueTokens.filter(
                (token) => !allowedTokensArray.includes(token)
            );

            if (invalidTokens.length > 0) {
                result.validationErrors.push(
                    `Invalid tokens for allowed case types (${allowedCaseTypes.join(', ')}): ${invalidTokens.join(', ')}`
                );
            }

            result.validTokens = validTokens;
            result.invalidTokens = invalidTokens;
        }

        return result;
    }

    /**
     * Get token statistics for a template
     */
    getTokenStatistics(extractionResult: TokenExtractionResult): {
        totalTokens: number;
        uniqueTokens: number;
        validTokens: number;
        invalidTokens: number;
        missingRequiredTokens: number;
        isValid: boolean;
    } {
        return {
            totalTokens: extractionResult.totalTokenCount,
            uniqueTokens: extractionResult.uniqueTokens.length,
            validTokens: extractionResult.validTokens.length,
            invalidTokens: extractionResult.invalidTokens.length,
            missingRequiredTokens: extractionResult.missingRequiredTokens.length,
            isValid: extractionResult.validationErrors.length === 0
        };
    }
}
