import { Injectable, Logger } from '@nestjs/common';
import { S3 } from 'aws-sdk';
import { AwsConfigService } from '@app/common/config/aws.config';
import { TenantContextService } from '@app/common/multi-tenancy';
import { createHash } from 'crypto';
import { Readable } from 'stream';

/**
 * Service for managing document storage in AWS S3
 * Using case-centric organization: tenant-${tenantId}/case/${caseId}/${folderId}/${filename}
 */
@Injectable()
export class S3StorageService {
    generateDocumentKey(caseId: string, folderId: string, filename: string) {
        const tenantId = 'tenant-' + caseId.substring(0, 8); // Simplified for now
        return `${tenantId}/case/${caseId}/${folderId}/${filename}`;
    }
    private readonly logger = new Logger(S3StorageService.name);
    private s3Client: S3;

    constructor(
        private readonly awsConfig: AwsConfigService,
        private readonly tenantContextService: TenantContextService
    ) {
        this.initializeS3Client();
    }

    /**
     * Initialize the S3 client with configuration
     */
    private initializeS3Client() {
        const options: S3.ClientConfiguration = {
            region: this.awsConfig.region,
            accessKeyId: this.awsConfig.accessKeyId,
            secretAccessKey: this.awsConfig.secretAccessKey
        };

        // For local development with MinIO
        if (this.awsConfig.s3Endpoint) {
            options.endpoint = this.awsConfig.s3Endpoint;
            options.s3ForcePathStyle = this.awsConfig.s3ForcePathStyle;
            options.sslEnabled = this.awsConfig.s3UseSSL;
        }

        this.s3Client = new S3(options);
    }

    /**
     * Generate S3 key for case-centric document organization
     * Format: tenant-{tenantId}/case/{caseId}/{folderId}/{filename}
     * If no folderId, format: tenant-{tenantId}/case/{caseId}/{filename}
     */
    private getS3Key(caseId: string, folderId: string | null, filename: string): string {
        if (!this.tenantContextService.hasTenant()) {
            throw new Error('Tenant context not available');
        }

        const tenantId = this.tenantContextService.getTenantId();

        if (folderId) {
            return `tenant-${tenantId}/case/${caseId}/${folderId}/${filename}`;
        } else {
            return `tenant-${tenantId}/case/${caseId}/${filename}`;
        }
    }

    /**
     * Generate S3 key for template organization
     * Format: tenant-{tenantId}/templates/{templateType}/{filename}
     */
    private getTemplateS3Key(templateType: string, filename: string): string {
        if (!this.tenantContextService.hasTenant()) {
            throw new Error('Tenant context not available');
        }

        const tenantId = this.tenantContextService.getTenantId();
        const sanitizedType = templateType.toLowerCase().replace(/[^a-z0-9_-]/g, '-');

        // Add timestamp to ensure uniqueness
        const timestamp = Date.now();
        const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.')) || filename;
        const extension = filename.substring(filename.lastIndexOf('.')) || '';
        const uniqueFilename = `${nameWithoutExt}-${timestamp}${extension}`;

        return `tenant-${tenantId}/templates/${sanitizedType}/${uniqueFilename}`;
    }

    /**
     * Upload a file to S3
     * @param caseId The case ID
     * @param folderId The folder ID (optional)
     * @param filename The original filename
     * @param data The file data as Buffer
     * @param mimeType The file MIME type
     * @returns Promise with upload result
     */
    async uploadFile(
        caseId: string,
        folderId: string | null,
        filename: string,
        data: Buffer,
        mimeType: string
    ): Promise<{
        url: string | undefined;
        s3Key: string;
        s3Bucket: string;
        eTag: string;
        checksum: string;
    }> {
        const s3Key = this.getS3Key(caseId, folderId, filename);
        const bucketName = this.awsConfig.documentBucketName;

        // Calculate MD5 checksum
        const checksum = this.calculateChecksum(data);

        try {
            const uploadResult = await this.s3Client
                .upload({
                    Bucket: bucketName,
                    Key: s3Key,
                    Body: data,
                    ContentType: mimeType,
                    Metadata: {
                        'case-id': caseId,
                        'folder-id': folderId || '',
                        'original-filename': filename,
                        'upload-date': new Date().toISOString(),
                        checksum
                    }
                })
                .promise();

            this.logger.log(`File uploaded successfully: ${s3Key}`);

            // Generate URL for the uploaded file
            const url = `https://${bucketName}.s3.amazonaws.com/${s3Key}`;

            return {
                url,
                s3Key,
                s3Bucket: bucketName,
                eTag: uploadResult.ETag || '',
                checksum
            };
        } catch (error) {
            this.logger.error(`Error uploading file to S3: ${error.message}`, error.stack);
            throw new Error(`Failed to upload file: ${error.message}`);
        }
    }

    /**
     * Upload a template file to S3
     * @param templateType The template type for organization
     * @param filename The original filename
     * @param data The file data as Buffer
     * @param mimeType The file MIME type
     * @returns Promise with upload result
     */
    async uploadTemplateFile(
        templateType: string,
        filename: string,
        data: Buffer,
        mimeType: string
    ): Promise<{ s3Key: string; s3Bucket: string; eTag: string; checksum: string }> {
        const s3Key = this.getTemplateS3Key(templateType, filename);
        const bucketName = this.awsConfig.documentBucketName;

        // Calculate MD5 checksum
        const checksum = this.calculateChecksum(data);

        try {
            const uploadResult = await this.s3Client
                .upload({
                    Bucket: bucketName,
                    Key: s3Key,
                    Body: data,
                    ContentType: mimeType,
                    Metadata: {
                        'template-type': templateType,
                        'original-filename': filename,
                        'upload-date': new Date().toISOString(),
                        checksum,
                        'content-category': 'template'
                    }
                })
                .promise();

            this.logger.log(`Template file uploaded successfully: ${s3Key}`);

            return {
                s3Key,
                s3Bucket: bucketName,
                eTag: uploadResult.ETag || '',
                checksum
            };
        } catch (error) {
            this.logger.error(`Error uploading template file to S3: ${error.message}`, error.stack);
            throw new Error(`Failed to upload template file: ${error.message}`);
        }
    }

    /**
     * Generate a presigned URL for downloading a file
     * @param s3Key The S3 key for the file
     * @param filename The original filename (for Content-Disposition)
     * @param expirationSeconds URL expiration time in seconds
     * @returns Presigned URL for download
     */
    async getDownloadUrl(
        s3Key: string,
        filename: string,
        expirationSeconds?: number
    ): Promise<string> {
        try {
            const expiration = expirationSeconds || this.awsConfig.presignedUrlExpirationSeconds;

            const url = await this.s3Client.getSignedUrlPromise('getObject', {
                Bucket: this.awsConfig.documentBucketName,
                Key: s3Key,
                ResponseContentDisposition: `attachment; filename="${encodeURIComponent(filename)}"`,
                Expires: Number(expiration.toString())
            });

            return url;
        } catch (error) {
            this.logger.error(`Error generating download URL: ${error.message}`, error.stack);
            throw new Error(`Failed to generate download URL: ${error.message}`);
        }
    }

    /**
     * Generate a presigned URL for uploading a file directly
     * @param caseId The case ID
     * @param folderId The folder ID (optional)
     * @param filename The filename
     * @param mimeType The file MIME type
     * @param metadata Additional metadata
     * @param expirationSeconds URL expiration time in seconds
     * @returns Presigned URL for upload
     */
    async getUploadUrl(
        caseId: string,
        folderId: string | null,
        filename: string,
        mimeType: string,
        metadata: Record<string, string>,
        expirationSeconds?: number
    ): Promise<{ url: string; s3Key: string }> {
        try {
            const s3Key = this.getS3Key(caseId, folderId, filename);
            const expiration = expirationSeconds || this.awsConfig.presignedUrlExpirationSeconds;

            const url = await this.s3Client.getSignedUrlPromise('putObject', {
                Bucket: this.awsConfig.documentBucketName,
                Key: s3Key,
                ContentType: mimeType,
                Metadata: {
                    ...metadata,
                    'case-id': caseId,
                    'folder-id': folderId || ''
                },
                Expires: expiration
            });

            return { url, s3Key };
        } catch (error) {
            this.logger.error(`Error generating upload URL: ${error.message}`, error.stack);
            throw new Error(`Failed to generate upload URL: ${error.message}`);
        }
    }

    /**
     * Delete a file from S3
     * @param s3Key The S3 key for the file
     * @returns Promise indicating success
     */
    async deleteFile(s3Key: string): Promise<boolean> {
        try {
            await this.s3Client
                .deleteObject({
                    Bucket: this.awsConfig.documentBucketName,
                    Key: s3Key
                })
                .promise();

            this.logger.log(`File deleted successfully: ${s3Key}`);
            return true;
        } catch (error) {
            this.logger.error(`Error deleting file from S3: ${error.message}`, error.stack);
            throw new Error(`Failed to delete file: ${error.message}`);
        }
    }

    /**
     * Check if a file exists in S3
     * @param s3Key The S3 key for the file
     * @returns Promise resolving to boolean indicating existence
     */
    async fileExists(s3Key: string): Promise<boolean> {
        try {
            await this.s3Client
                .headObject({
                    Bucket: this.awsConfig.documentBucketName,
                    Key: s3Key
                })
                .promise();
            return true;
        } catch (error) {
            if (error.code === 'NotFound') {
                return false;
            }
            this.logger.error(`Error checking if file exists: ${error.message}`, error.stack);
            throw new Error(`Failed to check file existence: ${error.message}`);
        }
    }

    /**
     * Get file metadata from S3
     * @param s3Key The S3 key for the file
     * @returns File metadata
     */
    async getFileMetadata(s3Key: string): Promise<S3.HeadObjectOutput> {
        try {
            const metadata = await this.s3Client
                .headObject({
                    Bucket: this.awsConfig.documentBucketName,
                    Key: s3Key
                })
                .promise();
            return metadata;
        } catch (error) {
            this.logger.error(`Error getting file metadata: ${error.message}`, error.stack);
            throw new Error(`Failed to get file metadata: ${error.message}`);
        }
    }

    /**
     * Get file as a readable stream
     * @param s3Key The S3 key for the file
     * @returns Readable stream of file data
     */
    getFileStream(s3Key: string): Readable {
        try {
            const stream = this.s3Client
                .getObject({
                    Bucket: this.awsConfig.documentBucketName,
                    Key: s3Key
                })
                .createReadStream();
            return stream;
        } catch (error) {
            this.logger.error(`Error getting file stream: ${error.message}`, error.stack);
            throw new Error(`Failed to get file stream: ${error.message}`);
        }
    }

    /**
     * Download file as buffer
     * @param s3Key The S3 key for the file
     * @returns File content as Buffer
     */
    async downloadFileAsBuffer(s3Key: string): Promise<Buffer> {
        try {
            const result = await this.s3Client
                .getObject({
                    Bucket: this.awsConfig.documentBucketName,
                    Key: s3Key
                })
                .promise();

            if (!result.Body) {
                throw new Error('File content is empty');
            }

            return result.Body as Buffer;
        } catch (error) {
            this.logger.error(`Error downloading file as buffer: ${error.message}`, error.stack);
            throw new Error(`Failed to download file: ${error.message}`);
        }
    }

    /**
     * Copy a file within S3
     * @param sourceKey Source S3 key
     * @param destinationKey Destination S3 key
     * @returns Promise with copy result
     */
    async copyFile(sourceKey: string, destinationKey: string): Promise<S3.CopyObjectOutput> {
        try {
            const result = await this.s3Client
                .copyObject({
                    Bucket: this.awsConfig.documentBucketName,
                    CopySource: `${this.awsConfig.documentBucketName}/${sourceKey}`,
                    Key: destinationKey
                })
                .promise();

            this.logger.log(`File copied from ${sourceKey} to ${destinationKey}`);
            return result;
        } catch (error) {
            this.logger.error(`Error copying file in S3: ${error.message}`, error.stack);
            throw new Error(`Failed to copy file: ${error.message}`);
        }
    }

    /**
     * Calculate MD5 checksum of file data
     * @param data File data as Buffer
     * @returns MD5 checksum as hex string
     */
    private calculateChecksum(data: Buffer): string {
        return createHash('md5').update(data).digest('hex');
    }

    /**
     * Create a bucket if it doesn't exist
     * @returns Promise indicating success
     */
    async ensureBucketExists(): Promise<boolean> {
        const bucketName = this.awsConfig.documentBucketName;

        try {
            // Check if the bucket exists
            await this.s3Client.headBucket({ Bucket: bucketName }).promise();
            this.logger.log(`Bucket ${bucketName} already exists`);
            return true;
        } catch (error) {
            if (error.code === 'NotFound') {
                // Create the bucket if it doesn't exist
                try {
                    await this.s3Client
                        .createBucket({
                            Bucket: bucketName,
                            CreateBucketConfiguration: {
                                LocationConstraint: this.awsConfig.region
                            }
                        })
                        .promise();

                    this.logger.log(`Bucket ${bucketName} created successfully`);
                    return true;
                } catch (createError) {
                    this.logger.error(
                        `Error creating bucket ${bucketName}: ${createError.message}`,
                        createError.stack
                    );
                    throw new Error(`Failed to create bucket: ${createError.message}`);
                }
            } else {
                this.logger.error(
                    `Error checking bucket ${bucketName}: ${error.message}`,
                    error.stack
                );
                throw new Error(`Failed to check bucket existence: ${error.message}`);
            }
        }
    }
}
