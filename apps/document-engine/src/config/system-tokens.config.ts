/**
 * Configuration for system-defined tokens
 * These tokens are automatically available in all templates
 */
export interface SystemTokenDefinition {
    name: string;
    description: string;
    category: string;
    dataType?: string;
    example?: any;
}

export const SYSTEM_TOKEN_DEFINITIONS: SystemTokenDefinition[] = [
    // System-generated date/time tokens
    {
        name: 'currentDate',
        description: 'Current date in GB format (DD/MM/YYYY)',
        category: 'Date/Time',
        dataType: 'DATE',
        example: '04/08/2025'
    },
    {
        name: 'currentDateTime',
        description: 'Current date and time in GB format',
        category: 'Date/Time',
        dataType: 'STRING',
        example: '04/08/2025 14:30:00'
    },
    {
        name: 'currentYear',
        description: 'Current year (e.g., 2025)',
        category: 'Date/Time',
        dataType: 'NUMBER',
        example: 2025
    },
    {
        name: 'currentMonth',
        description: 'Current month name (e.g., January)',
        category: 'Date/Time',
        dataType: 'STRING',
        example: 'August'
    },
    {
        name: 'currentDay',
        description: 'Current day of month (e.g., 30)',
        category: 'Date/Time',
        dataType: 'NUMBER',
        example: 4
    },

    // Current user tokens
    {
        name: 'currentUser.name',
        description: 'Name of the current user generating the document',
        category: 'User Context',
        dataType: 'STRING',
        example: 'Sarah Johnson'
    },
    {
        name: 'currentUser.email',
        description: 'Email of the current user generating the document',
        category: 'User Context',
        dataType: 'EMAIL',
        example: '<EMAIL>'
    },
    {
        name: 'currentUser.id',
        description: 'ID of the current user generating the document',
        category: 'User Context',
        dataType: 'STRING',
        example: 'usr_12345'
    },

    // System metadata tokens
    {
        name: 'generationTimestamp',
        description: 'When the document was generated (GB locale)',
        category: 'System Metadata',
        dataType: 'STRING',
        example: '04/08/2025 14:30:00'
    },
    {
        name: 'generatedBy',
        description: 'Who generated the document (user name, ID, or "System")',
        category: 'System Metadata',
        dataType: 'STRING',
        example: 'Sarah Johnson (usr_12345)'
    },
    {
        name: 'documentId',
        description: 'Unique document ID (format: DOC-{timestamp})',
        category: 'System Metadata',
        dataType: 'STRING',
        example: 'DOC-20250804143000'
    },
    {
        name: 'templateVersion',
        description: 'Template version (currently "1.0")',
        category: 'System Metadata',
        dataType: 'STRING',
        example: '1.0'
    }
];

/**
 * Get system token definitions grouped by category
 */
export function getSystemTokensByCategory(): Record<string, SystemTokenDefinition[]> {
    return SYSTEM_TOKEN_DEFINITIONS.reduce(
        (acc, token) => {
            if (!acc[token.category]) {
                acc[token.category] = [];
            }
            acc[token.category].push(token);
            return acc;
        },
        {} as Record<string, SystemTokenDefinition[]>
    );
}

/**
 * Get all system token names
 */
export function getSystemTokenNames(): string[] {
    return SYSTEM_TOKEN_DEFINITIONS.map((token) => token.name);
}

/**
 * Find system token by name
 */
export function findSystemToken(name: string): SystemTokenDefinition | undefined {
    return SYSTEM_TOKEN_DEFINITIONS.find((token) => token.name === name);
}
