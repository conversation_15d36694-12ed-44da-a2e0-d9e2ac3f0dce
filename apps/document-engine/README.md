# Document Engine - S3 Integration

## Overview

The Document Engine service provides document storage, organization, and retrieval functionality for the TK-LPM platform. Documents are stored in AWS S3 for the actual files, with metadata stored in the database.

## Architecture

The system uses a hybrid approach:
- **File Storage**: AWS S3 for document binary content
- **Metadata Storage**: PostgreSQL for document metadata and relationships

## AWS S3 Configuration

To configure AWS S3 integration, set the following environment variables:

```env
# AWS S3 Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DOCUMENT_BUCKET_NAME=tk-lpm-documents
AWS_S3_PRESIGNED_URL_EXPIRATION=3600
```

For local development with MinIO:

```env
AWS_S3_ENDPOINT=http://localhost:9000
AWS_S3_FORCE_PATH_STYLE=true
AWS_S3_USE_SSL=false
```

## S3 Storage Structure

Documents are stored in S3 with the following path structure:

```
tenant-{tenantId}/case/{caseId}/{folderId}/{filename}
tenant-{tenantId}/case/{caseId}/{filename}
```

This structure provides:
- Tenant isolation
- Case related document organization
- Original filename preservation

## API Endpoints

The Document Engine provides the following API endpoints:

### Document Management

- `POST /documents`: Create a new document
- `GET /documents`: List documents (by folder or case)
- `GET /documents/search`: Search for documents
- `GET /documents/:id`: Get document details
- `PUT /documents/:id`: Update document metadata
- `DELETE /documents/:id`: Delete a document

### Document Locking

- `POST /documents/:id/lock`: Lock a document for editing
- `POST /documents/:id/unlock`: Unlock a document

## Local Development with MinIO

For local development, you can use MinIO as an S3-compatible storage service:

1. Start MinIO using Docker:

```bash
docker compose up -d minio
```

2. Configure environment variables for MinIO:

```env
AWS_S3_ENDPOINT=http://localhost:9000
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin
AWS_DOCUMENT_BUCKET_NAME=tk-lpm-documents
AWS_S3_FORCE_PATH_STYLE=true
AWS_S3_USE_SSL=false
```

3. Create the `tk-lpm-documents` bucket in the MinIO console (http://localhost:9001) 