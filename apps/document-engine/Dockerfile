# Build stage
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Run tests as a build-time gate
RUN echo "::group::Running Tests" && \
    if grep -q "\"test:document-engine\":" package.json; then \
      echo "Running service-specific tests" && \
      yarn test:document-engine --passWithNoTests || exit 1; \
    elif grep -q "\"test\":" package.json; then \
      echo "Running general tests" && \
      yarn test --passWithNoTests || exit 1; \
    else \
      echo "No test script found, skipping tests"; \
    fi && \
    echo "::endgroup::"

# Build the application
RUN echo "::group::Building Application" && \
    if grep -q "\"build:document-engine\":" package.json; then \
      echo "Running service-specific build" && \
      yarn build:document-engine; \
    else \
      echo "Running general build" && \
      yarn build; \
    fi && \
    echo "::endgroup::"

# Production stage
FROM node:20-alpine AS production

# Set environment variables
ENV NODE_ENV=production

# Set working directory
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./

# Install production dependencies only
RUN yarn install --production --frozen-lockfile

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/libs ./libs
COPY --from=builder /app/nest-cli.json ./

# Expose the service port (will be overridden by environment variable)
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
  CMD wget -qO- http://localhost:${PORT:-3002}/api/document-engine/health || exit 1

# Start the service
CMD if grep -q "\"start:document-engine:prod\":" package.json; then \
      yarn start:document-engine:prod; \
    elif grep -q "\"start:prod\":" package.json; then \
      yarn start:prod; \
    else \
      node dist/apps/document-engine/main.js; \
    fi
