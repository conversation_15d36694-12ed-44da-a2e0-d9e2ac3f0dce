import { Job } from 'bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { SingleChannelJobData } from '@app/common/communication/interfaces/communication-job.interface';
import { QUEUE_NAMES } from '@app/common/constants/queue.constants';
import { ConfigService } from '@nestjs/config';
import { Processor, WorkerHost } from '@nestjs/bullmq';
import * as FormData from 'form-data';
import Mailgun from 'mailgun.js';
import * as sgMail from '@sendgrid/mail';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import {
    TemplateService,
    UnifiedTemplateConfig
} from '@app/common/communication/services/template.service';
import { CircuitBreakerService } from '@app/common/communication/services/circuit-breaker.service';
import {
    RecipientValidationException,
    ConfigurationException
} from '@app/common/communication/exceptions/communication.exceptions';
import { getCommunicationConfig } from '@app/common/communication/config/templates.config';
import { InjectRepository } from '@nestjs/typeorm';
import { Tenant } from '@app/common/typeorm/entities';
import { Repository } from 'typeorm';
import { SESEmailService } from '@app/common/communication/services/ses-email.service';

interface EmailResult {
    messageId: string;
    provider: 'mailgun' | 'sendgrid' | 'ses';
    status: 'sent' | 'failed';
    templateType?: string;
    error?: string;
}

@Processor(QUEUE_NAMES.EMAIL)
@Injectable()
export class EmailConsumer extends WorkerHost {
    protected readonly logger = new Logger(EmailConsumer.name);
    private mailgunClient: any = null;
    private sendGridConfigured = false;
    private readonly config = getCommunicationConfig();

    constructor(
        private readonly configService: ConfigService,
        private readonly templateService: TemplateService,
        private readonly circuitBreaker: CircuitBreakerService,
        private readonly sesEmailService: SESEmailService,
        @InjectRepository(Tenant) private tenantRepository: Repository<Tenant>
    ) {
        super();
        this.initializeEmailProviders();
        this.initializeCircuitBreakers();
    }

    private initializeEmailProviders(): void {
        // Initialize SendGrid
        if (this.config.email.sendgrid.enabled) {
            sgMail.setApiKey(this.config.email.sendgrid.apiKey!);
            this.sendGridConfigured = true;
            this.logger.log('SendGrid initialized as primary email provider');
        } else {
            this.logger.warn('SendGrid not configured - missing SENDGRID_API_KEY');
        }

        // Initialize Mailgun
        if (this.config.email.mailgun.enabled) {
            const mailgun = new Mailgun(FormData);
            this.mailgunClient = mailgun.client({
                username: 'api',
                key: this.config.email.mailgun.apiKey!,
                url: this.config.email.mailgun.url
            });
            this.logger.log('Mailgun initialized as fallback email provider');
        } else {
            this.logger.warn('Mailgun not configured - missing MAILGUN_API_KEY or MAILGUN_DOMAIN');
        }

        // Validate at least one provider is configured
        if (!this.sendGridConfigured && !this.mailgunClient && !this.sesEmailService.isEnabled()) {
            const missingConfig: string[] = [];
            if (!this.config.email.sendgrid.enabled) missingConfig.push('SENDGRID_API_KEY');
            if (!this.config.email.mailgun.enabled)
                missingConfig.push('MAILGUN_API_KEY, MAILGUN_DOMAIN');
            if (!this.config.email.ses.enabled)
                missingConfig.push('AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY');

            throw new ConfigurationException('Email Service', missingConfig);
        }
    }

    private initializeCircuitBreakers(): void {
        if (this.sesEmailService.isEnabled()) {
            this.circuitBreaker.registerCircuit('ses', this.config.circuitBreaker.email);
        }

        if (this.sendGridConfigured) {
            this.circuitBreaker.registerCircuit('sendgrid', this.config.circuitBreaker.email);
        }

        if (this.mailgunClient) {
            this.circuitBreaker.registerCircuit('mailgun', this.config.circuitBreaker.email);
        }
    }

    async process(job: Job<SingleChannelJobData>): Promise<any> {
        const { tenantId, userId, recipient, variables, caseId } = job.data;

        this.logger.log(
            `Processing email job ${job.id} for tenant ${tenantId}, recipient: ${recipient}`
        );

        try {
            this.validateRecipient(recipient);

            const processedTemplate = await this.templateService.processTemplate(
                variables,
                tenantId
            );

            const emailResult = await this.sendEmailWithFallback({
                to: recipient,
                templateType: processedTemplate.templateType,
                templateData: processedTemplate.templateData,
                tenantId
            });

            this.logger.log(
                `Email sent successfully for job ${job.id}, Provider: ${emailResult.provider}, Template: ${emailResult.templateType}, MessageID: ${emailResult.messageId}`
            );

            return {
                channel: COMMUNICATION_CHANNELS.EMAIL,
                recipient,
                messageId: emailResult.messageId,
                provider: emailResult.provider,
                templateType: emailResult.templateType,
                status: 'sent',
                sentAt: new Date(),
                tenantId,
                userId,
                caseId
            };
        } catch (error) {
            this.logger.error(`Email job ${job.id} failed: ${error.message}`, error.stack);
            throw error;
        }
    }

    private validateRecipient(recipient: string): void {
        if (!recipient) {
            throw new RecipientValidationException('email', recipient, 'Recipient is required');
        }

        if (!this.isValidEmail(recipient)) {
            throw new RecipientValidationException('email', recipient, 'Invalid email format');
        }
    }

    private async sendEmailWithFallback(emailData: {
        to: string;
        templateType: string;
        templateData: Record<string, any>;
        tenantId: string;
    }): Promise<EmailResult> {
        if (!emailData.to || !this.isValidEmail(emailData.to)) {
            throw new Error(`Invalid email recipient: ${emailData.to}`);
        }

        const errors: string[] = [];

        // Try SES first (primary provider)
        if (this.sesEmailService.isEnabled()) {
            try {
                this.logger.debug('Attempting SES (primary provider)...');
                return await this.sendViaSES(emailData);
            } catch (error) {
                this.logger.warn(`SES failed: ${error.message}`);
                errors.push(`SES: ${error.message}`);
            }
        }

        // Try SendGrid second (fallback)
        if (this.sendGridConfigured) {
            try {
                this.logger.debug('Attempting SendGrid fallback...');
                return await this.sendViaSendGrid(emailData);
            } catch (error) {
                this.logger.warn(`SendGrid failed: ${error.message}`);
                errors.push(`SendGrid: ${error.message}`);
            }
        }

        // Try Mailgun last (final fallback)
        if (this.mailgunClient) {
            try {
                this.logger.debug('Attempting Mailgun fallback...');
                return await this.sendViaMailgun(emailData);
            } catch (error) {
                this.logger.warn(`Mailgun failed: ${error.message}`);
                errors.push(`Mailgun: ${error.message}`);
            }
        }

        if (errors.length > 0) {
            throw new Error(`All email providers failed: ${errors.join(', ')}`);
        }

        throw new Error('No email providers configured');
    }

    private async sendViaSendGrid(emailData: {
        to: string;
        templateType: string;
        templateData: Record<string, any>;
        tenantId: string;
    }): Promise<EmailResult> {
        const template = this.templateService.getTemplateConfig(emailData.templateType);
        if (!template) {
            throw new Error(`Template not found for type: ${emailData.templateType}`);
        }

        const fromEmail = this.configService.get('FROM_EMAIL') || '<EMAIL>';

        const msg = {
            to: {
                email: emailData.to.trim(),
                name: emailData.templateData.recipientName
            },
            from: {
                email: fromEmail,
                name: emailData.templateData.tenantName
            },
            templateId: template.sendGridTemplateId,
            dynamicTemplateData: emailData.templateData,
            categories: [...template.categories, `tenant-${emailData.tenantId}`],
            customArgs: {
                tenant_id: emailData.tenantId,
                template_type: emailData.templateType,
                user_id: emailData.templateData.userId || '',
                case_id: emailData.templateData.caseId || ''
            },
            trackingSettings: {
                clickTracking: { enable: true },
                openTracking: { enable: true },
                subscriptionTracking: { enable: false }
            }
        };

        if (emailData.templateData.subject) {
            (msg as any).subject = emailData.templateData.subject;
        }

        try {
            this.logger.debug(`Sending via SendGrid template: ${template.sendGridTemplateId}`);
            const response = await sgMail.send(msg);

            return {
                messageId: response[0].headers['x-message-id'] || `sg-${Date.now()}`,
                provider: 'sendgrid',
                status: 'sent',
                templateType: emailData.templateType
            };
        } catch (error) {
            this.logger.error(`SendGrid error: ${error.message}`, error.response?.body);
            throw new Error(`SendGrid template failed: ${error.message}`);
        }
    }

    private async sendViaMailgun(emailData: {
        to: string;
        templateType: string;
        templateData: Record<string, any>;
        tenantId: string;
    }): Promise<EmailResult> {
        const template = this.templateService.getTemplateConfig(emailData.templateType);
        if (!template) {
            throw new Error(`Template not found for type: ${emailData.templateType}`);
        }

        const domain = this.configService.get('MAILGUN_DOMAIN');
        const fromEmail = `noreply@${domain}`;

        if (!domain) {
            throw new Error('MAILGUN_DOMAIN is required for Mailgun');
        }

        const mailgunData = {
            from: fromEmail,
            to: emailData.to.trim(),
            template: 'case_update',
            'h:X-Mailgun-Variables': JSON.stringify(emailData.templateData),
            'o:tag': [
                ...template.categories,
                `tenant-${emailData.tenantId}`,
                emailData.templateType
            ],
            'v:tenant_id': emailData.tenantId,
            'v:template_type': emailData.templateType,
            'v:user_id': emailData.templateData.userId || '',
            'v:case_id': emailData.templateData.caseId || ''
        };

        try {
            this.logger.debug(`Sending via Mailgun template: ${template.mailgunTemplateName}`);
            const response = await this.mailgunClient.messages.create(domain, mailgunData);

            return {
                messageId: response.id,
                provider: 'mailgun',
                status: 'sent',
                templateType: emailData.templateType
            };
        } catch (error) {
            this.logger.error(`Mailgun error: ${error.message}`, error.response?.body);
            throw new Error(`Mailgun template failed: ${error.message}`);
        }
    }

    private async sendViaSES(emailData: {
        to: string;
        templateType: string;
        templateData: Record<string, any>;
        tenantId: string;
    }): Promise<EmailResult> {
        const template = this.templateService.getTemplateConfig(emailData.templateType);
        if (!template) {
            throw new Error(`Template not found for type: ${emailData.templateType}`);
        }

        try {
            // Use only SES templated emails - no HTML fallback
            const sesResult = await this.sesEmailService.sendTemplatedEmail(emailData, template);
            this.logger.debug(
                `SES templated email sent with template: ${template.sesTemplateName} to ${emailData.to}`
            );

            return {
                messageId: sesResult.messageId,
                provider: 'ses',
                status: 'sent',
                templateType: emailData.templateType
            };
        } catch (error) {
            this.logger.error(
                `SES email failed for ${emailData.templateType} to ${emailData.to}: ${error.message}`,
                { templateType: emailData.templateType, recipient: emailData.to }
            );
            throw new Error(`SES email failed: ${error.message}`);
        }
    }

    // Utility methods
    private isValidEmail(email: string): boolean {
        if (!email || typeof email !== 'string') {
            return false;
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email.trim());
    }

    // Health and monitoring methods
    getProviderStatus(): {
        sendgrid: { configured: boolean; status: string; templatesAvailable: number };
        ses: { configured: boolean; status: string; templatesAvailable: number; region?: string };
        mailgun: { configured: boolean; status: string; templatesAvailable: number };
        overall: { healthy: boolean; primaryProvider: string };
    } {
        const templates = this.templateService.getUnifiedTemplates();
        const sesStatus = this.sesEmailService.getStatus();

        return {
            sendgrid: {
                configured: this.sendGridConfigured,
                status: this.sendGridConfigured ? 'secondary' : 'not configured',
                templatesAvailable: Object.keys(templates).length
            },
            ses: {
                configured: this.sesEmailService.isEnabled(),
                status: this.sesEmailService.isEnabled() ? 'primary' : 'not configured',
                templatesAvailable: Object.keys(templates).length,
                region: sesStatus.region
            },
            mailgun: {
                configured: !!this.mailgunClient,
                status: this.mailgunClient ? 'fallback' : 'not configured',
                templatesAvailable: Object.keys(templates).length
            },
            overall: {
                healthy:
                    this.sesEmailService.isEnabled() ||
                    this.sendGridConfigured ||
                    !!this.mailgunClient,
                primaryProvider: this.sesEmailService.isEnabled()
                    ? 'ses'
                    : this.sendGridConfigured
                      ? 'sendgrid'
                      : this.mailgunClient
                        ? 'mailgun'
                        : 'none'
            }
        };
    }

    getUnifiedTemplates(): Record<string, UnifiedTemplateConfig> {
        return this.templateService.getUnifiedTemplates();
    }

    getTemplateVariables(templateType: string): {
        required: string[];
        optional: string[];
    } {
        return this.templateService.getTemplateVariables(templateType);
    }
}
