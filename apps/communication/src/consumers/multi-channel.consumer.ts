import { Logger, Injectable } from '@nestjs/common';
import {
    ChannelR<PERSON>ult,
    CommunicationJobData,
    SingleChannelJobData
} from '@app/common/communication/interfaces/communication-job.interface';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { QUEUE_NAMES } from '@app/common/constants/queue.constants';
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';

@Processor(QUEUE_NAMES.MULTI_CHANNEL)
@Injectable()
export class MultiChannelProcessor extends WorkerHost {
    private readonly logger = new Logger(MultiChannelProcessor.name);

    constructor(private readonly messageProducer: MessageProducerService) {
        super();
    }

    async process(job: Job<CommunicationJobData>): Promise<any> {
        return this.handleMultiChannelJob(job);
    }

    async handleMultiChannelJob(job: Job<CommunicationJobData>): Promise<any> {
        const { tenantId, userId, channels } = job.data;

        this.logger.log(
            `Processing multi-channel job ${job.id} for tenant ${tenantId}, userId: ${userId}, channels: ${channels.join(', ')}`
        );

        try {
            if (job.data.processInParallel !== false) {
                // Process all channels in parallel (default behavior)
                return await this.processChannelsInParallel(job);
            } else {
                // Process channels sequentially
                return await this.processChannelsSequentially(job);
            }
        } catch (error) {
            this.logger.error(`Multi-channel job ${job.id} failed: ${error.message}`, error.stack);
            throw error;
        }
    }

    private async processChannelsInParallel(job: Job<CommunicationJobData>): Promise<any> {
        const { channels, tenantId } = job.data;

        this.logger.log(
            `Processing ${channels.length} channels in parallel for tenant ${tenantId}`
        );

        const channelJobs = channels.map((channel) =>
            this.createSingleChannelJob(job.data, channel)
        );

        if (job.data.failureStrategy === 'fail-fast') {
            // If any channel fails, the entire job fails
            const results = await Promise.all(
                channelJobs.map((channelJob) => this.processChannelJob(channelJob))
            );
            return { results, strategy: 'fail-fast', status: 'all-completed' };
        } else {
            // Continue processing even if some channels fail
            const results = await Promise.allSettled(
                channelJobs.map((channelJob) => this.processChannelJob(channelJob))
            );

            const successes = results.filter((r) => r.status === 'fulfilled').length;
            const failures = results.filter((r) => r.status === 'rejected').length;

            this.logger.log(
                `Multi-channel job completed: ${successes} successes, ${failures} failures`
            );

            return {
                results,
                strategy: 'continue-on-error',
                status: failures > 0 ? 'partial-success' : 'all-completed',
                successes,
                failures
            };
        }
    }

    private async processChannelsSequentially(job: Job<CommunicationJobData>): Promise<any> {
        const { channels, tenantId } = job.data;

        this.logger.log(
            `Processing ${channels.length} channels sequentially for tenant ${tenantId}`
        );

        const results: ChannelResult[] = [];

        for (const channel of channels) {
            try {
                const channelJob = this.createSingleChannelJob(job.data, channel);
                const result: ChannelResult[] = await this.processChannelJob(channelJob);
                results.push({ channel, status: 'fulfilled', result });
            } catch (error) {
                results.push({ channel, status: 'rejected', error: error.message });

                if (job.data.failureStrategy === 'fail-fast') {
                    throw error;
                }
            }
        }

        const successes = results.filter((r) => r.status === 'fulfilled').length;
        const failures = results.filter((r) => r.status === 'rejected').length;

        return {
            results,
            strategy: 'sequential',
            status: failures > 0 ? 'partial-success' : 'all-completed',
            successes,
            failures
        };
    }

    private createSingleChannelJob(
        originalJob: CommunicationJobData,
        channel: string
    ): SingleChannelJobData {
        return {
            tenantId: originalJob.tenantId,
            userId: originalJob.userId,
            channel: channel as any,
            recipient: this.getRecipientForChannel(originalJob.recipients, channel),
            variables: originalJob.variables,
            priority: originalJob.priority,
            caseId: originalJob.caseId,
            scheduledAt: originalJob.scheduledAt,
            metadata: {
                ...originalJob.metadata,
                originalJobId: originalJob.metadata?.jobId,
                isMultiChannelChild: true
            }
        };
    }

    private async processChannelJob(channelJob: SingleChannelJobData): Promise<any> {
        // This would typically queue the job to the appropriate single-channel queue
        // For now, we'll simulate processing
        this.logger.debug(
            `Processing ${channelJob.channel} for tenant ${channelJob.tenantId}, recipient: ${channelJob.recipient}`
        );

        // Simulate processing time
        await new Promise((resolve) => setTimeout(resolve, Math.random() * 1000));

        return {
            tenantId: channelJob.tenantId,
            channel: channelJob.channel,
            recipient: channelJob.recipient,
            processedAt: new Date(),
            status: 'sent'
        };
    }

    private getRecipientForChannel(recipients: any, channel: string): string {
        const channelRecipients = recipients[channel];
        return channelRecipients && channelRecipients.length > 0 ? channelRecipients[0] : '';
    }
}
