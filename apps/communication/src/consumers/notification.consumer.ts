import { Logger, Injectable } from '@nestjs/common';
import { SingleChannelJobData } from '@app/common/communication/interfaces/communication-job.interface';
import { QUEUE_NAMES } from '@app/common/constants/queue.constants';
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import { NotificationService } from '@app/common/communication/services/notification.service';
import { CircuitBreakerService } from '@app/common/communication/services/circuit-breaker.service';

@Processor(QUEUE_NAMES.NOTIFICATION)
@Injectable()
export class NotificationProcessor extends WorkerHost {
    protected readonly logger = new Logger(NotificationProcessor.name);

    constructor(
        private readonly notificationService: NotificationService,
        private readonly circuitBreaker: CircuitBreakerService
    ) {
        super();
    }

    async process(job: Job<SingleChannelJobData>) {
        return await this.processJob(job);
    }

    protected async processJob(job: Job<SingleChannelJobData>): Promise<any> {
        const { tenantId, userId, recipient, variables } = job.data;

        this.logger.log(
            `Processing notification job ${job.id?.toString() || 'unknown'} for tenant ${tenantId}, recipient: ${recipient}`
        );

        try {
            // Get user notification preferences to determine which channels to use
            const preferences =
                await this.notificationService.getUserNotificationPreferences(recipient);
            const enabledChannels = Object.entries(preferences)
                .filter(([key, value]) => key !== 'userId' && value === true)
                .map(([key]) => key);

            this.logger.debug(
                `User ${recipient} has enabled channels: ${enabledChannels.join(', ')}`
            );

            // Send notification using the enhanced service
            const results = await this.circuitBreaker.execute(
                'notification-processing',
                () =>
                    this.notificationService.sendNotification(
                        recipient,
                        variables,
                        tenantId,
                        enabledChannels
                    ),
                () =>
                    Promise.resolve([
                        {
                            messageId: `fallback-${Date.now()}`,
                            provider: 'fallback',
                            status: 'sent' as const
                        }
                    ])
            );

            const successfulResults = results.filter((r) => r.status === 'sent');
            const failedResults = results.filter((r) => r.status === 'failed');

            if (successfulResults.length === 0) {
                throw new Error(
                    `All notification providers failed: ${failedResults.map((r) => r.error).join(', ')}`
                );
            }

            this.logger.log(
                `Notification job ${job.id?.toString() || 'unknown'} completed: ${successfulResults.length} sent, ${failedResults.length} failed`
            );

            return {
                channel: COMMUNICATION_CHANNELS.NOTIFICATION,
                recipient,
                messageId: successfulResults[0].messageId,
                status: 'sent',
                sentAt: new Date(),
                tenantId,
                userId,
                results: {
                    successful: successfulResults.length,
                    failed: failedResults.length,
                    providers: results.map((r) => ({ provider: r.provider, status: r.status }))
                }
            };
        } catch (error) {
            this.logger.error(
                `Notification job ${job.id?.toString() || 'unknown'} failed: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }
}
