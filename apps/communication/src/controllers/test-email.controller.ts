import { Controller, Post, Body, HttpCode, HttpStatus, Logger } from '@nestjs/common';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';

interface TestEmailDto {
    to: string;
    caseData?: {
        caseNumber?: string;
        clientName?: string;
        status?: string;
        caseSummary?: string;
        handlerName?: string;
        urgency?: string;
    };
}

@Controller('test')
export class TestEmailController {
    private readonly logger = new Logger(TestEmailController.name);

    constructor(private readonly messageProducer: MessageProducerService) {}

    @Post('send-email')
    @HttpCode(HttpStatus.OK)
    async sendTestEmail(@Body() testData: TestEmailDto) {
        this.logger.log(`Sending test email to: ${testData.to}`);

        const emailJob = {
            tenantId: '5ccb4bd3-60c0-4ad8-8b2c-d438b8bfed70',
            userId: 'test-user-456',
            channels: [COMMUNICATION_CHANNELS.EMAIL],
            recipients: {
                email: [testData.to],
                sms: [],
                notification: []
            },
            variables: {
                type: 'case-update',

                // Law firm details
                tenantName: 'Smith & Associates Law Firm',
                tenantLogo: 'S&A Legal',
                tenantAddress: '123 Chancery Lane, London, EC4A 1NF',
                supportEmail: '<EMAIL>',

                // Client details
                recipientName: 'Allan Nsereko',
                clientName: testData.caseData?.clientName || 'Tech Innovations Ltd.',

                // Case information
                caseNumber: testData.caseData?.caseNumber || 'LCS-2025-06789',
                caseType: 'Corporate Acquisition',
                status: testData.caseData?.status || 'In Progress',
                urgency: testData.caseData?.urgency || 'normal',

                // Case content
                caseSummary:
                    testData.caseData?.caseSummary ||
                    "We have completed the initial due diligence on the target company's financial records and have identified several areas that require further investigation. Our team has scheduled meetings with the target's financial team for next week to clarify certain discrepancies in their Q1 2025 reports. The regulatory compliance review is proceeding as planned, with no major issues identified thus far.",

                additionalDetails:
                    'We anticipate completing this phase by June 15th, after which we will provide you with a comprehensive report of our findings and recommendations.',

                nextSteps: [
                    "Meeting with target company's financial team - scheduled for June 10th, 2025",
                    'Completion of regulatory compliance review - expected by June 15th, 2025',
                    'Draft acquisition agreement review - to be sent by June 20th, 2025'
                ],

                // Handler information
                handlerName: testData.caseData?.handlerName || 'Sarah Richardson',
                handlerTitle: 'Senior Partner, Corporate Acquisitions',
                handlerEmail: '<EMAIL>',
                handlerPhone: '+44 20 7123 4567',

                // Links and dates
                caseUrl: 'https://portal.smithassociates.com/cases/LCS-2025-06789',
                formattedOpeningDate: '15 May 2025',

                // Auto-populated
                currentYear: '2025'
            } as any,
            priority: 10
        };

        try {
            const result = await this.messageProducer.enqueueMessage(emailJob);

            this.logger.log(`Email queued successfully: ${result.jobId}`);

            return {
                success: true,
                message: `Test email queued successfully for ${testData.to}`,
                jobId: result.jobId,
                queueName: result.queueName,
                status: result.status,
                estimatedDelay: result.estimatedDelay
            };
        } catch (error) {
            this.logger.error(`Failed to send test email: ${error.message}`, error.stack);

            return {
                success: false,
                message: `Failed to send test email: ${error.message}`,
                error: error.message
            };
        }
    }

    @Post('send-simple')
    @HttpCode(HttpStatus.OK)
    async sendSimpleTestEmail(@Body() { to }: { to: string }) {
        this.logger.log(`Sending simple test email to: ${to}`);

        const emailJob = {
            tenantId: '5ccb4bd3-60c0-4ad8-8b2c-d438b8bfed70',
            userId: 'test-user-456',
            channels: [COMMUNICATION_CHANNELS.EMAIL],
            recipients: {
                email: [to],
                sms: [],
                notification: []
            },
            variables: {
                type: 'generic-notification',
                tenantName: 'Smith & Associates Law Firm',
                recipientName: 'Allan Nsereko',
                title: 'System Notification',
                message:
                    'This is a test email from the TK-LPM communication system. Both the case-update and generic templates are now working correctly with AWS SES!',
                ctaText: 'View Dashboard',
                ctaUrl: 'https://portal.smithassociates.com/dashboard',
                supportEmail: '<EMAIL>',
                currentYear: '2025'
            } as any,
            priority: 10
        };

        try {
            const result = await this.messageProducer.enqueueMessage(emailJob);

            this.logger.log(`Simple email queued successfully: ${result.jobId}`);

            return {
                success: true,
                message: `Simple test email queued successfully for ${to}`,
                jobId: result.jobId,
                queueName: result.queueName,
                status: result.status
            };
        } catch (error) {
            this.logger.error(`Failed to send simple test email: ${error.message}`, error.stack);

            return {
                success: false,
                message: `Failed to send simple test email: ${error.message}`,
                error: error.message
            };
        }
    }
}
