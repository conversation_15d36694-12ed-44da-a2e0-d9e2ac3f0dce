import {
    Controller,
    Post,
    Get,
    Put,
    Delete,
    Body,
    Param,
    Query,
    HttpCode,
    HttpStatus,
    Logger
} from '@nestjs/common';
import { Roles } from '@app/common/roles/decorators';
import {
    SESTemplateManagerService,
    SESTemplateData,
    SESTemplateResult
} from '@app/common/communication/services/ses-template-manager.service';
import {
    TemplateService,
    UnifiedTemplateConfig
} from '@app/common/communication/services/template.service';

interface CreateTemplateDto {
    templateName: string;
    subject: string;
    htmlPart: string;
    textPart?: string;
}

interface InitializeTemplatesDto {
    templates: CreateTemplateDto[];
    overwriteExisting?: boolean;
}

interface TemplateInitializationResult {
    totalTemplates: number;
    successful: number;
    failed: number;
    results: SESTemplateResult[];
}

@Controller('templates')
// @UseGuards(JwtGuard, RolesGuard) // Temporarily disabled for testing
export class TemplateManagementController {
    private readonly logger = new Logger(TemplateManagementController.name);

    constructor(
        private readonly sesTemplateManager: SESTemplateManagerService,
        private readonly templateService: TemplateService
    ) {}

    @Post('initialize')
    @HttpCode(HttpStatus.OK)
    // @Roles('system_admin', 'tenant_admin')
    async initializeTemplates(
        @Body() initData: InitializeTemplatesDto
    ): Promise<TemplateInitializationResult> {
        this.logger.log(`Initializing ${initData.templates.length} SES templates`);

        const results = await this.sesTemplateManager.initializeDefaultTemplates(
            initData.templates
        );

        const successful = results.filter((r) => !r.error).length;
        const failed = results.filter((r) => r.error).length;

        return {
            totalTemplates: initData.templates.length,
            successful,
            failed,
            results
        };
    }

    @Post('initialize/default')
    @HttpCode(HttpStatus.OK)
    // @Roles('system_admin')
    async initializeDefaultTemplate(): Promise<TemplateInitializationResult> {
        this.logger.log('Initializing default templates (case-update and generic)');

        // Custom case-update template with professional design
        const caseUpdateTemplate: SESTemplateData = {
            templateName: 'case-update-template',
            subject: 'Case Update - {{caseNumber}}',
            htmlPart: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Case Update - {{tenantName}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .email-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .header {
            padding: 30px 40px 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .logo {
            font-size: 28px;
            color: #4a90e2;
            font-weight: 300;
            margin-bottom: 5px;
        }
        
        .company-name {
            color: #666;
            font-size: 14px;
            font-weight: 400;
        }
        
        .content {
            padding: 40px;
        }
        
        .main-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .greeting {
            color: #666;
            margin-bottom: 20px;
        }
        
        .intro-text {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .case-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .case-item {
            margin-bottom: 20px;
        }
        
        .case-label {
            font-size: 12px;
            color: #888;
            margin-bottom: 5px;
            text-transform: uppercase;
            font-weight: 500;
        }
        
        .case-value {
            font-size: 16px;
            color: #333;
            font-weight: 500;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            background-color: {{#if urgency}}{{#eq urgency "high"}}#dc3545{{else}}{{#eq urgency "critical"}}#dc3545{{else}}#28a745{{/eq}}{{/eq}}{{else}}#28a745{{/if}};
            border-radius: 50%;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .case-summary {
            background-color: #f8f9fa;
            border-left: 4px solid #4a90e2;
            padding: 20px;
            margin-bottom: 30px;
            font-size: 14px;
            line-height: 1.6;
            color: #555;
        }
        
        .next-steps {
            margin-bottom: 30px;
        }
        
        .next-steps ul {
            list-style: none;
            padding-left: 0;
        }
        
        .next-steps li {
            position: relative;
            padding-left: 20px;
            margin-bottom: 10px;
            color: #555;
            font-size: 14px;
        }
        
        .next-steps li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: #4a90e2;
            font-weight: bold;
        }
        
        .contact-info {
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .signature {
            margin-bottom: 15px;
        }
        
        .name {
            font-weight: 600;
            color: #333;
        }
        
        .title {
            color: #666;
        }
        
        .contact-details {
            margin-bottom: 20px;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 20px 40px;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #888;
            line-height: 1.4;
        }
        
        .copyright {
            margin-bottom: 5px;
        }
        
        .address {
            margin-bottom: 10px;
        }
        
        .disclaimer {
            font-style: italic;
        }
        
        .urgent-banner {
            background-color: #dc3545;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        @media (max-width: 600px) {
            .case-details {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .footer {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">{{#if tenantLogo}}{{tenantLogo}}{{else}}{{tenantName}}{{/if}}</div>
            <div class="company-name">{{tenantName}}</div>
        </div>
        
        <div class="content">
            {{#if urgency}}{{#eq urgency "high"}}<div class="urgent-banner">🔴 URGENT CASE UPDATE</div>{{/eq}}{{#eq urgency "critical"}}<div class="urgent-banner">🚨 CRITICAL CASE UPDATE</div>{{/eq}}{{/if}}
            
            <h1 class="main-title">Case Update</h1>
            
            <div class="greeting">Dear {{recipientName}},</div>
            
            <div class="intro-text">
                Please find below the latest update regarding your case:
            </div>
            
            <div class="case-details">
                <div class="left-column">
                    <div class="case-item">
                        <div class="case-label">Case Number</div>
                        <div class="case-value">{{caseNumber}}</div>
                    </div>
                    
                    <div class="case-item">
                        <div class="case-label">Client Name</div>
                        <div class="case-value">{{#if clientName}}{{clientName}}{{else}}{{recipientName}}{{/if}}</div>
                    </div>
                    
                    <div class="case-item">
                        <div class="case-label">Case Opening Date</div>
                        <div class="case-value">{{#if formattedOpeningDate}}{{formattedOpeningDate}}{{else}}{{formattedOpeningDate}}{{/if}}</div>
                    </div>
                </div>
                
                <div class="right-column">
                    <div class="case-item">
                        <div class="case-label">Current Status</div>
                        <div class="case-value status">
                            <span class="status-indicator"></span>
                            {{status}}
                        </div>
                    </div>
                    
                    <div class="case-item">
                        <div class="case-label">Case Handler</div>
                        <div class="case-value">{{#if handlerName}}{{handlerName}}{{else}}Legal Team{{/if}}</div>
                    </div>
                    
                    <div class="case-item">
                        <div class="case-label">Case Type</div>
                        <div class="case-value">{{#if caseType}}{{caseType}}{{else}}Legal Matter{{/if}}</div>
                    </div>
                </div>
            </div>
            
            {{#if caseSummary}}
            <h2 class="section-title">Case Summary</h2>
            
            <div class="case-summary">
                {{caseSummary}}
                {{#if additionalDetails}}<br><br>{{additionalDetails}}{{/if}}
            </div>
            {{/if}}
            
            {{#if nextSteps}}
            <h2 class="section-title">Next Steps</h2>
            
            <div class="next-steps">
                {{#if nextStepsArray}}
                <ul>
                    {{#each nextStepsArray}}
                    <li>{{this}}</li>
                    {{/each}}
                </ul>
                {{else}}
                <div style="color: #555; font-size: 14px;">{{nextSteps}}</div>
                {{/if}}
            </div>
            {{/if}}
            
            {{#if courtDate}}
            <h2 class="section-title">Important Dates</h2>
            <div style="background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin-bottom: 30px;">
                <strong>Court Date:</strong> {{courtDate}}{{#if courtTime}} at {{courtTime}}{{/if}}
            </div>
            {{/if}}
            
            <div class="contact-info">
                <div>If you have any questions or require further information, please do not hesitate to contact {{#if handlerName}}me{{else}}us{{/if}} directly.</div>
                <br>
                <div class="signature">
                    <div>Best regards,</div>
                    <br>
                    <div class="name">{{#if handlerName}}{{handlerName}}{{else}}{{tenantName}} Legal Team{{/if}}</div>
                    {{#if handlerTitle}}<div class="title">{{handlerTitle}}</div>{{/if}}
                </div>
                
                <div class="contact-details">
                    {{#if handlerEmail}}Email: {{handlerEmail}}<br>{{/if}}
                    {{#if handlerPhone}}Direct: {{handlerPhone}}<br>{{/if}}
                    {{#if supportEmail}}Support: {{supportEmail}}{{/if}}
                </div>
                
                {{#if caseUrl}}
                <div style="margin-top: 20px; text-align: center;">
                    <a href="{{caseUrl}}" style="background: #4a90e2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: 500;">View Case Details</a>
                </div>
                {{/if}}
            </div>
        </div>
        
        <div class="footer">
            <div class="copyright">© {{currentYear}} {{tenantName}}. All rights reserved.</div>
            {{#if tenantAddress}}<div class="address">{{tenantAddress}}</div>{{/if}}
            <div class="disclaimer">
                This email contains confidential information and is intended solely for the addressee. If you have received this email in error, please notify the sender immediately and delete it from your system.
            </div>
        </div>
    </div>
</body>
</html>`,
            textPart: `{{#if urgency}}{{#eq urgency "high"}}🔴 URGENT CASE UPDATE{{/eq}}{{#eq urgency "critical"}}🚨 CRITICAL CASE UPDATE{{/eq}}

{{/if}}CASE UPDATE

Dear {{recipientName}},

Please find below the latest update regarding your case:

CASE DETAILS:
- Case Number: {{caseNumber}}
- Client Name: {{#if clientName}}{{clientName}}{{else}}{{recipientName}}{{/if}}
- Current Status: {{status}}
- Case Handler: {{#if handlerName}}{{handlerName}}{{else}}Legal Team{{/if}}
- Case Type: {{#if caseType}}{{caseType}}{{else}}Legal Matter{{/if}}
{{#if formattedOpeningDate}}- Case Opening Date: {{formattedOpeningDate}}{{/if}}

{{#if caseSummary}}CASE SUMMARY:
{{caseSummary}}
{{#if additionalDetails}}

{{additionalDetails}}{{/if}}

{{/if}}{{#if nextSteps}}NEXT STEPS:
{{#if nextStepsArray}}{{#each nextStepsArray}}• {{this}}
{{/each}}{{else}}{{nextSteps}}{{/if}}

{{/if}}{{#if courtDate}}IMPORTANT DATES:
Court Date: {{courtDate}}{{#if courtTime}} at {{courtTime}}{{/if}}

{{/if}}If you have any questions or require further information, please do not hesitate to contact {{#if handlerName}}me{{else}}us{{/if}} directly.

Best regards,

{{#if handlerName}}{{handlerName}}{{else}}{{tenantName}} Legal Team{{/if}}
{{#if handlerTitle}}{{handlerTitle}}{{/if}}

{{#if handlerEmail}}Email: {{handlerEmail}}{{/if}}
{{#if handlerPhone}}Direct: {{handlerPhone}}{{/if}}
{{#if supportEmail}}Support: {{supportEmail}}{{/if}}

{{#if caseUrl}}View Case Details: {{caseUrl}}{{/if}}

---
© {{currentYear}} {{tenantName}}. All rights reserved.
{{#if tenantAddress}}{{tenantAddress}}{{/if}}

This email contains confidential information and is intended solely for the addressee.`
        };

        // Generic notification template for other communication types
        const genericTemplate: SESTemplateData = {
            templateName: 'generic-notification-template',
            subject: '{{#if subject}}{{subject}}{{else}}Notification from {{tenantName}}{{/if}}',
            htmlPart: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{#if title}}{{title}}{{else}}Notification{{/if}} - {{tenantName}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .header {
            padding: 30px 40px 20px;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
        }
        
        .logo {
            font-size: 24px;
            color: #4a90e2;
            font-weight: 300;
            margin-bottom: 5px;
        }
        
        .company-name {
            color: #666;
            font-size: 14px;
            font-weight: 400;
        }
        
        .content {
            padding: 40px;
        }
        
        .main-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .greeting {
            color: #666;
            margin-bottom: 20px;
            font-size: 16px;
        }
        
        .message-content {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            color: #333;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        
        .cta-button {
            display: inline-block;
            background-color: #4a90e2;
            color: white;
            text-decoration: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 16px;
        }
        
        .footer {
            padding: 30px 40px;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
            text-align: center;
        }
        
        .contact-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .legal-notice {
            color: #999;
            font-size: 12px;
            line-height: 1.4;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 0 10px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .footer {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">{{#if tenantLogo}}{{tenantLogo}}{{else}}{{tenantName}}{{/if}}</div>
            <div class="company-name">{{tenantName}}</div>
        </div>
        
        <div class="content">
            {{#if title}}<h1 class="main-title">{{title}}</h1>{{/if}}
            
            <div class="greeting">{{#if recipientName}}Dear {{recipientName}},{{else}}Hello,{{/if}}</div>
            
            <div class="message-content">
                {{#if message}}{{message}}{{else}}This is a notification from {{tenantName}}.{{/if}}
            </div>
            
            {{#if ctaText}}
            <div class="cta-section">
                {{#if ctaUrl}}<a href="{{ctaUrl}}" class="cta-button">{{ctaText}}</a>{{else}}<div class="cta-button" style="background-color: #6c757d;">{{ctaText}}</div>{{/if}}
            </div>
            {{/if}}
        </div>
        
        <div class="footer">
            <div class="contact-info">
                {{#if supportEmail}}Support: {{supportEmail}}{{/if}}
                {{#if supportPhone}}{{#if supportEmail}} | {{/if}}Phone: {{supportPhone}}{{/if}}
            </div>
            
            <div class="legal-notice">
                © {{currentYear}} {{tenantName}}. All rights reserved.
                {{#if tenantAddress}}<br>{{tenantAddress}}{{/if}}
                <br><br>This email contains confidential information and is intended solely for the addressee.
            </div>
        </div>
    </div>
</body>
</html>`,
            textPart: `{{#if title}}{{title}}{{else}}Notification{{/if}} - {{tenantName}}

{{#if recipientName}}Dear {{recipientName}},{{else}}Hello,{{/if}}

{{#if message}}{{message}}{{else}}This is a notification from {{tenantName}}.{{/if}}

{{#if ctaText}}{{#if ctaUrl}}{{ctaText}}: {{ctaUrl}}{{else}}{{ctaText}}{{/if}}{{/if}}

{{#if supportEmail}}Support: {{supportEmail}}{{/if}}
{{#if supportPhone}}{{#if supportEmail}} | {{/if}}Phone: {{supportPhone}}{{/if}}

---
© {{currentYear}} {{tenantName}}. All rights reserved.
{{#if tenantAddress}}{{tenantAddress}}{{/if}}

This email contains confidential information and is intended solely for the addressee.`
        };

        const templates = [caseUpdateTemplate, genericTemplate];
        const results = await this.sesTemplateManager.initializeDefaultTemplates(templates);

        return {
            totalTemplates: templates.length,
            successful: results.filter((r) => !r.error).length,
            failed: results.filter((r) => r.error).length,
            results
        };
    }

    @Post()
    @HttpCode(HttpStatus.CREATED)
    @Roles('system_admin', 'tenant_admin')
    async createTemplate(@Body() templateData: CreateTemplateDto): Promise<SESTemplateResult> {
        this.logger.log(`Creating SES template: ${templateData.templateName}`);
        return await this.sesTemplateManager.createTemplate(templateData);
    }

    @Put(':templateName')
    @HttpCode(HttpStatus.OK)
    @Roles('system_admin', 'tenant_admin')
    async updateTemplate(
        @Param('templateName') templateName: string,
        @Body() templateData: Omit<CreateTemplateDto, 'templateName'>
    ): Promise<SESTemplateResult> {
        this.logger.log(`Updating SES template: ${templateName}`);
        return await this.sesTemplateManager.updateTemplate({
            templateName,
            ...templateData
        });
    }

    @Delete(':templateName')
    @HttpCode(HttpStatus.OK)
    @Roles('system_admin', 'tenant_admin')
    async deleteTemplate(@Param('templateName') templateName: string): Promise<SESTemplateResult> {
        this.logger.log(`Deleting SES template: ${templateName}`);
        return await this.sesTemplateManager.deleteTemplate(templateName);
    }

    @Get(':templateName')
    @Roles('system_admin', 'tenant_admin', 'case_manager', 'lawyer')
    async getTemplate(@Param('templateName') templateName: string) {
        this.logger.log(`Getting SES template: ${templateName}`);
        return await this.sesTemplateManager.getTemplate(templateName);
    }

    @Get()
    @Roles('system_admin', 'tenant_admin', 'case_manager', 'lawyer')
    async listTemplates(
        @Query('maxItems') maxItems?: number,
        @Query('nextToken') nextToken?: string
    ) {
        this.logger.log('Listing SES templates');
        return await this.sesTemplateManager.listTemplates(maxItems, nextToken);
    }

    @Get('unified/configurations')
    @Roles('system_admin', 'tenant_admin', 'case_manager', 'lawyer')
    getUnifiedTemplateConfigurations(): Record<string, UnifiedTemplateConfig> {
        this.logger.log('Getting unified template configurations');
        return this.templateService.getUnifiedTemplates();
    }

    @Get('status/providers')
    // @Roles('system_admin', 'tenant_admin')
    getProviderStatus() {
        this.logger.log('Getting template provider status');
        return {
            ses: this.sesTemplateManager.getStatus(),
            enabled: this.sesTemplateManager.isEnabled()
        };
    }

    @Post('sync/unified')
    @HttpCode(HttpStatus.OK)
    @Roles('system_admin')
    async syncUnifiedTemplates(): Promise<TemplateInitializationResult> {
        this.logger.log('Synchronizing unified templates with SES');

        const unifiedTemplates = this.templateService.getUnifiedTemplates();
        const sesTemplates: SESTemplateData[] = [];

        // Create basic templates for each unified template
        // In production, you'd load actual HTML templates from files or database
        for (const [templateType, config] of Object.entries(unifiedTemplates)) {
            sesTemplates.push({
                templateName: config.sesTemplateName,
                subject: config.defaultSubject,
                htmlPart: this.generateBasicHtmlTemplate(templateType, config),
                textPart: this.generateBasicTextTemplate(templateType, config)
            });
        }

        const results = await this.sesTemplateManager.initializeDefaultTemplates(sesTemplates);

        return {
            totalTemplates: sesTemplates.length,
            successful: results.filter((r) => !r.error).length,
            failed: results.filter((r) => r.error).length,
            results
        };
    }

    private generateBasicHtmlTemplate(templateType: string, config: UnifiedTemplateConfig): string {
        // Basic HTML template generator - in production, load from files
        return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${config.defaultSubject}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: #f8f9fa; padding: 30px; border-radius: 8px;">
        <h2 style="color: #333; margin-top: 0; text-align: center;">${config.defaultSubject}</h2>
        <p>Dear {{recipientName}},</p>
        
        <p>This is a ${templateType} notification from {{tenantName}}.</p>
        
        ${config.requiredVariables
            .map(
                (variable) =>
                    `<p><strong>${this.formatVariableName(variable)}:</strong> {{${variable}}}</p>`
            )
            .join('')}
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <p>Best regards,<br><strong>{{tenantName}}</strong></p>
            {{#if supportEmail}}<p style="margin-top: 20px; font-size: 12px; color: #666;">For support: <a href="mailto:{{supportEmail}}">{{supportEmail}}</a></p>{{/if}}
        </div>
    </div>
</body>
</html>`;
    }

    private generateBasicTextTemplate(templateType: string, config: UnifiedTemplateConfig): string {
        return `Dear {{recipientName}},

This is a ${templateType} notification from {{tenantName}}.

${config.requiredVariables.map((variable) => `${this.formatVariableName(variable)}: {{${variable}}}`).join('\n')}

Best regards,
{{tenantName}}

{{#if supportEmail}}For support: {{supportEmail}}{{/if}}`;
    }

    private formatVariableName(variable: string): string {
        return variable
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, (str) => str.toUpperCase())
            .trim();
    }
}
