import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Config } from '@app/common';
import { Logger } from '@nestjs/common';
import { instance } from '@app/common/utils';
import { WinstonModule } from 'nest-winston';

async function bootstrap() {
    const app = await NestFactory.create(AppModule, {
        logger: WinstonModule.createLogger({
            instance: instance
        })
    });
    app.setGlobalPrefix(Config.COMMUNICATION_PREFIX);
    await app.listen(Config.COMMUNICATION_PORT!);
    Logger.log(`Communication microservice running on ${await app.getUrl()}`);
}
bootstrap();
// Rebuild trigger: HealthAggregator fix in CommonModule - 20251014-022856
