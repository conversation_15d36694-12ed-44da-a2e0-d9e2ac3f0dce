# Redis ACL configuration for TK-LPM Communication Service
# Defines user access controls for secure queue processing

# Default user - disabled for security
user default off

# Communication service user - full access to communication queues
user communication_service on >${REDIS_PASSWORD} ~communication:* ~email:* ~notification:* ~bull:* +@all -flushdb -flushall -keys -config -shutdown -debug -eval

# Queue monitoring user - read-only access for monitoring tools
user queue_monitor on >${REDIS_MONITOR_PASSWORD:-monitor123} ~communication:* ~email:* ~notification:* ~bull:* +@read +info +ping +client +memory -@write -@dangerous

# Health check user - minimal access for health checks
user health_checker on >${REDIS_HEALTH_PASSWORD:-health123} ~* +ping +info +client -@all

# Admin user - full system access (use sparingly)
user redis_admin on >${REDIS_ADMIN_PASSWORD:-admin456} ~* +@all

# BullMQ specific user - optimized for queue operations
user bullmq_worker on >${REDIS_BULLMQ_PASSWORD:-bullmq789} ~bull:* ~communication:* ~email:* ~notification:* +@all -flushdb -flushall -keys -config -shutdown -debug

# Application read-only user - for metrics and monitoring
user app_readonly on >${REDIS_READONLY_PASSWORD:-readonly321} ~* +@read +ping +info +client +memory -@write -@dangerous