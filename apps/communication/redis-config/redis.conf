# Redis configuration for TK-LPM Communication Service
# Optimized for BullMQ queue processing and email communications

# Network settings
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300

# General settings
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile /var/log/redis/redis.log
databases 16

# Security
requirepass placeholder_password
# ACL users file will be loaded separately
aclfile /usr/local/etc/redis/users.acl

# Memory management - optimized for queue processing
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Persistence - balanced for queue reliability
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF (Append Only File) - important for queue durability
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Communication service specific optimizations
# Increase client output buffer limits for bulk operations
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Queue processing optimizations
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# Performance tuning for email queue processing
hz 10
dynamic-hz yes
latency-monitor-threshold 100

# Enable keyspace notifications for BullMQ
notify-keyspace-events Ex

# Disable dangerous commands in production
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_9a1b2c3d4e5f"
rename-command SHUTDOWN "SHUTDOWN_9a1b2c3d4e5f"
rename-command DEBUG ""
rename-command EVAL ""

# Connection limits
maxclients 10000

# Slow log configuration
slowlog-log-slower-than 10000
slowlog-max-len 128

# Advanced memory optimization
activerehashing yes
rdbsave-incremental-fsync yes

# Lua script settings
lua-time-limit 5000

# Enable protected mode
protected-mode yes