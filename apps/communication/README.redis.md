# Redis Configuration for Communication Service

This directory contains Redis-specific configuration for the TK-LPM Communication Service, optimized for email queue processing and message handling.

## Files

### `redis.Dockerfile`
Custom Redis Docker image with:
- Communication service optimizations
- Security hardening
- Health checks
- Monitoring capabilities

### `redis-config/redis.conf`
Redis configuration optimized for:
- BullMQ queue processing
- Email communication workflows
- Memory efficiency
- Persistence and durability
- Security (disabled dangerous commands)

### `redis-config/users.acl`
Access Control List defining users:
- `communication_service` - Full queue access
- `queue_monitor` - Read-only monitoring
- `health_checker` - Health check access
- `redis_admin` - Full system access
- `bullmq_worker` - Queue worker access
- `app_readonly` - Application metrics access

### `docker-compose.redis.yml`
Standalone Redis setup with:
- Communication-specific Redis instance
- Optional Redis Insight (GUI)
- Optional Redis Exporter (Prometheus monitoring)
- Resource limits and security settings

## Usage

### Option 1: Use Existing Redis (Recommended)
The main `docker-compose.yml` already includes Redis. The communication service will connect to this shared instance.

### Option 2: Dedicated Redis Instance
For isolated Redis or development:

```bash
# Start dedicated Redis for communication
cd apps/communication
docker-compose -f docker-compose.redis.yml up -d

# Stop
docker-compose -f docker-compose.redis.yml down
```

### Option 3: Development with Monitoring
```bash
# Start with monitoring tools
docker-compose -f docker-compose.redis.yml --profile development up -d

# Access Redis Insight: http://localhost:8001
# Access Redis metrics: http://localhost:9121/metrics
```

## Environment Variables

Add to your `.env` file:

```bash
# Basic Redis settings
REDIS_PASSWORD=your_secure_password
REDIS_PORT=6379
REDIS_MAXMEMORY=512mb
REDIS_MAXMEMORY_POLICY=allkeys-lru

# ACL user passwords (use strong passwords in production)
REDIS_MONITOR_PASSWORD=monitor_secure_password
REDIS_HEALTH_PASSWORD=health_secure_password  
REDIS_ADMIN_PASSWORD=admin_secure_password
REDIS_BULLMQ_PASSWORD=bullmq_secure_password
REDIS_READONLY_PASSWORD=readonly_secure_password

# Optional settings
REDIS_LOG_LEVEL=notice
REDIS_APPENDONLY=yes
REDIS_APPENDFSYNC=everysec
```

## Security Features

1. **ACL Users**: Role-based access control
2. **Disabled Commands**: Dangerous commands disabled
3. **Protected Mode**: Network security enabled
4. **Password Authentication**: Required for all connections
5. **Resource Limits**: Memory and CPU constraints

## Monitoring

### Health Checks
- Built-in health check script
- Monitors Redis connectivity
- Automatic container restart on failure

### Metrics (with Redis Exporter)
- Queue metrics
- Memory usage
- Connection statistics
- Performance metrics

### Logging
- Configurable log levels
- Persistent log storage
- Queue operation logging

## Queue Optimization

Configuration optimized for:
- **BullMQ Compatibility**: Keyspace notifications enabled
- **Memory Management**: LRU eviction for queues
- **Persistence**: AOF + RDB for durability
- **Performance**: Optimized hash/list settings
- **Bulk Operations**: Increased output buffer limits

## Production Considerations

1. **Memory Sizing**: Adjust `REDIS_MAXMEMORY` based on queue volume
2. **Persistence**: Consider RDB vs AOF based on durability needs
3. **Monitoring**: Enable Redis Exporter for production metrics
4. **Security**: Use strong passwords for all ACL users
5. **Backup**: Regular backup of Redis data volume
6. **Scaling**: Consider Redis Cluster for high availability

## Troubleshooting

### Connection Issues
```bash
# Test Redis connectivity
docker exec tk-lpm-communication-redis redis-cli -a your_password ping

# Check logs
docker logs tk-lpm-communication-redis

# Monitor real-time commands
docker exec tk-lpm-communication-redis redis-cli -a your_password monitor
```

### Queue Monitoring
```bash
# List all keys
docker exec tk-lpm-communication-redis redis-cli -a your_password --scan

# Monitor queue operations
docker exec tk-lpm-communication-redis redis-cli -a your_password monitor | grep "bull:"

# Check queue stats
docker exec tk-lpm-communication-redis redis-cli -a your_password info memory
```

### Performance Tuning
- Monitor memory usage and adjust limits
- Check slow query log for optimization opportunities
- Adjust persistence settings based on requirements
- Use `redis-cli --latency` to monitor response times

## Integration with Communication Service

The communication service connects using these environment variables:
```bash
REDIS_HOST=communication-redis  # or localhost for shared Redis
REDIS_PORT=6379
REDIS_PASSWORD=your_password
REDIS_DB=0  # Default database
```

BullMQ will automatically create queues:
- `communication:email`
- `communication:notification` 
- `communication:sms`

Monitor these queues through Redis Insight or command line tools.