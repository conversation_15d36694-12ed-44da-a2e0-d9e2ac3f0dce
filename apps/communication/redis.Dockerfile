# Redis Dockerfile for TK-LPM Communication Service
# Optimized for queue processing and session management

FROM redis:7-alpine

# Set maintainer
LABEL maintainer="TK-LPM Team"
LABEL service="communication-redis"
LABEL version="1.0.0"

# Install additional tools for monitoring and debugging
RUN apk add --no-cache \
    curl \
    wget \
    bash

# Use existing redis user and prepare directories

# Create necessary directories
RUN mkdir -p /usr/local/etc/redis /var/log/redis /data && \
    chown -R redis:redis /usr/local/etc/redis /var/log/redis /data

# Copy custom Redis configuration
COPY apps/communication/redis-config/redis.conf /usr/local/etc/redis/redis.conf
COPY apps/communication/redis-config/users.acl /usr/local/etc/redis/users.acl

# Set appropriate permissions
RUN chmod 640 /usr/local/etc/redis/redis.conf && \
    chmod 640 /usr/local/etc/redis/users.acl && \
    chown redis:redis /usr/local/etc/redis/redis.conf && \
    chown redis:redis /usr/local/etc/redis/users.acl

# Create health check script
RUN echo '#!/bin/bash' > /usr/local/bin/healthcheck.sh && \
    echo 'redis-cli -a "$REDIS_PASSWORD" ping > /dev/null 2>&1' >> /usr/local/bin/healthcheck.sh && \
    echo 'if [ $? -eq 0 ]; then' >> /usr/local/bin/healthcheck.sh && \
    echo '  echo "Redis is healthy"' >> /usr/local/bin/healthcheck.sh && \
    echo '  exit 0' >> /usr/local/bin/healthcheck.sh && \
    echo 'else' >> /usr/local/bin/healthcheck.sh && \
    echo '  echo "Redis is not responding"' >> /usr/local/bin/healthcheck.sh && \
    echo '  exit 1' >> /usr/local/bin/healthcheck.sh && \
    echo 'fi' >> /usr/local/bin/healthcheck.sh && \
    chmod +x /usr/local/bin/healthcheck.sh

# Create startup script for communication service specific settings
RUN echo '#!/bin/bash' > /usr/local/bin/start-communication-redis.sh && \
    echo 'echo "Starting Redis for TK-LPM Communication Service..."' >> /usr/local/bin/start-communication-redis.sh && \
    echo 'echo "Configuration: /usr/local/etc/redis/redis.conf"' >> /usr/local/bin/start-communication-redis.sh && \
    echo 'echo "Data directory: /data"' >> /usr/local/bin/start-communication-redis.sh && \
    echo 'echo "Log level: ${REDIS_LOG_LEVEL:-notice}"' >> /usr/local/bin/start-communication-redis.sh && \
    echo 'echo "Memory limit: ${REDIS_MAXMEMORY:-512mb}"' >> /usr/local/bin/start-communication-redis.sh && \
    echo '' >> /usr/local/bin/start-communication-redis.sh && \
    echo '# Start Redis with communication-optimized settings' >> /usr/local/bin/start-communication-redis.sh && \
    echo 'exec redis-server /usr/local/etc/redis/redis.conf \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --bind 0.0.0.0 \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --port ${REDIS_PORT:-6379} \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --requirepass "${REDIS_PASSWORD}" \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --maxmemory ${REDIS_MAXMEMORY:-512mb} \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --maxmemory-policy ${REDIS_MAXMEMORY_POLICY:-allkeys-lru} \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --loglevel ${REDIS_LOG_LEVEL:-notice} \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --logfile /var/log/redis/redis.log \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --appendonly ${REDIS_APPENDONLY:-yes} \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --appendfsync ${REDIS_APPENDFSYNC:-everysec} \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --save 900 1 \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --save 300 10 \' >> /usr/local/bin/start-communication-redis.sh && \
    echo '  --save 60 10000' >> /usr/local/bin/start-communication-redis.sh && \
    chmod +x /usr/local/bin/start-communication-redis.sh

# Switch to redis user
USER redis

# Expose Redis port
EXPOSE 6379

# Health check optimized for communication service
HEALTHCHECK --interval=10s --timeout=3s --start-period=5s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# Set default environment variables for communication service
ENV REDIS_PORT=6379
ENV REDIS_MAXMEMORY=512mb
ENV REDIS_MAXMEMORY_POLICY=allkeys-lru
ENV REDIS_LOG_LEVEL=notice
ENV REDIS_APPENDONLY=yes
ENV REDIS_APPENDFSYNC=everysec

# Start Redis with communication-specific configuration
CMD ["/usr/local/bin/start-communication-redis.sh"]