# Separate Redis service for TK-LPM Communication Service
# Use this for dedicated Redis instances or when running communication service independently

version: '3.8'

services:
  communication-redis:
    build:
      context: ../../
      dockerfile: ./apps/communication/redis.Dockerfile
    container_name: tk-lpm-communication-redis
    restart: unless-stopped
    env_file:
      - ../../.env
    environment:
      # Redis connection settings
      REDIS_PORT: ${REDIS_PORT:-6379}
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      
      # Memory and performance settings
      REDIS_MAXMEMORY: ${REDIS_MAXMEMORY:-512mb}
      REDIS_MAXMEMORY_POLICY: ${REDIS_MAXMEMORY_POLICY:-allkeys-lru}
      
      # Logging and monitoring
      REDIS_LOG_LEVEL: ${REDIS_LOG_LEVEL:-notice}
      
      # Persistence settings
      REDIS_APPENDONLY: ${REDIS_APPENDONLY:-yes}
      REDIS_APPENDFSYNC: ${REDIS_APPENDFSYNC:-everysec}
      
      # ACL user passwords (use strong passwords in production)
      REDIS_MONITOR_PASSWORD: ${REDIS_MONITOR_PASSWORD:-monitor123}
      REDIS_HEALTH_PASSWORD: ${REDIS_HEALTH_PASSWORD:-health123}
      REDIS_ADMIN_PASSWORD: ${REDIS_ADMIN_PASSWORD:-admin456}
      REDIS_BULLMQ_PASSWORD: ${REDIS_BULLMQ_PASSWORD:-bullmq789}
      REDIS_READONLY_PASSWORD: ${REDIS_READONLY_PASSWORD:-readonly321}
    
    ports:
      - "${REDIS_PORT:-6379}:6379"
    
    volumes:
      # Persistent data storage
      - communication_redis_data:/data
      
      # Configuration files
      - ./redis-config/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - ./redis-config/users.acl:/usr/local/etc/redis/users.acl:ro
      
      # Log directory
      - communication_redis_logs:/var/log/redis
    
    networks:
      - communication-network
    
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    
    # Resource limits for production
    deploy:
      resources:
        limits:
          memory: 768M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
    
    # Security settings
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=50M

  # Optional: Redis Insight for GUI management (development only)
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: tk-lpm-redis-insight
    restart: unless-stopped
    profiles:
      - development
      - monitoring
    ports:
      - "8001:8001"
    environment:
      - RITRUSTEDORIGINS=http://localhost:8001
    volumes:
      - redis_insight_data:/db
    depends_on:
      - communication-redis
    networks:
      - communication-network

  # Optional: Redis Exporter for Prometheus monitoring
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: tk-lpm-redis-exporter
    restart: unless-stopped
    profiles:
      - monitoring
      - production
    environment:
      REDIS_ADDR: "redis://communication-redis:6379"
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      REDIS_USER: "queue_monitor"
    ports:
      - "9121:9121"
    depends_on:
      - communication-redis
    networks:
      - communication-network

volumes:
  communication_redis_data:
    name: tk-lpm-communication-redis-data
    driver: local
  
  communication_redis_logs:
    name: tk-lpm-communication-redis-logs
    driver: local
  
  redis_insight_data:
    name: tk-lpm-redis-insight-data
    driver: local

networks:
  communication-network:
    name: tk-lpm-communication-network
    driver: bridge
    ipam:
      config:
        - subnet: **********/16