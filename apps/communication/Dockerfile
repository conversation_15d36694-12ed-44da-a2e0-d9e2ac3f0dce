# Build stage
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Run tests as a build-time gate
RUN echo "::group::Running Tests" && \
    if grep -q "\"test:communication\":" package.json; then \
      echo "Running service-specific tests" && \
      yarn test:communication --passWithNoTests || exit 1; \
    elif grep -q "\"test\":" package.json; then \
      echo "Running general tests" && \
      yarn test --passWithNoTests || exit 1; \
    else \
      echo "No test script found, skipping tests"; \
    fi && \
    echo "::endgroup::"

# Build the application
RUN echo "::group::Building Application" && \
    if grep -q "\"build:communication\":" package.json; then \
      echo "Running service-specific build" && \
      yarn build:communication; \
    else \
      echo "Running general build" && \
      yarn build; \
    fi && \
    echo "::endgroup::"

# Production stage
FROM node:20-alpine AS production

# Set environment variables
ENV NODE_ENV=production

# Set working directory
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./

# Install production dependencies only
RUN yarn install --production --frozen-lockfile

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/libs ./libs
COPY --from=builder /app/nest-cli.json ./

# Expose the service port (will be overridden by environment variable)
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
  CMD wget -qO- http://localhost:${PORT:-3001}/api/communication/health || exit 1

# Start the service
CMD if grep -q "\"start:communication:prod\":" package.json; then \
      yarn start:communication:prod; \
    elif grep -q "\"start:prod\":" package.json; then \
      yarn start:prod; \
    else \
      node dist/apps/communication/main.js; \
    fi
