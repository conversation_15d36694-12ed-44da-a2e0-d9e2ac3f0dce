#!/bin/sh
set -e

# Wait for Keycloak to be ready
echo "Waiting for Key<PERSON>loak to be available..."
MAX_RETRY=30
RETRY_INTERVAL=5

for i in $(seq 1 $MAX_RETRY); do
  if wget -q --spider http://keycloak:8080/ 2>/dev/null; then
    echo "Keycloak is available!"
    break
  fi

  echo "Waiting for Keycloak... (attempt $i/$MAX_RETRY)"

  if [ $i -eq $MAX_RETRY ]; then
    echo "Keycloak is not available after $MAX_RETRY attempts, but continuing anyway..."
  fi

  sleep $RETRY_INTERVAL
done

# Wait for postgres to be ready
echo "Waiting for PostgreSQL to be available..."
for i in $(seq 1 $MAX_RETRY); do
  if pg_isready -h postgres -U postgres > /dev/null 2>&1; then
    echo "PostgreSQL is available!"
    break
  fi

  echo "Waiting for PostgreSQL... (attempt $i/$MAX_RETRY)"

  if [ $i -eq $MAX_RETRY ]; then
    echo "PostgreSQL is not available after $MAX_RETRY attempts, but continuing anyway..."
  fi

  sleep $RETRY_INTERVAL
done

# Set up extra debugging
export NODE_OPTIONS="--unhandled-rejections=strict"
export DEBUG=*

# Start the application
echo "Starting auth service..."
if grep -q "\"start:auth:prod\":" package.json; then
  exec yarn start:auth:prod
elif grep -q "\"start:prod\":" package.json; then
  exec yarn start:prod
else
  # Check if the main.js file exists
  if [ -f "dist/apps/auth/main.js" ]; then
    echo "Found main.js file at dist/apps/auth/main.js"
    exec node dist/apps/auth/main.js
  else
    echo "ERROR: Could not find dist/apps/auth/main.js"
    echo "Contents of dist directory:"
    find dist -type f | sort
    exit 1
  fi
fi