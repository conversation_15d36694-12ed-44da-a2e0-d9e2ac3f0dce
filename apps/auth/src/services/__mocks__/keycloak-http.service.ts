import { KeycloakHttpError } from '../keycloak-http.service';
import axios, { AxiosError, AxiosInstance } from 'axios';

/**
 * Mock implementation of Keycloak error types and service for unit tests
 */
export enum KeycloakErrorType {
    NETWORK = 'NETWORK',
    CLIENT = 'CLIENT',
    SERVER = 'SERVER',
    UNAUTHORIZED = 'UNAUTHORIZED',
    NOT_FOUND = 'NOT_FOUND',
    TIMEOUT = 'TIMEOUT',
    UNKNOWN = 'UNKNOWN'
}

export interface HealthCheckResult {
    status: 'UP' | 'DOWN';
    message?: string;
    details?: any;
}

/**
 * Mock implementation of KeycloakHttpService that replaces the real
 * service during unit testing
 */
export class KeycloakHttpService {
    private readonly axiosInstance: AxiosInstance;

    // Metrics tracking
    private metrics = {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        requestLatencies: [] as number[],
        lastRequestTime: null as number | null,
        errorCounts: {
            [KeycloakErrorType.NETWORK]: 0,
            [KeycloakErrorType.CLIENT]: 0,
            [KeycloakErrorType.SERVER]: 0,
            [KeycloakErrorType.UNAUTHORIZED]: 0,
            [KeycloakErrorType.NOT_FOUND]: 0,
            [KeycloakErrorType.TIMEOUT]: 0,
            [KeycloakErrorType.UNKNOWN]: 0
        }
    };

    constructor() {
        // Create a mock axios instance
        this.axiosInstance = axios.create({
            baseURL: 'http://mock-keycloak:8080',
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        // Ensure interceptors are properly initialized
        // This is critical for fixing the interceptor issues
        if (!this.axiosInstance.interceptors) {
            this.axiosInstance.interceptors = {
                request: {
                    use: jest.fn(() => 0),
                    eject: jest.fn(),
                    clear: jest.fn()
                },
                response: {
                    use: jest.fn(() => 0),
                    eject: jest.fn(),
                    clear: jest.fn()
                }
            };
        } else {
            // Ensure use methods are mocked
            if (typeof this.axiosInstance.interceptors.request.use !== 'function') {
                this.axiosInstance.interceptors.request.use = jest.fn().mockReturnValue(0);
            }
            if (typeof this.axiosInstance.interceptors.response.use !== 'function') {
                this.axiosInstance.interceptors.response.use = jest.fn().mockReturnValue(0);
            }
        }

        // Add request interceptor - same as in real implementation
        this.axiosInstance.interceptors.request.use(
            (config) => {
                config.headers = config.headers || {};
                config.headers['request-start-time'] = Date.now().toString();
                this.metrics.totalRequests++;
                this.metrics.lastRequestTime = Date.now();
                return config;
            },
            (error: Error | AxiosError) => {
                return axios.isAxiosError(error) ? Promise.reject(error) : Promise.reject(error);
            }
        );

        // Add response interceptor - same as in real implementation
        this.axiosInstance.interceptors.response.use(
            (response) => {
                this.metrics.successfulRequests++;
                this.onRequestSuccess();
                return response;
            },
            (error: Error | AxiosError) => {
                this.metrics.failedRequests++;
                this.onRequestFailure();
                return axios.isAxiosError(error) ? Promise.reject(error) : Promise.reject(error);
            }
        );
    }

    resetCircuitBreaker = jest.fn();
    getCircuitState = jest.fn().mockReturnValue({ state: 'CLOSED', failureCount: 0 });

    getMetrics = jest.fn().mockReturnValue({
        requestCount: 0,
        successRate: 100,
        avgLatency: 0,
        errorBreakdown: {},
        circuitState: 'CLOSED'
    });

    resetMetrics = jest.fn();

    checkHealth = jest.fn().mockResolvedValue({
        status: 'UP',
        details: { circuitState: 'CLOSED' }
    });

    getInstance = jest.fn().mockImplementation(() => this.axiosInstance);

    // Internal methods that might be called in tests
    mapAxiosError = jest.fn().mockImplementation((error): KeycloakHttpError => {
        return {
            type: KeycloakErrorType.UNKNOWN,
            message: 'Mock error',
            originalError: error
        };
    });

    startPeriodicHealthCheck = jest.fn();

    // Additional private methods
    private onRequestSuccess = jest.fn();
    private onRequestFailure = jest.fn();
}
