import { Injectable, Logger } from '@nestjs/common';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import {
    WelcomeVariables,
    PasswordResetVariables,
    UserInvitationVariables,
    GenericNotificationVariables
} from '@app/common/communication/interfaces/communication-job.interface';

/**
 * Service for handling authentication-related notifications
 */
@Injectable()
export class AuthNotificationService {
    private readonly logger = new Logger(AuthNotificationService.name);

    constructor(private readonly messageProducer: MessageProducerService) {}

    /**
     * Sends welcome notification to newly created user
     */
    async sendWelcomeNotification(
        userEmail: string,
        userName: string,
        tenantName: string,
        tenantId: string,
        loginUrl: string,
        inviterName?: string,
        temporaryPassword?: string
    ): Promise<void> {
        try {
            const variables: WelcomeVariables = {
                type: 'welcome',
                tenantName,
                recipientName: userName,
                loginUrl,
                inviterName,
                welcomeMessage: temporaryPassword
                    ? 'Welcome to the platform! Please log in with your temporary password and change it on first login.'
                    : 'Welcome to the platform! You can now log in with your credentials.',
                accountSetupUrl: loginUrl,
                currentYear: new Date().getFullYear().toString()
            };

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: [userEmail]
                },
                variables,
                metadata: {
                    notificationType: 'user-welcome',
                    hasTemporaryPassword: !!temporaryPassword
                }
            });

            this.logger.log(`Welcome notification sent to ${userEmail} for tenant ${tenantName}`);
        } catch (error) {
            this.logger.error(
                `Failed to send welcome notification to ${userEmail}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Sends password reset notification
     */
    async sendPasswordResetNotification(
        userEmail: string,
        userName: string,
        tenantName: string,
        tenantId: string,
        resetUrl: string,
        expirationTime?: string
    ): Promise<void> {
        try {
            const variables: PasswordResetVariables = {
                type: 'password-reset',
                tenantName,
                recipientName: userName,
                resetUrl,
                expirationTime: expirationTime || '24 hours',
                securityTip:
                    'If you did not request this password reset, please ignore this email and contact support if you have concerns.',
                currentYear: new Date().getFullYear().toString()
            };

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: [userEmail]
                },
                variables,
                metadata: {
                    notificationType: 'password-reset'
                }
            });

            this.logger.log(`Password reset notification sent to ${userEmail}`);
        } catch (error) {
            this.logger.error(
                `Failed to send password reset notification to ${userEmail}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Sends user invitation notification
     */
    async sendUserInvitationNotification(
        userEmail: string,
        userName: string,
        tenantName: string,
        tenantId: string,
        inviteUrl: string,
        inviterName: string,
        roleGroupKey: string
    ): Promise<void> {
        try {
            const variables: UserInvitationVariables = {
                type: 'user-invitation',
                tenantName,
                recipientName: userName,
                inviteUrl,
                inviterName,
                role: this.formatRoleGroupName(roleGroupKey),
                loginUrl: inviteUrl,
                currentYear: new Date().getFullYear().toString()
            };

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: [userEmail]
                },
                variables,
                metadata: {
                    notificationType: 'user-invitation',
                    roleGroupKey
                }
            });

            this.logger.log(
                `User invitation sent to ${userEmail} for role ${roleGroupKey} in tenant ${tenantName}`
            );
        } catch (error) {
            this.logger.error(
                `Failed to send user invitation to ${userEmail}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Sends tenant creation confirmation to admin
     */
    async sendTenantCreationNotification(
        adminEmail: string,
        adminName: string,
        tenantName: string,
        tenantId: string,
        loginUrl: string,
        clientId: string,
        temporaryPassword?: string
    ): Promise<void> {
        try {
            const variables: GenericNotificationVariables = {
                type: 'generic-notification',
                tenantName,
                recipientName: adminName,
                message: `Congratulations! Your legal practice management system "${tenantName}" has been successfully created. You can now access your system using the credentials provided. ${
                    temporaryPassword
                        ? 'Please change your password on first login for security.'
                        : ''
                } Access your system at: ${loginUrl}`,
                supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
                currentYear: new Date().getFullYear().toString()
            };

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: [adminEmail]
                },
                variables,
                metadata: {
                    notificationType: 'tenant-created',
                    clientId
                }
            });

            this.logger.log(`Tenant creation notification sent to ${adminEmail} for ${tenantName}`);
        } catch (error) {
            this.logger.error(
                `Failed to send tenant creation notification to ${adminEmail}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Sends role promotion notification
     */
    async sendRolePromotionNotification(
        userEmail: string,
        userName: string,
        tenantName: string,
        tenantId: string,
        newRoleGroup: string,
        promotedBy: string
    ): Promise<void> {
        try {
            const variables: GenericNotificationVariables = {
                type: 'generic-notification',
                tenantName,
                recipientName: userName,
                message: `Your role in ${tenantName} has been updated to ${this.formatRoleGroupName(
                    newRoleGroup
                )} by ${promotedBy}. You may now have access to additional features and capabilities. Access your system at: ${process.env.CLIENT_PORTAL_URL || '#'}`,
                currentYear: new Date().getFullYear().toString()
            };

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: [userEmail],
                    notification: [userName] // Assuming username can be used for in-app notifications
                },
                variables,
                metadata: {
                    notificationType: 'role-promotion',
                    newRoleGroup,
                    promotedBy
                }
            });

            this.logger.log(`Role promotion notification sent to ${userEmail}`);
        } catch (error) {
            this.logger.error(
                `Failed to send role promotion notification to ${userEmail}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Sends security alert notification
     */
    async sendSecurityAlertNotification(
        userEmail: string,
        userName: string,
        tenantName: string,
        tenantId: string,
        alertType: string,
        alertMessage: string,
        ipAddress?: string
    ): Promise<void> {
        try {
            const variables: GenericNotificationVariables = {
                type: 'generic-notification',
                tenantName,
                recipientName: userName,
                message: `Security Alert: ${alertMessage}${
                    ipAddress ? ` from IP address ${ipAddress}` : ''
                }. If this was not you, please contact support immediately at ${process.env.SUPPORT_EMAIL || '<EMAIL>'} or review your account security at: ${process.env.CLIENT_PORTAL_URL || '#'}`,
                supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
                currentYear: new Date().getFullYear().toString()
            };

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: [userEmail],
                    notification: [userName]
                },
                variables,
                metadata: {
                    notificationType: 'security-alert',
                    alertType,
                    ipAddress
                }
            });

            this.logger.log(`Security alert sent to ${userEmail}: ${alertType}`);
        } catch (error) {
            this.logger.error(
                `Failed to send security alert to ${userEmail}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Formats role group key to human-readable name
     */
    private formatRoleGroupName(roleGroupKey: string): string {
        const roleMap: Record<string, string> = {
            super_admin: 'Super Administrator',
            admin: 'Administrator',
            lawyer: 'Lawyer',
            paralegal: 'Paralegal',
            secretary: 'Secretary',
            client: 'Client',
            conveyancer: 'Conveyancer'
        };

        return (
            roleMap[roleGroupKey] ||
            roleGroupKey.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())
        );
    }
}
