import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { KeycloakHttpService, KeycloakErrorType } from './keycloak-http.service';
import { createMockConfigService } from '@app/common/testing';
import { HttpStatusCode } from '@app/common/enums/http-status.enum';

// Create mock functions
const mockGet = jest.fn();
const mockPost = jest.fn();
const mockPut = jest.fn();
const mockDelete = jest.fn();
const mockResetCircuitBreaker = jest.fn();
const mockGetCircuitState = jest.fn();
const mockGetMetrics = jest.fn();
const mockResetMetrics = jest.fn();
const mockCheckHealth = jest.fn();

// Mock the entire service
jest.mock('./keycloak-http.service', () => {
    return {
        KeycloakErrorType: {
            NETWORK: 'NETWORK',
            CLIENT: 'CLIENT',
            SERVER: 'SERVER',
            UNAUTHORIZED: 'UNAUTHORIZED',
            NOT_FOUND: 'NOT_FOUND',
            TIMEOUT: 'TIMEOUT',
            UNKNOWN: 'UNKNOWN'
        },
        KeycloakHttpService: jest.fn().mockImplementation(() => {
            return {
                get: mockGet,
                post: mockPost,
                put: mockPut,
                delete: mockDelete,
                resetCircuitBreaker: mockResetCircuitBreaker,
                getCircuitState: mockGetCircuitState,
                getMetrics: mockGetMetrics,
                resetMetrics: mockResetMetrics,
                checkHealth: mockCheckHealth,
                // Mock private methods for testing
                mapAxiosError: jest.fn().mockImplementation((error) => {
                    if (error.code === 'ECONNREFUSED') {
                        return {
                            type: KeycloakErrorType.NETWORK,
                            message: 'Network error: Connection refused',
                            originalError: error
                        };
                    } else if (error.code === 'ETIMEDOUT') {
                        return {
                            type: KeycloakErrorType.TIMEOUT,
                            message: 'Request timeout',
                            originalError: error
                        };
                    } else if (error.response?.status === HttpStatusCode.UNAUTHORIZED) {
                        return {
                            type: KeycloakErrorType.UNAUTHORIZED,
                            status: HttpStatusCode.UNAUTHORIZED,
                            message: 'Unauthorized',
                            originalError: error
                        };
                    } else if (error.response?.status === HttpStatusCode.NOT_FOUND) {
                        return {
                            type: KeycloakErrorType.NOT_FOUND,
                            status: HttpStatusCode.NOT_FOUND,
                            message: 'Not found',
                            originalError: error
                        };
                    }
                    return {
                        type: KeycloakErrorType.UNKNOWN,
                        message: 'Unknown error',
                        originalError: error
                    };
                }),
                canMakeRequest: jest.fn().mockReturnValue(true),
                trackLatency: jest.fn()
            };
        })
    };
});

describe('KeycloakHttpService', () => {
    let service: KeycloakHttpService;

    beforeEach(async () => {
        // Reset mocks between tests
        jest.clearAllMocks();
        mockGetCircuitState.mockReturnValue({ state: 'CLOSED', failureCount: 2 });

        // Use the shared mock config factory
        const mockConfigService = createMockConfigService({
            auth: {
                serverUrl: 'http://keycloak:8080',
                admin: 'admin',
                adminPassword: 'admin'
            }
        });

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                KeycloakHttpService,
                { provide: ConfigService, useValue: mockConfigService }
            ]
        }).compile();

        service = module.get<KeycloakHttpService>(KeycloakHttpService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('HTTP methods', () => {
        it('should make GET requests correctly', async () => {
            mockGet.mockResolvedValueOnce({ key: 'value' });

            const result = await service.get('/test');

            expect(mockGet).toHaveBeenCalledWith('/test');
            expect(result).toEqual({ key: 'value' });
        });

        it('should make POST requests correctly', async () => {
            mockPost.mockResolvedValueOnce({ created: true });

            const data = { name: 'test' };
            const result = await service.post('/test', data);

            expect(mockPost).toHaveBeenCalledWith('/test', data);
            expect(result).toEqual({ created: true });
        });

        it('should set correct content type for form data', async () => {
            mockPost.mockResolvedValueOnce({ token: 'abc123' });

            const formData = new URLSearchParams({ grant_type: 'password' });
            await service.post('/token', formData);

            expect(mockPost).toHaveBeenCalledWith('/token', formData);
        });

        it('should make PUT requests correctly', async () => {
            mockPut.mockResolvedValueOnce({ updated: true });

            const data = { name: 'updated' };
            const result = await service.put('/test/1', data);

            expect(mockPut).toHaveBeenCalledWith('/test/1', data);
            expect(result).toEqual({ updated: true });
        });

        it('should make DELETE requests correctly', async () => {
            mockDelete.mockResolvedValueOnce({ deleted: true });

            const result = await service.delete('/test/1');

            expect(mockDelete).toHaveBeenCalledWith('/test/1');
            expect(result).toEqual({ deleted: true });
        });
    });

    describe('Circuit breaker', () => {
        it('should reset circuit breaker', () => {
            service.resetCircuitBreaker();
            expect(mockResetCircuitBreaker).toHaveBeenCalled();
        });

        it('should return circuit breaker state', () => {
            const state = service.getCircuitState();
            expect(mockGetCircuitState).toHaveBeenCalled();
            expect(state.state).toBe('CLOSED');
            expect(state.failureCount).toBe(2);
        });
    });

    describe('Health check', () => {
        it('should return UP status when server is healthy', async () => {
            mockCheckHealth.mockResolvedValueOnce({
                status: 'UP',
                details: { circuitState: 'CLOSED' }
            });

            const health = await service.checkHealth();

            expect(health.status).toBe('UP');
            expect(health.details.circuitState).toBe('CLOSED');
        });

        it('should try fallback endpoint when primary fails', async () => {
            mockCheckHealth.mockResolvedValueOnce({
                status: 'UP',
                details: { note: 'Using fallback endpoint', circuitState: 'CLOSED' }
            });

            const health = await service.checkHealth();

            expect(health.status).toBe('UP');
            expect(health.details).toHaveProperty('note');
            expect(health.details.note).toContain('fallback');
        });

        it('should return DOWN status when all endpoints fail', async () => {
            mockCheckHealth.mockResolvedValueOnce({
                status: 'DOWN',
                details: { error: 'All endpoints failed' }
            });

            const health = await service.checkHealth();

            expect(health.status).toBe('DOWN');
        });
    });

    describe('Metrics', () => {
        it('should track metrics and provide summary', () => {
            mockGetMetrics.mockReturnValueOnce({
                requestCount: 10,
                successRate: 80,
                avgLatency: 200,
                errorBreakdown: {
                    [KeycloakErrorType.TIMEOUT]: 1,
                    [KeycloakErrorType.NETWORK]: 1
                },
                circuitState: 'CLOSED'
            });

            const metrics = service.getMetrics();

            expect(metrics.requestCount).toBe(10);
            expect(metrics.successRate).toBe(80);
            expect(metrics.avgLatency).toBe(200);
            expect(metrics.errorBreakdown[KeycloakErrorType.TIMEOUT]).toBe(1);
            expect(metrics.errorBreakdown[KeycloakErrorType.NETWORK]).toBe(1);
        });

        it('should reset metrics', () => {
            service.resetMetrics();
            expect(mockResetMetrics).toHaveBeenCalled();
        });
    });
});
