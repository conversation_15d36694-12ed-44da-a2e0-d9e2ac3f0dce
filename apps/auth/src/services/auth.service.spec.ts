import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';
import { AuthService } from './auth.service';
import { KeycloakService } from './keycloak.service';
import { TenantRepository } from '../../../../libs/common/src/repositories/tenant.repository';
import { CreateTenantDto } from '../dto/create-tenant.dto';
import { LoginDto } from '../dto/login.dto';
import { ConflictException, UnauthorizedException } from '@nestjs/common';
import { KeycloakTokenResponse } from '../interfaces/keycloak-token.interface';
import { Tenant } from '@app/common/typeorm/entities/public/tenant.entity';
import { UserRepository } from '../../../../libs/common/src/repositories/user.repository';
import { PublicUserRepository } from '../../../../libs/common/src/repositories/public-user.repository';
import { RoleRepository } from '../../../../libs/common/src/repositories/role.repository';
import { TenantSchemaMigrationService } from '@app/common/typeorm/migrations/tenant-schema.migrations';
import { TenantConnectionService, TenantContextService } from '@app/common/multi-tenancy';
import { SystemRoleRepository } from '../../../../libs/common/src/repositories/system-role.repository';
import { TenantRoleRepository } from '../../../../libs/common/src/repositories/tenant-role.repository';
import { PublicSchemaMigrationService } from '@app/common/typeorm/migrations/public-schema.migrations';
import { REQUEST } from '@nestjs/core';
import {
    createMockResponse,
    createMockDataSource,
    createMockConfigService,
    mockResponses,
    createMockCommunicationProducer
} from '@app/common/testing';

// Create a mock TenantContextService class
class MockTenantContextService {
    private tenantId: string | null = null;
    private tenantMetadata: any | null = null;

    setTenant(tenantId: string, metadata: any): void {
        this.tenantId = tenantId.replace(/-/g, '_');
        this.tenantMetadata = metadata;
    }

    getTenantId(): string {
        return this.tenantId || 'public';
    }

    getTenantMetadata(): any | null {
        return this.tenantMetadata;
    }

    hasTenant(): boolean {
        return !!this.tenantId && !!this.tenantMetadata;
    }

    getTenantSchema(): string {
        return `tenant_${this.getTenantId()}`;
    }
}

describe('AuthService', () => {
    let service: AuthService;
    let keycloakService: jest.Mocked<KeycloakService>;
    let tenantRepository: jest.Mocked<TenantRepository>;
    let publicUserRepository: jest.Mocked<PublicUserRepository>;

    beforeEach(async () => {
        // Create mocks for all dependencies
        const mockKeycloakService = {
            createRealm: jest.fn(),
            createInitialAdminUser: jest.fn(),
            getToken: jest.fn(),
            refreshToken: jest.fn(),
            createUser: jest.fn(),
            findUsersByEmail: jest.fn(),
            deleteRealm: jest.fn(),
            createRoles: jest.fn(),
            getUserById: jest.fn(),
            getUserByUsername: jest.fn(),
            assignRolesToUser: jest.fn(),
            getClientToken: jest.fn(),
            getJwks: jest.fn(),
            checkAndFixClientConfiguration: jest.fn()
        };

        const mockTenantRepository = {
            findByRealm: jest.fn(),
            createTenant: jest.fn(),
            removeById: jest.fn()
        };

        const mockPublicUserRepository = {
            findByEmail: jest.fn(),
            createUser: jest.fn(),
            verifyCredentials: jest.fn(),
            removeById: jest.fn()
        };

        const mockUserRepository = {
            findByKeycloakId: jest.fn(),
            getUserWithRoles: jest.fn(),
            save: jest.fn()
        };

        const mockRoleRepository = {
            createRole: jest.fn(),
            findByName: jest.fn()
        };

        const mockSystemRoleRepository = {
            create: jest.fn(),
            findByName: jest.fn().mockImplementation((roleName) => {
                if (roleName === 'ADMIN') {
                    return Promise.resolve({
                        id: 'admin-role-id',
                        name: 'ADMIN',
                        description: 'Tenant Administrator',
                        permissions: { tenant: ['*'] }
                    });
                }
                return Promise.resolve(null);
            }),
            assignUserToTenant: jest.fn(),
            assignRoleToUser: jest.fn(),
            removeRoleFromUser: jest.fn(),
            removeUserFromTenant: jest.fn(),
            getUserRoles: jest.fn().mockResolvedValue([]),
            getUserTenants: jest.fn().mockResolvedValue([])
        };

        const mockTenantRoleRepository = {
            create: jest.fn(),
            findByName: jest.fn(),
            createUserProfile: jest.fn(),
            getUserProfile: jest.fn(),
            getUserRoles: jest.fn().mockResolvedValue([])
        };

        const mockTenantSchemaMigrationService = {
            createTenantSchema: jest.fn().mockResolvedValue(undefined)
        };

        const mockPublicSchemaMigrationService = {
            isPublicSchemaReady: jest.fn().mockResolvedValue(true)
        };

        // Mock request for TenantContextService
        const mockRequest = {};

        const mockTenantConnectionService = {
            getTenantDataSource: jest.fn().mockResolvedValue(createMockDataSource()),
            getPublicDataSource: jest.fn().mockReturnValue(createMockDataSource())
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                AuthService,
                { provide: KeycloakService, useValue: mockKeycloakService },
                { provide: TenantRepository, useValue: mockTenantRepository },
                { provide: UserRepository, useValue: mockUserRepository },
                { provide: PublicUserRepository, useValue: mockPublicUserRepository },
                { provide: RoleRepository, useValue: mockRoleRepository },
                { provide: SystemRoleRepository, useValue: mockSystemRoleRepository },
                { provide: TenantRoleRepository, useValue: mockTenantRoleRepository },
                {
                    provide: TenantSchemaMigrationService,
                    useValue: mockTenantSchemaMigrationService
                },
                {
                    provide: PublicSchemaMigrationService,
                    useValue: mockPublicSchemaMigrationService
                },
                { provide: TenantConnectionService, useValue: mockTenantConnectionService },
                { provide: ConfigService, useValue: createMockConfigService() },
                { provide: TenantContextService, useClass: MockTenantContextService },
                { provide: 'MessageProducerService', useValue: createMockCommunicationProducer() },
                { provide: REQUEST, useValue: mockRequest }
            ]
        }).compile();

        service = module.get<AuthService>(AuthService);
        keycloakService = module.get(KeycloakService);
        tenantRepository = module.get(TenantRepository);
        publicUserRepository = module.get(PublicUserRepository);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createTenant', () => {
        const createTenantDto: CreateTenantDto = {
            realm: 'test-realm',
            displayName: 'Test Realm',
            adminUsername: 'admin',
            adminPassword: 'password',
            adminEmail: '<EMAIL>',
            adminFirstName: 'Admin',
            adminLastName: 'User',
            registrationAllowed: false,
            verifyEmail: true,
            rememberMe: true,
            dedicatedRealmAdmin: true
        };

        const mockClientId = 'test-realm-admin-client';
        const mockClientSecret = 'client-secret';

        it('should create a tenant successfully', async () => {
            // Mock responses
            tenantRepository.findByRealm.mockResolvedValue(null);
            publicUserRepository.findByEmail.mockResolvedValue(null);
            keycloakService.createRealm.mockResolvedValue({ realm: createTenantDto.realm } as any);
            keycloakService.createInitialAdminUser.mockResolvedValue({
                user: { id: 'user-id' },
                client: {
                    clientId: mockClientId,
                    secret: mockClientSecret
                }
            } as any);
            tenantRepository.createTenant.mockResolvedValue({
                id: '1',
                realm: createTenantDto.realm,
                displayName: createTenantDto.displayName,
                adminUsername: createTenantDto.adminUsername,
                adminEmail: createTenantDto.adminEmail,
                clientId: mockClientId,
                clientSecret: mockClientSecret,
                enabled: true,
                createdAt: new Date(),
                updatedAt: new Date()
            } as Tenant);

            publicUserRepository.createUser.mockResolvedValue({
                id: 'public-user-id',
                email: createTenantDto.adminEmail
            } as any);

            // Call service method
            const result = await service.createTenant(createTenantDto);

            // Assertions
            expect(tenantRepository.findByRealm).toHaveBeenCalledWith(createTenantDto.realm);
            expect(keycloakService.createRealm).toHaveBeenCalledWith(createTenantDto);
            expect(keycloakService.createInitialAdminUser).toHaveBeenCalledWith(
                createTenantDto.realm,
                createTenantDto
            );
            expect(tenantRepository.createTenant).toHaveBeenCalledWith(
                createTenantDto,
                mockClientId,
                mockClientSecret
            );

            expect(result).toEqual({
                realm: createTenantDto.realm,
                displayName: createTenantDto.displayName,
                adminUsername: createTenantDto.adminUsername,
                adminPassword: createTenantDto.adminPassword,
                clientId: mockClientId
            });
        });

        it('should throw ConflictException if tenant already exists', async () => {
            // Mock tenant already exists
            tenantRepository.findByRealm.mockResolvedValue({
                realm: createTenantDto.realm
            } as Tenant);

            // Assertions
            await expect(service.createTenant(createTenantDto)).rejects.toThrow(ConflictException);
            expect(tenantRepository.findByRealm).toHaveBeenCalledWith(createTenantDto.realm);
            expect(keycloakService.createRealm).not.toHaveBeenCalled();
        });

        it('should throw an error if keycloak realm creation fails', async () => {
            // Mock responses
            tenantRepository.findByRealm.mockResolvedValue(null);
            keycloakService.createRealm.mockRejectedValue(new Error('Keycloak error'));

            // Assertions
            await expect(service.createTenant(createTenantDto)).rejects.toThrow();
            expect(tenantRepository.findByRealm).toHaveBeenCalledWith(createTenantDto.realm);
            expect(keycloakService.createRealm).toHaveBeenCalledWith(createTenantDto);
            expect(keycloakService.createInitialAdminUser).not.toHaveBeenCalled();
        });
    });

    describe('login', () => {
        const loginDto: LoginDto = {
            realm: 'test-realm',
            username: 'admin',
            password: 'password',
            rememberMe: true
        };

        const mockTokenResponse: KeycloakTokenResponse = {
            access_token: 'access-token',
            refresh_token: 'refresh-token',
            expires_in: 300,
            refresh_expires_in: 1800,
            token_type: 'Bearer',
            not_before_policy: 0,
            session_state: 'session-state',
            scope: 'profile email'
        };

        it('should login successfully with public client', async () => {
            // Mock tenant with public client (no secret)
            tenantRepository.findByRealm.mockResolvedValue({
                realm: loginDto.realm,
                clientId: 'test-client',
                clientSecret: ''
            } as Tenant);

            keycloakService.getToken.mockResolvedValue(mockResponses.keycloakToken);

            const response = createMockResponse();
            const result = await service.login(loginDto, response as unknown as Response);

            expect(tenantRepository.findByRealm).toHaveBeenCalledWith(loginDto.realm);
            expect(keycloakService.getToken).toHaveBeenCalledWith(
                loginDto.realm,
                loginDto.username,
                loginDto.password,
                'test-client',
                undefined // No client secret for public client
            );
            expect(response.cookie).toHaveBeenCalled();
            expect(result).toEqual({
                accessToken: mockResponses.keycloakToken.access_token,
                expiresIn: mockResponses.keycloakToken.expires_in
            });
        });

        it('should login successfully with confidential client', async () => {
            // Mock tenant with confidential client (with secret)
            tenantRepository.findByRealm.mockResolvedValue({
                realm: loginDto.realm,
                clientId: 'test-client',
                clientSecret: 'client-secret'
            } as Tenant);

            keycloakService.getToken.mockResolvedValue(mockTokenResponse);

            const response = createMockResponse();
            const result = await service.login(loginDto, response as any);

            expect(tenantRepository.findByRealm).toHaveBeenCalledWith(loginDto.realm);
            expect(keycloakService.getToken).toHaveBeenCalledWith(
                loginDto.realm,
                loginDto.username,
                loginDto.password,
                'test-client',
                'client-secret' // With client secret for confidential client
            );
            expect(response.cookie).toHaveBeenCalled();
            expect(result).toEqual({
                accessToken: mockTokenResponse.access_token,
                expiresIn: mockTokenResponse.expires_in
            });
        });

        it('should throw error if tenant not found', async () => {
            tenantRepository.findByRealm.mockResolvedValue(null);

            const response = createMockResponse();
            await expect(service.login(loginDto, response as unknown as Response)).rejects.toThrow(
                UnauthorizedException
            );
        });

        it('should throw error if authentication fails', async () => {
            tenantRepository.findByRealm.mockResolvedValue({
                realm: loginDto.realm,
                clientId: 'test-client',
                clientSecret: 'client-secret'
            } as Tenant);

            keycloakService.getToken.mockRejectedValue(
                new UnauthorizedException('Invalid credentials')
            );

            const response = createMockResponse();
            await expect(service.login(loginDto, response as any)).rejects.toThrow(
                UnauthorizedException
            );
        });
    });

    describe('refreshToken', () => {
        const refreshToken = 'refresh-token';
        const realm = 'test-realm';

        const mockTokenResponse: KeycloakTokenResponse = {
            access_token: 'new-access-token',
            refresh_token: 'new-refresh-token',
            expires_in: 300,
            refresh_expires_in: 1800,
            token_type: 'Bearer',
            not_before_policy: 0,
            session_state: 'session-state',
            scope: 'profile email'
        };

        it('should refresh token successfully with public client', async () => {
            // Mock tenant with public client (no secret)
            tenantRepository.findByRealm.mockResolvedValue({
                realm,
                clientId: 'test-client',
                clientSecret: ''
            } as Tenant);

            keycloakService.refreshToken.mockResolvedValue(mockTokenResponse);

            const response = createMockResponse();
            const result = await service.refreshToken(refreshToken, realm, response as any);

            expect(tenantRepository.findByRealm).toHaveBeenCalledWith(realm);
            expect(keycloakService.refreshToken).toHaveBeenCalledWith(
                realm,
                refreshToken,
                'test-client',
                undefined // No client secret for public client
            );
            expect(response.cookie).toHaveBeenCalled();
            expect(result).toEqual({
                accessToken: mockTokenResponse.access_token,
                expiresIn: mockTokenResponse.expires_in
            });
        });

        it('should refresh token successfully with confidential client', async () => {
            // Mock tenant with confidential client (with secret)
            tenantRepository.findByRealm.mockResolvedValue({
                realm,
                clientId: 'test-client',
                clientSecret: 'client-secret'
            } as Tenant);

            keycloakService.refreshToken.mockResolvedValue(mockTokenResponse);

            const response = createMockResponse();
            const result = await service.refreshToken(refreshToken, realm, response as any);

            expect(tenantRepository.findByRealm).toHaveBeenCalledWith(realm);
            expect(keycloakService.refreshToken).toHaveBeenCalledWith(
                realm,
                refreshToken,
                'test-client',
                'client-secret' // With client secret for confidential client
            );
            expect(response.cookie).toHaveBeenCalled();
            expect(result).toEqual({
                accessToken: mockTokenResponse.access_token,
                expiresIn: mockTokenResponse.expires_in
            });
        });

        it('should throw error if tenant not found', async () => {
            tenantRepository.findByRealm.mockResolvedValue(null);

            const response = createMockResponse();
            await expect(
                service.refreshToken(refreshToken, realm, response as any)
            ).rejects.toThrow(UnauthorizedException);
        });

        it('should throw error if refresh token is invalid', async () => {
            tenantRepository.findByRealm.mockResolvedValue({
                realm,
                clientId: 'test-client',
                clientSecret: 'client-secret'
            } as Tenant);

            keycloakService.refreshToken.mockRejectedValue(new Error('Invalid refresh token'));

            const response = createMockResponse();
            await expect(
                service.refreshToken(refreshToken, realm, response as any)
            ).rejects.toThrow(UnauthorizedException);
        });
    });

    describe('clearAuthCookies', () => {
        it('should clear auth cookies', () => {
            const response = createMockResponse();
            service.clearAuthCookies(response as unknown as Response);
            expect(response.clearCookie).toHaveBeenCalledTimes(3);
        });
    });
});
