/**
 * Keycloak realm representation
 */
export interface KeycloakRealmRepresentation {
    id?: string;
    realm: string;
    displayName?: string;
    displayNameHtml?: string;
    enabled?: boolean;
    sslRequired?: 'none' | 'external' | 'all';
    registrationAllowed?: boolean;
    registrationEmailAsUsername?: boolean;
    rememberMe?: boolean;
    verifyEmail?: boolean;
    loginWithEmailAllowed?: boolean;
    duplicateEmailsAllowed?: boolean;
    resetPasswordAllowed?: boolean;
    editUsernameAllowed?: boolean;
    bruteForceProtected?: boolean;
    permanentLockout?: boolean;
    maxFailureWaitSeconds?: number;
    minimumQuickLoginWaitSeconds?: number;
    waitIncrementSeconds?: number;
    quickLoginCheckMilliSeconds?: number;
    maxDeltaTimeSeconds?: number;
    failureFactor?: number;
    defaultRoles?: string[];
    requiredCredentials?: string[];
    passwordPolicy?: string;
    otpPolicyType?: string;
    otpPolicyAlgorithm?: string;
    otpPolicyInitialCounter?: number;
    otpPolicyDigits?: number;
    otpPolicyLookAheadWindow?: number;
    otpPolicyPeriod?: number;
    otpSupportedApplications?: string[];
    browserSecurityHeaders?: Record<string, string>;
    smtpServer?: Record<string, string>;
    eventsEnabled?: boolean;
    eventsListeners?: string[];
    enabledEventTypes?: string[];
    adminEventsEnabled?: boolean;
    adminEventsDetailsEnabled?: boolean;
    internationalizationEnabled?: boolean;
    supportedLocales?: string[];
    defaultLocale?: string;
    browserFlow?: string;
    registrationFlow?: string;
    directGrantFlow?: string;
    resetCredentialsFlow?: string;
    clientAuthenticationFlow?: string;
    dockerAuthenticationFlow?: string;
    attributes?: Record<string, string>;
    userManagedAccessAllowed?: boolean;
    // Token settings
    revokeRefreshToken?: boolean;
    refreshTokenMaxReuse?: number;
    ssoSessionIdleTimeout?: number;
    ssoSessionMaxLifespan?: number;
    offlineSessionIdleTimeout?: number;
    accessTokenLifespan?: number;
    accessTokenLifespanForImplicitFlow?: number;
    actionTokenGeneratedByAdminLifespan?: number;
    actionTokenGeneratedByUserLifespan?: number;
}

/**
 * Keycloak user representation
 */
export interface KeycloakUserRepresentation {
    id?: string;
    createdTimestamp?: number;
    username?: string;
    enabled?: boolean;
    totp?: boolean;
    emailVerified?: boolean;
    firstName?: string;
    lastName?: string;
    email?: string;
    disableableCredentialTypes?: string[];
    requiredActions?: string[];
    notBefore?: number;
    access?: {
        manageGroupMembership?: boolean;
        view?: boolean;
        mapRoles?: boolean;
        impersonate?: boolean;
        manage?: boolean;
    };
    attributes?: Record<string, string[]>;
    clientRoles?: Record<string, string[]>;
    clientConsents?: Record<string, unknown>[];
    federatedIdentities?: Record<string, unknown>[];
    federationLink?: string;
    groups?: string[];
    realmRoles?: string[];
    serviceAccountClientId?: string;
    credentials?: KeycloakCredentialRepresentation[];
}

/**
 * Keycloak credential representation
 */
export interface KeycloakCredentialRepresentation {
    type?: string;
    value?: string;
    temporary?: boolean;
    createdDate?: number;
    secretData?: string;
    credentialData?: string;
    priority?: number;
    userLabel?: string;
}

/**
 * Keycloak role representation
 */
export interface KeycloakRoleRepresentation {
    id?: string;
    name: string;
    description?: string;
    composite?: boolean;
    clientRole?: boolean;
    containerId?: string;
    attributes?: Record<string, string[]>;
}

/**
 * Keycloak client representation
 */
export interface KeycloakClientRepresentation {
    id?: string;
    clientId: string;
    name?: string;
    rootUrl?: string;
    baseUrl?: string;
    surrogateAuthRequired?: boolean;
    enabled?: boolean;
    alwaysDisplayInConsole?: boolean;
    clientAuthenticatorType?: string;
    redirectUris?: string[];
    webOrigins?: string[];
    notBefore?: number;
    bearerOnly?: boolean;
    consentRequired?: boolean;
    standardFlowEnabled?: boolean;
    implicitFlowEnabled?: boolean;
    directAccessGrantsEnabled?: boolean;
    serviceAccountsEnabled?: boolean;
    publicClient?: boolean;
    frontchannelLogout?: boolean;
    protocol?: string;
    attributes?: Record<string, string>;
    authenticationFlowBindingOverrides?: Record<string, string>;
    fullScopeAllowed?: boolean;
    nodeReRegistrationTimeout?: number;
    defaultClientScopes?: string[];
    optionalClientScopes?: string[];
    access?: {
        view?: boolean;
        configure?: boolean;
        manage?: boolean;
    };
    secret?: string;
    authorizationServicesEnabled?: boolean;
}

/**
 * Keycloak realm admin credentials
 * Used for storing and managing realm-specific admin tokens
 */
export interface KeycloakRealmAdminCredentials {
    realm: string;
    username: string;
    clientId: string;
    clientSecret: string;
    accessToken: string | null;
    tokenExpiry: number;
}
