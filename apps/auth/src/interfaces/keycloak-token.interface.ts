/**
 * Keycloak token response interface
 */
export interface KeycloakTokenResponse {
    access_token: string;
    expires_in: number;
    refresh_expires_in: number;
    refresh_token: string;
    token_type: string;
    not_before_policy: number;
    session_state: string;
    scope: string;
}

/**
 * Keycloak token request interface
 */
export interface KeycloakTokenRequest {
    grant_type: 'password' | 'refresh_token' | 'client_credentials';
    client_id: string;
    client_secret?: string;
    username?: string;
    password?: string;
    refresh_token?: string;
    scope?: string;
}

/**
 * Decoded JWT token claims
 */
export interface JwtTokenClaims {
    exp: number;
    iat: number;
    auth_time: number;
    jti: string;
    iss: string;
    aud: string | string[];
    sub: string;
    typ: string;
    azp: string;
    session_state: string;
    acr: string;
    realm_access?: {
        roles: string[];
    };
    resource_access?: {
        [key: string]: {
            roles: string[];
        };
    };
    scope: string;
    sid: string;
    email_verified: boolean;
    name?: string;
    preferred_username: string;
    given_name?: string;
    family_name?: string;
    email?: string;
    remember_me?: string;
    [key: string]: unknown;
}
