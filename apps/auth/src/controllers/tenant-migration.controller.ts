import { Controller, Post, Get, HttpCode, HttpStatus, Param } from '@nestjs/common';
import { TenantSchemaMigrationService } from '@app/common/typeorm/migrations/tenant-schema.migrations';

/**
 * Controller for managing tenant schema migrations using TypeORM migrations
 */
@Controller('tenant-migrations')
export class TenantMigrationController {
    constructor(private readonly tenantSchemaMigrationService: TenantSchemaMigrationService) {}

    /**
     * Runs migrations for all tenant schemas
     * This endpoint should be called when the schema changes
     */
    @Post('run-all')
    @HttpCode(HttpStatus.OK)
    async runMigrationsForAllTenants(): Promise<{
        message: string;
        success: number;
        failed: number;
        failures: Record<string, string>;
    }> {
        const result = await this.tenantSchemaMigrationService.runMigrationsForAllTenants();
        return {
            message: `Migrations completed: ${result.success} successful, ${result.failed} failed`,
            ...result
        };
    }

    /**
     * Runs migrations for a specific tenant
     * @param tenantId The tenant ID
     */
    @Post('run/:tenantId')
    @HttpCode(HttpStatus.OK)
    async runMigrationsForTenant(
        @Param('tenantId') tenantId: string
    ): Promise<{ message: string }> {
        await this.tenantSchemaMigrationService.runTenantMigrations(tenantId);
        return { message: `Migrations completed successfully for tenant ${tenantId}` };
    }

    /**
     * Gets migration status for a specific tenant
     * @param tenantId The tenant ID
     */
    @Get('status/:tenantId')
    async getMigrationStatus(@Param('tenantId') tenantId: string): Promise<{
        tenantId: string;
        migrations: { name: string; executed: boolean }[];
    }> {
        const migrations = await this.tenantSchemaMigrationService.getMigrationStatus(tenantId);
        return {
            tenantId,
            migrations
        };
    }

    /**
     * Reverts the last migration for a specific tenant
     * @param tenantId The tenant ID
     */
    @Post('revert/:tenantId')
    @HttpCode(HttpStatus.OK)
    async revertLastMigration(
        @Param('tenantId') tenantId: string
    ): Promise<{ message: string; reverted: string | null }> {
        const reverted = await this.tenantSchemaMigrationService.revertLastMigration(tenantId);

        if (reverted) {
            return {
                message: `Successfully reverted migration ${reverted} for tenant ${tenantId}`,
                reverted
            };
        } else {
            return {
                message: `No migrations to revert for tenant ${tenantId}`,
                reverted: null
            };
        }
    }
}
