import {
    IsString,
    <PERSON>NotEmpty,
    IsEmail,
    IsO<PERSON>al,
    <PERSON><PERSON><PERSON>th,
    Matches,
    IsBoolean
} from 'class-validator';

/**
 * DTO for creating a new tenant (realm)
 */
export class CreateTenantDto {
    /**
     * Realm name (unique identifier)
     * Must be lowercase, no spaces, only alphanumeric and hyphens
     */
    @IsString()
    @IsNotEmpty()
    @Matches(/^[a-z0-9-]+$/, {
        message: 'Realm name must be lowercase, contain only alphanumeric characters and hyphens'
    })
    realm: string;

    /**
     * Display name for the realm
     */
    @IsString()
    @IsNotEmpty()
    displayName: string;

    /**
     * Admin username for the initial admin user
     */
    @IsString()
    @IsNotEmpty()
    adminUsername: string;

    /**
     * Admin email for the initial admin user
     */
    @IsEmail()
    @IsNotEmpty()
    adminEmail: string;

    /**
     * Admin password for the initial admin user
     * Must be at least 8 characters and include at least one uppercase letter,
     * one lowercase letter, one number, and one special character
     */
    @IsString()
    @IsNotEmpty()
    @MinLength(8)
    @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/, {
        message:
            'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    })
    adminPassword: string;

    /**
     * First name for the initial admin user
     */
    @IsString()
    @IsOptional()
    adminFirstName?: string;

    /**
     * Last name for the initial admin user
     */
    @IsString()
    @IsOptional()
    adminLastName?: string;

    /**
     * Whether to enable registration for the realm
     */
    @IsBoolean()
    @IsOptional()
    registrationAllowed?: boolean;

    /**
     * Whether to enable email verification for the realm
     */
    @IsBoolean()
    @IsOptional()
    verifyEmail?: boolean;

    /**
     * Whether to enable remember me for the realm
     */
    @IsBoolean()
    @IsOptional()
    rememberMe?: boolean;

    /**
     * Whether the admin user should have full privileges only within this realm
     * If true, the admin user will be a dedicated realm admin with no access to other realms
     * If false, the admin user will be a global admin with access to all realms
     * Default is true for security best practices
     */
    @IsBoolean()
    @IsOptional()
    dedicatedRealmAdmin?: boolean = true;
}
