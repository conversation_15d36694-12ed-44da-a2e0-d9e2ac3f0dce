import { IsString, IsNotEmpty, IsOptional, ValidateNested, IsArray, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { Permission } from '@app/common/permissions/enums/permission.enum';
import { ResourceType } from '@app/common/permissions/permission.constants';

/**
 * DTO for resource permissions within a role group
 */
export class ResourcePermissionsDto {
    /**
     * Resource type from the ResourceType enum
     */
    @IsEnum(ResourceType)
    resource: ResourceType;

    /**
     * Array of permissions for this resource
     */
    @IsArray()
    @IsString({ each: true })
    permissions: Permission[];
}

/**
 * DTO for creating a new role group with permissions
 */
export class CreateRoleGroupDto {
    /**
     * Role group key (e.g., 'conveyancers', 'finance')
     */
    @IsString()
    @IsNotEmpty()
    key: string;

    /**
     * Human-readable label for the role group
     */
    @IsString()
    @IsNotEmpty()
    label: string;

    /**
     * Resource permissions for this role group
     */
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ResourcePermissionsDto)
    resourcePermissions: ResourcePermissionsDto[];

    /**
     * Optional description for the role group
     */
    @IsString()
    @IsOptional()
    description?: string;
}
