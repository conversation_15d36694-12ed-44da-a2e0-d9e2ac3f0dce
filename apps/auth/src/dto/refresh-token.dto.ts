import { IsString, IsOptional, IsBoolean } from 'class-validator';

/**
 * DTO for refreshing tokens
 */
export class RefreshTokenDto {
    /**
     * Refresh token
     * Optional because it might be provided in a cookie
     */
    @IsString()
    @IsOptional()
    refreshToken?: string;

    /**
     * Realm to refresh token for
     */
    @IsString()
    @IsOptional()
    realm?: string;

    /**
     * Whether this is a realm admin token refresh
     * If true, will use the realm-specific client for token refresh
     */
    @IsBoolean()
    @IsOptional()
    isRealmAdmin?: boolean;
}
