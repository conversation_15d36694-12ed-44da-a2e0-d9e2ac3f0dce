import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsString } from 'class-validator';
import { Permission } from '@app/common/permissions/enums/permission.enum';
import { ResourceType } from '@app/common/permissions/permission.constants';

/**
 * DTO for updating permissions for a specific resource in a role group
 */
export class UpdateRoleGroupPermissionsDto {
    /**
     * Resource type from the ResourceType enum
     */
    @IsEnum(ResourceType)
    resource: ResourceType;

    /**
     * Array of permissions for this resource
     */
    @IsArray()
    @IsString({ each: true })
    permissions: Permission[];
}
