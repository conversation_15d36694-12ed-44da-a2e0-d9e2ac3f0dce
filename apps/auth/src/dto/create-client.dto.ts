import { IsString, <PERSON><PERSON>otEmpty, <PERSON><PERSON><PERSON>al, IsBoolean, IsA<PERSON>y } from 'class-validator';

/**
 * DTO for creating a new OpenID client
 */
export class CreateClientDto {
    /**
     * Client ID (unique identifier)
     */
    @IsString()
    @IsNotEmpty()
    clientId: string;

    /**
     * Display name for the client
     */
    @IsString()
    @IsNotEmpty()
    name: string;

    /**
     * Description for the client
     */
    @IsString()
    @IsOptional()
    description?: string;

    /**
     * Whether the client is public (no client secret)
     */
    @IsBoolean()
    @IsOptional()
    publicClient?: boolean;

    /**
     * Whether to enable direct access grants (Resource Owner Password Credentials)
     */
    @IsBoolean()
    @IsOptional()
    directAccessGrantsEnabled?: boolean;

    /**
     * Valid redirect URIs for the client
     */
    @IsArray()
    @IsOptional()
    redirectUris?: string[];

    /**
     * Valid web origins for CORS
     */
    @IsArray()
    @IsOptional()
    webOrigins?: string[];

    /**
     * Realm to create the client in
     */
    @IsString()
    @IsNotEmpty()
    realm: string;
}
