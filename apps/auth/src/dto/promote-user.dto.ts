import { IsString, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean } from 'class-validator';

/**
 * DTO for promoting a user to admin within their role group
 */
export class PromoteUserDto {
    /**
     * Role group key (e.g., 'conveyancers', 'finance')
     */
    @IsString()
    @IsNotEmpty()
    roleGroupKey: string;

    /**
     * Whether to promote to admin (true) or demote to user (false)
     */
    @IsBoolean()
    @IsNotEmpty()
    promoteToAdmin: boolean;

    /**
     * Realm where the user exists
     */
    @IsString()
    @IsNotEmpty()
    realm: string;

    /**
     * Admin password for the realm admin
     * Only needed when using a realm-specific admin token
     */
    @IsString()
    @IsOptional()
    adminPassword?: string;
}
