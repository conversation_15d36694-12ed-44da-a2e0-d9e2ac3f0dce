/**
 * User data transfer object
 */
export interface UserDto {
    /**
     * User ID
     */
    id: string;

    /**
     * Username
     */
    username: string | undefined;

    /**
     * Email address
     */
    email: string | undefined;

    /**
     * First name
     */
    firstName: string | undefined;

    /**
     * Last name
     */
    lastName: string | undefined;

    /**
     * Whether the user is enabled
     */
    enabled: boolean | undefined;

    /**
     * Whether the user's email is verified
     */
    emailVerified: boolean | undefined;

    /**
     * User roles
     */
    roles?: string[];
}
