import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import { instance } from '@app/common/utils/logger.util';
import { TenantSchemaMigrationService } from '@app/common/typeorm/migrations/tenant-schema.migrations';
import { PublicSchemaMigrationService } from '@app/common/typeorm/migrations/public-schema.migrations';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';

async function bootstrap() {
    const app = await NestFactory.create(AppModule, {
        logger: WinstonModule.createLogger({
            instance: instance,
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.json(),
                winston.format.prettyPrint(),
                winston.format.colorize({ all: true }),
                winston.format.simple(),
                winston.format.ms()
            ),
            transports: [new winston.transports.Console()]
        })
    });
    const configService = app.get(ConfigService);

    // Configure security headers
    app.use(helmet());

    // Configure CORS with credentials support
    app.enableCors({
        origin: configService.get('app.corsOrigin') || '*',
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        credentials: true
    });

    // Get service configuration
    const serviceConfig = configService.get('service');

    // Set global prefix from service config
    app.setGlobalPrefix(serviceConfig.auth.prefix);

    app.useGlobalPipes(
        new ValidationPipe({ forbidNonWhitelisted: true, whitelist: true, stopAtFirstError: true })
    );

    // Run public schema migrations first
    try {
        const publicMigrationService = app.get(PublicSchemaMigrationService);
        await publicMigrationService.runPublicMigrations();
        Logger.log('Public schema migrations completed successfully');

        // Check if public schema is ready
        const isReady = await publicMigrationService.isPublicSchemaReady();
        if (!isReady) {
            throw new Error('Public schema migrations are not complete');
        }
    } catch (error) {
        Logger.error(`Failed to run public schema migrations: ${error.message}`, error.stack);
        process.exit(1); // Exit if public schema is not ready
    }

    // Run migrations for all tenant schemas
    try {
        const migrationService = app.get(TenantSchemaMigrationService);
        const result = await migrationService.runMigrationsForAllTenants();
        Logger.log(
            `Tenant migrations completed: ${result.success} successful, ${result.failed} failed`
        );

        if (result.failed > 0) {
            Logger.warn(`Failed tenant migrations: ${JSON.stringify(result.failures)}`);
        }
    } catch (error) {
        Logger.error(`Failed to run tenant migrations: ${error.message}`, error.stack);
        // Don't exit here as the app can still function with some failed tenant migrations
    }

    await app.listen(serviceConfig.auth.port);
    Logger.log(`Auth microservice running on ${await app.getUrl()}`);
}
bootstrap();
// Rebuild trigger: HealthAggregator fix in CommonModule - 20251014-022856
// Auth service build trigger - Mon Oct 20 17:56:20 EAT 2025
