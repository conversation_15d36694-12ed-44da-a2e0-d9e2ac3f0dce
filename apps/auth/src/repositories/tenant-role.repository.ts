import { Injectable } from '@nestjs/common';
import { DeepPartial } from 'typeorm';
import { TenantRole } from '@app/common/typeorm/entities/tenant/tenant-role.entity';
import { UserProfile } from '@app/common/typeorm/entities/tenant/user-profile.entity';
import { TenantConnectionService } from '@app/common/multi-tenancy';
import { TenantContextService } from '@app/common/multi-tenancy';
import { BaseTenantRepository } from '@app/common/multi-tenancy';

@Injectable()
export class TenantRoleRepository extends BaseTenantRepository<TenantRole> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(TenantRole, tenantContextService, tenantConnectionService);
    }

    async create(roleData: {
        name: string;
        description?: string;
        permissions?: Record<string, any>;
    }): Promise<TenantRole> {
        const role = this.create(roleData);
        return this.save(role as DeepPartial<TenantRole>);
    }

    async findByName(name: string): Promise<TenantRole | null> {
        return this.findOne({ where: { name } });
    }

    async createUserProfile(profileData: {
        username: string;
        email: string;
        firstName: string;
        lastName: string;
        userId: string;
        keycloakId?: string;
        additionalInfo?: Record<string, any>;
        roles?: string[];
    }): Promise<DeepPartial<UserProfile>> {
        const tenantId = this.tenantContextService.getTenantId();
        if (!tenantId) {
            throw new Error('Tenant ID not found');
        }

        const schemaName = this.tenantContextService.getTenantSchema();

        const tenantDataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const result = await tenantDataSource.query(
            `
            INSERT INTO "${schemaName}"."user_profiles" ("username", "email", "first_name", "last_name", "additional_info", "user_id", "keycloak_id")
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        `,
            [
                profileData.username,
                profileData.email,
                profileData.firstName,
                profileData.lastName,
                profileData.additionalInfo,
                profileData.userId,
                profileData.keycloakId
            ]
        );

        const userId = result[0].id;
        const adminRole = await this.findByName(profileData?.roles?.[0] ?? 'ADMIN');

        if (!adminRole) {
            throw new Error('ADMIN role not found');
        }

        await tenantDataSource
            .createQueryBuilder()
            .insert()
            .into('user_roles')
            .values({
                user_id: userId,
                role_id: adminRole.id
            })
            .execute();

        const savedUserProfile = await tenantDataSource
            .getRepository(UserProfile)
            .findOne({ where: { id: userId } });

        if (!savedUserProfile) {
            throw new Error('Error creating user profile');
        }

        return savedUserProfile;
    }

    async assignRoleToUser(userId: string, roleId: string): Promise<void> {
        await this.query(
            `INSERT INTO "user_roles" ("user_id", "role_id") VALUES ($1, $2)
             ON CONFLICT ("user_id", "role_id") DO NOTHING`,
            [userId, roleId]
        );
    }

    async getUserProfile(userId: string): Promise<DeepPartial<UserProfile> | null> {
        return this.findOne({
            where: { id: userId },
            relations: ['tenantRoles']
        });
    }

    async getUserRoles(userId: string): Promise<TenantRole[]> {
        return this.repository
            .createQueryBuilder('role')
            .innerJoin('role.userProfiles', 'profile', 'profile.userId = :userId', { userId })
            .getMany();
    }
}
