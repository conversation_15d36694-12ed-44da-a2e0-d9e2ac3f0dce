import { Injectable } from '@nestjs/common';
import { DeepPartial } from 'typeorm';
import { SystemRole } from '../../../../libs/common/src/typeorm/entities/public/system-role.entity';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';

@Injectable()
export class SystemRoleRepository extends BaseTenantRepository<SystemRole> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(SystemRole, tenantContextService, tenantConnectionService, true);
    }

    async findByName(name: string): Promise<SystemRole | null> {
        return this.findOne({ where: { name } });
    }

    async create(roleData: {
        name: string;
        description?: string;
        permissions?: Record<string, any>;
    }): Promise<SystemRole> {
        const role = this.create(roleData);
        return this.save(role as DeepPartial<SystemRole>);
    }

    // Role-user assignments - only concerned with roles, not tenants
    async assignRoleToUser(userId: string, roleId: string): Promise<void> {
        await this.query(
            `INSERT INTO "public"."user_system_roles" ("user_id", "role_id") VALUES ($1, $2)
             ON CONFLICT ("user_id", "role_id") DO NOTHING`,
            [userId, roleId]
        );
    }

    async removeRoleFromUser(userId: string, roleId: string): Promise<void> {
        await this.query(
            `DELETE FROM "public"."user_system_roles"
             WHERE "user_id" = $1 AND "role_id" = $2`,
            [userId, roleId]
        );
    }

    async getUserRoles(userId: string): Promise<SystemRole[]> {
        return this.find({
            where: {
                users: {
                    id: userId
                }
            }
        });
    }

    async getUserRolesByTenant(userId: string, tenantId: string): Promise<SystemRole[]> {
        const repository = await this.getTenantRepository();
        return repository
            .createQueryBuilder('role')
            .innerJoin('role.users', 'user', 'user.id = :userId', { userId })
            .innerJoin(
                'user_system_roles',
                'usr',
                'usr.role_id = role.id AND usr.user_id = :userId',
                { userId }
            )
            .innerJoin('user_tenants', 'ut', 'ut.user_id = :userId AND ut.tenant_id = :tenantId', {
                userId,
                tenantId
            })
            .getMany();
    }

    // Tenant-user assignments - only concerned with tenants, not roles
    async assignUserToTenant(userId: string, tenantId: string): Promise<void> {
        await this.query(
            `INSERT INTO "public"."user_tenants" ("user_id", "tenant_id") VALUES ($1, $2)
             ON CONFLICT ("user_id", "tenant_id") DO NOTHING`,
            [userId, tenantId]
        );
    }

    async removeUserFromTenant(userId: string, tenantId: string): Promise<void> {
        await this.query(
            `DELETE FROM "public"."user_tenants"
             WHERE "user_id" = $1 AND "tenant_id" = $2`,
            [userId, tenantId]
        );
    }

    async getUserTenants(
        userId: string
    ): Promise<{ tenantId: string; realm: string; displayName: string }[]> {
        const result = await this.query(
            `
            SELECT DISTINCT t.id as "tenantId", t.realm, t.display_name as "displayName"
            FROM public.tenants t
            INNER JOIN public.user_tenants ut ON ut.tenant_id = t.id
            WHERE ut.user_id = $1
        `,
            [userId]
        );

        return result;
    }
}
