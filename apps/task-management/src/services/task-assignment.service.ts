import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { TaskRepository } from '../repositories/task.repository';
import { Task } from '@app/common/typeorm/entities/tenant/task.entity';
import { AssignTaskDto } from '../dto/assign-task.dto';
import { TaskNotificationService } from './task-notification.service';
import { PublicUserRepository } from '@app/common/repositories/public-user.repository';
import { TenantRepository } from '@app/common/repositories/tenant.repository';
import { TenantContextService } from '@app/common/multi-tenancy';

/**
 * Service for handling task assignments
 */
@Injectable()
export class TaskAssignmentService {
    private readonly logger = new Logger(TaskAssignmentService.name);

    constructor(
        private readonly taskRepository: TaskRepository,
        private readonly taskNotificationService: TaskNotificationService,
        private readonly userRepository: PublicUserRepository,
        private readonly tenantRepository: TenantRepository,
        private readonly tenantContextService: TenantContextService
    ) {}

    /**
     * Assigns a task to a user
     * @param taskId The task ID
     * @param assignTaskDto The assignment DTO
     * @param assignedBy The ID of the user making the assignment
     * @param assignedByName The name of the user making the assignment
     * @returns The updated task
     */
    async assignTask(
        taskId: string,
        assignTaskDto: AssignTaskDto,
        assignedBy: string,
        assignedByName: string
    ): Promise<Task> {
        // Find the task with relations
        const task = await this.taskRepository.findOneById(taskId);
        if (!task) {
            throw new NotFoundException(`Task with ID ${taskId} not found`);
        }

        // Get the previous assignee information if exists
        let previousAssigneeEmail: string | null = null;
        let previousAssigneeName: string | null = null;
        if (task.assigneeId) {
            const previousAssignee = await this.userRepository.findById(task.assigneeId);
            if (previousAssignee) {
                previousAssigneeEmail = previousAssignee.email;
                previousAssigneeName =
                    `${previousAssignee.firstName || ''} ${previousAssignee.lastName || ''}`.trim() ||
                    previousAssignee.email;
            }
        }

        // Get new assignee information
        const newAssignee = await this.userRepository.findById(assignTaskDto.assigneeId);
        if (!newAssignee) {
            throw new NotFoundException(`User with ID ${assignTaskDto.assigneeId} not found`);
        }
        const newAssigneeName =
            `${newAssignee.firstName || ''} ${newAssignee.lastName || ''}`.trim() ||
            newAssignee.email;

        // Update the task with the new assignee
        task.assigneeId = assignTaskDto.assigneeId;
        task.updatedAt = new Date();

        // Save the updated task
        const updatedTask = await this.taskRepository.save(task);

        // Get tenant information
        const tenantId = this.tenantContextService.getTenantId();
        const tenant = await this.tenantRepository.findById(tenantId);
        const tenantName = tenant?.displayName || 'Organization';

        // Send notification
        await this.taskNotificationService.sendTaskAssignmentNotification(
            updatedTask,
            previousAssigneeEmail,
            previousAssigneeName,
            newAssignee.email,
            newAssigneeName,
            assignedByName,
            tenantId,
            tenantName
        );

        return updatedTask;
    }

    /**
     * Auto-assigns a task based on case assignees and workload
     * @param task The task to assign
     * @param caseAssignees Array of user IDs assigned to the case
     * @returns The assigned task
     */
    async autoAssignTask(task: Task, caseAssignees: string[]): Promise<Task> {
        // If no assignees available, leave unassigned
        if (!caseAssignees || caseAssignees.length === 0) {
            this.logger.warn(
                `No assignees available for case ${task.caseId}, leaving task unassigned`
            );
            return task;
        }

        // Get workload for each assignee
        const assigneeWorkloads = await Promise.all(
            caseAssignees.map(async (userId) => {
                const openTasksCount = await this.taskRepository.countOpenTasksByAssigneeId(userId);
                return { userId, openTasksCount };
            })
        );

        // Sort by workload (ascending)
        assigneeWorkloads.sort((a, b) => a.openTasksCount - b.openTasksCount);

        // Assign to the user with the lowest workload
        const leastLoadedUser = assigneeWorkloads[0];
        task.assigneeId = leastLoadedUser.userId;

        this.logger.log(
            `Auto-assigned task ${task.id} to user ${leastLoadedUser.userId} with workload ${leastLoadedUser.openTasksCount}`
        );

        return this.taskRepository.save(task);
    }

    /**
     * Unassigns a task
     * @param taskId The task ID
     * @param unassignedBy The ID of the user unassigning the task
     * @param unassignedByName The name of the user unassigning the task
     * @returns The updated task
     */
    async unassignTask(taskId: string, unassignedBy: string): Promise<Task> {
        // Find the task
        const task = await this.taskRepository.findOneById(taskId);
        if (!task) {
            throw new NotFoundException(`Task with ID ${taskId} not found`);
        }

        // Update the task to remove assignee
        const previousAssigneeId = task.assigneeId;
        task.assigneeId = null;
        task.updatedAt = new Date();

        // Save the updated task
        const updatedTask = await this.taskRepository.save(task);

        // Log the unassignment
        this.logger.log(
            `Task ${task.id} unassigned from user ${previousAssigneeId} by ${unassignedBy}`
        );

        return updatedTask;
    }
}
