import { Injectable, Logger } from '@nestjs/common';
import { Task, TaskPriority } from '@app/common/typeorm/entities/tenant/task.entity';

/**
 * Simplified service for task priorities
 */
@Injectable()
export class TaskPrioritizationService {
    private readonly logger = new Logger(TaskPrioritizationService.name);

    /**
     * Gets the numeric value for a priority level for sorting
     * @param priority The task priority
     * @returns A numeric value for sorting (5 = highest, 1 = lowest)
     */
    getPriorityValue(priority: TaskPriority): number {
        const priorityValues = {
            [TaskPriority.HIGHEST]: 5,
            [TaskPriority.HIGH]: 4,
            [TaskPriority.MEDIUM]: 3,
            [TaskPriority.LOW]: 2,
            [TaskPriority.LOWEST]: 1
        };

        return priorityValues[priority] || 3; // Default to MEDIUM if unknown
    }

    /**
     * Suggests a default priority based on due date
     * This is a very simple suggestion based only on due date
     * @param task The task to suggest priority for
     * @returns The suggested priority level
     */
    suggestPriority(task: Task): TaskPriority {
        // Default to MEDIUM
        if (!task.dueDate) {
            return TaskPriority.MEDIUM;
        }

        try {
            const now = new Date();
            const dueDate = new Date(task.dueDate);
            const daysUntilDue = Math.max(
                0,
                Math.floor((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
            );

            // Simple rules based only on due date
            if (daysUntilDue <= 1) {
                return TaskPriority.HIGHEST;
            } else if (daysUntilDue <= 3) {
                return TaskPriority.HIGH;
            } else if (daysUntilDue <= 14) {
                return TaskPriority.MEDIUM;
            } else if (daysUntilDue <= 30) {
                return TaskPriority.LOW;
            } else {
                return TaskPriority.LOWEST;
            }
        } catch (error) {
            this.logger.warn(`Error calculating priority: ${error.message}`);
            return TaskPriority.MEDIUM;
        }
    }
}
