import { Injectable, Logger } from '@nestjs/common';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import { GenericNotificationVariables } from '@app/common/communication/interfaces/communication-job.interface';
import { Task, TaskStatus, TaskPriority } from '@app/common/typeorm/entities/tenant/task.entity';
import {
    TaskNotificationType,
    TaskSystemUser,
    TASK_DEFAULT_VALUES,
    TASK_TEMPLATE_CONSTANTS
} from '@app/common/enums/task-notification-types.enum';

@Injectable()
export class TaskNotificationService {
    private readonly logger = new Logger(TaskNotificationService.name);

    constructor(private readonly messageProducer: MessageProducerService) {}

    async sendTaskCreatedNotification(
        task: Task,
        creatorName: string,
        assigneeEmail: string,
        assigneeName: string,
        tenantId: string,
        tenantName: string
    ): Promise<void> {
        try {
            const variables: GenericNotificationVariables = {
                type: TASK_TEMPLATE_CONSTANTS.GENERIC_NOTIFICATION,
                tenantName,
                recipientName: assigneeName,
                message: `
                    Subject: New Task Assigned: ${task.title}

                    A new task has been assigned to you by ${creatorName}.

                    Task Details:
                    - Title: ${task.title}
                    - Description: ${task.description || TASK_DEFAULT_VALUES.NO_DESCRIPTION}
                    - Priority: ${this.formatPriority(task.priority)}
                    - Due Date: ${task.dueDate ? new Date(task.dueDate).toLocaleDateString() : TASK_DEFAULT_VALUES.NO_DUE_DATE}
                    - Case: ${task.caseId || TASK_DEFAULT_VALUES.NO_CASE_ASSOCIATION}

                    Please log in to view and manage this task.
                `,
                clickAction: `${process.env.APP_URL}/tasks/${task.id}`,
                currentYear: new Date().getFullYear().toString()
            };

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: TaskSystemUser.SYSTEM as string,
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: [assigneeEmail]
                },
                variables,
                metadata: {
                    notificationType: TaskNotificationType.TASK_CREATED as string,
                    taskId: task.id,
                    caseId: task.caseId
                }
            });

            this.logger.log(
                `Task creation notification sent to ${assigneeEmail} for task ${task.id}`
            );
        } catch (error) {
            this.logger.error(
                `Failed to send task creation notification: ${error.message}`,
                error.stack
            );
        }
    }

    async sendTaskAssignmentNotification(
        task: Task,
        previousAssigneeEmail: string | null,
        previousAssigneeName: string | null,
        newAssigneeEmail: string,
        newAssigneeName: string,
        assignedByName: string,
        tenantId: string,
        tenantName: string
    ): Promise<void> {
        try {
            const variables: GenericNotificationVariables = {
                type: TASK_TEMPLATE_CONSTANTS.GENERIC_NOTIFICATION,
                tenantName,
                recipientName: newAssigneeName,
                message: `
                    Subject: Task Reassigned: ${task.title}

                    A task has been reassigned to you by ${assignedByName}.

                    Task Details:
                    - Title: ${task.title}
                    - Description: ${task.description || TASK_DEFAULT_VALUES.NO_DESCRIPTION}
                    - Priority: ${this.formatPriority(task.priority)}
                    - Status: ${this.formatStatus(task.status)}
                    - Due Date: ${task.dueDate ? new Date(task.dueDate).toLocaleDateString() : TASK_DEFAULT_VALUES.NO_DUE_DATE}
                    ${previousAssigneeName ? `- Previously assigned to: ${previousAssigneeName}` : ''}

                    Please log in to view and manage this task.
                `,
                clickAction: `${process.env.APP_URL}/tasks/${task.id}`,
                currentYear: new Date().getFullYear().toString()
            };

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: TaskSystemUser.SYSTEM as string,
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: [newAssigneeEmail]
                },
                variables,
                metadata: {
                    notificationType: TaskNotificationType.TASK_REASSIGNED as string,
                    taskId: task.id,
                    caseId: task.caseId
                }
            });

            if (previousAssigneeEmail && previousAssigneeEmail !== newAssigneeEmail) {
                const unassignVariables: GenericNotificationVariables = {
                    type: TASK_TEMPLATE_CONSTANTS.GENERIC_NOTIFICATION,
                    tenantName,
                    recipientName:
                        previousAssigneeName || TASK_DEFAULT_VALUES.DEFAULT_RECIPIENT_NAME,
                    message: `
                        Subject: Task Unassigned: ${task.title}

                        The task "${task.title}" has been reassigned to ${newAssigneeName} by ${assignedByName}.

                        You are no longer responsible for this task.
                    `,
                    currentYear: new Date().getFullYear().toString()
                };

                await this.messageProducer.enqueueMessage({
                    tenantId,
                    userId: TaskSystemUser.SYSTEM as string,
                    channels: [COMMUNICATION_CHANNELS.EMAIL],
                    recipients: {
                        email: [previousAssigneeEmail]
                    },
                    variables: unassignVariables,
                    metadata: {
                        notificationType: TaskNotificationType.TASK_UNASSIGNED as string,
                        taskId: task.id,
                        caseId: task.caseId
                    }
                });
            }

            this.logger.log(`Task assignment notification sent for task ${task.id}`);
        } catch (error) {
            this.logger.error(
                `Failed to send task assignment notification: ${error.message}`,
                error.stack
            );
        }
    }

    async sendTaskStatusUpdateNotification(
        task: Task,
        previousStatus: TaskStatus,
        updatedByName: string,
        assigneeEmail: string,
        assigneeName: string,
        tenantId: string,
        tenantName: string,
        additionalRecipients?: string[]
    ): Promise<void> {
        try {
            const isCompleted = task.status === TaskStatus.DONE;
            const wasCancelled = task.status === TaskStatus.BLOCKED;

            const variables: GenericNotificationVariables = {
                type: TASK_TEMPLATE_CONSTANTS.GENERIC_NOTIFICATION,
                tenantName,
                recipientName: assigneeName,
                message: `
                    Subject: Task ${this.formatStatus(task.status)}: ${task.title}

                    The task "${task.title}" status has been updated by ${updatedByName}.

                    Status Change:
                    - Previous Status: ${this.formatStatus(previousStatus)}
                    - New Status: ${this.formatStatus(task.status)}
                    ${isCompleted ? '\n✅ This task has been marked as completed.' : ''}
                    ${wasCancelled ? '\n⚠️ This task has been blocked.' : ''}

                    Task Details:
                    - Priority: ${this.formatPriority(task.priority)}
                    - Due Date: ${task.dueDate ? new Date(task.dueDate).toLocaleDateString() : TASK_DEFAULT_VALUES.NO_DUE_DATE}
                `,
                clickAction: `${process.env.APP_URL}/tasks/${task.id}`,
                currentYear: new Date().getFullYear().toString()
            };

            const recipients = [assigneeEmail, ...(additionalRecipients || [])];

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: TaskSystemUser.SYSTEM as string,
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: recipients
                },
                variables,
                metadata: {
                    notificationType: TaskNotificationType.TASK_STATUS_UPDATED as string,
                    taskId: task.id,
                    caseId: task.caseId,
                    previousStatus,
                    newStatus: task.status
                }
            });

            this.logger.log(`Task status update notification sent for task ${task.id}`);
        } catch (error) {
            this.logger.error(
                `Failed to send task status update notification: ${error.message}`,
                error.stack
            );
        }
    }

    async sendTaskDueDateReminderNotification(
        task: Task,
        assigneeEmail: string,
        assigneeName: string,
        tenantId: string,
        tenantName: string,
        daysUntilDue: number
    ): Promise<void> {
        try {
            const isOverdue = daysUntilDue < 0;
            const dueDate = new Date(task.dueDate!);

            const variables: GenericNotificationVariables = {
                type: TASK_TEMPLATE_CONSTANTS.GENERIC_NOTIFICATION,
                tenantName,
                recipientName: assigneeName,
                message: `
                    Subject: ${
                        isOverdue
                            ? `⚠️ Overdue Task: ${task.title}`
                            : `📅 Task Due Soon: ${task.title}`
                    }

                    ${
                        isOverdue
                            ? `This task is ${Math.abs(daysUntilDue)} day(s) overdue.`
                            : `This task is due in ${daysUntilDue} day(s).`
                    }

                    Task Details:
                    - Title: ${task.title}
                    - Description: ${task.description || TASK_DEFAULT_VALUES.NO_DESCRIPTION}
                    - Priority: ${this.formatPriority(task.priority)}
                    - Status: ${this.formatStatus(task.status)}
                    - Due Date: ${dueDate.toLocaleDateString()}

                    Please take action on this task as soon as possible.
                `,
                clickAction: `${process.env.APP_URL}/tasks/${task.id}`,
                currentYear: new Date().getFullYear().toString()
            };

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: TaskSystemUser.SYSTEM as string,
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: [assigneeEmail]
                },
                variables,
                metadata: {
                    notificationType: isOverdue
                        ? (TaskNotificationType.TASK_OVERDUE as string)
                        : (TaskNotificationType.TASK_DUE_REMINDER as string),
                    taskId: task.id,
                    caseId: task.caseId,
                    daysUntilDue,
                    dueDate: dueDate.toISOString()
                }
            });

            this.logger.log(
                `Task due date reminder sent to ${assigneeEmail} for task ${task.id} (${
                    isOverdue ? 'overdue' : `due in ${daysUntilDue} days`
                })`
            );
        } catch (error) {
            this.logger.error(
                `Failed to send task due date reminder: ${error.message}`,
                error.stack
            );
        }
    }

    async sendTaskCommentNotification(
        task: Task,
        commentAuthorName: string,
        comment: string,
        recipientEmails: string[],
        recipientNames: string[],
        tenantId: string,
        tenantName: string
    ): Promise<void> {
        try {
            const variables: GenericNotificationVariables = {
                type: TASK_TEMPLATE_CONSTANTS.GENERIC_NOTIFICATION,
                tenantName,
                recipientName: TASK_DEFAULT_VALUES.TEAM_MEMBER_NAME,
                message: `
                    Subject: New Comment on Task: ${task.title}

                    ${commentAuthorName} added a comment to the task "${task.title}".

                    Comment:
                    "${comment}"

                    Task Details:
                    - Status: ${this.formatStatus(task.status)}
                    - Priority: ${this.formatPriority(task.priority)}
                    - Due Date: ${task.dueDate ? new Date(task.dueDate).toLocaleDateString() : TASK_DEFAULT_VALUES.NO_DUE_DATE}
                `,
                clickAction: `${process.env.APP_URL}/tasks/${task.id}#comments`,
                currentYear: new Date().getFullYear().toString()
            };

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: TaskSystemUser.SYSTEM as string,
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: recipientEmails
                },
                variables,
                metadata: {
                    notificationType: TaskNotificationType.TASK_COMMENT_ADDED as string,
                    taskId: task.id,
                    caseId: task.caseId,
                    commentAuthor: commentAuthorName
                }
            });

            this.logger.log(`Task comment notification sent for task ${task.id}`);
        } catch (error) {
            this.logger.error(
                `Failed to send task comment notification: ${error.message}`,
                error.stack
            );
        }
    }

    private formatPriority(priority: TaskPriority): string {
        const priorityMap: Record<TaskPriority, string> = {
            [TaskPriority.LOWEST]: 'Lowest',
            [TaskPriority.LOW]: 'Low',
            [TaskPriority.MEDIUM]: 'Medium',
            [TaskPriority.HIGH]: 'High',
            [TaskPriority.HIGHEST]: 'Highest'
        };
        return priorityMap[priority] || priority;
    }

    private formatStatus(status: TaskStatus): string {
        const statusMap: Record<TaskStatus, string> = {
            [TaskStatus.OPEN]: 'Open',
            [TaskStatus.IN_PROGRESS]: 'In Progress',
            [TaskStatus.DONE]: 'Done',
            [TaskStatus.BLOCKED]: 'Blocked'
        };
        return statusMap[status] || status;
    }
}
