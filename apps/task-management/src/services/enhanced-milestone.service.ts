import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { Milestone, MilestoneStatus } from '@app/common/typeorm/entities/tenant/milestone.entity';
import { Task, TaskStatus } from '@app/common/typeorm/entities/tenant/task.entity';
import { CaseAuditService } from 'apps/case-management/src/services/case-audit.service';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';
import { MilestoneAction } from '@app/common/enums/milestone-actions.enum';
import { MilestoneRepository } from '../repositories/milestone.repository';
import { TaskRepository } from '../repositories/task.repository';
import { TaskHistoryRepository } from '../repositories/task-history.repository';
import { TenantConnectionService } from '@app/common/multi-tenancy/tenant-connection.service';
import { TenantContextService } from '@app/common/multi-tenancy/tenant-context.service';
import { TaskOperationType } from '@app/common/enums/task-notification-types.enum';
// Remove direct import of MilestoneSeederService to avoid circular dependencies

export interface MilestoneWithProgress extends Milestone {
    progressPercentage: number;
    taskSummary: {
        total: number;
        completed: number;
        inProgress: number;
        pending: number;
    };
}

export interface CaseStatistics {
    total: number;
    completed: number;
    inProgress: number;
    pending: number;
    averageProgress: number;
}

/**
 * Enhanced Milestone Service - Centralized milestone management
 * Consolidates all milestone functionality from multiple services
 */
@Injectable()
export class EnhancedMilestoneService {
    private readonly logger = new Logger(EnhancedMilestoneService.name);

    constructor(
        private readonly milestoneRepository: MilestoneRepository,
        private readonly taskRepository: TaskRepository,
        private readonly taskHistoryRepository: TaskHistoryRepository,
        private readonly caseAuditService: CaseAuditService,
        private readonly tenantConnectionService: TenantConnectionService,
        private readonly tenantContextService: TenantContextService
    ) {}

    /**
     * Create case-specific milestones and tasks from seeded templates
     * Reads milestone templates that were seeded during tenant creation
     * and creates actual milestone instances for the specific case
     */
    async createDefaultMilestonesForCase(
        caseId: string,
        caseType: string,
        createdBy: string
    ): Promise<Milestone[]> {
        // Get seeded milestones from database
        const tenantId = this.tenantContextService.getTenantId();
        this.logger.log(`Getting tenant data source for tenant: ${tenantId}`);
        const tenantDataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);

        this.logger.log(`Fetching seeded milestones for case type: ${caseType}`);
        const defaultConfig = await this.getSeededMilestones(tenantDataSource, caseType);
        this.logger.log(
            `Found ${defaultConfig.length} seeded milestones for case type: ${caseType}`
        );

        if (defaultConfig.length === 0) {
            this.logger.warn(
                `No default milestones found for case type: ${caseType}. Make sure milestones are seeded.`
            );
            return [];
        }

        this.logger.log(
            `Creating ${defaultConfig.length} default milestones for case ${caseId} (type: ${caseType})`
        );

        const createdMilestones: Milestone[] = [];

        for (const milestoneConfig of defaultConfig) {
            const milestone = await this.createMilestoneWithSeededTasks(
                caseId,
                milestoneConfig,
                createdBy
            );
            createdMilestones.push(milestone);
        }

        // Log milestone creation in audit trail
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.STATUS_CHANGED,
            createdBy,
            'System',
            'auto-generated',
            {
                action: MilestoneAction.DEFAULT_MILESTONES_CREATED,
                caseType,
                milestonesCount: createdMilestones.length,
                milestoneNames: createdMilestones.map((m) => m.name)
            }
        );

        this.logger.log(
            `Successfully created ${createdMilestones.length} default milestones for case ${caseId}`
        );
        return createdMilestones;
    }

    /**
     * Create a single milestone with its tasks from seeded data
     */
    private async createMilestoneWithSeededTasks(
        caseId: string,
        config: any,
        createdBy: string
    ): Promise<Milestone> {
        // Create milestone
        const milestone = await this.milestoneRepository.create({
            name: config.name,
            description: config.description,
            caseId,
            sortOrder: config.sortOrder,
            status: MilestoneStatus.PENDING,
            isDefault: true,
            targetDate: config.targetDays
                ? new Date(Date.now() + config.targetDays * 24 * 60 * 60 * 1000)
                : null,
            createdBy,
            progressPercentage: 0
        });

        const savedMilestone = await this.milestoneRepository.save(milestone);

        // Create tasks for this milestone
        for (const taskConfig of config.tasks) {
            const task = await this.taskRepository.create({
                title: taskConfig.title,
                description: taskConfig.description,
                priority: taskConfig.priority,
                status: TaskStatus.OPEN,
                caseId,
                milestoneId: savedMilestone.id,
                isDefault: taskConfig.isDefault,
                dueDate: taskConfig.estimatedDays
                    ? new Date(Date.now() + taskConfig.estimatedDays * 24 * 60 * 60 * 1000)
                    : null,
                createdBy
            });

            await this.taskRepository.save(task);
        }

        this.logger.log(`Created milestone '${milestone.name}' with ${config.tasks.length} tasks`);
        return savedMilestone;
    }

    /**
     * Get milestones with progress tracking for a case
     */
    async getMilestonesWithProgress(caseId: string): Promise<MilestoneWithProgress[]> {
        const milestones = await this.milestoneRepository.findByCaseId(caseId);
        return milestones.map((milestone) => this.calculateMilestoneProgress(milestone));
    }

    /**
     * Calculate progress for a single milestone
     */
    private calculateMilestoneProgress(milestone: Milestone): MilestoneWithProgress {
        const totalTasks = milestone.tasks?.length || 0;
        const completedTasks =
            milestone.tasks?.filter((task) => task.status === TaskStatus.DONE).length || 0;
        const inProgressTasks =
            milestone.tasks?.filter((task) => task.status === TaskStatus.IN_PROGRESS).length || 0;

        const progressPercentage =
            totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

        // Determine milestone status based on task completion
        let status = MilestoneStatus.PENDING;
        if (completedTasks === totalTasks && totalTasks > 0) {
            status = MilestoneStatus.COMPLETED;
        } else if (completedTasks > 0 || inProgressTasks > 0) {
            status = MilestoneStatus.IN_PROGRESS;
        }

        return {
            ...milestone,
            progressPercentage,
            status,
            taskSummary: {
                total: totalTasks,
                completed: completedTasks,
                inProgress: inProgressTasks,
                pending: totalTasks - completedTasks - inProgressTasks
            }
        };
    }

    /**
     * Update milestone progress when a task status changes
     * This is called by the task service when task status changes
     */
    async updateMilestoneProgress(milestoneId: string, triggeredByTaskId?: string): Promise<void> {
        const milestone = await this.milestoneRepository.findByIdWithTasks(milestoneId);

        if (!milestone) {
            this.logger.warn(`Milestone ${milestoneId} not found for progress update`);
            return;
        }

        const oldStatus = milestone.status;
        const oldProgress = milestone.progressPercentage;

        // Calculate new progress
        const progressData = this.calculateMilestoneProgress(milestone);

        // Update milestone with new progress
        milestone.progressPercentage = progressData.progressPercentage;
        milestone.status = progressData.status;

        // Set completion date if milestone is completed
        if (progressData.status === MilestoneStatus.COMPLETED && !milestone.completionDate) {
            milestone.completionDate = new Date();
        } else if (progressData.status !== MilestoneStatus.COMPLETED) {
            milestone.completionDate = null;
        }

        milestone.updatedAt = new Date();
        await this.milestoneRepository.save(milestone);

        // Log significant progress changes
        if (
            oldStatus !== milestone.status ||
            Math.abs(oldProgress - milestone.progressPercentage) >= 10
        ) {
            await this.caseAuditService.logAction(
                milestone.caseId,
                CaseAuditAction.STATUS_CHANGED,
                'system',
                'System',
                'auto-generated',
                {
                    action: MilestoneAction.MILESTONE_PROGRESS_UPDATED,
                    milestoneId: milestone.id,
                    milestoneName: milestone.name,
                    oldStatus,
                    newStatus: milestone.status,
                    oldProgress,
                    newProgress: milestone.progressPercentage,
                    triggeredByTaskId
                }
            );
        }

        this.logger.debug(
            `Updated milestone ${milestone.name} progress: ${milestone.progressPercentage}% (${milestone.status})`
        );
    }

    /**
     * Get milestone by ID with tasks
     */
    async getMilestoneById(id: string): Promise<Milestone> {
        const milestone = await this.milestoneRepository.findByIdWithTasks(id);

        if (!milestone) {
            throw new NotFoundException(`Milestone with ID ${id} not found`);
        }

        return milestone;
    }

    /**
     * Get case statistics
     */
    async getCaseStatistics(caseId: string): Promise<CaseStatistics> {
        const milestones = await this.getMilestonesWithProgress(caseId);

        const stats: CaseStatistics = {
            total: milestones.length,
            completed: milestones.filter((m) => m.status === MilestoneStatus.COMPLETED).length,
            inProgress: milestones.filter((m) => m.status === MilestoneStatus.IN_PROGRESS).length,
            pending: milestones.filter((m) => m.status === MilestoneStatus.PENDING).length,
            averageProgress: 0
        };

        if (stats.total > 0) {
            const totalProgress = milestones.reduce(
                (sum, m) => sum + (m.progressPercentage || 0),
                0
            );
            stats.averageProgress = Math.round((totalProgress / stats.total) * 100) / 100;
        }

        return stats;
    }

    /**
     * Add a custom task to an existing milestone
     */
    async addTaskToMilestone(
        milestoneId: string,
        taskData: Partial<Task>,
        userId: string,
        userName: string
    ): Promise<Task> {
        const milestone = await this.getMilestoneById(milestoneId);

        const task = await this.taskRepository.create({
            ...taskData,
            milestoneId: milestoneId,
            caseId: milestone.caseId,
            isDefault: false, // Custom tasks are not default
            createdBy: userId
        });

        const savedTask = await this.taskRepository.save(task);

        // Log task addition
        await this.caseAuditService.logAction(
            milestone.caseId,
            CaseAuditAction.STATUS_CHANGED,
            userId,
            userName,
            'user-action',
            {
                action: MilestoneAction.CUSTOM_TASK_ADDED_TO_MILESTONE,
                milestoneId: milestoneId,
                milestoneName: milestone.name,
                taskId: savedTask.id,
                taskTitle: savedTask.title
            }
        );

        // Update milestone progress after adding task
        await this.updateMilestoneProgress(milestoneId, savedTask.id);

        return savedTask;
    }

    /**
     * Update a task within a milestone context
     * This ensures milestone progress is updated when task status changes
     */
    async updateTaskInMilestone(
        milestoneId: string,
        taskId: string,
        updateData: Partial<Task>,
        userId: string,
        userName: string
    ): Promise<Task> {
        // Get the milestone to verify it exists
        const milestone = await this.getMilestoneById(milestoneId);

        // Get the task and verify it belongs to this milestone
        const task = await this.taskRepository.findOne({
            where: { id: taskId }
        });

        if (!task) {
            throw new NotFoundException(`Task with ID ${taskId} not found`);
        }

        if (task.milestoneId !== milestoneId) {
            throw new BadRequestException(
                `Task ${taskId} does not belong to milestone ${milestoneId}`
            );
        }

        // Store old status for comparison and history
        const oldStatus = task.status;

        // Validate status transition if status is being changed
        if (updateData.status && updateData.status !== oldStatus) {
            this.validateStatusTransition(oldStatus, updateData.status);
        }

        // Update task properties
        Object.assign(task, {
            ...updateData,
            updatedAt: new Date()
        });

        // Save the updated task
        const updatedTask = await this.taskRepository.save(task);

        // Record status change in history if status changed
        if (updateData.status && updateData.status !== oldStatus) {
            await this.taskHistoryRepository.save({
                taskId: updatedTask.id,
                fromStatus: oldStatus,
                toStatus: updatedTask.status,
                changedBy: userId,
                changedByName: userName,
                metadata: {
                    action: TaskOperationType.STATUS_CHANGED,
                    updatedInMilestoneContext: true,
                    milestoneId: milestoneId
                }
            });

            // Update milestone progress when task status changes
            await this.updateMilestoneProgress(milestoneId, taskId);

            // Log the task update in case audit
            await this.caseAuditService.logAction(
                milestone.caseId,
                CaseAuditAction.STATUS_CHANGED,
                userId,
                userName,
                'user-action',
                {
                    action: MilestoneAction.TASK_STATUS_UPDATED_IN_MILESTONE,
                    milestoneId: milestoneId,
                    milestoneName: milestone.name,
                    taskId: taskId,
                    taskTitle: updatedTask.title,
                    oldStatus,
                    newStatus: updatedTask.status
                }
            );
        }

        this.logger.log(
            `Updated task ${taskId} in milestone ${milestoneId}. Status: ${oldStatus} -> ${updatedTask.status}`
        );

        return updatedTask;
    }

    /**
     * Validate status transition for tasks
     * Only allows OPEN -> DONE transition (one-way, no reopening)
     */
    private validateStatusTransition(fromStatus: TaskStatus, toStatus: TaskStatus): void {
        // Validate that only OPEN and DONE statuses are used
        const allowedStatuses = [TaskStatus.OPEN, TaskStatus.DONE];

        if (!allowedStatuses.includes(fromStatus)) {
            throw new BadRequestException(
                `Invalid current status ${fromStatus}. Only OPEN and DONE statuses are allowed.`
            );
        }

        if (!allowedStatuses.includes(toStatus)) {
            throw new BadRequestException(
                `Invalid target status ${toStatus}. Only OPEN and DONE statuses are allowed.`
            );
        }

        // Define valid transitions - OPEN -> DONE only (no reopening)
        const validTransitions: Record<TaskStatus, TaskStatus[]> = {
            [TaskStatus.OPEN]: [TaskStatus.DONE],
            [TaskStatus.DONE]: [], // Cannot transition from DONE to any status
            [TaskStatus.IN_PROGRESS]: [], // Not allowed
            [TaskStatus.BLOCKED]: [] // Not allowed
        };

        // Check if the transition is valid
        if (!validTransitions[fromStatus]?.includes(toStatus)) {
            if (fromStatus === TaskStatus.DONE) {
                throw new BadRequestException(
                    `Cannot reopen a task that is marked as DONE. Tasks can only transition from OPEN to DONE.`
                );
            }
            throw new BadRequestException(
                `Invalid status transition from ${fromStatus} to ${toStatus}. Only OPEN -> DONE transition is allowed.`
            );
        }
    }

    /**
     * Get seeded milestones for a case type from database
     */
    private async getSeededMilestones(tenantDataSource: any, caseType: string): Promise<any[]> {
        try {
            this.logger.log(
                `Executing query to fetch seeded milestones for case type: ${caseType}`
            );
            const milestones = await tenantDataSource.query(
                `
                SELECT
                    m.id,
                    m.name,
                    m.description,
                    m.order_index as "sortOrder",
                    m.target_days as "targetDays",
                    json_agg(
                        json_build_object(
                            'title', t.title,
                            'description', t.description,
                            'priority', t.priority,
                            'estimatedDays', t.estimated_days,
                            'isDefault', t.is_required
                        ) ORDER BY t.order_index
                    ) as tasks
                FROM default_milestones m
                LEFT JOIN default_milestone_tasks t ON m.id = t.milestone_id
                WHERE m.case_type = $1
                GROUP BY m.id, m.name, m.description, m.order_index, m.target_days
                ORDER BY m.order_index
            `,
                [caseType]
            );

            this.logger.log(
                `Query returned ${milestones.length} milestones for case type: ${caseType}`
            );
            if (milestones.length > 0) {
                this.logger.log(`First milestone: ${JSON.stringify(milestones[0])}`);
            }
            return milestones;
        } catch (error) {
            this.logger.error(`Error fetching seeded milestones for case type ${caseType}:`, error);
            return [];
        }
    }
}
