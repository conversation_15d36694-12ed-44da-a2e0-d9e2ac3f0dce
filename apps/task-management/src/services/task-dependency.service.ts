import {
    Injectable,
    Logger,
    NotFoundException,
    BadRequestException,
    ConflictException
} from '@nestjs/common';
import { TaskDependencyRepository } from '../repositories/task-dependency.repository';
import { TaskRepository } from '../repositories/task.repository';
import { TaskDependency } from '@app/common/typeorm/entities/tenant/task-dependency.entity';
import { TaskStatus } from '@app/common/typeorm/entities/tenant/task.entity';
import { AddDependencyDto } from '../dto/add-dependency.dto';

/**
 * Service for managing task dependencies
 */
@Injectable()
export class TaskDependencyService {
    private readonly logger = new Logger(TaskDependencyService.name);

    constructor(
        private readonly taskDependencyRepository: TaskDependencyRepository,
        private readonly taskRepository: TaskRepository
    ) {}

    /**
     * Adds a dependency to a task
     * @param taskId The task ID
     * @param addDependencyDto The dependency DTO
     * @param createdBy The ID of the user creating the dependency
     * @returns The created dependency
     */
    async addDependency(
        taskId: string,
        addDependencyDto: AddDependencyDto,
        createdBy: string
    ): Promise<TaskDependency> {
        const { dependsOnId } = addDependencyDto;

        // Verify both tasks exist
        const task = await this.taskRepository.findOneById(taskId);
        if (!task) {
            throw new NotFoundException(`Task with ID ${taskId} not found`);
        }

        const dependsOnTask = await this.taskRepository.findOneById(dependsOnId);
        if (!dependsOnTask) {
            throw new NotFoundException(`Dependency task with ID ${dependsOnId} not found`);
        }

        // Prevent self-dependency
        if (taskId === dependsOnId) {
            throw new BadRequestException('A task cannot depend on itself');
        }

        // Check if the dependency already exists
        const existingDependency = await this.taskDependencyRepository.findOne({
            where: { taskId, dependsOnId }
        });

        if (existingDependency) {
            throw new ConflictException('This dependency already exists');
        }

        // Check for circular dependencies
        const wouldCreateCircular =
            await this.taskDependencyRepository.wouldCreateCircularDependency(taskId, dependsOnId);

        if (wouldCreateCircular) {
            throw new BadRequestException(
                'Adding this dependency would create a circular reference'
            );
        }

        // Create the dependency
        const dependency = await this.taskDependencyRepository.create({
            taskId,
            dependsOnId,
            createdBy
        });

        return this.taskDependencyRepository.save(dependency);
    }

    /**
     * Gets dependencies for a task
     * @param taskId The task ID
     * @returns Array of dependencies
     */
    async getDependencies(taskId: string): Promise<TaskDependency[]> {
        // Verify task exists
        const task = await this.taskRepository.findOneById(taskId);
        if (!task) {
            throw new NotFoundException(`Task with ID ${taskId} not found`);
        }

        return this.taskDependencyRepository.findByTaskId(taskId);
    }

    /**
     * Gets a specific dependency by task ID and depends-on ID
     * @param taskId The task ID
     * @param dependsOnId The ID of the task it depends on
     * @returns The dependency or null if not found
     */
    async getDependencyByTaskAndDependsOn(
        taskId: string,
        dependsOnId: string
    ): Promise<TaskDependency | null> {
        return this.taskDependencyRepository.findOne({
            where: { taskId, dependsOnId }
        });
    }

    /**
     * Gets tasks that depend on a specific task
     * @param taskId The task ID
     * @returns Array of dependent tasks
     */
    async getDependentTasks(taskId: string): Promise<TaskDependency[]> {
        // Verify task exists
        const task = await this.taskRepository.findOneById(taskId);
        if (!task) {
            throw new NotFoundException(`Task with ID ${taskId} not found`);
        }

        return this.taskDependencyRepository.findDependentTasks(taskId);
    }

    /**
     * Removes a dependency
     * @param taskId The task ID
     * @param dependencyId The dependency ID
     * @param userId The ID of the user removing the dependency
     * @returns True if the dependency was removed
     */
    async removeDependency(taskId: string, dependencyId: string, userId: string): Promise<boolean> {
        // Verify task exists
        const task = await this.taskRepository.findOneById(taskId);
        if (!task) {
            throw new NotFoundException(`Task with ID ${taskId} not found`);
        }

        // Verify dependency exists and belongs to the task
        const dependency = await this.taskDependencyRepository.findOneById(dependencyId);
        if (!dependency) {
            throw new NotFoundException(`Dependency with ID ${dependencyId} not found`);
        }

        if (dependency.taskId !== taskId) {
            throw new BadRequestException(
                `Dependency with ID ${dependencyId} does not belong to task ${taskId}`
            );
        }

        this.logger.log(
            `User ${userId} is removing dependency ${dependencyId} from task ${taskId}`
        );

        // Remove the dependency
        return this.taskDependencyRepository.removeById(dependencyId);
    }

    /**
     * Checks if all dependencies of a task are completed
     * @param taskId The task ID
     * @returns True if all dependencies are completed
     */
    async areAllDependenciesCompleted(taskId: string): Promise<boolean> {
        const dependencies = await this.taskDependencyRepository.findByTaskId(taskId);

        // If no dependencies, return true
        if (dependencies.length === 0) {
            return true;
        }

        // Check if all dependencies are in DONE status
        for (const dependency of dependencies) {
            const dependsOnTask = dependency.dependsOn;
            if (dependsOnTask.status !== TaskStatus.DONE) {
                return false;
            }
        }

        return true;
    }

    /**
     * Checks if a task has any incomplete dependent tasks
     * @param taskId The task ID
     * @returns True if there are incomplete dependent tasks
     */
    async hasIncompleteDependentTasks(taskId: string): Promise<boolean> {
        const dependentTasks = await this.taskDependencyRepository.findDependentTasks(taskId);

        // If no dependent tasks, return false
        if (dependentTasks.length === 0) {
            return false;
        }

        // Check if any dependent tasks are not in DONE status
        for (const dependency of dependentTasks) {
            if (dependency.task.status !== TaskStatus.DONE) {
                return true;
            }
        }

        return false;
    }
}
