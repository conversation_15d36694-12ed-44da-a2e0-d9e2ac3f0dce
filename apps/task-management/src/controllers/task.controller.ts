import {
    Controller,
    Get,
    Post,
    Put,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    Req,
    ParseUUIDPipe,
    ParseBoolPipe
} from '@nestjs/common';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { HasAnyRole } from '@app/common/roles/decorators';
import { AppRole } from '@app/common/enums/roles.enums';
import { TaskService } from '../services/task.service';
import { TaskAssignmentService } from '../services/task-assignment.service';
import { EnhancedMilestoneService } from '../services/enhanced-milestone.service';
import { CreateTaskDto } from '../dto/create-task.dto';
import { UpdateTaskDto } from '../dto/update-task.dto';
import { TaskFilterDto } from '../dto/task-filter.dto';
import { TaskStatusChangeDto } from '../dto/task-status-change.dto';
import { AssignTaskDto } from '../dto/assign-task.dto';
import { AddTaskToMilestoneDto } from '../dto/add-task-to-milestone.dto';
import { UpdateTaskInMilestoneDto } from '../dto/update-task-in-milestone.dto';
import { Request } from 'express';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { ConveyancerCaseGuard } from '@app/common/guards';
import { RequireSuperAdmin } from '@app/common/permissions/role-group.decorators';
import { ApiResponseUtil } from '@app/common/utils/api-response.util';

@Controller('tasks')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, ConveyancerCaseGuard)
export class TaskController {
    constructor(
        private readonly taskService: TaskService,
        private readonly taskAssignmentService: TaskAssignmentService,
        private readonly enhancedMilestoneService: EnhancedMilestoneService
    ) {}

    /**
     * Create a new task
     */
    @Post()
    @HasAnyRole(AppRole.ADMIN)
    async createTask(@Body() createTaskDto: CreateTaskDto, @Req() request: Request) {
        const user = request['user'];
        const userId = user.systemUserId;
        const userName = user.preferred_username || user.email;

        return this.taskService.createTask(createTaskDto, userId, userName);
    }

    /**
     * Get a task by ID
     */
    @Get(':id')
    @HasAnyRole(AppRole.ADMIN)
    async getTaskById(@Param('id', ParseUUIDPipe) id: string, @Req() request: Request) {
        const user = request['user'];
        return this.taskService.getTaskById(id, user.systemUserId);
    }

    /**
     * Get tasks with filtering (requires caseId)
     */
    @Get()
    @HasAnyRole(AppRole.ADMIN)
    async getTasks(@Query() filterDto: TaskFilterDto) {
        return this.taskService.getTasks(filterDto);
    }

    /**
     * Get tasks for a specific case
     */
    @Get('case/:caseId')
    @HasAnyRole(AppRole.ADMIN)
    async getTasksByCaseId(@Param('caseId', ParseUUIDPipe) caseId: string) {
        return this.taskService.getTasksByCaseId(caseId);
    }

    /**
     * Get tasks for a specific case in a hierarchical structure based on dependencies
     */
    @Get('case/:caseId/hierarchy')
    @HasAnyRole(AppRole.ADMIN)
    async getTasksHierarchyByCaseId(@Param('caseId', ParseUUIDPipe) caseId: string) {
        return this.taskService.getTasksHierarchyByCaseId(caseId);
    }

    /**
     * Update a task
     */
    @Put(':id')
    @HasAnyRole(AppRole.ADMIN)
    async updateTask(
        @Param('id', ParseUUIDPipe) id: string,
        @Body() updateTaskDto: UpdateTaskDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const userId = user.systemUserId;
        return this.taskService.updateTask(id, updateTaskDto, userId);
    }

    /**
     * Change task status
     */
    @Put(':id/status')
    @HasAnyRole(AppRole.ADMIN)
    async changeTaskStatus(
        @Param('id', ParseUUIDPipe) id: string,
        @Body() statusChangeDto: TaskStatusChangeDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const userId = user.systemUserId;
        const userName = user.preferred_username || user.email;
        return this.taskService.changeTaskStatus(id, statusChangeDto, userId, userName);
    }

    /**
     * Assign a task to a user
     */
    @Put(':id/assign')
    @HasAnyRole(AppRole.ADMIN)
    async assignTask(
        @Param('id', ParseUUIDPipe) id: string,
        @Body() assignTaskDto: AssignTaskDto,
        @Req() request: Request
    ) {
        const user = (request as any).user;
        const userId = user.sub || user.id;
        const userName = user.preferred_username || user.email;
        return this.taskAssignmentService.assignTask(id, assignTaskDto, userId, userName);
    }

    /**
     * Unassign a task
     */
    @Put(':id/unassign')
    @HasAnyRole(AppRole.ADMIN)
    async unassignTask(@Param('id', ParseUUIDPipe) id: string, @Req() request: Request) {
        const user = (request as any).user;
        const userId = user.sub || user.id;
        return this.taskAssignmentService.unassignTask(id, userId);
    }

    /**
     * Delete a task
     * @param id The task ID
     * @param force Whether to force deletion even if there are dependent tasks
     * @returns True if the task was deleted
     */
    @Delete(':id')
    @RequireSuperAdmin()
    async deleteTask(
        @Param('id', ParseUUIDPipe) id: string,
        @Query('force', ParseBoolPipe) force: boolean = false,
        @Req() request: Request
    ) {
        const user = request['user'];
        const result = await this.taskService.deleteTask(id, user.systemUserId, force);
        return ApiResponseUtil.ok(result, 'Task deleted successfully');
    }

    /**
     * Add a task to a milestone
     * This endpoint ensures the task is properly associated with the milestone
     * and triggers milestone progress calculation
     */
    @Post('milestones/:milestoneId/tasks')
    @RequireSuperAdmin()
    async addTaskToMilestone(
        @Param('milestoneId', ParseUUIDPipe) milestoneId: string,
        @Body() addTaskDto: AddTaskToMilestoneDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const userId = user.systemUserId;
        const userName = user.preferred_username || user.email;

        const task = await this.enhancedMilestoneService.addTaskToMilestone(
            milestoneId,
            addTaskDto,
            userId,
            userName
        );

        return ApiResponseUtil.created(task, 'Task added to milestone successfully');
    }

    /**
     * Update a task within a milestone context
     * This endpoint ensures milestone progress is updated when task status changes
     * Simplified to only allow OPEN -> DONE status transitions (one-way, no reopening)
     */
    @Put('milestones/:milestoneId/tasks/:taskId')
    @RequireSuperAdmin()
    async updateTaskInMilestone(
        @Param('milestoneId', ParseUUIDPipe) milestoneId: string,
        @Param('taskId', ParseUUIDPipe) taskId: string,
        @Body() updateTaskDto: UpdateTaskInMilestoneDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const userId = user.systemUserId;
        const userName = user.preferred_username || user.email;

        const task = await this.enhancedMilestoneService.updateTaskInMilestone(
            milestoneId,
            taskId,
            updateTaskDto,
            userId,
            userName
        );

        return ApiResponseUtil.ok(task, 'Task updated successfully');
    }
}
