import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { TaskManagementModule } from './task-management.module';
import { WinstonModule } from 'nest-winston';
import { instance } from '@app/common/utils/logger.util';
import { Config } from '@app/common/config/config';
async function bootstrap() {
    const app = await NestFactory.create(TaskManagementModule, {
        logger: WinstonModule.createLogger({
            instance: instance
        })
    });
    // Global prefix
    app.setGlobalPrefix(Config.TASK_MANAGEMENT_PREFIX);

    const port = Config.TASK_MANAGEMENT_PORT || 3005;
    await app.listen(port);

    const logger = new Logger('TaskManagementService');
    logger.log(`Task Management Service is running on: ${await app.getUrl()}`);
}

bootstrap();
// Rebuild trigger: HealthAggregator fix in CommonModule - 20251014-022856
