import { IsString, <PERSON>NotEmpty, <PERSON>Optional, IsEnum, IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import { TaskStatus, TaskPriority } from '@app/common/typeorm/entities/tenant/task.entity';

/**
 * DTO for adding a task to a milestone
 */
export class AddTaskToMilestoneDto {
    @IsString()
    @IsNotEmpty()
    title: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsEnum(TaskStatus)
    @IsOptional()
    status?: TaskStatus = TaskStatus.OPEN;

    @IsEnum(TaskPriority)
    @IsOptional()
    priority?: TaskPriority = TaskPriority.MEDIUM;

    @IsDate()
    @Type(() => Date)
    @IsOptional()
    dueDate?: Date;

    @IsString()
    @IsOptional()
    assigneeId?: string;
}
