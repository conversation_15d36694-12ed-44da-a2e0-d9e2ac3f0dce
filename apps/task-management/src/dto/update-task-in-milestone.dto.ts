import { IsString, IsOptional, IsEnum, IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import { TaskStatus, TaskPriority } from '@app/common/typeorm/entities/tenant/task.entity';

/**
 * DTO for updating a task within a milestone context
 * This ensures task updates trigger milestone progress updates
 */
export class UpdateTaskInMilestoneDto {
    @IsString()
    @IsOptional()
    title?: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsEnum(TaskStatus)
    @IsOptional()
    status?: TaskStatus;

    @IsEnum(TaskPriority)
    @IsOptional()
    priority?: TaskPriority;

    @IsDate()
    @Type(() => Date)
    @IsOptional()
    dueDate?: Date;

    @IsString()
    @IsOptional()
    assigneeId?: string;

    @IsString()
    @IsOptional()
    comment?: string;
}
