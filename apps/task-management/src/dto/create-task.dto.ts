import { IsString, <PERSON>NotEmpty, <PERSON>Optional, IsUUI<PERSON>, IsEnum, IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import { TaskStatus, TaskPriority } from '@app/common/typeorm/entities/tenant/task.entity';

export class CreateTaskDto {
    @IsString()
    @IsNotEmpty()
    title: string;
    @IsString()
    @IsOptional()
    description?: string;
    @IsEnum(TaskStatus)
    @IsOptional()
    status?: TaskStatus = TaskStatus.OPEN;
    @IsEnum(TaskPriority)
    @IsOptional()
    priority?: TaskPriority = TaskPriority.MEDIUM;
    @IsDate()
    @Type(() => Date)
    @IsOptional()
    dueDate?: Date;
    @IsUUID()
    @IsNotEmpty()
    caseId: string;
    @IsString()
    @IsOptional()
    assigneeId?: string;
}
