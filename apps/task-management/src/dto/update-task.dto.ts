import { IsString, IsOptional, IsEnum, IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import { TaskStatus, TaskPriority } from '@app/common/typeorm/entities/tenant/task.entity';

export class UpdateTaskDto {
    @IsString()
    @IsOptional()
    title?: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsEnum(TaskStatus)
    @IsOptional()
    status?: TaskStatus;

    @IsEnum(TaskPriority)
    @IsOptional()
    priority?: TaskPriority;

    @IsDate()
    @Type(() => Date)
    @IsOptional()
    dueDate?: Date;

    // estimatedEffort and actualEffort fields removed as they're not needed

    @IsString()
    @IsOptional()
    assigneeId?: string;

    // Removed importanceWeight field to simplify the priority system
}
