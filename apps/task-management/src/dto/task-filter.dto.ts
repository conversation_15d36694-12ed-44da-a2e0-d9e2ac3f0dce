import { IsEnum, IsOptional, IsUUID, IsString, IsDate, IsIn } from 'class-validator';
import { Type } from 'class-transformer';
import { TaskStatus, TaskPriority } from '@app/common/typeorm/entities/tenant/task.entity';
import { PaginationDto } from './pagination.dto';

/**
 * DTO for filtering tasks
 *
 * Allowed sort fields:
 * - priority: Sort by task priority (HIGHEST, HIGH, MEDIUM, LOW, LOWEST)
 * - dueDate: Sort by task due date
 * - title: Sort by task title
 * - status: Sort by task status (OPEN, IN_PROGRESS, BLOCKED, DONE)
 * - createdAt: Sort by task creation date
 * - updatedAt: Sort by task last update date
 */
export class TaskFilterDto extends PaginationDto {
    @IsEnum(TaskStatus)
    @IsOptional()
    status?: TaskStatus;

    @IsEnum(TaskPriority)
    @IsOptional()
    priority?: TaskPriority;

    @IsDate()
    @Type(() => Date)
    @IsOptional()
    dueDateStart?: Date;

    @IsDate()
    @Type(() => Date)
    @IsOptional()
    dueDateEnd?: Date;

    @IsUUID()
    caseId: string;

    @IsString()
    @IsOptional()
    assigneeId?: string;

    @IsString()
    @IsOptional()
    search?: string;

    @IsOptional()
    @IsIn(['priority', 'dueDate', 'title', 'status', 'createdAt', 'updatedAt'])
    sortBy?: string = 'priority';

    @IsOptional()
    @IsIn(['ASC', 'DESC'])
    order?: 'ASC' | 'DESC' = 'DESC';
}
