import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { TaskDependency } from '@app/common/typeorm/entities/tenant/task-dependency.entity';

@Injectable()
export class TaskDependencyRepository extends BaseTenantRepository<TaskDependency> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(TaskDependency, tenantContextService, tenantConnectionService);
    }

    /**
     * Finds dependencies for a task
     */
    async findByTaskId(taskId: string): Promise<TaskDependency[]> {
        return this.find({
            where: { taskId },
            relations: ['dependsOn']
        });
    }

    /**
     * Finds tasks that depend on a specific task
     */
    async findDependentTasks(dependsOnId: string): Promise<TaskDependency[]> {
        return this.find({
            where: { dependsOnId },
            relations: ['task']
        });
    }

    /**
     * Checks if a dependency would create a circular reference
     * Returns true if adding the dependency would create a cycle
     */
    async wouldCreateCircularDependency(taskId: string, dependsOnId: string): Promise<boolean> {
        // If task depends on itself, it's circular
        if (taskId === dependsOnId) {
            return true;
        }

        // Get all tasks that the dependsOnId task depends on (directly or indirectly)
        const repository = await this.getTenantRepository();

        // This recursive CTE finds all dependencies of the dependsOnId task
        const result = await repository.query(
            `
            WITH RECURSIVE dependency_chain AS (
                -- Base case: direct dependencies of the dependsOnId task
                SELECT td.task_id, td.depends_on_id
                FROM task_dependencies td
                WHERE td.task_id = $1
                
                UNION
                
                -- Recursive case: dependencies of dependencies
                SELECT td.task_id, td.depends_on_id
                FROM task_dependencies td
                JOIN dependency_chain dc ON td.task_id = dc.depends_on_id
            )
            -- Check if the taskId appears in the dependency chain
            SELECT EXISTS (
                SELECT 1 FROM dependency_chain WHERE depends_on_id = $2
            ) as creates_cycle;
        `,
            [dependsOnId, taskId]
        );

        return result[0]?.creates_cycle === true;
    }
}
