import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { Repository } from 'typeorm';
import { Task, TaskStatus } from '@app/common/typeorm/entities/tenant/task.entity';
import { TaskFilterDto } from '../dto/task-filter.dto';

@Injectable()
export class TaskRepository extends BaseTenantRepository<Task> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(Task, tenantContextService, tenantConnectionService);
    }

    /**
     * Finds tasks with filtering, pagination, and sorting
     */
    async findWithFilters(filterDto: TaskFilterDto): Promise<[Task[], number]> {
        const {
            page = 1,
            limit = 10,
            sortBy = 'priority',
            order = 'DESC',
            search,
            ...filters
        } = filterDto;

        const repository = await this.getTenantRepository();
        const queryBuilder = repository
            .createQueryBuilder('task')
            .leftJoinAndSelect('task.dependencies', 'dependencies')
            .leftJoinAndSelect('dependencies.dependsOn', 'dependsOn');

        // Apply filters
        if (filters.status) {
            queryBuilder.andWhere('task.status = :status', { status: filters.status });
        }

        if (filters.priority) {
            queryBuilder.andWhere('task.priority = :priority', { priority: filters.priority });
        }

        if (filters.dueDateStart && filters.dueDateEnd) {
            queryBuilder.andWhere('task.dueDate BETWEEN :dueDateStart AND :dueDateEnd', {
                dueDateStart: filters.dueDateStart,
                dueDateEnd: filters.dueDateEnd
            });
        } else if (filters.dueDateStart) {
            queryBuilder.andWhere('task.dueDate >= :dueDateStart', {
                dueDateStart: filters.dueDateStart
            });
        } else if (filters.dueDateEnd) {
            queryBuilder.andWhere('task.dueDate <= :dueDateEnd', {
                dueDateEnd: filters.dueDateEnd
            });
        }

        // CaseId is now required
        queryBuilder.andWhere('task.caseId = :caseId', { caseId: filters.caseId });

        if (filters.assigneeId) {
            queryBuilder.andWhere('task.assigneeId = :assigneeId', {
                assigneeId: filters.assigneeId
            });
        }

        // Apply search
        if (search) {
            queryBuilder.andWhere('(task.title ILIKE :search OR task.description ILIKE :search)', {
                search: `%${search}%`
            });
        }

        // Apply sorting
        switch (sortBy) {
            case 'priority':
                // For priority, we need to use a CASE statement to sort in the correct order
                queryBuilder.orderBy(
                    `
                    CASE task.priority
                        WHEN 'HIGHEST' THEN 1
                        WHEN 'HIGH' THEN 2
                        WHEN 'MEDIUM' THEN 3
                        WHEN 'LOW' THEN 4
                        WHEN 'LOWEST' THEN 5
                        ELSE 6
                    END
                `,
                    order === 'DESC' ? 'ASC' : 'DESC'
                ); // Invert the order for priority (1 is highest)

                // Secondary sort by due date (null values last)
                queryBuilder.addOrderBy('task.dueDate IS NULL', 'ASC');
                queryBuilder.addOrderBy('task.dueDate', 'ASC');
                break;

            case 'dueDate':
                // Handle null values for due date
                queryBuilder.orderBy('task.dueDate IS NULL', 'ASC');
                queryBuilder.addOrderBy('task.dueDate', order);

                // Secondary sort by priority
                queryBuilder.addOrderBy(
                    `
                    CASE task.priority
                        WHEN 'HIGHEST' THEN 1
                        WHEN 'HIGH' THEN 2
                        WHEN 'MEDIUM' THEN 3
                        WHEN 'LOW' THEN 4
                        WHEN 'LOWEST' THEN 5
                        ELSE 6
                    END
                `,
                    'ASC'
                );
                break;

            case 'status':
                // For status, use a CASE statement to sort in a logical order
                queryBuilder.orderBy(
                    `
                    CASE task.status
                        WHEN 'OPEN' THEN 1
                        WHEN 'IN_PROGRESS' THEN 2
                        WHEN 'BLOCKED' THEN 3
                        WHEN 'DONE' THEN 4
                        ELSE 5
                    END
                `,
                    order
                );
                break;

            default:
                // For other fields, use standard ordering
                queryBuilder.orderBy(`task.${sortBy}`, order);
                break;
        }

        // Apply pagination
        queryBuilder.skip((page - 1) * limit).take(limit);

        // Execute query
        const [tasks, total] = await queryBuilder.getManyAndCount();

        return [tasks, total];
    }

    /**
     * Finds tasks by case ID
     */
    async findByCaseId(caseId: string): Promise<Task[]> {
        const repository = await this.getTenantRepository();
        return repository
            .createQueryBuilder('task')
            .where('task.caseId = :caseId', { caseId })
            .orderBy(
                `
                CASE task.priority
                    WHEN 'HIGHEST' THEN 1
                    WHEN 'HIGH' THEN 2
                    WHEN 'MEDIUM' THEN 3
                    WHEN 'LOW' THEN 4
                    WHEN 'LOWEST' THEN 5
                    ELSE 6
                END
            `,
                'ASC'
            ) // 1 is highest priority
            .addOrderBy('task.dueDate IS NULL', 'ASC')
            .addOrderBy('task.dueDate', 'ASC')
            .getMany();
    }

    /**
     * Finds tasks assigned to a specific user
     */
    async findByAssigneeId(assigneeId: string): Promise<Task[]> {
        const repository = await this.getTenantRepository();
        return repository
            .createQueryBuilder('task')
            .where('task.assigneeId = :assigneeId', { assigneeId })
            .orderBy(
                `
                CASE task.priority
                    WHEN 'HIGHEST' THEN 1
                    WHEN 'HIGH' THEN 2
                    WHEN 'MEDIUM' THEN 3
                    WHEN 'LOW' THEN 4
                    WHEN 'LOWEST' THEN 5
                    ELSE 6
                END
            `,
                'ASC'
            ) // 1 is highest priority
            .addOrderBy('task.dueDate IS NULL', 'ASC')
            .addOrderBy('task.dueDate', 'ASC')
            .getMany();
    }

    /**
     * Counts open tasks assigned to a user
     */
    async countOpenTasksByAssigneeId(assigneeId: string): Promise<number> {
        return this.count({
            where: {
                assigneeId,
                status: TaskStatus.OPEN
            }
        });
    }

    /**
     * Gets the tenant repository - public method to allow access from services
     */
    async getRepository(): Promise<Repository<Task>> {
        return this.getTenantRepository();
    }
}
