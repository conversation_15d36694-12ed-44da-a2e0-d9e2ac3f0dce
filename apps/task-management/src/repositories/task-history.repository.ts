import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { TaskHistory } from '@app/common/typeorm/entities/tenant/task-history.entity';

@Injectable()
export class TaskHistoryRepository extends BaseTenantRepository<TaskHistory> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(TaskHistory, tenantContextService, tenantConnectionService);
    }

    /**
     * Finds history entries for a task
     */
    async findByTaskId(taskId: string): Promise<TaskHistory[]> {
        return this.find({
            where: { taskId },
            order: { changedAt: 'DESC' }
        });
    }

    /**
     * Gets the most recent status change for a task
     */
    async findMostRecentStatusChange(taskId: string): Promise<TaskHistory | null> {
        return this.findOne({
            where: { taskId },
            order: { changedAt: 'DESC' }
        });
    }
}
