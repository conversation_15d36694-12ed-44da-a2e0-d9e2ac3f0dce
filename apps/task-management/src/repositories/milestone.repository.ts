import { Injectable } from '@nestjs/common';
import { BaseTenantRepository } from '@app/common/multi-tenancy/base-tenant.repository';
import { Milestone, MilestoneStatus } from '@app/common/typeorm/entities/tenant/milestone.entity';
import { TenantContextService } from '@app/common/multi-tenancy/tenant-context.service';
import { TenantConnectionService } from '@app/common/multi-tenancy/tenant-connection.service';

/**
 * Simplified Milestone Repository
 * Contains only data access methods - business logic moved to EnhancedMilestoneService
 */
@Injectable()
export class MilestoneRepository extends BaseTenantRepository<Milestone> {
    constructor(
        tenantContextService: TenantContextService,
        tenantConnectionService: TenantConnectionService
    ) {
        super(Milestone, tenantContextService, tenantConnectionService, false);
    }

    /**
     * Find milestones by case ID with their tasks
     */
    async findByCaseId(caseId: string): Promise<Milestone[]> {
        return this.find({
            where: { caseId },
            relations: ['tasks'],
            order: { sortOrder: 'ASC' }
        });
    }

    /**
     * Find milestone by ID with tasks
     */
    async findByIdWithTasks(id: string): Promise<Milestone | null> {
        return this.findOne({
            where: { id },
            relations: ['tasks']
        });
    }

    /**
     * Find milestones by status
     */
    async findByStatus(status: MilestoneStatus): Promise<Milestone[]> {
        return this.find({
            where: { status },
            relations: ['tasks'],
            order: { sortOrder: 'ASC' }
        });
    }
}
