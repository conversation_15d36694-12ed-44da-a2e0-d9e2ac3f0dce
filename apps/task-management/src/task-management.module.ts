import { Module, NestModule, MiddlewareConsumer, forwardRef } from '@nestjs/common';
import { CommonModule } from '@app/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CONTROLLERS } from './controllers';
import { SERVICES } from './services';
import { REPOSITORIES } from './repositories';
import { RoleHierarchyService } from '@app/common/roles/hierarchy';
import {
    Task,
    TaskDependency,
    TaskHistory,
    SystemUser,
    Tenant
} from '@app/common/typeorm/entities';
import { TenantContextMiddleware } from 'apps/case-management/src/middleware/tenant-context.middleware';
import { CaseServiceModule } from 'apps/case-management/src/case-service.module';
import { Milestone } from '@app/common/typeorm/entities/tenant';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { PublicUserRepository } from '@app/common/repositories/public-user.repository';
import { TenantRepository } from '@app/common/repositories/tenant.repository';
import { BullProviderModule } from '../../../libs/common/src/bull/bull.module';
import * as cookieParser from 'cookie-parser';

@Module({
    imports: [
        CommonModule,
        BullProviderModule,
        // MultiTenancyModule.forRoot(),
        TypeOrmModule.forFeature([
            Task,
            TaskDependency,
            TaskHistory,
            SystemUser,
            Tenant,
            Milestone
        ]),
        forwardRef(() => CaseServiceModule) // Import the CaseServiceModule to provide the CaseService
    ],
    controllers: [...CONTROLLERS],
    providers: [
        ...SERVICES,
        ...REPOSITORIES,
        RoleHierarchyService,
        MessageProducerService,
        PublicUserRepository,
        TenantRepository
    ],
    exports: [...SERVICES]
})
export class TaskManagementModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        // Apply cookie-parser middleware first
        consumer.apply(cookieParser()).forRoutes('*');
        // Then apply tenant context middleware
        consumer.apply(TenantContextMiddleware).forRoutes('*');
    }
}
