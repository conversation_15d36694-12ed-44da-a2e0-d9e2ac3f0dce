module.exports = {
  displayName: 'case-management',
  preset: '../../jest.preset.js',
  rootDir: '../../',
  testMatch: ['<rootDir>/apps/case-management/**/*.spec.ts'],
  moduleNameMapper: {
    '^@app/common(.*)$': '<rootDir>/libs/common/src$1'
  },
  setupFilesAfterEnv: ['<rootDir>/test/jest-setup.ts'],
  coverageDirectory: '<rootDir>/coverage/apps/case-management',
  testEnvironment: 'node',
  // Pass tests if no tests are found
  testPathIgnorePatterns: ['/node_modules/'],
  passWithNoTests: true
};
