import { Injectable, Logger } from '@nestjs/common';
import { CaseRepository } from '../repositories/case.repository';

/**
 * Service for generating unique case numbers
 */
@Injectable()
export class CaseNumberGenerator {
    private readonly logger = new Logger(CaseNumberGenerator.name);
    private readonly PREFIX = 'CASE';
    private readonly MAX_RETRIES = 5;

    constructor(private readonly caseRepository: CaseRepository) {}

    /**
     * Generates a unique case number in the format CASE-YYYY-XXXXX
     * where YYYY is the current year and XXXXX is a sequential number
     */
    async generateCaseNumber(): Promise<string> {
        const currentYear = new Date().getFullYear();
        const basePattern = `${this.PREFIX}-${currentYear}-`;

        // Find the highest existing case number for this year
        const repository = await this.caseRepository.getRepository();
        const result = await repository
            .createQueryBuilder('case')
            .select('case.caseNumber')
            .where('case.caseNumber LIKE :pattern', { pattern: `${basePattern}%` })
            .orderBy('case.caseNumber', 'DESC')
            .limit(1)
            .getRawOne();

        let nextNumber = 1;

        if (result && result.caseNumber) {
            // Extract the number part from the existing case number
            const lastNumber = result.caseNumber.substring(basePattern.length);
            nextNumber = parseInt(lastNumber, 10) + 1;
        }

        // Format with leading zeros (5 digits)
        const formattedNumber = nextNumber.toString().padStart(5, '0');
        const caseNumber = `${basePattern}${formattedNumber}`;

        this.logger.debug(`Generated case number: ${caseNumber}`);

        // Verify uniqueness
        return this.ensureUniqueCaseNumber(caseNumber);
    }

    /**
     * Ensures the generated case number is unique
     * If a collision is detected, it will increment the number and try again
     */
    private async ensureUniqueCaseNumber(caseNumber: string, attempt: number = 0): Promise<string> {
        if (attempt >= this.MAX_RETRIES) {
            this.logger.error(
                `Failed to generate unique case number after ${this.MAX_RETRIES} attempts`
            );
            throw new Error('Failed to generate unique case number');
        }

        const existing = await this.caseRepository.findByCaseNumber(caseNumber);

        if (!existing) {
            return caseNumber;
        }

        // Collision detected, increment the number and try again
        const parts = caseNumber.split('-');
        const currentNumber = parseInt(parts[2], 10);
        const nextNumber = (currentNumber + 1).toString().padStart(5, '0');
        const newCaseNumber = `${parts[0]}-${parts[1]}-${nextNumber}`;

        this.logger.debug(`Case number collision detected, trying: ${newCaseNumber}`);

        return this.ensureUniqueCaseNumber(newCaseNumber, attempt + 1);
    }
}
