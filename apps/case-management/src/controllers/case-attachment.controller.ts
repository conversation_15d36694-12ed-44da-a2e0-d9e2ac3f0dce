import { Body, Controller, Delete, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { CaseAttachmentService } from '../services/case-attachment.service';
import { CreateAttachmentDto } from '../dto/create-attachment.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { Request } from 'express';
import { DocumentType } from '@app/common/typeorm/entities/tenant/case-attachment.entity';
import { RolesGuard } from '@app/common/guards';
import { PermissionGuard } from '@app/common/guards/permission.guard';
import {
    SuperAdminOrRoleGroup,
    AllowRoleGroups
} from '@app/common/permissions/role-group.decorators';
import { HasPermission } from '@app/common/permissions/permission.decorators';
import { ResourceType } from '@app/common/permissions/permission.constants';
import { Permission } from '@app/common/permissions/enums/permission.enum';

@Controller('cases/:caseId/attachments')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, PermissionGuard)
export class CaseAttachmentController {
    constructor(private readonly caseAttachmentService: CaseAttachmentService) {}

    /**
     * Create a new attachment for a case
     * Requires CREATE permission on DOCUMENT resource and access to conveyancers role group
     */
    @Post()
    @HasPermission(ResourceType.DOCUMENT, Permission.CREATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async createAttachment(
        @Param('caseId') caseId: string,
        @Body() createAttachmentDto: CreateAttachmentDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const attachment = await this.caseAttachmentService.createAttachment(
            caseId,
            createAttachmentDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(attachment, 'Attachment created successfully');
    }

    /**
     * Get all attachments for a case
     * Requires READ permission on DOCUMENT resource and access to conveyancers or finance role groups
     */
    @Get()
    @HasPermission(ResourceType.DOCUMENT, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getCaseAttachments(@Param('caseId') caseId: string) {
        const attachments = await this.caseAttachmentService.getCaseAttachments(caseId);
        return ApiResponseUtil.ok(attachments, 'Attachments retrieved successfully');
    }

    /**
     * Get a specific attachment by ID
     * Requires READ permission on DOCUMENT resource and access to conveyancers or finance role groups
     */
    @Get(':attachmentId')
    @HasPermission(ResourceType.DOCUMENT, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getAttachmentById(@Param('attachmentId') attachmentId: string) {
        const attachment = await this.caseAttachmentService.getAttachmentById(attachmentId);
        return ApiResponseUtil.ok(attachment, 'Attachment retrieved successfully');
    }

    /**
     * Delete an attachment
     * Requires DELETE permission on DOCUMENT resource and access to conveyancers role group
     */
    @Delete(':attachmentId')
    @HasPermission(ResourceType.DOCUMENT, Permission.DELETE)
    @SuperAdminOrRoleGroup('conveyancers')
    async deleteAttachment(@Param('attachmentId') attachmentId: string, @Req() request: Request) {
        const user = request['user'];
        await this.caseAttachmentService.deleteAttachment(
            attachmentId,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(null, 'Attachment deleted successfully');
    }

    /**
     * Search attachments by filename
     * Requires READ permission on DOCUMENT resource and access to conveyancers or finance role groups
     */
    @Get('search')
    @HasPermission(ResourceType.DOCUMENT, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async searchAttachmentsByFilename(
        @Param('caseId') caseId: string,
        @Query('filename') filename: string
    ) {
        const attachments = await this.caseAttachmentService.searchAttachmentsByFilename(
            caseId,
            filename
        );

        return ApiResponseUtil.ok(attachments, 'Attachments retrieved successfully');
    }

    /**
     * Get attachments by document type
     * Requires READ permission on DOCUMENT resource and access to conveyancers or finance role groups
     */
    @Get('type/:documentType')
    @HasPermission(ResourceType.DOCUMENT, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getAttachmentsByDocumentType(
        @Param('caseId') caseId: string,
        @Param('documentType') documentType: DocumentType
    ) {
        const attachments = await this.caseAttachmentService.getAttachmentsByDocumentType(
            caseId,
            documentType
        );

        return ApiResponseUtil.ok(
            attachments,
            `Attachments of type ${documentType} retrieved successfully`
        );
    }
}
