import {
    Controller,
    Post,
    Body,
    Param,
    UseGuards,
    Request,
    Get,
    BadRequestException,
    Ip
} from '@nestjs/common';
import { CaseStateMachineService } from '@app/common/cases/case-state-machine.service';
import { ConveyancerCaseGuard, RolesGuard } from '@app/common/guards';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { CaseTransitionDto } from '../dto/case-transition.dto';
import { CaseRepository } from '../repositories';
import { TenantGuard } from '@app/common/multi-tenancy/tenant.guard';
import { PermissionGuard } from '@app/common/guards/permission.guard';
import {
    SuperAdminOrRoleGroup,
    AllowRoleGroups
} from '@app/common/permissions/role-group.decorators';
import { HasPermission } from '@app/common/permissions/permission.decorators';
import { ResourceType } from '@app/common/permissions/permission.constants';
import { Permission } from '@app/common/permissions/enums/permission.enum';

@Controller('cases')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, PermissionGuard, ConveyancerCaseGuard)
export class CaseStateController {
    constructor(
        private readonly caseStateMachineService: CaseStateMachineService,
        private readonly caseManagementRepository: CaseRepository
    ) {}

    /**
     * Transition case state
     * Requires UPDATE permission on CASE resource and access to conveyancers role group
     */
    @Post(':id/transition')
    @HasPermission(ResourceType.CASE, Permission.UPDATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async transitionState(
        @Param('id') id: string,
        @Body() transitionDto: CaseTransitionDto,
        @Request() req,
        @Ip() ipAddress: string
    ) {
        // Prepare the data object with consistent format
        const data = {
            ...transitionDto?.data,
            notes: transitionDto?.notes,
            holdReason: transitionDto?.holdReason,
            reopenReason: transitionDto?.reopenReason,
            assignments: transitionDto?.assignments
        };

        //Temporary fix for user id
        req.user.id = req.user.id || req.user.systemUserId;
        // The RolesGuard has already verified the user has appropriate roles
        return this.caseStateMachineService.transition(
            id,
            transitionDto.targetState,
            req.user,
            data,
            ipAddress
        );
    }

    /**
     * Get valid transitions for a case
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get(':id/valid-transitions')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getValidTransitions(@Param('id') id: string, @Request() req) {
        const caseEntity = await this.caseManagementRepository.findByCaseId(id);

        if (!caseEntity) {
            throw new BadRequestException(`Case with ID ${id} not found`);
        }

        // Get available transitions based on case state and user
        const availablePermissions = req.user.roles;
        return {
            currentState: caseEntity.status,
            allowedPermissions: availablePermissions
        };
    }
}
