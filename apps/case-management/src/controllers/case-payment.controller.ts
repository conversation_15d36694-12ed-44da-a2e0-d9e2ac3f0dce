import { Body, Controller, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { CasePaymentService } from '../services/case-payment.service';
import { CreateCasePaymentDto } from '../dto/create-case-payment.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards';
import { TenantGuard } from '@app/common/multi-tenancy';
import { Request } from 'express';
import { PermissionGuard } from '@app/common/guards/permission.guard';
import {
    SuperAdminOrRoleGroup,
    AllowRoleGroups
} from '@app/common/permissions/role-group.decorators';
import { HasPermission } from '@app/common/permissions/permission.decorators';
import { ResourceType } from '@app/common/permissions/permission.constants';
import { Permission } from '@app/common/permissions/enums/permission.enum';

@Controller('cases/:caseId/payments')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, PermissionGuard)
export class CasePaymentController {
    constructor(private readonly casePaymentService: CasePaymentService) {}

    /**
     * Create a new payment for a case
     * Only SuperAdmin or Conveyancer role group can add payments
     * No editing or deletion is allowed
     */
    @Post()
    @HasPermission(ResourceType.CASE, Permission.CREATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async createPayment(
        @Param('caseId') caseId: string,
        @Body() createPaymentDto: CreateCasePaymentDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const payment = await this.casePaymentService.createPayment(
            caseId,
            createPaymentDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(payment, 'Payment added successfully');
    }

    /**
     * Get all payments for a case
     * Allows conveyancers and finance role groups to view payments
     */
    @Get()
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getCasePayments(@Param('caseId') caseId: string) {
        const payments = await this.casePaymentService.getCasePayments(caseId);
        return ApiResponseUtil.ok(payments, 'Payments retrieved successfully');
    }

    /**
     * Get a specific payment by ID
     * Allows conveyancers and finance role groups to view payment details
     */
    @Get(':paymentId')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getPaymentById(@Param('caseId') caseId: string, @Param('paymentId') paymentId: string) {
        const payment = await this.casePaymentService.getPaymentById(paymentId, caseId);
        return ApiResponseUtil.ok(payment, 'Payment retrieved successfully');
    }
}
