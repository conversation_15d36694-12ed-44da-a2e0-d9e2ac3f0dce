import { Body, Controller, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { CaseNotificationService } from '../services/case-notification.service';
import { CaseReminderType } from '@app/common/enums/case-notification-types.enum';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards';
import { TenantGuard } from '@app/common/multi-tenancy';
import { Request } from 'express';
import { PermissionGuard } from '@app/common/guards/permission.guard';
import {
    SuperAdminOrRoleGroup,
    AllowRoleGroups
} from '@app/common/permissions/role-group.decorators';
import { HasPermission } from '@app/common/permissions/permission.decorators';
import { ResourceType } from '@app/common/permissions/permission.constants';
import { Permission } from '@app/common/permissions/enums/permission.enum';

@Controller('cases/notifications')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, PermissionGuard)
export class CaseNotificationController {
    constructor(private readonly caseNotificationService: CaseNotificationService) {}

    /**
     * Get cases with upcoming deadlines
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get('upcoming-deadlines')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getUpcomingDeadlines(@Query('hours') hours: number = 24) {
        const cases = await this.caseNotificationService.checkUpcomingDeadlines(hours);
        return ApiResponseUtil.ok(
            cases,
            `Cases with deadlines in the next ${hours} hours retrieved successfully`
        );
    }

    /**
     * Get cases with missed deadlines
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get('missed-deadlines')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getMissedDeadlines() {
        const cases = await this.caseNotificationService.checkMissedDeadlines();
        return ApiResponseUtil.ok(cases, 'Cases with missed deadlines retrieved successfully');
    }

    /**
     * Set a custom reminder for a case deadline
     * Requires CREATE permission on CASE resource and access to conveyancers role group
     */
    @Post(':caseId/reminders')
    @HasPermission(ResourceType.CASE, Permission.CREATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async setCustomReminder(
        @Param('caseId') caseId: string,
        @Body('reminderTime') reminderTime: string,
        @Body('reminderType') reminderType: CaseReminderType = CaseReminderType.EMAIL,
        @Req() request: Request
    ) {
        const user = request['user'];
        const reminder = await this.caseNotificationService.setCustomReminder(
            caseId,
            user.systemUserId,
            user.preferred_username || user.email,
            reminderTime,
            reminderType
        );

        return ApiResponseUtil.created(reminder, 'Custom reminder set successfully');
    }
}
