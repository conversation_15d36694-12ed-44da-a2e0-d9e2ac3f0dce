import { Body, Controller, Delete, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { CaseRelationService } from '../services/case-relation.service';
import { CreateCaseRelationDto } from '../dto/create-case-relation.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards';
import { TenantGuard } from '@app/common/multi-tenancy';
import { Request } from 'express';
import { PermissionGuard } from '@app/common/guards/permission.guard';
import {
    SuperAdminOrRoleGroup,
    AllowRoleGroups
} from '@app/common/permissions/role-group.decorators';
import { HasPermission } from '@app/common/permissions/permission.decorators';
import { ResourceType } from '@app/common/permissions/permission.constants';
import { Permission } from '@app/common/permissions/enums/permission.enum';

@Controller('cases/:caseId/related')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, PermissionGuard)
export class CaseRelationController {
    constructor(private readonly caseRelationService: CaseRelationService) {}

    /**
     * Create a new relation between two cases
     * Requires CREATE permission on CASE resource and access to conveyancers role group
     */
    @Post()
    @HasPermission(ResourceType.CASE, Permission.CREATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async createRelation(
        @Param('caseId') caseId: string,
        @Body() createCaseRelationDto: CreateCaseRelationDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const relation = await this.caseRelationService.createRelation(
            caseId,
            createCaseRelationDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(relation, 'Case relation created successfully');
    }

    /**
     * Get all relations for a case
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get()
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getRelations(@Param('caseId') caseId: string) {
        const relations = await this.caseRelationService.getCaseRelations(caseId);
        return ApiResponseUtil.ok(relations, 'Case relations retrieved successfully');
    }

    /**
     * Delete a relation
     * Requires DELETE permission on CASE resource and access to conveyancers role group
     */
    @Delete(':relationId')
    @HasPermission(ResourceType.CASE, Permission.DELETE)
    @SuperAdminOrRoleGroup('conveyancers')
    async deleteRelation(
        @Param('caseId') caseId: string,
        @Param('relationId') relationId: string,
        @Req() request: Request
    ) {
        const user = request['user'];
        await this.caseRelationService.deleteRelation(
            caseId,
            relationId,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(null, 'Case relation deleted successfully');
    }
}
