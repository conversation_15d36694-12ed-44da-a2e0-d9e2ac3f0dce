import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Patch,
    Post,
    Query,
    Req,
    UseGuards
} from '@nestjs/common';
import { CaseService } from '../services/case.service';
import { CreateCaseDto } from '../dto/create-case.dto';
import { UpdateCaseDto } from '../dto/update-case.dto';
import { CaseFilterDto } from '../dto/case-filter.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { PermissionGuard } from '@app/common/guards/permission.guard';
import { Permission } from '@app/common/permissions/enums/permission.enum';
import { Request } from 'express';

import { HasPermission } from '@app/common/permissions/permission.decorators';
import { ResourceType } from '@app/common/permissions/permission.constants';
import {
    RequireSuperAdmin,
    SuperAdminOrRoleGroup
} from '@app/common/permissions/role-group.decorators';
import { ConveyancerCaseGuard } from '@app/common/guards';

@Controller('cases')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, PermissionGuard)
export class CaseController {
    constructor(private readonly caseService: CaseService) {}

    /**
     * Create a new case
     * Requires: Permission to create cases AND role group access
     */
    @Post()
    @HasPermission(ResourceType.CASE, Permission.CREATE)
    @RequireSuperAdmin()
    async createCase(@Body() createCaseDto: CreateCaseDto, @Req() request: Request) {
        const user = request['user'];
        const newCase = await this.caseService.createCase(
            createCaseDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        // Enhance case response with rate card fee information
        const transactionType = createCaseDto.transactionType;
        const enhancedCase = await this.caseService.enhanceCaseWithRateCardFees(
            newCase,
            transactionType
        );

        return ApiResponseUtil.created(enhancedCase, 'Case created successfully');
    }

    /**
     * Get a case by ID
     */
    @Get(':id')
    @UseGuards(ConveyancerCaseGuard)
    async getCaseById(@Param('id') id: string, @Req() request: Request) {
        const user = request['user'];
        const caseEntity = await this.caseService.findCaseById(id, user.systemUserId);
        return ApiResponseUtil.ok(caseEntity, 'Case retrieved successfully');
    }

    /**
     * Get comprehensive case details including all related information
     */
    @Get(':id/details')
    @UseGuards(ConveyancerCaseGuard)
    async getCaseDetails(@Param('id') id: string, @Req() request: Request) {
        const user = request['user'];
        const caseDetails = await this.caseService.getCaseDetails(id, user.systemUserId);
        await this.caseService.auditCaseAccess(
            id,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );
        return ApiResponseUtil.ok(caseDetails, 'Case details retrieved successfully');
    }

    /**
     * Get a case by case number
     */
    @Get('number/:caseNumber')
    @UseGuards(ConveyancerCaseGuard)
    async getCaseByCaseNumber(@Param('caseNumber') caseNumber: string, @Req() request: Request) {
        const user = request['user'];
        const caseEntity = await this.caseService.findCaseByCaseNumber(
            caseNumber,
            user.systemUserId
        );
        return ApiResponseUtil.ok(caseEntity, 'Case retrieved successfully');
    }

    /**
     * Get all cases (requires READ permission and role group access)
     */
    @Get()
    @HasPermission(ResourceType.CASE, Permission.READ)
    //@SuperAdminOrRoleGroup('conveyancers')
    @RequireSuperAdmin()
    @UseGuards(ConveyancerCaseGuard)
    async getCases(@Query() filterDto: CaseFilterDto) {
        const paginatedCases = await this.caseService.findCases(filterDto);
        return ApiResponseUtil.ok(paginatedCases.data, 'Cases retrieved successfully', {
            pagination: paginatedCases.meta.pagination
        });
    }

    // Search endpoints have been consolidated into the searchCases endpoint

    /**
     * Search cases by multiple criteria
     * Allows looking up cases by ID, case number, assigned person name, or client name
     */
    @Get('global/search')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @UseGuards(ConveyancerCaseGuard)
    async searchCases(
        @Query('query') searchQuery: string,
        @Query('type') searchType: 'id' | 'caseNumber' | 'assignedTo' | 'clientName',
        @Query('limit') limit: number = 10,
        @Req() request: Request
    ) {
        if (!searchQuery || searchQuery.length < 2) {
            return ApiResponseUtil.ok([], 'Please provide at least 2 characters to search');
        }

        const user = request['user'];
        const results = await this.caseService.searchCases(
            searchQuery,
            searchType,
            limit,
            user.systemUserId
        );
        return ApiResponseUtil.ok(results, 'Search results retrieved successfully');
    }

    /**
     * Update a case (requires UPDATE permission and role group access)
     */
    @Patch(':id')
    @HasPermission(ResourceType.CASE, Permission.UPDATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async updateCase(
        @Param('id') id: string,
        @Body() updateCaseDto: UpdateCaseDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const updatedCase = await this.caseService.updateCase(
            id,
            updateCaseDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );
        return ApiResponseUtil.ok(updatedCase, 'Case updated successfully');
    }

    /**
     * Change case status
     */
    // @Patch(':id/status/:status')
    // @IsLawyer()
    // async changeStatus(
    //     @Param('id') id: string,
    //     @Param('status') status: CaseStatus,
    //     @Req() request: Request
    // ) {
    //     const user = request['user'];
    //     const updatedCase = await this.caseService.changeStatus(
    //         id,
    //         status,
    //         user.systemUserId,
    //         user.preferred_username || user.email,
    //         request
    //     );

    //     return ApiResponseUtil.ok(updatedCase, `Case status changed to ${status}`);
    // }

    /**
     * Close a case with validation
     */
    // @Patch(':id/close')
    // @IsManager()
    // async closeCase(
    //     @Param('id') id: string,
    //     @Body('closureReason') closureReason: string,
    //     @Req() request: Request
    // ) {
    //     if (!closureReason) {
    //         throw new BadRequestException('Closure reason is required');
    //     }

    //     const user = request['user'];

    //     // Check if case can be closed
    //     const canCloseCheck = await this.caseService.canCloseCaseCheck(id);
    //     if (!canCloseCheck.canClose) {
    //         return ApiResponseUtil.badRequest(`Cannot close case: ${canCloseCheck.reason}`);
    //     }

    //     // Close the case
    //     const updatedCase = await this.caseService.changeStatus(
    //         id,
    //         CaseStatus.CLOSED,
    //         user.systemUserId,
    //         user.preferred_username || user.email,
    //         request,
    //         { closureReason }
    //     );

    //     return ApiResponseUtil.ok(updatedCase, 'Case closed successfully');
    // }

    /**
     * Delete a case (Super Admin only)
     */
    @Delete(':id')
    @RequireSuperAdmin()
    async deleteCase(@Param('id') id: string, @Req() request: Request) {
        const user = request['user'];
        await this.caseService.deleteCase(id, user.systemUserId);
        return ApiResponseUtil.ok(null, 'Case deleted successfully');
    }
}
