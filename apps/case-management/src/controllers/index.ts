import { <PERSON><PERSON><PERSON>roll<PERSON> } from './case.controller';
import { <PERSON><PERSON><PERSON><PERSON>roll<PERSON> } from './client.controller';
import { CaseAssignmentController } from './case-assignment.controller';
import { CaseNoteController } from './case-note.controller';
import { Case<PERSON>ttachmentController } from './case-attachment.controller';
import { <PERSON><PERSON><PERSON><PERSON>Controller } from './case-contact.controller';
import { CaseEventController } from './case-event.controller';
import { CaseRelationController } from './case-relation.controller';
import { CaseNotificationController } from './case-notification.controller';
import { CaseStateController } from './case-state.controller';
import { CasePaymentController } from './case-payment.controller';

export const CASE_MANAGEMENT_CONTROLLERS = [
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ClientController,
    CaseAssignmentController,
    CaseNoteController,
    CaseAttachmentController,
    CaseNotificationController,
    CaseStateController,
    CasePaymentController
];

export {
    Case<PERSON><PERSON>roll<PERSON>,
    <PERSON>lient<PERSON>ontroller,
    CaseAssignmentController,
    CaseNoteController,
    CaseAttachmentController,
    CaseContactController,
    CaseEventController,
    CaseRelationController,
    CaseNotificationController,
    CasePaymentController
};
