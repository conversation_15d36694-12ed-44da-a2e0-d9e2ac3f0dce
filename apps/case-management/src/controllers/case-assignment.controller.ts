import { Body, Controller, Delete, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { CaseAssignmentService } from '../services/case-assignment.service';
import { AssignCaseDto } from '../dto/assign-case.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { Request } from 'express';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { PermissionGuard } from '@app/common/guards/permission.guard';
import {
    SuperAdminOrRoleGroupAdmin,
    AllowRoleGroups
} from '@app/common/permissions/role-group.decorators';
import { HasPermission } from '@app/common/permissions/permission.decorators';
import { ResourceType } from '@app/common/permissions/permission.constants';
import { Permission } from '@app/common/permissions/enums/permission.enum';

@Controller('cases/:caseId/assignments')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, PermissionGuard)
export class CaseAssignmentController {
    constructor(private readonly caseAssignmentService: CaseAssignmentService) {}

    /**
     * Assign a case to a user
     * Requires ASSIGN permission on CASE resource and admin access to conveyancers role group
     */
    @Post()
    @HasPermission(ResourceType.CASE, Permission.ASSIGN)
    @SuperAdminOrRoleGroupAdmin('conveyancers')
    async assignCase(
        @Param('caseId') caseId: string,
        @Body() assignCaseDto: AssignCaseDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const assignment = await this.caseAssignmentService.assignCase(
            caseId,
            assignCaseDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(assignment, 'Case assigned successfully');
    }

    /**
     * Unassign a user from a case
     * Requires UNASSIGN permission on CASE resource and admin access to conveyancers role group
     */
    @Delete('users/:userId')
    @HasPermission(ResourceType.CASE, Permission.UNASSIGN)
    @SuperAdminOrRoleGroupAdmin('conveyancers')
    async unassignCase(
        @Param('caseId') caseId: string,
        @Param('userId') userId: string,
        @Req() request: Request
    ) {
        const user = request['user'];
        await this.caseAssignmentService.unassignCase(
            caseId,
            userId,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(null, 'Case unassigned successfully');
    }

    /**
     * Get all assignments for a case
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get()
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getCaseAssignments(@Param('caseId') caseId: string) {
        const assignments = await this.caseAssignmentService.getCaseAssignments(caseId);
        return ApiResponseUtil.ok(assignments, 'Case assignments retrieved successfully');
    }

    /**
     * Reassign a case from one user to another
     * Requires REASSIGN permission on CASE resource and admin access to conveyancers role group
     */
    @Post('reassign/:oldUserId')
    @HasPermission(ResourceType.CASE, Permission.REASSIGN)
    @SuperAdminOrRoleGroupAdmin('conveyancers')
    async reassignCase(
        @Param('caseId') caseId: string,
        @Param('oldUserId') oldUserId: string,
        @Body() assignCaseDto: AssignCaseDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const assignment = await this.caseAssignmentService.reassignCase(
            caseId,
            oldUserId,
            assignCaseDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(assignment, 'Case reassigned successfully');
    }
}
