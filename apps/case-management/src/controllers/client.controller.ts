import { Body, Controller, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ClientService } from '../services/client.service';
import { CreateClientDto } from '../dto/create-client.dto';
import { UpdateClientDto } from '../dto/update-client.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards';
import { TenantGuard } from '@app/common/multi-tenancy';
import { Request } from 'express';
import { PermissionGuard } from '@app/common/guards/permission.guard';
import {
    SuperAdminOrRoleGroup,
    AllowRoleGroups,
    RequireSuperAdmin
} from '@app/common/permissions/role-group.decorators';
import { HasPermission } from '@app/common/permissions/permission.decorators';
import { ResourceType } from '@app/common/permissions/permission.constants';
import { Permission } from '@app/common/permissions/enums/permission.enum';

@Controller('clients')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, PermissionGuard)
export class ClientController {
    constructor(private readonly clientService: ClientService) {}

    /**
     * Create a new client
     * Requires CREATE permission on CLIENT resource and admin access to conveyancers role group
     */
    @Post()
    @HasPermission(ResourceType.CLIENT, Permission.CREATE)
    @RequireSuperAdmin()
    async createClient(@Body() createClientDto: CreateClientDto, @Req() request: Request) {
        const user = request['user'];
        const newClient = await this.clientService.createClient(createClientDto, user.systemUserId);

        return ApiResponseUtil.created(newClient, 'Client created successfully');
    }

    /**
     * Get a client by ID
     * Requires READ permission on CLIENT resource and access to conveyancers or finance role groups
     */
    @Get(':id')
    @HasPermission(ResourceType.CLIENT, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getClientById(@Param('id') id: string) {
        const client = await this.clientService.findClientById(id);
        return ApiResponseUtil.ok(client, 'Client retrieved successfully');
    }

    /**
     * Get all clients with pagination and search
     * Requires READ permission on CLIENT resource and access to conveyancers or finance role groups
     */
    @Get()
    @HasPermission(ResourceType.CLIENT, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getClients(
        @Query('page') page: number,
        @Query('limit') limit: number,
        @Query('search') search: string
    ) {
        const paginatedClients = await this.clientService.findClients(page, limit, search);
        return ApiResponseUtil.ok(paginatedClients.data, 'Clients retrieved successfully', {
            pagination: paginatedClients.meta.pagination
        });
    }

    /**
     * Update a client
     * Requires UPDATE permission on CLIENT resource and access to conveyancers role group
     */
    @Patch(':id')
    @HasPermission(ResourceType.CLIENT, Permission.UPDATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async updateClient(@Param('id') id: string, @Body() updateClientDto: UpdateClientDto) {
        const updatedClient = await this.clientService.updateClient(id, updateClientDto);
        return ApiResponseUtil.ok(updatedClient, 'Client updated successfully');
    }

    /**
     * Quick search for clients - optimized for fast response (<1ms)
     * Returns top 5 matches with client name and case information
     * Requires READ permission on CLIENT resource and access to conveyancers or finance role groups
     */
    @Get('search/quick')
    @HasPermission(ResourceType.CLIENT, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async quickSearch(@Query('term') searchTerm: string) {
        if (!searchTerm || searchTerm.length < 2) {
            return ApiResponseUtil.ok([], 'Please provide at least 2 characters to search');
        }

        const clients = await this.clientService.searchClientsByName(searchTerm, 5);
        return ApiResponseUtil.ok(clients, 'Search results retrieved successfully');
    }

    /**
     * Search clients by name
     * Real-time search with results updating as user types
     * Requires READ permission on CLIENT resource and access to conveyancers or finance role groups
     */
    @Get('search/:name')
    @HasPermission(ResourceType.CLIENT, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async searchClientsByName(@Param('name') name: string) {
        const clients = await this.clientService.searchClientsByName(name);
        return ApiResponseUtil.ok(clients, 'Clients retrieved successfully');
    }
}
