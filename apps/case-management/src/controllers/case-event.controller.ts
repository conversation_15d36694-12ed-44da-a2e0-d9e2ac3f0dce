import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    UseGuards
} from '@nestjs/common';
import { CaseEventService } from '../services/case-event.service';
import { CreateCaseEventDto } from '../dto/create-case-event.dto';
import { UpdateCaseEventDto } from '../dto/update-case-event.dto';
import { CaseEventFilterDto } from '../dto/case-event-filter.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { Request } from 'express';
import { PermissionGuard } from '@app/common/guards/permission.guard';
import {
    SuperAdminOrRoleGroup,
    AllowRoleGroups
} from '@app/common/permissions/role-group.decorators';
import { HasPermission } from '@app/common/permissions/permission.decorators';
import { ResourceType } from '@app/common/permissions/permission.constants';
import { Permission } from '@app/common/permissions/enums/permission.enum';
import {
    CaseEventCategory,
    CaseEventType
} from '@app/common/typeorm/entities/tenant/case-event.entity';
import { ConveyancerCaseGuard, RolesGuard } from '@app/common/guards';

@Controller('cases/:caseId/events')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, PermissionGuard, ConveyancerCaseGuard)
export class CaseEventController {
    constructor(private readonly caseEventService: CaseEventService) {}

    /**
     * Create a new event for a case
     * Requires CREATE permission on CASE resource and access to conveyancers role group
     */
    @Post()
    @HasPermission(ResourceType.CASE, Permission.CREATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async createEvent(
        @Param('caseId') caseId: string,
        @Body() createCaseEventDto: CreateCaseEventDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const event = await this.caseEventService.createEvent(
            caseId,
            createCaseEventDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(event, 'Case event created successfully');
    }

    /**
     * Update an event
     * Requires UPDATE permission on CASE resource and access to conveyancers role group
     */
    @Put(':eventId')
    @HasPermission(ResourceType.CASE, Permission.UPDATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async updateEvent(
        @Param('caseId') caseId: string,
        @Param('eventId') eventId: string,
        @Body() updateCaseEventDto: UpdateCaseEventDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const event = await this.caseEventService.updateEvent(
            caseId,
            eventId,
            updateCaseEventDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(event, 'Case event updated successfully');
    }

    /**
     * Delete an event
     * Requires DELETE permission on CASE resource and access to conveyancers role group
     */
    @Delete(':eventId')
    @HasPermission(ResourceType.CASE, Permission.DELETE)
    @SuperAdminOrRoleGroup('conveyancers')
    async deleteEvent(
        @Param('caseId') caseId: string,
        @Param('eventId') eventId: string,
        @Req() request: Request
    ) {
        const user = request['user'];
        await this.caseEventService.deleteEvent(
            caseId,
            eventId,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(null, 'Case event deleted successfully');
    }

    /**
     * Get all events for a case
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get()
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getEvents(@Param('caseId') caseId: string, @Query() filterDto: CaseEventFilterDto) {
        // If filter criteria are provided, use filtered query
        if (
            filterDto.category ||
            filterDto.type ||
            filterDto.startDate ||
            filterDto.endDate ||
            filterDto.searchTerm ||
            filterDto.page ||
            filterDto.limit
        ) {
            const paginatedEvents = await this.caseEventService.getFilteredCaseEvents(
                caseId,
                filterDto
            );

            return ApiResponseUtil.ok(paginatedEvents.data, 'Case events retrieved successfully', {
                pagination: paginatedEvents.meta.pagination
            });
        }

        // Otherwise, get all events
        const events = await this.caseEventService.getCaseEvents(caseId);
        return ApiResponseUtil.ok(events, 'Case events retrieved successfully');
    }

    /**
     * Get an event by ID
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get(':eventId')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getEventById(@Param('caseId') caseId: string, @Param('eventId') eventId: string) {
        const event = await this.caseEventService.getEventById(caseId, eventId);
        return ApiResponseUtil.ok(event, 'Case event retrieved successfully');
    }

    /**
     * Get events by category
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get('category/:category')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getEventsByCategory(
        @Param('caseId') caseId: string,
        @Param('category') category: CaseEventCategory
    ) {
        const events = await this.caseEventService.getEventsByCategory(caseId, category);
        return ApiResponseUtil.ok(
            events,
            `Case events of category ${category} retrieved successfully`
        );
    }

    /**
     * Get events by type
     * Requires READ permission on CASE resource and access to conveyancers or finance role groups
     */
    @Get('type/:type')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getEventsByType(@Param('caseId') caseId: string, @Param('type') type: CaseEventType) {
        const events = await this.caseEventService.getEventsByType(caseId, type);
        return ApiResponseUtil.ok(events, `Case events of type ${type} retrieved successfully`);
    }
}
