import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { TenantContextService } from '@app/common/multi-tenancy';
import { TenantRepository } from '@app/common/repositories/tenant.repository';

/**
 * Middleware to set the tenant context for the case-management service
 * This middleware extracts the tenant ID from the request headers and sets it in the TenantContextService
 */
@Injectable()
export class TenantContextMiddleware implements NestMiddleware {
    private readonly logger = new Logger(TenantContextMiddleware.name);

    constructor(
        private readonly tenantContextService: TenantContextService,
        private readonly tenantRepository: TenantRepository
    ) {}

    async use(req: Request, res: Response, next: NextFunction) {
        try {
            // Extract tenant ID from header first, then fallback to cookie
            const headerTenantId = req.headers['x-tenant-id'] as string;
            const cookieTenantId = req.cookies?.selected_tenant_id;
            const tenantId = headerTenantId || cookieTenantId;

            this.logger.debug(
                `TenantContextMiddleware - Header tenant ID: ${headerTenantId}, Cookie tenant ID: ${cookieTenantId}, Selected: ${tenantId}`
            );

            if (tenantId) {
                // Find the tenant in the database
                const tenant = await this.tenantRepository.findOneById(tenantId);

                if (tenant) {
                    // Set the tenant context
                    this.tenantContextService.setTenant(tenant.id, {
                        id: tenant.id,
                        realm: tenant.realm,
                        displayName: tenant.displayName || tenant.realm,
                        enabled: tenant.enabled
                    });

                    this.logger.debug(`Tenant context set to ${tenant.id} (${tenant.realm})`);
                } else {
                    this.logger.warn(`Tenant with ID ${tenantId} not found`);
                }
            }
        } catch (error) {
            this.logger.error(`Error setting tenant context: ${error.message}`, error.stack);
        }

        next();
    }
}
