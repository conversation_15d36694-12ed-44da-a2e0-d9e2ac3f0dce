import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Config } from '@app/common';
import { Logger, ValidationPipe } from '@nestjs/common';
import { instance } from '@app/common/utils';
import { WinstonModule } from 'nest-winston';

async function bootstrap() {
    const app = await NestFactory.create(AppModule, {
        logger: WinstonModule.createLogger({
            instance: instance
        })
    });
    app.setGlobalPrefix(Config.CASE_MANAGEMENT_PREFIX);
    app.useGlobalPipes(
        new ValidationPipe({ forbidNonWhitelisted: true, whitelist: true, stopAtFirstError: true })
    );
    await app.listen(Config.CASE_MANAGEMENT_PORT!);
    Logger.log(`Case Management microservice running on ${await app.getUrl()}`);
}
bootstrap();
// Rebuild trigger: HealthAggregator fix in CommonModule - 20251014-022856
