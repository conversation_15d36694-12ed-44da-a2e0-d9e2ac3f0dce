import { Type } from 'class-transformer';
import {
    IsArray,
    IsEnum,
    IsNotEmpty,
    IsObject,
    IsOptional,
    IsString,
    IsUUID,
    MaxLength,
    ValidateIf,
    ValidateNested
} from 'class-validator';
import { CreateClientDto } from './create-client.dto';
import { CreateNoteDto } from './create-note.dto';
import { CreatePropertyDto } from './create-property.dto';
import { CreateAttachmentDto } from './create-attachment.dto';
import { CasePriority, CaseType } from '@app/common/typeorm/entities/tenant/case.entity';
import { CaseStatus } from '@app/common/enums/case-status.enum';

export enum TransactionType {
    BUY = 'buy',
    SELL = 'sell',
    REMORTGAGE = 'remortgage',
    BUY_SELL = 'buy,sell',
    BUY_SELL_REMORTGAGE = 'buy,sell,remortgage'
}

export class CreateCaseDto {
    @IsString()
    @IsNotEmpty()
    @MaxLength(255)
    title: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsEnum(CasePriority)
    @IsOptional()
    priority?: CasePriority = CasePriority.MEDIUM;

    @IsEnum(CaseType)
    @IsOptional()
    type?: CaseType = CaseType.OTHER;

    @IsEnum(CaseStatus)
    @IsOptional()
    status?: CaseStatus = CaseStatus.DRAFT;

    @IsUUID()
    @IsNotEmpty()
    @ValidateIf((o) => !o.client)
    clientId?: string;

    @IsObject()
    @ValidateNested()
    @Type(() => CreateClientDto)
    @ValidateIf((o) => !o.clientId)
    @IsOptional()
    client?: CreateClientDto;

    @IsString()
    @IsOptional()
    deadline?: string;

    @IsObject()
    @ValidateNested()
    @Type(() => CreateNoteDto)
    @IsOptional()
    initialNote?: CreateNoteDto;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateNoteDto)
    @IsOptional()
    notes?: CreateNoteDto[];

    // Property fields
    @IsUUID()
    @IsOptional()
    @ValidateIf((o) => !o.property)
    propertyId?: string;

    @IsObject()
    @ValidateNested()
    @Type(() => CreatePropertyDto)
    @ValidateIf((o) => !o.propertyId)
    @IsOptional()
    property?: CreatePropertyDto;

    // Rate card field
    @IsUUID()
    @IsOptional()
    rateCardId?: string;

    // Transaction type for filtering applicable fees
    @IsEnum(TransactionType)
    @IsOptional()
    transactionType?: TransactionType;

    @IsObject()
    @ValidateNested()
    @Type(() => CreateAttachmentDto)
    @IsOptional()
    initialAttachment?: CreateAttachmentDto;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateAttachmentDto)
    @IsOptional()
    attachments?: CreateAttachmentDto[];
}
