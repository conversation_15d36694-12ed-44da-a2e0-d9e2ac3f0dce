import {
    IsDate,
    IsEnum,
    IsNotEmpty,
    IsObject,
    IsOptional,
    IsString,
    MaxLength
} from 'class-validator';
import { Type } from 'class-transformer';
import {
    CaseEventCategory,
    CaseEventType
} from '@app/common/typeorm/entities/tenant/case-event.entity';

export class CreateCaseEventDto {
    @IsEnum(CaseEventCategory)
    @IsOptional()
    category?: CaseEventCategory = CaseEventCategory.OTHER;

    @IsEnum(CaseEventType)
    @IsNotEmpty()
    type: CaseEventType;

    @IsString()
    @IsNotEmpty()
    @MaxLength(255)
    title: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsDate()
    @Type(() => Date)
    @IsNotEmpty()
    eventDate: Date;

    @IsObject()
    @IsOptional()
    metadata?: Record<string, any>;
}
