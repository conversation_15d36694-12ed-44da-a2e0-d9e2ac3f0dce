import { IsString, IsNotEmpty, IsOptional, IsEnum, IsNumber, IsBoolean } from 'class-validator';
import {
    PropertyType,
    PropertyTenure,
    PropertyStatus
} from '@app/common/typeorm/entities/tenant/property.entity';

export class CreatePropertyDto {
    // Address Information (Required)
    @IsString()
    @IsNotEmpty()
    fullAddress: string;

    @IsString()
    @IsNotEmpty()
    addressLine1: string;

    @IsString()
    @IsOptional()
    addressLine2?: string;

    @IsString()
    @IsOptional()
    city?: string;

    @IsString()
    @IsOptional()
    county?: string;

    @IsString()
    @IsNotEmpty()
    postalCode: string;

    @IsString()
    @IsOptional()
    country?: string;

    // Property Classification (Required)
    @IsEnum(PropertyType)
    propertyType: PropertyType;

    @IsEnum(PropertyTenure)
    @IsOptional()
    tenure?: PropertyTenure;

    @IsEnum(PropertyStatus)
    @IsOptional()
    status?: PropertyStatus;

    // Property Details (Optional)
    @IsNumber()
    @IsOptional()
    bedrooms?: number;

    @IsNumber()
    @IsOptional()
    bathrooms?: number;

    @IsNumber()
    @IsOptional()
    receptions?: number;

    @IsNumber()
    @IsOptional()
    floorArea?: number;

    @IsNumber()
    @IsOptional()
    plotSize?: number;

    // Financial Information (Optional)
    @IsNumber()
    @IsOptional()
    purchasePrice?: number;

    @IsNumber()
    @IsOptional()
    currentValue?: number;

    @IsNumber()
    @IsOptional()
    mortgageAmount?: number;

    @IsNumber()
    @IsOptional()
    depositAmount?: number;

    // Legal Information (Optional)
    @IsString()
    @IsOptional()
    titleNumber?: string;

    @IsString()
    @IsOptional()
    landRegistryOffice?: string;

    @IsString()
    @IsOptional()
    localAuthority?: string;

    @IsBoolean()
    @IsOptional()
    planningPermissionRequired?: boolean;

    @IsBoolean()
    @IsOptional()
    listedBuilding?: boolean;

    @IsBoolean()
    @IsOptional()
    conservationArea?: boolean;

    // Energy and Environmental (Optional)
    @IsString()
    @IsOptional()
    epcRating?: string;

    @IsBoolean()
    @IsOptional()
    gasSupply?: boolean;

    @IsBoolean()
    @IsOptional()
    electricitySupply?: boolean;

    @IsBoolean()
    @IsOptional()
    waterSupply?: boolean;

    // Vendor Information (Optional)
    @IsString()
    @IsOptional()
    vendorName?: string;

    @IsString()
    @IsOptional()
    vendorAddress?: string;

    @IsString()
    @IsOptional()
    vendorSolicitorName?: string;

    @IsString()
    @IsOptional()
    vendorSolicitorFirm?: string;

    @IsString()
    @IsOptional()
    vendorSolicitorAddress?: string;

    @IsString()
    @IsOptional()
    vendorSolicitorPhone?: string;

    @IsString()
    @IsOptional()
    vendorSolicitorEmail?: string;
}
