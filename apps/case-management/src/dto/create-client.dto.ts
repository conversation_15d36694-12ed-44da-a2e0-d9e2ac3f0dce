import { IsEmail, IsNotEmpty, IsObject, IsOptional, IsString, Max<PERSON>eng<PERSON> } from 'class-validator';

export class CreateClientDto {
    @IsString()
    @IsNotEmpty()
    @MaxLength(255)
    name: string;

    @IsEmail()
    @IsOptional()
    email?: string;

    @IsString()
    @IsOptional()
    phone?: string;

    @IsString()
    @IsOptional()
    address?: string;

    @IsObject()
    @IsOptional()
    additionalInfo?: Record<string, any>;
}
