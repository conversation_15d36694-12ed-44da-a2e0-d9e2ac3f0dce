import { CaseStatus } from '@app/common/enums/case-status.enum';
import { CasePriority, CaseType } from '@app/common/typeorm/entities/tenant/case.entity';
import { IsEnum, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';

export class UpdateCaseDto {
    @IsString()
    @IsOptional()
    @MaxLength(255)
    title?: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsEnum(CaseStatus)
    @IsOptional()
    status?: CaseStatus;

    @IsEnum(CasePriority)
    @IsOptional()
    priority?: CasePriority;

    @IsEnum(CaseType)
    @IsOptional()
    type?: CaseType;

    @IsUUID()
    @IsOptional()
    clientId?: string;

    @IsString()
    @IsOptional()
    deadline?: string;
}
