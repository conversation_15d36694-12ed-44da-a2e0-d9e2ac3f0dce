import { <PERSON><PERSON>num, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { DocumentType } from '@app/common/typeorm/entities/tenant/case-attachment.entity';

export class CreateAttachmentDto {
    @IsString()
    @IsNotEmpty()
    filename: string;

    @IsString()
    @IsNotEmpty()
    url: string;

    @IsNumber()
    @IsOptional()
    fileSize?: number;

    @IsString()
    @IsOptional()
    mimeType?: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsEnum(DocumentType)
    @IsOptional()
    documentType?: DocumentType = DocumentType.OTHER;
}
