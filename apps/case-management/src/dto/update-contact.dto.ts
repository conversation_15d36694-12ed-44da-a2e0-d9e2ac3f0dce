import { IsEmail, IsEnum, IsO<PERSON>, IsOptional, IsString, Max<PERSON><PERSON>th } from 'class-validator';
import { ContactType } from '@app/common/typeorm/entities/tenant/case-contact.entity';

export class UpdateContactDto {
    @IsString()
    @IsOptional()
    @MaxLength(255)
    name?: string;

    @IsEmail()
    @IsOptional()
    email?: string;

    @IsString()
    @IsOptional()
    phone?: string;

    @IsString()
    @IsOptional()
    address?: string;

    @IsEnum(ContactType)
    @IsOptional()
    type?: ContactType;

    @IsObject()
    @IsOptional()
    additionalInfo?: Record<string, any>;
}
