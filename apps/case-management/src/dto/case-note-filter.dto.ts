import { IsOptional, IsBoolean, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class CaseNoteFilterDto {
    @IsOptional()
    @IsInt()
    @Min(1)
    @Type(() => Number)
    page?: number = 1;

    @IsOptional()
    @IsInt()
    @Min(1)
    @Type(() => Number)
    limit?: number = 10;

    @IsOptional()
    @IsBoolean()
    @Type(() => Boolean)
    includePrivate?: boolean = false;

    @IsOptional()
    @IsBoolean()
    @Type(() => Boolean)
    pinnedOnly?: boolean = false;
}
