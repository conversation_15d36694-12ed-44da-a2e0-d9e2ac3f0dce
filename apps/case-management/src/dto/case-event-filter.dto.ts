import { IsDate, IsEnum, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import {
    CaseEventCategory,
    CaseEventType
} from '@app/common/typeorm/entities/tenant/case-event.entity';
import { PaginationDto } from './pagination.dto';

export class CaseEventFilterDto extends PaginationDto {
    @IsEnum(CaseEventCategory)
    @IsOptional()
    category?: CaseEventCategory;

    @IsEnum(CaseEventType)
    @IsOptional()
    type?: CaseEventType;

    @IsDate()
    @Type(() => Date)
    @IsOptional()
    startDate?: Date;

    @IsDate()
    @Type(() => Date)
    @IsOptional()
    endDate?: Date;

    @IsString()
    @IsOptional()
    searchTerm?: string;
}
