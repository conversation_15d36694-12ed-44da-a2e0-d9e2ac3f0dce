import { IsDate, IsEnum, IsObject, IsOptional, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import {
    CaseEventCategory,
    CaseEventType
} from '@app/common/typeorm/entities/tenant/case-event.entity';

export class UpdateCaseEventDto {
    @IsEnum(CaseEventCategory)
    @IsOptional()
    category?: CaseEventCategory;

    @IsEnum(CaseEventType)
    @IsOptional()
    type?: CaseEventType;

    @IsString()
    @IsOptional()
    @MaxLength(255)
    title?: string;

    @IsString()
    @IsOptional()
    description?: string;

    @IsDate()
    @Type(() => Date)
    @IsOptional()
    eventDate?: Date;

    @IsObject()
    @IsOptional()
    metadata?: Record<string, any>;
}
