import {
    IsDateString,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    Min
} from 'class-validator';
import { PaymentMethod } from '@app/common/typeorm/entities';

export class CreateCasePaymentDto {
    @IsDateString()
    @IsNotEmpty()
    paymentDate: string;

    @IsString()
    @IsNotEmpty()
    referenceNumber: string;

    @IsEnum(PaymentMethod)
    @IsNotEmpty()
    method: PaymentMethod;

    @IsString()
    @IsOptional()
    description?: string;

    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0.01)
    @IsNotEmpty()
    amount: number;
}
