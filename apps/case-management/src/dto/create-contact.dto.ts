import {
    IsEmail,
    IsEnum,
    IsNotEmpty,
    IsObject,
    IsOptional,
    IsString,
    MaxLength
} from 'class-validator';
import { ContactType } from '@app/common/typeorm/entities/tenant/case-contact.entity';

export class CreateContactDto {
    @IsString()
    @IsNotEmpty()
    @MaxLength(255)
    name: string;

    @IsEmail()
    @IsOptional()
    email?: string;

    @IsString()
    @IsOptional()
    phone?: string;

    @IsString()
    @IsOptional()
    address?: string;

    @IsEnum(ContactType)
    @IsOptional()
    type?: ContactType = ContactType.OTHER;

    @IsObject()
    @IsOptional()
    additionalInfo?: Record<string, any>;
}
