import {
    IsEnum,
    IsOptional,
    IsString,
    IsUUID,
    IsArray,
    IsBoolean,
    ValidateNested,
    IsObject,
    IsNotEmpty
} from 'class-validator';
import { Type } from 'class-transformer';
import { CaseStatus } from '@app/common/enums/case-status.enum';

/**
 * DTO for case assignment within a transition
 */
export class CaseAssignmentDto {
    @IsUUID()
    userId: string;

    @IsString()
    @IsOptional()
    userName?: string;

    @IsString()
    @IsOptional()
    notes?: string;
}

/**
 * DTO for case state transitions
 */
export class CaseTransitionDto {
    @IsEnum(CaseStatus)
    @IsNotEmpty()
    targetState: CaseStatus;

    @IsString()
    @IsOptional()
    notes?: string;

    @IsUUID()
    @IsOptional()
    assignedTo?: string;

    @IsString()
    @IsOptional()
    holdReason?: string;

    @IsString()
    @IsOptional()
    reopenReason?: string;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CaseAssignmentDto)
    @IsOptional()
    assignments?: CaseAssignmentDto[];

    @IsBoolean()
    @IsOptional()
    isPrivate?: boolean;

    @IsObject()
    @IsOptional()
    data?: Record<string, any> = {};
}
