import { IsEmail, IsObject, IsOptional, IsString, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

export class UpdateClientDto {
    @IsString()
    @IsOptional()
    @MaxLength(255)
    name?: string;

    @IsEmail()
    @IsOptional()
    email?: string;

    @IsString()
    @IsOptional()
    phone?: string;

    @IsString()
    @IsOptional()
    address?: string;

    @IsObject()
    @IsOptional()
    additionalInfo?: Record<string, any>;
}
