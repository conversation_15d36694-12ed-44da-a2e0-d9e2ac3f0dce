import { IsIn, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export enum TimeFilterOption {
    TODAY = 'today',
    THIS_WEEK = 'this_week',
    THIS_MONTH = 'this_month',
    THIS_YEAR = 'this_year'
}

export class CaseFilterDto {
    @IsOptional()
    @Type(() => Number)
    page?: number = 1;

    @IsOptional()
    @Type(() => Number)
    limit?: number = 10;

    @IsOptional()
    @IsString()
    sortBy?: string = 'createdAt';

    @IsOptional()
    @IsIn(['ASC', 'DESC'])
    order?: 'ASC' | 'DESC' = 'DESC';

    @IsOptional()
    @IsIn([
        TimeFilterOption.TODAY,
        TimeFilterOption.THIS_WEEK,
        TimeFilterOption.THIS_MONTH,
        TimeFilterOption.THIS_YEAR
    ])
    filterBy?: TimeFilterOption;
}
