import { <PERSON><PERSON><PERSON>, IsNotEmpty, Is<PERSON><PERSON>al, IsString, IsUUID } from 'class-validator';
import { RelationType } from '@app/common/typeorm/entities/tenant/case-relation.entity';

export class CreateCaseRelationDto {
    @IsUUID()
    @IsNotEmpty()
    relatedCaseId: string;

    @IsEnum(RelationType)
    @IsOptional()
    type?: RelationType = RelationType.RELATED;

    @IsString()
    @IsOptional()
    notes?: string;
}
