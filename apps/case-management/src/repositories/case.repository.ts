import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { Repository } from 'typeorm';
import { CaseFilterDto, TimeFilterOption } from '../dto/case-filter.dto';
import { Case } from '@app/common/typeorm/entities';

@Injectable()
export class CaseRepository extends BaseTenantRepository<Case> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(Case, tenantContextService, tenantConnectionService);
    }

    async findByCaseNumber(caseNumber: string): Promise<Case | null> {
        return this.findOne({ where: { caseNumber } });
    }

    async findByCaseId(id: string): Promise<Case | null> {
        return this.findOne({ where: { id } });
    }
    /**
 * export enum TimeFilterOption {
    TODAY = 'today',
    THIS_WEEK = 'this_week',
    THIS_MONTH = 'this_month',
    THIS_YEAR = 'this_year'
}

 */

    /**
     * Finds and paginates cases with optional time-based filters.
     * All date calculations are performed in UTC to ensure time zone consistency.
     * @param filterDto - DTO containing filter, pagination, and sorting options.
     * @returns A promise that resolves to an array containing the cases and the total count.
     */
    async findWithFilters(filterDto: CaseFilterDto): Promise<[Case[], number]> {
        const { page = 1, limit = 10, sortBy = 'createdAt', order = 'DESC', filterBy } = filterDto;

        const repository = await this.getTenantRepository();
        const queryBuilder = repository
            .createQueryBuilder('case')
            .leftJoinAndSelect('case.client', 'client')
            .leftJoinAndSelect('case.assignments', 'assignments');

        if (filterBy as TimeFilterOption) {
            const nowUTC = new Date();
            // ⭐️ FIX: Initialize the variable to null to guarantee it is assigned.
            let startDateUTC: Date | null = null;

            switch (filterBy) {
                case TimeFilterOption.TODAY:
                    startDateUTC = new Date(
                        Date.UTC(nowUTC.getUTCFullYear(), nowUTC.getUTCMonth(), nowUTC.getUTCDate())
                    );
                    break;
                case TimeFilterOption.THIS_WEEK: {
                    const dayOfWeek = nowUTC.getUTCDay(); // 0=Sunday
                    startDateUTC = new Date(
                        Date.UTC(
                            nowUTC.getUTCFullYear(),
                            nowUTC.getUTCMonth(),
                            nowUTC.getUTCDate() - dayOfWeek
                        )
                    );
                    break;
                }
                case TimeFilterOption.THIS_MONTH:
                    startDateUTC = new Date(
                        Date.UTC(nowUTC.getUTCFullYear(), nowUTC.getUTCMonth(), 1)
                    );
                    break;
                case TimeFilterOption.THIS_YEAR:
                    startDateUTC = new Date(Date.UTC(nowUTC.getUTCFullYear(), 0, 1));
                    break;
            }

            // This check now safely handles cases where filterBy is not a valid option.
            if (startDateUTC) {
                queryBuilder.andWhere(
                    'case.createdAt >= :startDate AND case.createdAt <= :endDate',
                    {
                        startDate: startDateUTC,
                        endDate: nowUTC
                    }
                );
            }
        }

        // Apply sorting.
        if (sortBy.includes('.')) {
            const [relation, field] = sortBy.split('.');
            queryBuilder.orderBy(`${relation}.${field}`, order);
        } else {
            queryBuilder.orderBy(`case.${sortBy}`, order);
        }

        // Apply pagination.
        queryBuilder.skip((page - 1) * limit).take(limit);

        // Execute the query.
        const [cases, total] = await queryBuilder.getManyAndCount();

        return [cases, total];
    }

    /**
     * Find cases by assigned person's name (first or last name)
     */
    async findByAssignedPersonName(nameQuery: string, limit: number = 10): Promise<Case[]> {
        const repository = await this.getTenantRepository();

        const queryBuilder = repository
            .createQueryBuilder('case')
            .leftJoinAndSelect('case.client', 'client')
            .innerJoin('case_assignments', 'assignment', 'case.id = assignment.case_id')
            .where('assignment.user_name ILIKE :nameQuery', { nameQuery: `%${nameQuery}%` })
            .andWhere('assignment.is_active = true')
            .orderBy('case.updatedAt', 'DESC')
            .take(limit);

        return queryBuilder.getMany();
    }

    /**
     * Find cases by client name
     */
    async findByClientName(nameQuery: string, limit: number = 10): Promise<Case[]> {
        const repository = await this.getTenantRepository();

        const queryBuilder = repository
            .createQueryBuilder('case')
            .leftJoinAndSelect('case.client', 'client')
            .where('client.name ILIKE :nameQuery', { nameQuery: `%${nameQuery}%` })
            .orderBy('case.updatedAt', 'DESC')
            .take(limit);

        return queryBuilder.getMany();
    }

    /**
     * Gets the tenant repository - public method to allow access from services
     */
    async getRepository(): Promise<Repository<Case>> {
        return this.getTenantRepository();
    }
}
