import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { CaseRelation } from '@app/common/typeorm/entities/tenant/case-relation.entity';

@Injectable()
export class CaseRelationRepository extends BaseTenantRepository<CaseRelation> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(CaseRelation, tenantContextService, tenantConnectionService);
    }

    /**
     * Find relations by case ID
     * @param caseId The case ID
     * @returns Array of relations for the case
     */
    async findByCaseId(caseId: string): Promise<CaseRelation[]> {
        return this.find({
            where: {
                caseId
            },
            order: {
                createdAt: 'DESC'
            }
        });
    }

    /**
     * Find relations where the case is the related case
     * @param caseId The case ID
     * @returns Array of relations where the case is the related case
     */
    async findByRelatedCaseId(caseId: string): Promise<CaseRelation[]> {
        return this.find({
            where: {
                relatedCaseId: caseId
            },
            order: {
                createdAt: 'DESC'
            }
        });
    }

    /**
     * Find all relations for a case (both directions)
     * @param caseId The case ID
     * @returns Array of relations for the case (both directions)
     */
    async findAllRelations(caseId: string): Promise<CaseRelation[]> {
        const repository = await this.getTenantRepository();

        return repository
            .createQueryBuilder('relation')
            .where('relation.case_id = :caseId OR relation.related_case_id = :caseId', { caseId })
            .orderBy('relation.created_at', 'DESC')
            .getMany();
    }

    /**
     * Check if a relation exists between two cases
     * @param caseId The case ID
     * @param relatedCaseId The related case ID
     * @returns True if a relation exists, false otherwise
     */
    async relationExists(caseId: string, relatedCaseId: string): Promise<boolean> {
        const repository = await this.getTenantRepository();

        const count = await repository
            .createQueryBuilder('relation')
            .where(
                '(relation.case_id = :caseId AND relation.related_case_id = :relatedCaseId) OR ' +
                    '(relation.case_id = :relatedCaseId AND relation.related_case_id = :caseId)',
                { caseId, relatedCaseId }
            )
            .getCount();

        return count > 0;
    }

    /**
     * Delete a relation
     * @param id The relation ID
     * @returns void
     */
    async deleteRelation(id: string): Promise<void> {
        const repository = await this.getTenantRepository();
        await repository.delete(id);
    }
}
