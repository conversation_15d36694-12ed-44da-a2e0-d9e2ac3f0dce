import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { CaseAssignment } from '@app/common/typeorm/entities';

@Injectable()
export class CaseAssignmentRepository extends BaseTenantRepository<CaseAssignment> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(CaseAssignment, tenantContextService, tenantConnectionService);
    }

    async findByCaseId(caseId: string): Promise<CaseAssignment[]> {
        return this.find({
            where: {
                caseId,
                isActive: true
            },
            order: {
                assignedAt: 'DESC'
            }
        });
    }

    async findActiveAssignment(caseId: string, userId: string): Promise<CaseAssignment | null> {
        return this.findOne({
            where: {
                caseId,
                userId,
                isActive: true
            }
        });
    }

    async deactivateAssignments(caseId: string): Promise<void> {
        const repository = await this.getTenantRepository();
        await repository
            .createQueryBuilder()
            .update(CaseAssignment)
            .set({ isActive: false })
            .where('case_id = :caseId', { caseId })
            .execute();
    }
}
