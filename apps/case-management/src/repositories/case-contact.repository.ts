import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { ILike } from 'typeorm';
import { CaseContact, ContactType } from '@app/common/typeorm/entities/tenant/case-contact.entity';

@Injectable()
export class CaseContactRepository extends BaseTenantRepository<CaseContact> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(CaseContact, tenantContextService, tenantConnectionService);
    }

    /**
     * Find contacts by case ID
     * @param caseId The case ID
     * @returns Array of contacts for the case
     */
    async findByCaseId(caseId: string): Promise<CaseContact[]> {
        return this.find({
            where: {
                caseId
            },
            order: {
                createdAt: 'DESC'
            }
        });
    }

    /**
     * Find contacts by case ID and type
     * @param caseId The case ID
     * @param type The contact type
     * @returns Array of contacts for the case with the specified type
     */
    async findByCaseIdAndType(caseId: string, type: ContactType): Promise<CaseContact[]> {
        return this.find({
            where: {
                caseId,
                type
            },
            order: {
                createdAt: 'DESC'
            }
        });
    }

    /**
     * Search contacts by name
     * @param caseId The case ID
     * @param name The name to search for
     * @returns Array of contacts matching the search criteria
     */
    async searchByName(caseId: string, name: string): Promise<CaseContact[]> {
        return this.find({
            where: {
                caseId,
                name: ILike(`%${name}%`)
            }
        });
    }

    /**
     * Search contacts by email
     * @param caseId The case ID
     * @param email The email to search for
     * @returns Array of contacts matching the search criteria
     */
    async searchByEmail(caseId: string, email: string): Promise<CaseContact[]> {
        return this.find({
            where: {
                caseId,
                email: ILike(`%${email}%`)
            }
        });
    }

    /**
     * Delete a contact
     * @param id The contact ID
     * @returns void
     */
    async deleteContact(id: string): Promise<void> {
        const repository = await this.getTenantRepository();
        await repository.delete(id);
    }
}
