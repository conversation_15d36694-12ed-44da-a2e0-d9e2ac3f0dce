import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { Property } from '@app/common/typeorm/entities/tenant/property.entity';
import { ILike } from 'typeorm';

@Injectable()
export class PropertyRepository extends BaseTenantRepository<Property> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(Property, tenantContextService, tenantConnectionService);
    }

    async findById(id: string): Promise<Property | null> {
        return this.findOne({
            where: {
                id
            }
        });
    }

    async findByAddress(address: string): Promise<Property[]> {
        return this.find({
            where: {
                fullAddress: ILike(`%${address}%`)
            }
        });
    }

    async findByPostalCode(postalCode: string): Promise<Property[]> {
        return this.find({
            where: {
                postalCode: ILike(`%${postalCode}%`)
            }
        });
    }

    async findByTitleNumber(titleNumber: string): Promise<Property | null> {
        return this.findOne({
            where: {
                titleNumber
            }
        });
    }
}
