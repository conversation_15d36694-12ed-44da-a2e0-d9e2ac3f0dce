import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { CaseNote } from '@app/common/typeorm/entities';

@Injectable()
export class CaseNoteRepository extends BaseTenantRepository<CaseNote> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(CaseNote, tenantContextService, tenantConnectionService);
    }

    async findByCaseId(caseId: string, includePrivate: boolean = false): Promise<CaseNote[]> {
        const where: any = { caseId };

        if (!includePrivate) {
            where.isPrivate = false;
        }

        return this.find({
            where,
            order: {
                isPinned: 'DESC',
                createdAt: 'DESC'
            }
        });
    }

    async findPinnedNotes(caseId: string): Promise<CaseNote[]> {
        return this.find({
            where: {
                caseId,
                isPinned: true
            },
            order: {
                createdAt: 'DESC'
            }
        });
    }

    async togglePinStatus(noteId: string, isPinned: boolean): Promise<void> {
        const repository = await this.getTenantRepository();
        await repository
            .createQueryBuilder()
            .update(CaseNote)
            .set({ isPinned })
            .where('id = :id', { id: noteId })
            .execute();
    }

    /**
     * Finds the most recent notes for a case
     * @param caseId The case ID
     * @param limit The maximum number of notes to return
     * @returns The most recent notes
     */
    async findRecentNotes(caseId: string, limit: number = 3): Promise<CaseNote[]> {
        return this.find({
            where: {
                caseId,
                isPrivate: false
            },
            order: {
                createdAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Finds case notes with filtering and pagination
     * @param caseId The case ID
     * @param filterDto The filter criteria
     * @returns Array of notes and total count
     */
    async findWithFilters(caseId: string, filterDto: any): Promise<[CaseNote[], number]> {
        const { page = 1, limit = 10, includePrivate = false, pinnedOnly = false } = filterDto;

        const where: any = { caseId };

        if (!includePrivate) {
            where.isPrivate = false;
        }

        if (pinnedOnly) {
            where.isPinned = true;
        }

        const repository = await this.getTenantRepository();
        const [notes, total] = await repository.findAndCount({
            where,
            order: {
                isPinned: 'DESC',
                createdAt: 'DESC'
            },
            skip: (page - 1) * limit,
            take: limit
        });

        return [notes, total];
    }
}
