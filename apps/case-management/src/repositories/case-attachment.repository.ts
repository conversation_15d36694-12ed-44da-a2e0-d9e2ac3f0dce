import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { ILike } from 'typeorm';
import { CaseAttachment } from '@app/common/typeorm/entities';
import { DocumentType } from '@app/common/typeorm/entities/tenant/case-attachment.entity';

@Injectable()
export class CaseAttachmentRepository extends BaseTenantRepository<CaseAttachment> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(CaseAttachment, tenantContextService, tenantConnectionService);
    }

    async findByCaseId(caseId: string): Promise<CaseAttachment[]> {
        return this.find({
            where: {
                caseId
            },
            order: {
                uploadedAt: 'DESC'
            }
        });
    }

    async findByFilename(caseId: string, filename: string): Promise<CaseAttachment[]> {
        return this.find({
            where: {
                caseId,
                filename: ILike(`%${filename}%`)
            }
        });
    }

    async findByDocumentType(
        caseId: string,
        documentType: DocumentType
    ): Promise<CaseAttachment[]> {
        return this.find({
            where: {
                caseId,
                documentType
            },
            order: {
                uploadedAt: 'DESC'
            }
        });
    }

    async deleteAttachment(id: string): Promise<void> {
        const repository = await this.getTenantRepository();
        await repository.delete(id);
    }
}
