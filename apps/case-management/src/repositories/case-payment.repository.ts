import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { CasePayment } from '@app/common/typeorm/entities';

@Injectable()
export class CasePaymentRepository extends BaseTenantRepository<CasePayment> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(CasePayment, tenantContextService, tenantConnectionService);
    }

    /**
     * Find payments by case ID
     */
    async findByCaseId(caseId: string): Promise<CasePayment[]> {
        return this.find({
            where: { caseId },
            order: { paymentDate: 'DESC' }
        });
    }

    /**
     * Find payment by ID and case ID (for security)
     */
    async findByIdAndCaseId(id: string, caseId: string): Promise<CasePayment | null> {
        return this.findOne({
            where: { id, caseId }
        });
    }
}
