import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { ILike } from 'typeorm';
import {
    CaseEvent,
    CaseEventCategory,
    CaseEventType
} from '@app/common/typeorm/entities/tenant/case-event.entity';
import { CaseEventFilterDto } from '../dto/case-event-filter.dto';

@Injectable()
export class CaseEventRepository extends BaseTenantRepository<CaseEvent> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(CaseEvent, tenantContextService, tenantConnectionService);
    }

    /**
     * Find events by case ID
     * @param caseId The case ID
     * @returns Array of events for the case
     */
    async findByCaseId(caseId: string): Promise<CaseEvent[]> {
        return this.find({
            where: {
                caseId
            },
            order: {
                eventDate: 'ASC'
            }
        });
    }

    /**
     * Find events by case ID and category
     * @param caseId The case ID
     * @param category The event category
     * @returns Array of events for the case with the specified category
     */
    async findByCaseIdAndCategory(
        caseId: string,
        category: CaseEventCategory
    ): Promise<CaseEvent[]> {
        return this.find({
            where: {
                caseId,
                category
            },
            order: {
                eventDate: 'ASC'
            }
        });
    }

    /**
     * Find events by case ID and type
     * @param caseId The case ID
     * @param type The event type
     * @returns Array of events for the case with the specified type
     */
    async findByCaseIdAndType(caseId: string, type: CaseEventType): Promise<CaseEvent[]> {
        return this.find({
            where: {
                caseId,
                type
            },
            order: {
                eventDate: 'ASC'
            }
        });
    }

    /**
     * Find events by case ID with filtering
     * @param caseId The case ID
     * @param filterDto The filter criteria
     * @returns Array of events matching the filter criteria
     */
    async findWithFilters(
        caseId: string,
        filterDto: CaseEventFilterDto
    ): Promise<[CaseEvent[], number]> {
        const repository = await this.getTenantRepository();
        const queryBuilder = repository.createQueryBuilder('event');

        // Base condition: match the case ID
        queryBuilder.where('event.case_id = :caseId', { caseId });

        // Apply filters
        if (filterDto.category) {
            queryBuilder.andWhere('event.category = :category', { category: filterDto.category });
        }

        if (filterDto.type) {
            queryBuilder.andWhere('event.type = :type', { type: filterDto.type });
        }

        if (filterDto.startDate && filterDto.endDate) {
            queryBuilder.andWhere('event.event_date BETWEEN :startDate AND :endDate', {
                startDate: filterDto.startDate,
                endDate: filterDto.endDate
            });
        } else if (filterDto.startDate) {
            queryBuilder.andWhere('event.event_date >= :startDate', {
                startDate: filterDto.startDate
            });
        } else if (filterDto.endDate) {
            queryBuilder.andWhere('event.event_date <= :endDate', { endDate: filterDto.endDate });
        }

        if (filterDto.searchTerm) {
            queryBuilder.andWhere(
                '(event.title ILIKE :searchTerm OR event.description ILIKE :searchTerm)',
                { searchTerm: `%${filterDto.searchTerm}%` }
            );
        }

        // Add sorting
        queryBuilder.orderBy('event.event_date', 'ASC');

        // Add pagination
        if (filterDto.page && filterDto.limit) {
            const skip = (filterDto.page - 1) * filterDto.limit;
            queryBuilder.skip(skip).take(filterDto.limit);
        }

        return queryBuilder.getManyAndCount();
    }

    /**
     * Search events by title or description
     * @param caseId The case ID
     * @param searchTerm The search term
     * @returns Array of events matching the search criteria
     */
    async searchEvents(caseId: string, searchTerm: string): Promise<CaseEvent[]> {
        return this.find({
            where: [
                { caseId, title: ILike(`%${searchTerm}%`) },
                { caseId, description: ILike(`%${searchTerm}%`) }
            ],
            order: {
                eventDate: 'ASC'
            }
        });
    }

    /**
     * Delete an event
     * @param id The event ID
     * @returns void
     */
    async deleteEvent(id: string): Promise<void> {
        const repository = await this.getTenantRepository();
        await repository.delete(id);
    }
}
