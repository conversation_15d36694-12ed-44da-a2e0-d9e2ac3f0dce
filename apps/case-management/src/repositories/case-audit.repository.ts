import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { CaseAudit } from '@app/common/typeorm/entities';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';

@Injectable()
export class CaseAuditRepository extends BaseTenantRepository<CaseAudit> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(CaseAudit, tenantContextService, tenantConnectionService);
    }

    async findByCaseId(caseId: string, limit: number = 50): Promise<CaseAudit[]> {
        return this.find({
            where: {
                caseId
            },
            order: {
                performedAt: 'DESC'
            },
            take: limit
        });
    }

    async logAction(
        caseId: string,
        action: CaseAuditAction,
        performedBy: string,
        performedByName?: string,
        ipAddress?: string,
        details?: Record<string, any>
    ): Promise<CaseAudit> {
        const auditEntry = await this.create({
            caseId,
            action,
            performedBy,
            performedByName,
            ipAddress,
            details,
            performedAt: new Date()
        });

        return this.save(auditEntry);
    }
}
