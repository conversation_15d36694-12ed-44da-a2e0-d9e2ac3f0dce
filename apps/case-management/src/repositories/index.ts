import { CaseRepository } from './case.repository';
import { ClientRepository } from './client.repository';
import { PropertyRepository } from './property.repository';
import { CaseAssignmentRepository } from './case-assignment.repository';
import { CaseNoteRepository } from './case-note.repository';
import { CaseAttachmentRepository } from './case-attachment.repository';
import { CaseAuditRepository } from './case-audit.repository';
import { CaseContactRepository } from './case-contact.repository';
import { CaseEventRepository } from './case-event.repository';
import { CaseRelationRepository } from './case-relation.repository';
import { CasePaymentRepository } from './case-payment.repository';
import { RateCardRepository } from 'apps/quote-engine/src/repositories/rate-card.repository';
import { RateCardFeeItemRepository } from 'apps/quote-engine/src/repositories/rate-card-fee-item.repository';

export const CASE_MANAGEMENT_REPOSITORIES = [
    CaseRepository,
    ClientRepository,
    PropertyRepository,
    CaseAssignmentRepository,
    CaseNoteRepository,
    CaseAttachmentRepository,
    CaseAuditRepository,
    CaseContactRepository,
    CaseEventRepository,
    CaseRelationRepository,
    CasePaymentRepository,
    RateCardRepository,
    RateCardFeeItemRepository
];

export {
    CaseRepository,
    ClientRepository,
    PropertyRepository,
    CaseAssignmentRepository,
    CaseNoteRepository,
    CaseAttachmentRepository,
    CaseAuditRepository,
    CaseContactRepository,
    CaseEventRepository,
    CaseRelationRepository,
    CasePaymentRepository,
    RateCardRepository,
    RateCardFeeItemRepository
};
