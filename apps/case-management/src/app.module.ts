import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CommonModule } from '@app/common';
import { ApiResponseModule } from '@app/common/api-response';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CASE_MANAGEMENT_CONTROLLERS } from './controllers';
import { CASE_MANAGEMENT_SERVICES } from './services';
import { CASE_MANAGEMENT_REPOSITORIES } from './repositories';
import { CaseAssignmentService } from './services/case-assignment.service';
import { CaseAuditService } from './services/case-audit.service';
import { CASE_ASSIGNMENT_CHECKER } from '@app/common/interfaces/case-assignment-checker.interface';
import { CASE_AUDIT_SERVICE } from '@app/common';
import { AuthModule } from '@app/common/auth/auth.module';
import { BullProviderModule } from '@app/common/bull/bull.module';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import {
    Case,
    CaseAssignment,
    CaseAttachment,
    CaseAudit,
    CaseNote,
    CasePayment,
    Client,
    Milestone,
    Task,
    Property
} from '@app/common/typeorm/entities';
import { RateCard } from '@app/common/typeorm/entities/tenant/rate-card.entity';
import { RateCardFeeItem } from '@app/common/typeorm/entities/tenant/rate-card-fee-item.entity';
import { TenantContextMiddleware } from './middleware/tenant-context.middleware';
import { TaskManagementModule } from 'apps/task-management/src/task-management.module';
import * as cookieParser from 'cookie-parser';

const apiResponseExcludedPaths = [
    '/api/case-management/health',
    '/api/case-management/health/db',
    '/health',
    '/ping'
];

@Module({
    imports: [
        CommonModule,
        ConfigModule.forRoot({
            isGlobal: true
        }),
        ApiResponseModule.forRoot({
            excludePaths: apiResponseExcludedPaths
        }),
        TypeOrmModule.forFeature([
            Case,
            Client,
            Property,
            CaseAssignment,
            CaseNote,
            CaseAttachment,
            CaseAudit,
            Milestone,
            Task,
            CasePayment,
            RateCard,
            RateCardFeeItem
        ]),
        AuthModule, // Import the AuthModule from the common library
        TaskManagementModule, // Import TaskManagementModule to access EnhancedMilestoneService
        BullProviderModule // Import BullProviderModule for queue operations
    ],
    controllers: [...CASE_MANAGEMENT_CONTROLLERS],
    providers: [
        ...CASE_MANAGEMENT_SERVICES,
        ...CASE_MANAGEMENT_REPOSITORIES,
        MessageProducerService, // Add MessageProducerService for communication
        {
            provide: CASE_ASSIGNMENT_CHECKER,
            useExisting: CaseAssignmentService
        },
        {
            provide: CASE_AUDIT_SERVICE,
            useExisting: CaseAuditService
        }
    ]
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        // Apply cookie-parser middleware first
        consumer.apply(cookieParser()).forRoutes('*');
        // Then apply tenant context middleware
        consumer.apply(TenantContextMiddleware).forRoutes('*');
    }
}
