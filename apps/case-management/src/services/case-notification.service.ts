import { Injectable, Logger } from '@nestjs/common';
import { CaseRepository } from '../repositories/case.repository';
import { CaseAssignmentRepository } from '../repositories/case-assignment.repository';
import { CaseAuditService } from './case-audit.service';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import { TenantContextService } from '@app/common/multi-tenancy/tenant-context.service';
import {
    CaseUpdateVariables,
    CaseCreatedVariables,
    CaseUrgentVariables
} from '@app/common/communication/interfaces/communication-job.interface';
import {
    CaseNotificationType,
    CaseUrgencyLevel,
    CaseReminderType,
    ReminderStatus,
    DEFAULT_NAMES,
    DEFAULT_MESSAGES
} from '@app/common/enums/case-notification-types.enum';
import { AuditSource } from '@app/common/enums/case-operations.enum';
import { CaseTemplateType } from '@app/common/enums/case-template-types.enum';
import { CasePriority } from '@app/common/typeorm/entities/tenant/case.entity';

/**
 * Service for handling case notifications
 */
@Injectable()
export class CaseNotificationService {
    private readonly logger = new Logger(CaseNotificationService.name);

    constructor(
        private readonly caseRepository: CaseRepository,
        private readonly caseAssignmentRepository: CaseAssignmentRepository,
        private readonly caseAuditService: CaseAuditService,
        private readonly messageProducer: MessageProducerService,
        private readonly tenantContext: TenantContextService
    ) {}

    /**
     * Checks for upcoming deadlines and returns cases with deadlines within the specified hours
     * @param hours Number of hours to check for upcoming deadlines
     * @returns Cases with upcoming deadlines
     */
    async checkUpcomingDeadlines(hours: number = 24): Promise<any[]> {
        const repository = await this.caseRepository.getRepository();

        // Calculate the date range for upcoming deadlines
        const now = new Date();
        const futureDate = new Date(now.getTime() + hours * 60 * 60 * 1000);

        // Find cases with deadlines in the specified range
        const queryBuilder = repository
            .createQueryBuilder('case')
            .leftJoinAndSelect('case.client', 'client')
            .where('case.deadline IS NOT NULL')
            .andWhere('case.deadline > :now', { now })
            .andWhere('case.deadline <= :futureDate', { futureDate })
            .orderBy('case.deadline', 'ASC');

        const cases = await queryBuilder.getMany();

        // For each case, get the assignments
        const casesWithAssignments = await Promise.all(
            cases.map(async (caseEntity) => {
                const assignments = await this.caseAssignmentRepository.findByCaseId(caseEntity.id);
                return {
                    ...caseEntity,
                    assignments
                };
            })
        );

        return casesWithAssignments;
    }

    /**
     * Checks for missed deadlines and returns cases with deadlines that have passed
     * @returns Cases with missed deadlines
     */
    async checkMissedDeadlines(): Promise<any[]> {
        const repository = await this.caseRepository.getRepository();

        // Calculate the date range for missed deadlines
        const now = new Date();

        // Find cases with deadlines that have passed
        const queryBuilder = repository
            .createQueryBuilder('case')
            .leftJoinAndSelect('case.client', 'client')
            .where('case.deadline IS NOT NULL')
            .andWhere('case.deadline <= :now', { now })
            .orderBy('case.deadline', 'DESC');

        const cases = await queryBuilder.getMany();

        // For each case, get the assignments
        const casesWithAssignments = await Promise.all(
            cases.map(async (caseEntity) => {
                const assignments = await this.caseAssignmentRepository.findByCaseId(caseEntity.id);
                return {
                    ...caseEntity,
                    assignments
                };
            })
        );

        return casesWithAssignments;
    }

    /**
     * Sets a custom reminder for a case deadline
     * @param caseId The case ID
     * @param userId The user ID setting the reminder
     * @param reminderTime The time for the reminder (ISO string)
     * @param reminderType The type of reminder (e.g., email, in-app)
     * @returns The created reminder
     */
    async setCustomReminder(
        caseId: string,
        userId: string,
        userName: string,
        reminderTime: string,
        reminderType: CaseReminderType = CaseReminderType.EMAIL
    ): Promise<any> {
        // In a real implementation, this would create a record in a reminders table
        // For now, we'll just log it in the audit trail

        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.REMINDER_SET,
            userId,
            userName,
            AuditSource.SYSTEM,
            {
                reminderTime,
                reminderType,
                setAt: new Date().toISOString()
            }
        );

        return {
            caseId,
            userId,
            reminderTime,
            reminderType,
            status: ReminderStatus.SCHEDULED
        };
    }

    /**
     * Sends notification for case status change
     */
    async sendCaseStatusNotification(
        caseEntity: any,
        oldStatus: string,
        newStatus: string,
        userId: string,
        recipientEmails: string[]
    ): Promise<void> {
        try {
            const tenantId = this.tenantContext.getTenantId();
            const client = caseEntity.client;

            const variables: CaseUpdateVariables = {
                type: CaseTemplateType.CASE_UPDATE,
                tenantName: tenantId,
                recipientName: client?.name || DEFAULT_NAMES.CLIENT,
                caseId: caseEntity.id,
                caseNumber: caseEntity.caseNumber,
                status: newStatus,
                caseSummary: `Case status changed from ${oldStatus} to ${newStatus}`,
                caseType: caseEntity.type,
                deadline: caseEntity.deadline,
                urgency: this.determineUrgency(caseEntity)
            };

            for (const email of recipientEmails) {
                await this.messageProducer.enqueueMessage({
                    tenantId,
                    userId,
                    channels: [COMMUNICATION_CHANNELS.EMAIL],
                    recipients: {
                        email: [email]
                    },
                    variables,
                    caseId: caseEntity.id,
                    metadata: {
                        notificationType: CaseNotificationType.STATUS_CHANGE,
                        oldStatus,
                        newStatus
                    }
                });
            }

            this.logger.log(
                `Case status notification sent for case ${caseEntity.caseNumber} to ${recipientEmails.length} recipients`
            );
        } catch (error) {
            this.logger.error(
                `Failed to send case status notification: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Sends notification for case creation
     */
    async sendCaseCreatedNotification(
        caseEntity: any,
        userId: string,
        recipientEmails: string[]
    ): Promise<void> {
        try {
            const tenantId = this.tenantContext.getTenantId();
            const client = caseEntity.client;

            const variables: CaseCreatedVariables = {
                type: CaseTemplateType.CASE_CREATED,
                tenantName: tenantId,
                recipientName: client?.name || DEFAULT_NAMES.CLIENT,
                caseId: caseEntity.id,
                caseNumber: caseEntity.caseNumber,
                status: caseEntity.status,
                caseType: caseEntity.type,
                caseSummary: caseEntity.description || DEFAULT_MESSAGES.NEW_CASE_CREATED,
                deadline: caseEntity.deadline
            };

            for (const email of recipientEmails) {
                await this.messageProducer.enqueueMessage({
                    tenantId,
                    userId,
                    channels: [COMMUNICATION_CHANNELS.EMAIL],
                    recipients: {
                        email: [email]
                    },
                    variables,
                    caseId: caseEntity.id,
                    metadata: {
                        notificationType: CaseNotificationType.CASE_CREATED
                    }
                });
            }

            this.logger.log(
                `Case creation notification sent for case ${caseEntity.caseNumber} to ${recipientEmails.length} recipients`
            );
        } catch (error) {
            this.logger.error(
                `Failed to send case creation notification: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Sends notification for case assignment
     */
    async sendCaseAssignmentNotification(
        caseEntity: any,
        assignedUserId: string,
        assignedUserEmail: string,
        assignedBy: string
    ): Promise<void> {
        try {
            const tenantId = this.tenantContext.getTenantId();

            const variables: CaseUpdateVariables = {
                type: CaseTemplateType.CASE_UPDATE,
                tenantName: tenantId,
                recipientName: assignedUserEmail.split('@')[0],
                caseId: caseEntity.id,
                caseNumber: caseEntity.caseNumber,
                status: caseEntity.status,
                caseSummary: `You have been assigned to case ${caseEntity.caseNumber} by ${assignedBy}`,
                caseType: caseEntity.type,
                deadline: caseEntity.deadline,
                urgency: this.determineUrgency(caseEntity)
            };

            await this.messageProducer.enqueueMessage({
                tenantId,
                userId: assignedUserId,
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: [assignedUserEmail],
                    notification: [assignedUserId]
                },
                variables,
                caseId: caseEntity.id,
                metadata: {
                    notificationType: CaseNotificationType.CASE_ASSIGNMENT,
                    assignedBy
                }
            });

            this.logger.log(
                `Case assignment notification sent for case ${caseEntity.caseNumber} to ${assignedUserEmail}`
            );
        } catch (error) {
            this.logger.error(
                `Failed to send case assignment notification: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Sends deadline reminder notifications
     */
    async sendDeadlineReminders(): Promise<void> {
        try {
            const tenantId = this.tenantContext.getTenantId();
            const upcomingCases = await this.checkUpcomingDeadlines(24);

            for (const caseEntity of upcomingCases) {
                const hoursUntilDeadline = Math.round(
                    (new Date(caseEntity.deadline).getTime() - Date.now()) / (1000 * 60 * 60)
                );

                const variables: CaseUrgentVariables = {
                    type: CaseTemplateType.CASE_URGENT,
                    tenantName: tenantId,
                    recipientName: caseEntity.client?.name || DEFAULT_NAMES.CLIENT,
                    caseId: caseEntity.id,
                    caseNumber: caseEntity.caseNumber,
                    status: caseEntity.status,
                    caseSummary: `Deadline approaching in ${hoursUntilDeadline} hours`,
                    deadline: caseEntity.deadline,
                    urgency:
                        hoursUntilDeadline <= 6 ? CaseUrgencyLevel.CRITICAL : CaseUrgencyLevel.HIGH
                };

                for (const assignment of caseEntity.assignments || []) {
                    if (assignment.userEmail) {
                        await this.messageProducer.enqueueMessage({
                            tenantId,
                            userId: assignment.userId,
                            channels: [
                                COMMUNICATION_CHANNELS.EMAIL,
                                COMMUNICATION_CHANNELS.NOTIFICATION
                            ],
                            recipients: {
                                email: [assignment.userEmail],
                                notification: [assignment.userId]
                            },
                            variables,
                            caseId: caseEntity.id,
                            metadata: {
                                notificationType: CaseNotificationType.DEADLINE_REMINDER,
                                hoursUntilDeadline
                            }
                        });
                    }
                }
            }

            this.logger.log(`Sent deadline reminders for ${upcomingCases.length} cases`);
        } catch (error) {
            this.logger.error(`Failed to send deadline reminders: ${error.message}`, error.stack);
        }
    }

    /**
     * Determines urgency level based on case properties
     */
    private determineUrgency(caseEntity: any): CaseUrgencyLevel {
        if (
            caseEntity.priority === CasePriority.URGENT ||
            caseEntity.priority === CasePriority.HIGH
        ) {
            return CaseUrgencyLevel.HIGH;
        }

        if (caseEntity.deadline) {
            const hoursUntilDeadline =
                (new Date(caseEntity.deadline).getTime() - Date.now()) / (1000 * 60 * 60);
            if (hoursUntilDeadline < 0) return CaseUrgencyLevel.CRITICAL;
            if (hoursUntilDeadline <= 24) return CaseUrgencyLevel.HIGH;
            if (hoursUntilDeadline <= 72) return CaseUrgencyLevel.NORMAL;
        }

        return CaseUrgencyLevel.LOW;
    }
}
