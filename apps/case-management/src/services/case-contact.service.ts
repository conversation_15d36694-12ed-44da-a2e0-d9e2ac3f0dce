import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CaseContactRepository } from '../repositories/case-contact.repository';
import { CaseRepository } from '../repositories/case.repository';
import { CaseContact, ContactType } from '@app/common/typeorm/entities/tenant/case-contact.entity';
import { CreateContactDto } from '../dto/create-contact.dto';
import { UpdateContactDto } from '../dto/update-contact.dto';
import { CaseAuditService } from './case-audit.service';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';
import { Request } from 'express';

@Injectable()
export class CaseContactService {
    private readonly logger = new Logger(CaseContactService.name);

    constructor(
        private readonly caseContactRepository: CaseContactRepository,
        private readonly caseRepository: CaseRepository,
        private readonly caseAuditService: CaseAuditService
    ) {}

    /**
     * Creates a new contact for a case
     * @param caseId The case ID
     * @param createContactDto The contact data
     * @param createdBy The user ID who created the contact
     * @param createdByName The name of the user who created the contact
     * @param request The HTTP request
     * @returns The created contact
     */
    async createContact(
        caseId: string,
        createContactDto: CreateContactDto,
        createdBy: string,
        createdByName: string,
        request: Request
    ): Promise<CaseContact> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Create contact
        const contact = await this.caseContactRepository.create({
            caseId,
            name: createContactDto.name,
            email: createContactDto.email,
            phone: createContactDto.phone,
            address: createContactDto.address,
            type: createContactDto.type || ContactType.OTHER,
            additionalInfo: createContactDto.additionalInfo,
            createdBy,
            createdByName
        });

        const savedContact = await this.caseContactRepository.save(contact);

        // Log contact creation
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.CONTACT_ADDED,
            createdBy,
            createdByName,
            this.getIpAddress(request),
            {
                contactId: savedContact.id,
                contactName: savedContact.name,
                contactType: savedContact.type
            }
        );

        return savedContact;
    }

    /**
     * Updates a contact
     * @param caseId The case ID
     * @param contactId The contact ID
     * @param updateContactDto The updated contact data
     * @param updatedBy The user ID who updated the contact
     * @param updatedByName The name of the user who updated the contact
     * @param request The HTTP request
     * @returns The updated contact
     */
    async updateContact(
        caseId: string,
        contactId: string,
        updateContactDto: UpdateContactDto,
        updatedBy: string,
        updatedByName: string,
        request: Request
    ): Promise<CaseContact> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Verify contact exists and belongs to the case
        const contact = await this.caseContactRepository.findOne({
            where: { id: contactId, caseId }
        });

        if (!contact) {
            throw new NotFoundException(
                `Contact with ID ${contactId} not found for case ${caseId}`
            );
        }

        // Store old values for audit
        const oldValues = { ...contact };

        // Update contact
        Object.assign(contact, {
            ...updateContactDto,
            updatedAt: new Date()
        });

        const updatedContact = await this.caseContactRepository.save(contact);

        // Log contact update
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.CONTACT_UPDATED,
            updatedBy,
            updatedByName,
            this.getIpAddress(request),
            {
                contactId: updatedContact.id,
                contactName: updatedContact.name,
                oldValues,
                newValues: updatedContact
            }
        );

        return updatedContact;
    }

    /**
     * Deletes a contact
     * @param caseId The case ID
     * @param contactId The contact ID
     * @param deletedBy The user ID who deleted the contact
     * @param deletedByName The name of the user who deleted the contact
     * @param request The HTTP request
     * @returns void
     */
    async deleteContact(
        caseId: string,
        contactId: string,
        deletedBy: string,
        deletedByName: string,
        request: Request
    ): Promise<void> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Verify contact exists and belongs to the case
        const contact = await this.caseContactRepository.findOne({
            where: { id: contactId, caseId }
        });

        if (!contact) {
            throw new NotFoundException(
                `Contact with ID ${contactId} not found for case ${caseId}`
            );
        }

        // Delete contact
        await this.caseContactRepository.deleteContact(contactId);

        // Log contact deletion
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.CONTACT_DELETED,
            deletedBy,
            deletedByName,
            this.getIpAddress(request),
            {
                contactId,
                contactName: contact.name,
                contactType: contact.type
            }
        );
    }

    /**
     * Gets all contacts for a case
     * @param caseId The case ID
     * @returns Array of contacts for the case
     */
    async getCaseContacts(caseId: string): Promise<CaseContact[]> {
        return this.caseContactRepository.findByCaseId(caseId);
    }

    /**
     * Gets a contact by ID
     * @param caseId The case ID
     * @param contactId The contact ID
     * @returns The contact
     */
    async getContactById(caseId: string, contactId: string): Promise<CaseContact> {
        const contact = await this.caseContactRepository.findOne({
            where: { id: contactId, caseId }
        });

        if (!contact) {
            throw new NotFoundException(
                `Contact with ID ${contactId} not found for case ${caseId}`
            );
        }

        return contact;
    }

    /**
     * Gets contacts by type
     * @param caseId The case ID
     * @param type The contact type
     * @returns Array of contacts with the specified type
     */
    async getContactsByType(caseId: string, type: ContactType): Promise<CaseContact[]> {
        return this.caseContactRepository.findByCaseIdAndType(caseId, type);
    }

    /**
     * Extracts the IP address from the request
     * @param request The HTTP request
     * @returns The IP address
     */
    private getIpAddress(request: Request): string {
        return (
            request.headers['x-forwarded-for'] ||
            request.socket.remoteAddress ||
            'unknown'
        ).toString();
    }
}
