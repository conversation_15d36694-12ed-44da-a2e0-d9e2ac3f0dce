import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CaseAssignmentRepository } from '../repositories/case-assignment.repository';
import { CaseRepository } from '../repositories/case.repository';
import { CaseAssignment } from '@app/common/typeorm/entities';
import { AssignCaseDto } from '../dto/assign-case.dto';
import { CaseAuditService } from './case-audit.service';
import { CaseNotificationService } from './case-notification.service';
import { Request } from 'express';
import { ICaseAssignmentChecker } from '@app/common/interfaces/case-assignment-checker.interface';

@Injectable()
export class CaseAssignmentService implements ICaseAssignmentChecker {
    private readonly logger = new Logger(CaseAssignmentService.name);

    constructor(
        private readonly caseAssignmentRepository: CaseAssignmentRepository,
        private readonly caseRepository: CaseRepository,
        private readonly caseAuditService: CaseAuditService,
        private readonly caseNotificationService: CaseNotificationService
    ) {}

    /**
     * Assigns a case to a user
     */
    async assignCase(
        caseId: string,
        assignCaseDto: AssignCaseDto,
        assignedBy: string,
        assignedByName: string,
        request: Request
    ): Promise<CaseAssignment> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Check if user is already assigned to this case
        const existingAssignment = await this.caseAssignmentRepository.findActiveAssignment(
            caseId,
            assignCaseDto.userId
        );

        if (existingAssignment) {
            // User is already assigned, just return the existing assignment
            return existingAssignment;
        }

        // Create new assignment
        const assignment = await this.caseAssignmentRepository.create({
            caseId,
            userId: assignCaseDto.userId,
            userName: assignCaseDto.userName,
            assignedBy,
            notes: assignCaseDto.notes,
            isActive: true
        });

        const savedAssignment = await this.caseAssignmentRepository.save(assignment);

        // Log assignment
        await this.caseAuditService.logAssignment(
            caseId,
            assignedBy,
            assignedByName,
            request,
            assignCaseDto.userId,
            assignCaseDto.userName || 'Unknown'
        );

        // Send assignment notification
        if (assignCaseDto.userEmail) {
            await this.caseNotificationService.sendCaseAssignmentNotification(
                caseEntity,
                assignCaseDto.userId,
                assignCaseDto.userEmail,
                assignedByName
            );
        }

        return savedAssignment;
    }

    /**
     * Unassigns a user from a case
     */
    async unassignCase(
        caseId: string,
        userId: string,
        unassignedBy: string,
        unassignedByName: string,
        request: Request
    ): Promise<void> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Find active assignment
        const assignment = await this.caseAssignmentRepository.findActiveAssignment(caseId, userId);

        if (!assignment) {
            throw new NotFoundException(`User ${userId} is not assigned to case ${caseId}`);
        }

        // Deactivate assignment
        assignment.isActive = false;
        await this.caseAssignmentRepository.save(assignment);

        // Log unassignment
        await this.caseAuditService.logUnassignment(
            caseId,
            unassignedBy,
            unassignedByName,
            request,
            userId,
            assignment.userName || 'Unknown'
        );
    }

    /**
     * Gets all active assignments for a case
     */
    async getCaseAssignments(caseId: string): Promise<CaseAssignment[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseAssignmentRepository.findByCaseId(caseId);
    }

    /**
     * Reassigns a case from one user to another
     */
    async reassignCase(
        caseId: string,
        oldUserId: string,
        assignCaseDto: AssignCaseDto,
        reassignedBy: string,
        reassignedByName: string,
        request: Request
    ): Promise<CaseAssignment> {
        // Unassign old user
        await this.unassignCase(caseId, oldUserId, reassignedBy, reassignedByName, request);

        // Assign new user
        return this.assignCase(caseId, assignCaseDto, reassignedBy, reassignedByName, request);
    }

    /**
     * Checks if a user is assigned to a specific case
     * This method is used by guards to enforce case access restrictions
     */
    async isUserAssignedToCase(caseId: string, userId: string): Promise<boolean> {
        try {
            const assignment = await this.caseAssignmentRepository.findActiveAssignment(
                caseId,
                userId
            );
            return !!assignment;
        } catch (error) {
            this.logger.error(
                `Error checking case assignment for user ${userId} and case ${caseId}:`,
                error
            );
            return false; // Fail closed - deny access if we can't verify assignment
        }
    }

    /**
     * Gets all cases assigned to a specific user
     * This method can be used to get a user's assigned cases
     */
    async getUserAssignedCases(userId: string): Promise<CaseAssignment[]> {
        try {
            return await this.caseAssignmentRepository.find({
                where: {
                    userId,
                    isActive: true
                },
                relations: ['case'],
                order: {
                    assignedAt: 'DESC'
                }
            });
        } catch (error) {
            this.logger.error(`Error getting assigned cases for user ${userId}:`, error);
            return [];
        }
    }
}
