import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CasePaymentRepository } from '../repositories/case-payment.repository';
import { CaseRepository } from '../repositories/case.repository';
import { CreateCasePaymentDto } from '../dto/create-case-payment.dto';
import { CaseAuditService } from './case-audit.service';
import { Request } from 'express';
import { CasePayment } from '@app/common/typeorm/entities';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';

@Injectable()
export class CasePaymentService {
    private readonly logger = new Logger(CasePaymentService.name);

    constructor(
        private readonly casePaymentRepository: CasePaymentRepository,
        private readonly caseRepository: CaseRepository,
        private readonly caseAuditService: CaseAuditService
    ) {}

    /**
     * Creates a new payment for a case
     * Only allows adding payments - no editing or deletion
     */
    async createPayment(
        caseId: string,
        createPaymentDto: CreateCasePaymentDto,
        createdBy: string,
        createdByName: string,
        request: Request
    ): Promise<CasePayment> {
        this.logger.log(`Creating payment for case ${caseId} by user ${createdBy}`);

        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Create payment
        const payment = await this.casePaymentRepository.create({
            caseId,
            paymentDate: new Date(createPaymentDto.paymentDate),
            referenceNumber: createPaymentDto.referenceNumber,
            method: createPaymentDto.method,
            description: createPaymentDto.description,
            amount: createPaymentDto.amount,
            createdBy,
            createdAt: new Date()
        });

        const savedPayment = await this.casePaymentRepository.save(payment);

        this.logger.log(`Payment created successfully: ${savedPayment.id} for case ${caseId}`);

        // Log payment creation with audit tracking
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.PAYMENT_ADDED,
            createdBy,
            createdByName,
            this.getIpAddress(request),
            {
                paymentId: savedPayment.id,
                amount: savedPayment.amount,
                method: savedPayment.method,
                referenceNumber: savedPayment.referenceNumber,
                paymentDate: savedPayment.paymentDate.toISOString()
            }
        );

        return savedPayment;
    }

    /**
     * Gets all payments for a case
     */
    async getCasePayments(caseId: string): Promise<CasePayment[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.casePaymentRepository.findByCaseId(caseId);
    }

    /**
     * Gets a specific payment by ID
     */
    async getPaymentById(paymentId: string, caseId: string): Promise<CasePayment> {
        const payment = await this.casePaymentRepository.findByIdAndCaseId(paymentId, caseId);

        if (!payment) {
            throw new NotFoundException(
                `Payment with ID ${paymentId} not found for case ${caseId}`
            );
        }

        return payment;
    }
    /**
     * Extracts the IP address from the request
     */
    private getIpAddress(request: Request): string {
        return (
            (request.headers['x-forwarded-for'] as string) ||
            request.socket.remoteAddress ||
            'unknown'
        );
    }
}
