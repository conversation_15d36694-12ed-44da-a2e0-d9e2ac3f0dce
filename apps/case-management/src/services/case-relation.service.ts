import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CaseRelationRepository } from '../repositories/case-relation.repository';
import { CaseRepository } from '../repositories/case.repository';
import {
    CaseRelation,
    RelationType
} from '@app/common/typeorm/entities/tenant/case-relation.entity';
import { CreateCaseRelationDto } from '../dto/create-case-relation.dto';
import { CaseAuditService } from './case-audit.service';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';
import { Request } from 'express';

@Injectable()
export class CaseRelationService {
    private readonly logger = new Logger(CaseRelationService.name);

    constructor(
        private readonly caseRelationRepository: CaseRelationRepository,
        private readonly caseRepository: CaseRepository,
        private readonly caseAuditService: CaseAuditService
    ) {}

    /**
     * Creates a new relation between two cases
     * @param caseId The case ID
     * @param createCaseRelationDto The relation data
     * @param createdBy The user ID who created the relation
     * @param createdByName The name of the user who created the relation
     * @param request The HTTP request
     * @returns The created relation
     */
    async createRelation(
        caseId: string,
        createCaseRelationDto: CreateCaseRelationDto,
        createdBy: string,
        createdByName: string,
        request: Request
    ): Promise<CaseRelation> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Verify related case exists
        const relatedCaseEntity = await this.caseRepository.findOne({
            where: { id: createCaseRelationDto.relatedCaseId }
        });

        if (!relatedCaseEntity) {
            throw new NotFoundException(
                `Related case with ID ${createCaseRelationDto.relatedCaseId} not found`
            );
        }

        // Prevent self-relations
        if (caseId === createCaseRelationDto.relatedCaseId) {
            throw new BadRequestException('Cannot relate a case to itself');
        }

        // Check if relation already exists
        const relationExists = await this.caseRelationRepository.relationExists(
            caseId,
            createCaseRelationDto.relatedCaseId
        );

        if (relationExists) {
            throw new BadRequestException('Relation already exists between these cases');
        }

        // Create relation
        const relation = await this.caseRelationRepository.create({
            caseId,
            relatedCaseId: createCaseRelationDto.relatedCaseId,
            type: createCaseRelationDto.type || RelationType.RELATED,
            notes: createCaseRelationDto.notes,
            createdBy,
            createdByName
        });

        const savedRelation = await this.caseRelationRepository.save(relation);

        // Log relation creation
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.CASE_RELATION_ADDED,
            createdBy,
            createdByName,
            this.getIpAddress(request),
            {
                relatedCaseId: savedRelation.relatedCaseId,
                relationType: savedRelation.type
            }
        );

        return savedRelation;
    }

    /**
     * Deletes a relation
     * @param caseId The case ID
     * @param relationId The relation ID
     * @param deletedBy The user ID who deleted the relation
     * @param deletedByName The name of the user who deleted the relation
     * @param request The HTTP request
     * @returns void
     */
    async deleteRelation(
        caseId: string,
        relationId: string,
        deletedBy: string,
        deletedByName: string,
        request: Request
    ): Promise<void> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Verify relation exists and belongs to the case
        const relation = await this.caseRelationRepository.findOne({
            where: [
                { id: relationId, caseId },
                { id: relationId, relatedCaseId: caseId }
            ]
        });

        if (!relation) {
            throw new NotFoundException(
                `Relation with ID ${relationId} not found for case ${caseId}`
            );
        }

        // Delete relation
        await this.caseRelationRepository.deleteRelation(relationId);

        // Log relation deletion
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.CASE_RELATION_DELETED,
            deletedBy,
            deletedByName,
            this.getIpAddress(request),
            {
                relationId,
                relatedCaseId: relation.relatedCaseId,
                relationType: relation.type
            }
        );
    }

    /**
     * Gets all relations for a case (both directions)
     * @param caseId The case ID
     * @returns Array of relations for the case (both directions)
     */
    async getCaseRelations(caseId: string): Promise<any[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Get all relations
        const relations = await this.caseRelationRepository.findAllRelations(caseId);

        // Format the relations for the response
        return Promise.all(
            relations.map(async (relation) => {
                const isSource = relation.caseId === caseId;
                const otherCaseId = isSource ? relation.relatedCaseId : relation.caseId;

                // Get the other case details
                const otherCase = await this.caseRepository.findOne({
                    where: { id: otherCaseId }
                });

                if (!otherCase) {
                    return null; // Skip if the other case doesn't exist
                }

                return {
                    id: relation.id,
                    caseId: otherCaseId,
                    title: otherCase.title,
                    caseNumber: otherCase.caseNumber,
                    type: relation.type,
                    direction: isSource ? 'outgoing' : 'incoming',
                    createdAt: relation.createdAt,
                    notes: relation.notes
                };
            })
        ).then((results) => results.filter(Boolean)); // Filter out null values
    }

    /**
     * Extracts the IP address from the request
     * @param request The HTTP request
     * @returns The IP address
     */
    private getIpAddress(request: Request): string {
        return (
            request.headers['x-forwarded-for'] ||
            request.socket.remoteAddress ||
            'unknown'
        ).toString();
    }
}
