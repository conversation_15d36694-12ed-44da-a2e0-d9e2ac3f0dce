import { Injectable, Logger } from '@nestjs/common';
import { EnhancedMilestoneService } from 'apps/task-management/src/services/enhanced-milestone.service';
import { CaseType } from '@app/common/typeorm/entities/tenant/case.entity';

/**
 * Conveyancing-specific milestone service
 * Handles conveyancing case milestone operations
 * Note: Default milestone templates are seeded during tenant creation,
 * this service creates actual case milestones from those templates
 */
@Injectable()
export class ConveyancingMilestoneService {
    private readonly logger = new Logger(ConveyancingMilestoneService.name);

    constructor(private readonly enhancedMilestoneService: EnhancedMilestoneService) {}

    /**
     * Create case-specific milestones from seeded templates for a new conveyancing case
     * Uses the milestone templates that were seeded during tenant creation
     */
    async createDefaultMilestonesForCase(caseId: string, createdBy: string): Promise<void> {
        this.logger.log(`Creating default conveyancing milestones for case ${caseId}`);

        await this.enhancedMilestoneService.createDefaultMilestonesForCase(
            caseId,
            CaseType.CONVEYANCING,
            createdBy
        );

        this.logger.log(`Successfully created default milestones for conveyancing case ${caseId}`);
    }

    /**
     * Get milestones with progress tracking for a case
     * Delegates to EnhancedMilestoneService for implementation
     */
    async getMilestonesWithProgress(caseId: string): Promise<any[]> {
        return await this.enhancedMilestoneService.getMilestonesWithProgress(caseId);
    }

    /**
     * Update milestone progress when a task status changes
     * Delegates to EnhancedMilestoneService for implementation
     */
    async updateMilestoneProgress(milestoneId: string): Promise<void> {
        await this.enhancedMilestoneService.updateMilestoneProgress(milestoneId);
    }

    /**
     * Get case statistics including milestone progress
     * Delegates to EnhancedMilestoneService for implementation
     */
    async getCaseStatistics(caseId: string): Promise<any> {
        return await this.enhancedMilestoneService.getCaseStatistics(caseId);
    }

    /**
     * Add a custom task to an existing milestone
     * Delegates to EnhancedMilestoneService for implementation
     */
    async addTaskToMilestone(
        milestoneId: string,
        taskData: any,
        userId: string,
        userName: string
    ): Promise<any> {
        return await this.enhancedMilestoneService.addTaskToMilestone(
            milestoneId,
            taskData,
            userId,
            userName
        );
    }
}
