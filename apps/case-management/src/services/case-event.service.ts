import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CaseEventRepository } from '../repositories/case-event.repository';
import { CaseRepository } from '../repositories/case.repository';
import {
    CaseEvent,
    CaseEventCategory,
    CaseEventType
} from '@app/common/typeorm/entities/tenant/case-event.entity';
import { CreateCaseEventDto } from '../dto/create-case-event.dto';
import { UpdateCaseEventDto } from '../dto/update-case-event.dto';
import { CaseEventFilterDto } from '../dto/case-event-filter.dto';
import { CaseAuditService } from './case-audit.service';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';
import { PaginationService } from './pagination.service';
import { Request } from 'express';

@Injectable()
export class CaseEventService {
    private readonly logger = new Logger(CaseEventService.name);

    constructor(
        private readonly caseEventRepository: CaseEventRepository,
        private readonly caseRepository: CaseRepository,
        private readonly caseAuditService: CaseAuditService,
        private readonly paginationService: PaginationService
    ) {}

    /**
     * Creates a new event for a case
     * @param caseId The case ID
     * @param createCaseEventDto The event data
     * @param createdBy The user ID who created the event
     * @param createdByName The name of the user who created the event
     * @param request The HTTP request
     * @returns The created event
     */
    async createEvent(
        caseId: string,
        createCaseEventDto: CreateCaseEventDto,
        createdBy: string,
        createdByName: string,
        request: Request
    ): Promise<CaseEvent> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Create event
        const event = await this.caseEventRepository.create({
            caseId,
            category: createCaseEventDto.category || CaseEventCategory.OTHER,
            type: createCaseEventDto.type,
            title: createCaseEventDto.title,
            description: createCaseEventDto.description,
            eventDate: createCaseEventDto.eventDate,
            metadata: createCaseEventDto.metadata,
            createdBy,
            createdByName
        });

        const savedEvent = await this.caseEventRepository.save(event);

        // Log event creation in audit trail
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.CREATED,
            createdBy,
            createdByName,
            this.getIpAddress(request),
            {
                entityType: 'CaseEvent',
                entityId: savedEvent.id,
                eventType: savedEvent.type,
                eventTitle: savedEvent.title
            }
        );

        return savedEvent;
    }

    /**
     * Updates an event
     * @param caseId The case ID
     * @param eventId The event ID
     * @param updateCaseEventDto The updated event data
     * @param updatedBy The user ID who updated the event
     * @param updatedByName The name of the user who updated the event
     * @param request The HTTP request
     * @returns The updated event
     */
    async updateEvent(
        caseId: string,
        eventId: string,
        updateCaseEventDto: UpdateCaseEventDto,
        updatedBy: string,
        updatedByName: string,
        request: Request
    ): Promise<CaseEvent> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Verify event exists and belongs to the case
        const event = await this.caseEventRepository.findOne({
            where: { id: eventId, caseId }
        });

        if (!event) {
            throw new NotFoundException(`Event with ID ${eventId} not found for case ${caseId}`);
        }

        // Store old values for audit
        const oldValues = { ...event };

        // Update event
        Object.assign(event, {
            ...updateCaseEventDto,
            updatedAt: new Date()
        });

        const updatedEvent = await this.caseEventRepository.save(event);

        // Log event update in audit trail
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.UPDATED,
            updatedBy,
            updatedByName,
            this.getIpAddress(request),
            {
                entityType: 'CaseEvent',
                entityId: updatedEvent.id,
                oldValues,
                newValues: updatedEvent
            }
        );

        return updatedEvent;
    }

    /**
     * Deletes an event
     * @param caseId The case ID
     * @param eventId The event ID
     * @param deletedBy The user ID who deleted the event
     * @param deletedByName The name of the user who deleted the event
     * @param request The HTTP request
     * @returns void
     */
    async deleteEvent(
        caseId: string,
        eventId: string,
        deletedBy: string,
        deletedByName: string,
        request: Request
    ): Promise<void> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Verify event exists and belongs to the case
        const event = await this.caseEventRepository.findOne({
            where: { id: eventId, caseId }
        });

        if (!event) {
            throw new NotFoundException(`Event with ID ${eventId} not found for case ${caseId}`);
        }

        // Delete event
        await this.caseEventRepository.deleteEvent(eventId);

        // Log event deletion in audit trail
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.UPDATED, // Using UPDATED since there's no specific DELETED action
            deletedBy,
            deletedByName,
            this.getIpAddress(request),
            {
                action: 'deleted',
                entityType: 'CaseEvent',
                entityId: eventId,
                eventType: event.type,
                eventTitle: event.title
            }
        );
    }

    /**
     * Gets all events for a case
     * @param caseId The case ID
     * @returns Array of events for the case
     */
    async getCaseEvents(caseId: string): Promise<CaseEvent[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseEventRepository.findByCaseId(caseId);
    }

    /**
     * Gets events for a case with filtering
     * @param caseId The case ID
     * @param filterDto The filter criteria
     * @returns Paginated array of events matching the filter criteria
     */
    async getFilteredCaseEvents(caseId: string, filterDto: CaseEventFilterDto): Promise<any> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        const [events, total] = await this.caseEventRepository.findWithFilters(caseId, filterDto);

        return this.paginationService.createPaginatedResponse(
            events,
            total,
            filterDto.page || 1,
            filterDto.limit || 10
        );
    }

    /**
     * Gets an event by ID
     * @param caseId The case ID
     * @param eventId The event ID
     * @returns The event
     */
    async getEventById(caseId: string, eventId: string): Promise<CaseEvent> {
        const event = await this.caseEventRepository.findOne({
            where: { id: eventId, caseId }
        });

        if (!event) {
            throw new NotFoundException(`Event with ID ${eventId} not found for case ${caseId}`);
        }

        return event;
    }

    /**
     * Gets events by category
     * @param caseId The case ID
     * @param category The event category
     * @returns Array of events with the specified category
     */
    async getEventsByCategory(caseId: string, category: CaseEventCategory): Promise<CaseEvent[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseEventRepository.findByCaseIdAndCategory(caseId, category);
    }

    /**
     * Gets events by type
     * @param caseId The case ID
     * @param type The event type
     * @returns Array of events with the specified type
     */
    async getEventsByType(caseId: string, type: CaseEventType): Promise<CaseEvent[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseEventRepository.findByCaseIdAndType(caseId, type);
    }

    /**
     * Extracts the IP address from the request
     * @param request The HTTP request
     * @returns The IP address
     */
    private getIpAddress(request: Request): string {
        return (
            request.headers['x-forwarded-for'] ||
            request.socket.remoteAddress ||
            'unknown'
        ).toString();
    }
}
