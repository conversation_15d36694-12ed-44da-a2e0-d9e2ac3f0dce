import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ClientRepository } from '../repositories/client.repository';
import { CreateClientDto } from '../dto/create-client.dto';
import { UpdateClientDto } from '../dto/update-client.dto';
import { PaginatedResponse, PaginationService } from './pagination.service';
import { Client } from '@app/common/typeorm/entities/tenant/client.entity';

@Injectable()
export class ClientService {
    private readonly logger = new Logger(ClientService.name);

    constructor(
        private readonly clientRepository: ClientRepository,
        private readonly paginationService: PaginationService
    ) {}

    /**
     * Creates a new client
     */
    async createClient(createClientDto: CreateClientDto, userId: string): Promise<Client> {
        // Check if client with same email already exists
        if (createClientDto.email) {
            const existingClient = await this.clientRepository.findByEmail(createClientDto.email);

            if (existingClient) {
                throw new BadRequestException(
                    `Client with email ${createClientDto.email} already exists`
                );
            }
        }

        // Create client
        const newClient = await this.clientRepository.create({
            ...createClientDto,
            createdBy: userId
        });

        return this.clientRepository.save(newClient);
    }

    /**
     * Finds a client by ID
     */
    async findClientById(id: string): Promise<Client> {
        const client = await this.clientRepository.findOne({
            where: { id }
        });

        if (!client) {
            throw new NotFoundException(`Client with ID ${id} not found`);
        }

        return client;
    }

    /**
     * Finds clients with pagination and search
     */
    async findClients(
        page: number = 1,
        limit: number = 10,
        search?: string
    ): Promise<PaginatedResponse<Client>> {
        const [clients, total] = await this.clientRepository.findWithPagination(
            page,
            limit,
            search
        );

        return this.paginationService.createPaginatedResponse(clients, total, page, limit);
    }

    /**
     * Updates a client
     */
    async updateClient(id: string, updateClientDto: UpdateClientDto): Promise<Client> {
        const client = await this.findClientById(id);

        // Check if email is being changed and if it's already in use
        if (updateClientDto.email && updateClientDto.email !== client.email) {
            const existingClient = await this.clientRepository.findByEmail(updateClientDto.email);

            if (existingClient && existingClient.id !== id) {
                throw new BadRequestException(
                    `Client with email ${updateClientDto.email} already exists`
                );
            }
        }

        // Update client
        Object.assign(client, {
            ...updateClientDto,
            updatedAt: new Date()
        });

        return this.clientRepository.save(client);
    }

    /**
     * Searches for clients by name
     * @param name The search term
     * @param limit Optional limit for the number of results
     * @returns List of clients matching the search term
     */
    async searchClientsByName(name: string, limit?: number): Promise<Client[]> {
        if (!name || name.length < 2) {
            throw new BadRequestException('Search term must be at least 2 characters long');
        }

        const clients = await this.clientRepository.findByName(name);

        // Apply limit if provided
        if (limit && clients.length > limit) {
            return clients.slice(0, limit);
        }

        return clients;
    }

    /**
     * Searches for clients with their associated cases
     * @param searchTerm The search term
     * @param limit The maximum number of results to return
     * @returns List of clients with their cases
     */
    async searchClientsWithCases(searchTerm: string, limit: number = 5): Promise<any[]> {
        if (!searchTerm || searchTerm.length < 2) {
            throw new BadRequestException('Search term must be at least 2 characters long');
        }

        // Get clients matching the search term
        const clients = await this.clientRepository.findByName(searchTerm);

        // For each client, get their cases
        const clientsWithCases = await Promise.all(
            clients.slice(0, limit).map(async (client) => {
                const cases = await this.clientRepository.findClientCases(client.id);
                return {
                    ...client,
                    cases: cases.slice(0, 3) // Only include the 3 most recent cases
                };
            })
        );

        return clientsWithCases;
    }
}
