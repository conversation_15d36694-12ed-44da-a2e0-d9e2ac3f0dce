import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CaseAttachmentRepository } from '../repositories/case-attachment.repository';
import { CaseRepository } from '../repositories/case.repository';
import { CaseAttachment } from '@app/common/typeorm/entities';
import { DocumentType } from '@app/common/typeorm/entities/tenant/case-attachment.entity';
import { CreateAttachmentDto } from '../dto/create-attachment.dto';
import { CaseAuditService } from './case-audit.service';
import { Request } from 'express';

@Injectable()
export class CaseAttachmentService {
    private readonly logger = new Logger(CaseAttachmentService.name);

    constructor(
        private readonly caseAttachmentRepository: CaseAttachmentRepository,
        private readonly caseRepository: CaseRepository,
        private readonly caseAuditService: CaseAuditService
    ) {}

    /**
     * Creates a new attachment for a case
     */
    async createAttachment(
        caseId: string,
        createAttachmentDto: CreateAttachmentDto,
        uploadedBy: string,
        uploadedByName: string,
        request: Request
    ): Promise<CaseAttachment> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Create attachment
        const attachment = await this.caseAttachmentRepository.create({
            caseId,
            filename: createAttachmentDto.filename,
            url: createAttachmentDto.url,
            fileSize: createAttachmentDto.fileSize,
            mimeType: createAttachmentDto.mimeType,
            description: createAttachmentDto.description,
            documentType: createAttachmentDto.documentType || DocumentType.OTHER,
            uploadedBy,
            uploadedByName
        });

        const savedAttachment = await this.caseAttachmentRepository.save(attachment);

        // Log attachment creation
        await this.caseAuditService.logAttachmentAdded(
            caseId,
            uploadedBy,
            uploadedByName,
            request,
            savedAttachment.id,
            savedAttachment.filename
        );

        return savedAttachment;
    }

    /**
     * Gets all attachments for a case
     */
    async getCaseAttachments(caseId: string): Promise<CaseAttachment[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseAttachmentRepository.findByCaseId(caseId);
    }

    /**
     * Gets a specific attachment by ID
     */
    async getAttachmentById(attachmentId: string): Promise<CaseAttachment> {
        const attachment = await this.caseAttachmentRepository.findOne({
            where: { id: attachmentId }
        });

        if (!attachment) {
            throw new NotFoundException(`Attachment with ID ${attachmentId} not found`);
        }

        return attachment;
    }

    /**
     * Deletes an attachment
     */
    async deleteAttachment(
        attachmentId: string,
        deletedBy: string,
        deletedByName: string,
        request: Request
    ): Promise<void> {
        const attachment = await this.getAttachmentById(attachmentId);

        // Log attachment deletion before actually deleting it
        await this.caseAuditService.logAttachmentRemoved(
            attachment.caseId,
            deletedBy,
            deletedByName,
            request,
            attachment.id,
            attachment.filename
        );

        // Delete attachment
        await this.caseAttachmentRepository.deleteAttachment(attachmentId);
    }

    /**
     * Searches for attachments by filename
     */
    async searchAttachmentsByFilename(caseId: string, filename: string): Promise<CaseAttachment[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseAttachmentRepository.findByFilename(caseId, filename);
    }

    /**
     * Gets attachments by document type
     */
    async getAttachmentsByDocumentType(
        caseId: string,
        documentType: DocumentType
    ): Promise<CaseAttachment[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseAttachmentRepository.findByDocumentType(caseId, documentType);
    }
}
