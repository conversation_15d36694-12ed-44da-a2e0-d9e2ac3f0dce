import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { CoreCommonModule } from '@app/common/core-common.module'; // Minimal deps for Core
import { ApiResponseModule } from '@app/common/api-response';
import { ExampleController } from './example/example.controller';
import { HealthModule } from './health/health.module'; // Re-enabled with HealthAggregator fix
import { HttpModule } from '@nestjs/axios';
import { ProxyController } from './proxy/proxy.controller';
import { ProxyService } from './proxy/proxy.service';
import * as cookieParser from 'cookie-parser';

const apiResponseExcludedPaths = [
    '/api/health',
    '/api/health/db',
    '/api/health/all',
    '/health',
    '/ping',
    '/metrics'
];

@Module({
    imports: [
        CoreCommonModule, // Minimal dependencies - no TypeORM, Bull, <PERSON>is, Keycloak
        HealthModule, // Re-enabled with HealthAggregator service discovery fix
        HttpModule,
        ApiResponseModule.forRoot({
            excludePaths: apiResponseExcludedPaths
        })
    ],
    controllers: [ExampleController, ProxyController],
    providers: [ProxyService]
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        // Apply cookie-parser middleware first to parse cookies from requests
        consumer.apply(cookieParser()).forRoutes('*');
    }
}
