import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { serviceConfig } from '@app/common';
import { ValidationPipe } from '@nestjs/common';
import helmet from 'helmet';
import { WinstonModule } from 'nest-winston';
import { instance } from '@app/common/utils/logger.util';
import { Logger } from '@nestjs/common';

async function bootstrap() {
    const logger = new Logger('Bootstrap');

    try {
        logger.log('Starting Core application...');
        logger.log(`Port configured: ${serviceConfig().core.port}`);

        logger.log('Creating NestJS application...');
        const app = await NestFactory.create(AppModule, {
            logger: WinstonModule.createLogger({
                instance: instance
            })
        });
        logger.log('App instance created successfully');

        // app.setGlobalPrefix(appConfig().apiGlobalPrefix);
        logger.log('Configuring Helmet...');
        app.use(helmet());
        logger.log('Helmet configured');

        logger.log('Configuring CORS...');
        app.enableCors({
            origin: (origin, callback) => {
                // Allow requests with no origin (like mobile apps or curl requests)
                if (!origin) return callback(null, true);

                // Allow localhost for development
                if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
                    return callback(null, true);
                }

                // Allow Vercel deployments
                if (origin.includes('vercel.app')) {
                    return callback(null, true);
                }

                // Allow your API domain
                if (origin.includes('api.tklpm.com') || origin.includes('tklpm.com')) {
                    return callback(null, true);
                }

                // Allow any HTTPS origin (for production flexibility)
                if (origin.startsWith('https://')) {
                    return callback(null, true);
                }

                // Block HTTP origins (except localhost)
                callback(new Error('Not allowed by CORS'));
            },
            methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
            credentials: true
        });
        logger.log('CORS configured');

        // Set up global validation pipe
        logger.log('Configuring validation pipe...');
        app.useGlobalPipes(
            new ValidationPipe({
                transform: true,
                forbidNonWhitelisted: true,
                whitelist: true,
                stopAtFirstError: true,
                transformOptions: {
                    enableImplicitConversion: true
                }
            })
        );
        logger.log('Validation pipe configured');

        // Start the application
        logger.log('Starting HTTP server...');
        await app.listen(serviceConfig().core.port);
        logger.log(`✅✅✅ LISTENING ON PORT ${serviceConfig().core.port}!`);

        Logger.log(`Core application running on ${await app.getUrl()}`);
        logger.log('Core application fully started!');
    } catch (error) {
        logger.error('FATAL ERROR DURING BOOTSTRAP:', error);
        logger.error(`Error message: ${error.message}`);
        logger.error(`Error stack: ${error.stack}`);
        logger.error(`Error name: ${error.name}`);
        process.exit(1);
    }
}

bootstrap().catch((err) => {
    const logger = new Logger('Main');
    logger.error('Unhandled bootstrap error:', err);
    process.exit(1);
});
// Rebuild trigger: HealthAggregator fix in CommonModule - 20251014-022856
