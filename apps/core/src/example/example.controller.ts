import { Controller, Get, Post, Body, Param, Delete, Put, Query } from '@nestjs/common';
import { ApiResponseUtil, BadRequestException, NotFoundException } from '@app/common/api-response';

/**
 * Example DTO for creating a user
 */
class CreateUserDto {
    name: string;
    email: string;
    age: number;
}

/**
 * Example controller that demonstrates the standardized response format
 */
@Controller('users')
export class ExampleController {
    private users = [
        { id: 1, name: '<PERSON>', email: '<EMAIL>', age: 30 },
        { id: 2, name: '<PERSON>', email: '<EMAIL>', age: 25 }
    ];

    /**
     * Get all users
     */
    @Get()
    findAll(@Query('page') page = 1, @Query('limit') limit = 10) {
        // Calculate pagination
        const startIndex = (page - 1) * limit;
        const endIndex = page * limit;
        const results = this.users.slice(startIndex, endIndex);

        // Return paginated results with metadata
        return ApiResponseUtil.ok(results, 'Users retrieved successfully', {
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total: this.users.length,
                pages: Math.ceil(this.users.length / limit)
            }
        });
    }

    /**
     * Get a user by ID
     */
    @Get(':id')
    findOne(@Param('id') id: string) {
        const userId = Number(id);
        const user = this.users.find((u) => u.id === userId);

        if (!user) {
            throw new NotFoundException(`User with ID ${id} not found`);
        }

        return ApiResponseUtil.ok(user, 'User retrieved successfully');
    }

    /**
     * Create a new user
     */
    @Post()
    create(@Body() createUserDto: CreateUserDto) {
        // Validate input
        if (!createUserDto.name || !createUserDto.email) {
            throw new BadRequestException('Name and email are required');
        }

        // Create new user
        const newUser = {
            id: this.users.length + 1,
            ...createUserDto
        };

        this.users.push(newUser);

        return ApiResponseUtil.created(newUser, 'User created successfully');
    }

    /**
     * Update a user
     */
    @Put(':id')
    update(@Param('id') id: string, @Body() updateUserDto: Partial<CreateUserDto>) {
        const userId = Number(id);
        const userIndex = this.users.findIndex((u) => u.id === userId);

        if (userIndex === -1) {
            throw new NotFoundException(`User with ID ${id} not found`);
        }

        // Update user
        const updatedUser = {
            ...this.users[userIndex],
            ...updateUserDto
        };

        this.users[userIndex] = updatedUser;

        return ApiResponseUtil.ok(updatedUser, 'User updated successfully');
    }

    /**
     * Delete a user
     */
    @Delete(':id')
    remove(@Param('id') id: string) {
        const userId = Number(id);
        const userIndex = this.users.findIndex((u) => u.id === userId);

        if (userIndex === -1) {
            throw new NotFoundException(`User with ID ${id} not found`);
        }

        // Remove user
        this.users.splice(userIndex, 1);

        return ApiResponseUtil.ok(null, 'User deleted successfully');
    }

    /**
     * Example of a 500 error
     */
    @Get('test/server-error')
    testServerError() {
        throw new Error('This is a test server error');
    }

    /**
     * Example of a custom error
     */
    @Get('test/custom-error')
    testCustomError() {
        throw new BadRequestException('This is a custom bad request error', 'VALIDATION_ERROR', {
            fields: {
                email: 'Invalid email format',
                password: 'Password too short'
            }
        });
    }
}
