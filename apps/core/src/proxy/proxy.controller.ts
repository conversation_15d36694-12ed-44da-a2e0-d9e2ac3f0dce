import { <PERSON>, All, Req, Res, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { ProxyService } from './proxy.service';

@Controller()
export class ProxyController {
    private readonly logger = new Logger(ProxyController.name);

    constructor(private readonly proxyService: ProxyService) {}

    @All('api/:service/*')
    async proxyToService(@Req() req: Request, @Res() res: Response): Promise<void> {
        const fullPath = req.path;
        const method = req.method;
        const body = req.body;
        const headers = req.headers;
        const query = req.query;

        this.logger.debug(`Proxying ${method} ${fullPath}`);

        try {
            // Extract service and remaining path
            const serviceInfo = this.proxyService.getServiceFromPath(fullPath);

            if (!serviceInfo) {
                throw new HttpException('Service not found in path', HttpStatus.NOT_FOUND);
            }

            const { serviceName, remainingPath } = serviceInfo;

            // Build the target path using the service's configured prefix
            const targetPath = `/api/${serviceName}${remainingPath}`;

            this.logger.debug(`Routing to service: ${serviceName}, path: ${targetPath}`);

            // Proxy the request
            const result = await this.proxyService.proxyRequest(
                serviceName,
                targetPath,
                method,
                body,
                headers,
                query
            );

            // Forward Set-Cookie and other relevant headers
            if (result.headers['set-cookie']) {
                res.setHeader('Set-Cookie', result.headers['set-cookie']);
            }
            // Forward the response body
            res.json(result.data);
        } catch (error) {
            this.logger.error(`Proxy error: ${error.message}`, error.stack);

            if (error instanceof HttpException) {
                res.status(error.getStatus()).json({
                    statusCode: error.getStatus(),
                    message: error.message,
                    error: error.name
                });
            } else {
                res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                    message: 'Internal server error',
                    error: 'InternalServerErrorException'
                });
            }
        }
    }

    @All('*')
    handleUnmatchedRoutes(@Req() req: Request, @Res() res: Response) {
        const path = req.path;

        // Skip certain paths that should be handled by other controllers
        if (
            path.includes('/health') ||
            path.includes('/ping') ||
            path.includes('/example') ||
            path.includes('/metrics')
        ) {
            return;
        }

        // Check if this is an API route that should be proxied but wasn't matched
        if (path.startsWith('/api/')) {
            this.logger.warn(`Unmatched API route: ${req.method} ${path}`);

            res.status(HttpStatus.NOT_FOUND).json({
                statusCode: HttpStatus.NOT_FOUND,
                message: `API endpoint ${req.method} ${path} not found`,
                error: 'Not Found',
                availableServices: [
                    'auth',
                    'document-engine',
                    'communication',
                    'case-management',
                    'quote-engine'
                ]
            });
            return;
        }

        this.logger.warn(`Unmatched route: ${req.method} ${path}`);

        res.status(HttpStatus.NOT_FOUND).json({
            statusCode: HttpStatus.NOT_FOUND,
            message: `Route ${req.method} ${path} not found`,
            error: 'Not Found'
        });
    }
}
