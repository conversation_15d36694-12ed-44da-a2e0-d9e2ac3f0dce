module.exports = {
  displayName: 'core',
  preset: '../../jest.preset.js',
  rootDir: '../../',
  testMatch: ['<rootDir>/apps/core/**/*.spec.ts'],
  moduleNameMapper: {
    '^@app/common(.*)$': '<rootDir>/libs/common/src$1'
  },
  setupFilesAfterEnv: ['<rootDir>/test/jest-setup.ts'],
  coverageDirectory: '<rootDir>/coverage/apps/core',
  testEnvironment: 'node',
  // Pass tests if no tests are found
  testPathIgnorePatterns: ['/node_modules/'],
  passWithNoTests: true
};
