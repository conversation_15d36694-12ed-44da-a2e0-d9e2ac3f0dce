import {
    Controller,
    Get,
    Post,
    Put,
    Delete,
    Body,
    Param,
    HttpStatus,
    HttpCode,
    UseGuards,
    HttpException,
    Logger
} from '@nestjs/common';
import { RateCardManagementService } from '../services/rate-card-management.service';
import { QuoteCalculationService } from '../services/quote-calculation.service';
import {
    CreateRateCardDto,
    UpdateRateCardDto,
    CreateFeeItemDto
} from '../services/rate-card-management.service';
import { QuoteRequest } from '../services/quote-calculation.service';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { ApiResponseUtil } from '@app/common/api-response';

@Controller('rate-cards')
@UseGuards(JwtGuard, RolesGuard, TenantGuard)
export class RateCardController {
    constructor(
        private readonly rateCardManagementService: RateCardManagementService,
        private readonly quoteCalculationService: QuoteCalculationService
    ) {}

    @Get()
    async getAllRateCards() {
        try {
            this.logRequest('getAllRateCards', {});

            const rateCards = await this.rateCardManagementService.getActiveRateCards();

            this.logResponse('getAllRateCards', { count: rateCards.length });
            return ApiResponseUtil.ok(rateCards, 'Rate cards retrieved successfully');
        } catch (error) {
            this.logError('getAllRateCards', error);
            throw new HttpException(
                `Failed to get rate cards: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('providers/available')
    async getAvailableProviders() {
        try {
            this.logRequest('getAvailableProviders', {});

            const providers = await this.rateCardManagementService.getAvailableProviders();

            this.logResponse('getAvailableProviders', { count: providers.length });
            return ApiResponseUtil.ok(providers, 'Available providers retrieved successfully');
        } catch (error) {
            this.logError('getAvailableProviders', error);
            throw new HttpException(
                `Failed to get available providers: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('stats')
    async getRateCardStats() {
        try {
            this.logRequest('getRateCardStats', {});

            const stats = await this.rateCardManagementService.getRateCardStats();

            this.logResponse('getRateCardStats', stats);
            return ApiResponseUtil.ok(stats, 'Rate card statistics retrieved successfully');
        } catch (error) {
            this.logError('getRateCardStats', error);
            throw new HttpException(
                `Failed to get rate card statistics: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':rateCardId')
    async getRateCardById(@Param('rateCardId') rateCardId: string) {
        try {
            this.logRequest('getRateCardById', { rateCardId });

            // This would need to be implemented in the service
            const rateCard = await this.rateCardManagementService.getActiveRateCards();
            const found = rateCard.find((rc) => rc.id === rateCardId);

            if (!found) {
                throw new HttpException('Rate card not found', HttpStatus.NOT_FOUND);
            }

            this.logResponse('getRateCardById', { rateCardId, found: true });
            return ApiResponseUtil.ok(found, 'Rate card retrieved successfully');
        } catch (error) {
            this.logError('getRateCardById', error);
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                `Failed to get rate card: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('provider/:providerCode')
    async getRateCardsByProvider(@Param('providerCode') providerCode: string) {
        try {
            this.logRequest('getRateCardsByProvider', { providerCode });

            const rateCards = await this.rateCardManagementService.getRateCardsByProvider(
                providerCode as any
            );

            this.logResponse('getRateCardsByProvider', { providerCode, count: rateCards.length });
            return ApiResponseUtil.ok(
                rateCards,
                `Rate cards for provider ${providerCode} retrieved successfully`
            );
        } catch (error) {
            this.logError('getRateCardsByProvider', error);
            throw new HttpException(
                `Failed to get rate cards for provider ${providerCode}: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post()
    @HttpCode(HttpStatus.CREATED)
    async createRateCard(@Body() createRateCardDto: CreateRateCardDto) {
        try {
            this.logRequest('createRateCard', {
                providerCode: createRateCardDto.providerCode,
                displayName: createRateCardDto.displayName
            });

            const result = await this.rateCardManagementService.createRateCard(createRateCardDto);

            this.logResponse('createRateCard', {
                id: result.id,
                providerCode: result.providerCode
            });
            return ApiResponseUtil.created(result, 'Rate card created successfully');
        } catch (error) {
            this.logError('createRateCard', error);
            throw new HttpException(
                `Failed to create rate card: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put(':rateCardId')
    async updateRateCard(
        @Param('rateCardId') rateCardId: string,
        @Body() updateRateCardDto: UpdateRateCardDto
    ) {
        try {
            this.logRequest('updateRateCard', {
                rateCardId,
                updates: Object.keys(updateRateCardDto)
            });

            const result = await this.rateCardManagementService.updateRateCard(
                rateCardId,
                updateRateCardDto
            );

            this.logResponse('updateRateCard', { rateCardId, updated: true });
            return ApiResponseUtil.ok(result, 'Rate card updated successfully');
        } catch (error) {
            this.logError('updateRateCard', error);
            throw new HttpException(
                `Failed to update rate card: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Delete(':rateCardId')
    @HttpCode(HttpStatus.NO_CONTENT)
    async deleteRateCard(@Param('rateCardId') rateCardId: string) {
        try {
            this.logRequest('deleteRateCard', { rateCardId });

            await this.rateCardManagementService.deleteRateCard(rateCardId);

            this.logResponse('deleteRateCard', { rateCardId, deleted: true });
            return ApiResponseUtil.ok(null, 'Rate card deleted successfully');
        } catch (error) {
            this.logError('deleteRateCard', error);
            throw new HttpException(
                `Failed to delete rate card: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put(':rateCardId/default')
    async setDefaultRateCard(@Param('rateCardId') rateCardId: string) {
        try {
            this.logRequest('setDefaultRateCard', { rateCardId });

            const result = await this.rateCardManagementService.setDefaultRateCard(rateCardId);

            this.logResponse('setDefaultRateCard', { rateCardId, setAsDefault: true });
            return ApiResponseUtil.ok(result, 'Rate card set as default successfully');
        } catch (error) {
            this.logError('setDefaultRateCard', error);
            throw new HttpException(
                `Failed to set rate card as default: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':rateCardId/fee-items')
    async getRateCardFeeItems(@Param('rateCardId') rateCardId: string) {
        try {
            this.logRequest('getRateCardFeeItems', { rateCardId });

            const feeItems = await this.rateCardManagementService.getRateCardFeeItems(rateCardId);

            this.logResponse('getRateCardFeeItems', { rateCardId, count: feeItems.length });
            return ApiResponseUtil.ok(feeItems, 'Rate card fee items retrieved successfully');
        } catch (error) {
            this.logError('getRateCardFeeItems', error);
            throw new HttpException(
                `Failed to get rate card fee items: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post(':rateCardId/fee-items')
    @HttpCode(HttpStatus.CREATED)
    async addFeeItem(
        @Param('rateCardId') rateCardId: string,
        @Body() createFeeItemDto: CreateFeeItemDto
    ) {
        try {
            this.logRequest('addFeeItem', { rateCardId, feeItem: createFeeItemDto.label });

            const result = await this.rateCardManagementService.addFeeItem(
                rateCardId,
                createFeeItemDto
            );

            this.logResponse('addFeeItem', { rateCardId, feeItemId: result.id });
            return ApiResponseUtil.created(result, 'Fee item added successfully');
        } catch (error) {
            this.logError('addFeeItem', error);
            throw new HttpException(
                `Failed to add fee item: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('fee-items/:feeItemId')
    async updateFeeItem(
        @Param('feeItemId') feeItemId: string,
        @Body() updateFeeItemDto: Partial<CreateFeeItemDto>
    ) {
        try {
            this.logRequest('updateFeeItem', { feeItemId, updates: Object.keys(updateFeeItemDto) });

            const result = await this.rateCardManagementService.updateFeeItem(
                feeItemId,
                updateFeeItemDto
            );

            this.logResponse('updateFeeItem', { feeItemId, updated: true });
            return ApiResponseUtil.ok(result, 'Fee item updated successfully');
        } catch (error) {
            this.logError('updateFeeItem', error);
            throw new HttpException(
                `Failed to update fee item: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Delete('fee-items/:feeItemId')
    @HttpCode(HttpStatus.NO_CONTENT)
    async deleteFeeItem(@Param('feeItemId') feeItemId: string) {
        try {
            this.logRequest('deleteFeeItem', { feeItemId });

            await this.rateCardManagementService.deleteFeeItem(feeItemId);

            this.logResponse('deleteFeeItem', { feeItemId, deleted: true });
            return ApiResponseUtil.ok(null, 'Fee item deleted successfully');
        } catch (error) {
            this.logError('deleteFeeItem', error);
            throw new HttpException(
                `Failed to delete fee item: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('compare')
    async compareRateCards(
        @Body()
        comparisonRequest: {
            propertyValue: number;
            transactionType: string;
            propertyConditions: Record<string, any>;
        }
    ) {
        try {
            this.logRequest('compareRateCards', {
                propertyValue: comparisonRequest.propertyValue,
                transactionType: comparisonRequest.transactionType
            });

            const result = await this.rateCardManagementService.compareRateCards(
                comparisonRequest.propertyValue,
                comparisonRequest.transactionType,
                comparisonRequest.propertyConditions
            );

            this.logResponse('compareRateCards', { comparisonCount: result.length });
            return ApiResponseUtil.ok(result, 'Rate cards compared successfully');
        } catch (error) {
            this.logError('compareRateCards', error);
            throw new HttpException(
                `Failed to compare rate cards: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post(':rateCardId/calculate')
    async calculateQuoteWithRateCard(
        @Param('rateCardId') rateCardId: string,
        @Body() quoteRequest: Omit<QuoteRequest, 'rateCardId'>
    ) {
        try {
            this.logRequest('calculateQuoteWithRateCard', {
                rateCardId,
                propertyValue: quoteRequest.propertyValue,
                transactionType: quoteRequest.transactionType
            });

            // Add the rate card ID to the request
            const fullRequest: QuoteRequest = {
                ...quoteRequest,
                rateCardId
            };

            const result = await this.quoteCalculationService.calculateQuote(fullRequest);

            this.logResponse('calculateQuoteWithRateCard', {
                rateCardId,
                grandTotal: result.grandTotal,
                reference: result.reference
            });
            return ApiResponseUtil.ok(result, 'Quote calculated successfully with rate card');
        } catch (error) {
            this.logError('calculateQuoteWithRateCard', error);
            throw new HttpException(
                `Failed to calculate quote with rate card: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    // Helper methods for logging
    private logRequest(method: string, data: any): void {
        Logger.log(`[RateCardController] ${method} request:`, JSON.stringify(data, null, 2));
    }

    private logResponse(method: string, data: any): void {
        Logger.log(`[RateCardController] ${method} response:`, JSON.stringify(data, null, 2));
    }

    private logError(method: string, error: any): void {
        Logger.error(`[RateCardController] ${method} error:`, error.message, error.stack);
    }
}
