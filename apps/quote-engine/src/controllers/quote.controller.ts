import {
    <PERSON>,
    Post,
    Get,
    Body,
    Param,
    Query,
    UseGuards,
    HttpStatus,
    HttpException,
    Put,
    Delete,
    Logger,
    Req
} from '@nestjs/common';
import { QuoteCalculationService, QuoteBreakdown } from '../services/quote-calculation.service';
import { QuotePersistenceService } from '../services/quote-persistence.service';
import { QuoteAuditService } from '../services/quote-audit.service';
import { QuoteAttachmentService } from '../services/quote-attachment.service';
import { QuoteCommunicationService } from '../services/quote-communication.service';
import { QuoteClientCallNoteService } from '../services/quote-client-call-note.service';
import { PromoCodeManagementService } from '../services/promo-code-management.service';
import { QuoteRequestDto, BulkQuoteRequestDto } from '../dto/quote-request.dto';
import {
    SaveQuoteAsLeadDto,
    ConvertQuoteToCaseDto,
    QuoteStatusUpdate<PERSON>to,
    SimpleSaveQuoteAsLeadDto,
    PaginatedQuotesQueryDto
} from '../dto/quote-persistence.dto';
import {
    CreatePromoCodeDto,
    UpdatePromoCodeDto,
    ValidatePromoCodeDto,
    PromoCodeListQueryDto
} from '../dto/promo-code.dto';
import { RateCardRepository } from '../repositories/rate-card.repository';
import { QuoteRepository } from '../repositories/quote.repository';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { ApiResponseUtil } from '@app/common/api-response';
import { RateCardFeeItemRepository } from '../repositories/rate-card-fee-item.repository';
import { QuoteStatus } from '@app/common/typeorm/entities/tenant/quote.entity';

@Controller('quotes')
@UseGuards(JwtGuard, RolesGuard, TenantGuard)
export class QuoteController {
    constructor(
        private readonly quoteCalculationService: QuoteCalculationService,
        private readonly quotePersistenceService: QuotePersistenceService,
        private readonly quoteAuditService: QuoteAuditService,
        private readonly quoteAttachmentService: QuoteAttachmentService,
        private readonly quoteCommunicationService: QuoteCommunicationService,
        private readonly quoteClientCallNoteService: QuoteClientCallNoteService,
        private readonly rateCardRepository: RateCardRepository,
        private readonly rateCardFeeItemRepository: RateCardFeeItemRepository,
        private readonly promoCodeManagementService: PromoCodeManagementService,
        private readonly quoteRepository: QuoteRepository
    ) {}

    // Promo Code Management - Must come BEFORE wildcard routes
    @Get('promo-codes')
    async getPromoCodes(@Query() query: PromoCodeListQueryDto) {
        try {
            this.logRequest('getPromoCodes', query);

            // Use paginated method if page is provided, otherwise use the old method for backward compatibility
            if (query.page !== undefined) {
                const result = await this.promoCodeManagementService.getPromoCodesPaginated(
                    query.page,
                    query.limit || 20,
                    query.status,
                    query.search
                );

                const response = {
                    promoCodes: result.promoCodes,
                    pagination: {
                        page: result.page,
                        limit: query.limit || 20,
                        total: result.total,
                        totalPages: result.totalPages,
                        hasNext: result.hasNext,
                        hasPrevious: result.hasPrevious
                    },
                    filters: {
                        status: query.status,
                        search: query.search
                    }
                };

                this.logResponse('getPromoCodes', {
                    total: result.total,
                    page: result.page,
                    totalPages: result.totalPages,
                    promoCodeCount: result.promoCodes.length
                });

                return ApiResponseUtil.ok(response, 'Paginated promo codes retrieved successfully');
            } else {
                // Backward compatibility - use old method
                const promoCodes = await this.promoCodeManagementService.getPromoCodes(
                    query.status
                );

                // Apply pagination if provided
                let paginatedResults = promoCodes;
                if (query.limit || query.offset) {
                    const offset = query.offset || 0;
                    const limit = query.limit || 20;
                    paginatedResults = promoCodes.slice(offset, offset + limit);
                }

                this.logResponse('getPromoCodes', { count: paginatedResults.length });
                return ApiResponseUtil.ok(paginatedResults, 'Promo codes retrieved successfully');
            }
        } catch (error) {
            this.logError('getPromoCodes', error);
            throw new HttpException(
                `Failed to get promo codes: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('promo-codes')
    async createPromoCode(@Body() request: CreatePromoCodeDto) {
        try {
            this.logRequest('createPromoCode', { name: request.name, code: request.code });

            // Convert string date to Date object if provided
            const createData = {
                ...request,
                expirationDate: request.expirationDate
                    ? new Date(request.expirationDate)
                    : undefined
            };

            const promoCode = await this.promoCodeManagementService.createPromoCode(createData);

            this.logResponse('createPromoCode', { id: promoCode.id, code: promoCode.code });
            return ApiResponseUtil.created(promoCode, 'Promo code created successfully');
        } catch (error) {
            this.logError('createPromoCode', error);
            throw new HttpException(
                `Failed to create promo code: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('promo-codes/:id')
    async getPromoCodeById(@Param('id') id: string) {
        try {
            this.logRequest('getPromoCodeById', { id });

            const promoCode = await this.promoCodeManagementService.getPromoCodeById(id);

            if (!promoCode) {
                throw new HttpException('Promo code not found', HttpStatus.NOT_FOUND);
            }

            this.logResponse('getPromoCodeById', { id, code: promoCode.code });
            return ApiResponseUtil.ok(promoCode, 'Promo code retrieved successfully');
        } catch (error) {
            this.logError('getPromoCodeById', error);
            throw new HttpException(
                `Failed to get promo code: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('promo-codes/:id')
    async updatePromoCode(@Param('id') id: string, @Body() request: UpdatePromoCodeDto) {
        try {
            this.logRequest('updatePromoCode', { id, updates: request });

            // Convert string date to Date object if provided
            const updateData = {
                ...request,
                expirationDate: request.expirationDate
                    ? new Date(request.expirationDate)
                    : undefined
            };

            const promoCode = await this.promoCodeManagementService.updatePromoCode(id, updateData);

            this.logResponse('updatePromoCode', { id, code: promoCode.code });
            return ApiResponseUtil.ok(promoCode, 'Promo code updated successfully');
        } catch (error) {
            this.logError('updatePromoCode', error);
            throw new HttpException(
                `Failed to update promo code: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('promo-codes/validate')
    async validatePromoCode(@Body() request: ValidatePromoCodeDto) {
        try {
            this.logRequest('validatePromoCode', { code: request.code });

            const validation = await this.promoCodeManagementService.validatePromoCode(
                request.code
            );

            this.logResponse('validatePromoCode', {
                code: request.code,
                isValid: validation.isValid,
                discount: validation.discount
            });
            return ApiResponseUtil.ok(validation, 'Promo code validation completed');
        } catch (error) {
            this.logError('validatePromoCode', error);
            throw new HttpException(
                `Failed to validate promo code: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('promo-codes/:id/usage-stats')
    async getPromoCodeUsageStats(@Param('id') id: string) {
        try {
            this.logRequest('getPromoCodeUsageStats', { id });

            const stats = await this.promoCodeManagementService.getPromoCodeUsageStats(id);

            this.logResponse('getPromoCodeUsageStats', { id, stats });
            return ApiResponseUtil.ok(stats, 'Promo code usage stats retrieved successfully');
        } catch (error) {
            this.logError('getPromoCodeUsageStats', error);
            throw new HttpException(
                `Failed to get promo code usage stats: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('promo-codes/:id/deactivate')
    async deactivatePromoCode(@Param('id') id: string) {
        try {
            this.logRequest('deactivatePromoCode', { id });

            const promoCode = await this.promoCodeManagementService.deactivatePromoCode(id);

            this.logResponse('deactivatePromoCode', { id, code: promoCode.code });
            return ApiResponseUtil.ok(promoCode, 'Promo code deactivated successfully');
        } catch (error) {
            this.logError('deactivatePromoCode', error);
            throw new HttpException(
                `Failed to deactivate promo code: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Delete('promo-codes/:id')
    async deletePromoCode(@Param('id') id: string) {
        try {
            this.logRequest('deletePromoCode', { id });

            await this.promoCodeManagementService.deletePromoCode(id);

            this.logResponse('deletePromoCode', { id });
            return ApiResponseUtil.ok(null, 'Promo code deleted successfully');
        } catch (error) {
            this.logError('deletePromoCode', error);
            throw new HttpException(
                `Failed to delete promo code: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('promo-codes/generate')
    generatePromoCode(@Body() request: { length?: number }) {
        try {
            this.logRequest('generatePromoCode', request);

            const code = this.promoCodeManagementService.generatePromoCode(request.length || 6);

            this.logResponse('generatePromoCode', { code });
            return ApiResponseUtil.ok({ code }, 'Promo code generated successfully');
        } catch (error) {
            this.logError('generatePromoCode', error);
            throw new HttpException(
                `Failed to generate promo code: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    // Paginated Quotes - Must come BEFORE wildcard routes
    @Get('paginated')
    async getPaginatedQuotes(@Query() query: PaginatedQuotesQueryDto) {
        try {
            this.logRequest('getPaginatedQuotes', query);

            // Validate status if provided
            if (query.status) {
                const validStatuses = ['draft', 'sent', 'accepted', 'expired'];
                if (!validStatuses.includes(query.status)) {
                    throw new HttpException(
                        `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
                        HttpStatus.BAD_REQUEST
                    );
                }
            }

            // Validate date range if provided
            if (query.startDate && query.endDate) {
                const startDate = new Date(query.startDate);
                const endDate = new Date(query.endDate);

                if (startDate > endDate) {
                    throw new HttpException(
                        'Start date cannot be after end date',
                        HttpStatus.BAD_REQUEST
                    );
                }
            }

            const result = await this.quotePersistenceService.getPaginatedQuotes(query);

            this.logResponse('getPaginatedQuotes', {
                total: result.pagination.total,
                page: result.pagination.page,
                limit: result.pagination.limit,
                quotesCount: result.quotes.length
            });

            return ApiResponseUtil.ok(result, 'Paginated quotes retrieved successfully');
        } catch (error) {
            this.logError('getPaginatedQuotes', error);
            throw new HttpException(
                `Failed to get paginated quotes: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    // Rate Card Management - Must come BEFORE wildcard routes
    @Get('rate-cards')
    async getAvailableRateCards() {
        try {
            this.logRequest('getAvailableRateCards', {});

            const rateCards = await this.rateCardRepository.findActiveRateCards();

            const response = rateCards.map((rateCard) => ({
                id: rateCard.id,
                providerName: rateCard.providerName,
                displayName: rateCard.displayName,
                description: rateCard.description,
                version: rateCard.version,
                isDefault: rateCard.isDefault,
                priority: rateCard.priority,
                metadata: rateCard.metadata
            }));

            this.logResponse('getAvailableRateCards', { count: response.length });
            return ApiResponseUtil.ok(response, 'Available rate cards retrieved successfully');
        } catch (error) {
            this.logError('getAvailableRateCards', error);
            throw new HttpException(
                `Failed to get available rate cards: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('rate-cards/:rateCardId/fees')
    async getRateCardFees(@Param('rateCardId') rateCardId: string) {
        try {
            this.logRequest('getRateCardFees', { rateCardId });

            // Get rate card details
            const rateCard = await this.rateCardRepository.findOne({
                where: { id: rateCardId }
            });

            if (!rateCard) {
                throw new HttpException('Rate card not found', HttpStatus.NOT_FOUND);
            }

            // Get all fee items for this rate card
            const feeItems = await this.rateCardFeeItemRepository.findByRateCardId(rateCardId);

            // Group fees by category
            const feesByCategory = feeItems.reduce((acc, item) => {
                const category = item.categoryName || 'Other Fees';
                if (!acc[category]) {
                    acc[category] = [];
                }
                acc[category].push({
                    id: item.id,
                    label: item.label,
                    feeType: item.feeType,
                    netFee: item.netFee,
                    vatFee: item.vatFee,
                    totalFee: item.totalFee,
                    vatType: item.vatType,
                    applicableFor: item.applicableFor,
                    perParty: item.perParty,
                    dynamic: item.dynamic,
                    rangeStart: item.rangeStart,
                    rangeEnd: item.rangeEnd,
                    conditionSlug: item.conditionSlug,
                    displayOrder: item.displayOrder,
                    notes: item.notes,
                    active: item.active
                });
                return acc;
            }, {});

            const response = {
                rateCard: {
                    id: rateCard.id,
                    providerName: rateCard.providerName,
                    displayName: rateCard.displayName,
                    description: rateCard.description,
                    version: rateCard.version,
                    effectiveDate: rateCard.effectiveDate
                },
                feesByCategory,
                totalFeeItems: feeItems.length,
                categories: Object.keys(feesByCategory)
            };

            this.logResponse('getRateCardFees', { rateCardId, feeItemsCount: feeItems.length });
            return ApiResponseUtil.ok(response, 'Rate card fees retrieved successfully');
        } catch (error) {
            this.logError('getRateCardFees', error);
            throw new HttpException(
                `Failed to get rate card fees: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    // Pipeline Metrics - Must come BEFORE wildcard routes
    @Get('pipeline-metrics')
    async getPipelineMetrics() {
        try {
            this.logRequest('getPipelineMetrics', {});

            // Get all leads count
            const allLeadsCount = await this.quoteRepository.count();

            // Get accepted leads count
            const acceptedLeadsCount = await this.quoteRepository.count({
                where: { status: QuoteStatus.ACCEPTED }
            });

            // Get draft leads count
            const draftLeadsCount = await this.quoteRepository.count({
                where: { status: QuoteStatus.DRAFT }
            });

            // Get sent leads count
            const sentLeadsCount = await this.quoteRepository.count({
                where: { status: QuoteStatus.SENT }
            });

            // Get expired leads count
            const expiredLeadsCount = await this.quoteRepository.count({
                where: { status: QuoteStatus.EXPIRED }
            });

            const response = {
                allLeads: allLeadsCount,
                acceptedLeads: acceptedLeadsCount,
                draftLeads: draftLeadsCount,
                sentLeads: sentLeadsCount,
                expiredLeads: expiredLeadsCount,
                breakdown: {
                    all: allLeadsCount,
                    accepted: acceptedLeadsCount,
                    draft: draftLeadsCount,
                    sent: sentLeadsCount,
                    expired: expiredLeadsCount
                },
                summary: {
                    total: allLeadsCount,
                    active: draftLeadsCount + sentLeadsCount,
                    completed: acceptedLeadsCount,
                    expired: expiredLeadsCount
                }
            };

            this.logResponse('getPipelineMetrics', response);
            return ApiResponseUtil.ok(response, 'Pipeline metrics retrieved successfully');
        } catch (error) {
            this.logError('getPipelineMetrics', error);
            throw new HttpException(
                `Failed to get pipeline metrics: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('calculate')
    async calculateQuote(@Body() request: QuoteRequestDto) {
        try {
            this.logRequest('calculateQuote', request);

            const result = await this.quoteCalculationService.calculateQuote(request);

            // Add basic metrics for single quote
            const metrics = {
                total: {
                    requests: 1,
                    successful: 1,
                    failed: 0,
                    successRate: '100.00%'
                },
                financial: {
                    totalValue: Number((result.grandTotal || 0).toFixed(2)),
                    totalNetValue: Number((result.grandTotalNet || 0).toFixed(2)),
                    totalVatValue: Number((result.grandTotalVat || 0).toFixed(2)),
                    averageValue: Number((result.grandTotal || 0).toFixed(2)),
                    averageNetValue: Number((result.grandTotalNet || 0).toFixed(2)),
                    averageVatValue: Number((result.grandTotalVat || 0).toFixed(2))
                },
                processingTime: new Date().toISOString()
            };

            this.logResponse('calculateQuote', { ...result, metrics });
            return ApiResponseUtil.ok(
                {
                    result,
                    metrics
                },
                'Quote calculated successfully'
            );
        } catch (error) {
            this.logError('calculateQuote', error);

            // Add error metrics for failed single quote
            const errorMetrics = {
                total: {
                    requests: 1,
                    successful: 0,
                    failed: 1,
                    successRate: '0.00%'
                },
                financial: {
                    totalValue: 0,
                    totalNetValue: 0,
                    totalVatValue: 0,
                    averageValue: 0,
                    averageNetValue: 0,
                    averageVatValue: 0
                },
                errors: [
                    {
                        index: 0,
                        error: error.message,
                        reference: `ERROR-${Date.now()}-0`
                    }
                ],
                processingTime: new Date().toISOString()
            };

            throw new HttpException(
                {
                    message: `Failed to calculate quote: ${error.message}`,
                    metrics: errorMetrics
                },
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('calculate/bulk')
    async calculateBulkQuotes(@Body() request: BulkQuoteRequestDto) {
        try {
            this.logRequest('calculateBulkQuotes', { count: request.requests.length });

            const results: QuoteBreakdown[] = [];
            const errors: Array<{ index: number; error: string; reference: string }> = [];
            let totalValue = 0;
            let totalNetValue = 0;
            let totalVatValue = 0;

            // Process quotes in parallel for better performance
            const quotePromises = request.requests.map(async (quoteRequest, index) => {
                try {
                    const result = await this.quoteCalculationService.calculateQuote(quoteRequest);

                    // Track successful quote totals
                    totalValue += result.grandTotal || 0;
                    totalNetValue += result.grandTotalNet || 0;
                    totalVatValue += result.grandTotalVat || 0;

                    return { ...result, requestIndex: index, success: true };
                } catch (error) {
                    // Log individual quote errors but continue processing others
                    Logger.error(`Error calculating quote ${index}:`, error.message);

                    const errorReference = `ERROR-${Date.now()}-${index}`;
                    errors.push({
                        index,
                        error: error.message,
                        reference: errorReference
                    });

                    return {
                        reference: errorReference,
                        grandTotal: 0,
                        grandTotalNet: 0,
                        grandTotalVat: 0,
                        categories: [],
                        discount: 0,
                        calculationTimestamp: new Date(),
                        dynamicFactors: {},
                        error: error.message,
                        requestIndex: index,
                        success: false
                    };
                }
            });

            const resultsWithIndex = await Promise.all(quotePromises);

            // Sort by original request order and remove index
            resultsWithIndex
                .sort((a, b) => (a as any).requestIndex - (b as any).requestIndex)
                .forEach((result) => {
                    // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    const { requestIndex, error, success, ...cleanResult } = result as any;
                    results.push(cleanResult);
                });

            // Calculate comprehensive metrics
            const totalRequests = request.requests.length;
            const successfulQuotes = resultsWithIndex.filter((r: any) => r.success).length;
            const failedQuotes = errors.length;
            const successRate =
                totalRequests > 0 ? ((successfulQuotes / totalRequests) * 100).toFixed(2) : '0.00';

            const metrics = {
                total: {
                    requests: totalRequests,
                    successful: successfulQuotes,
                    failed: failedQuotes,
                    successRate: `${successRate}%`
                },
                financial: {
                    totalValue: Number(totalValue.toFixed(2)),
                    totalNetValue: Number(totalNetValue.toFixed(2)),
                    totalVatValue: Number(totalVatValue.toFixed(2)),
                    averageValue:
                        successfulQuotes > 0
                            ? Number((totalValue / successfulQuotes).toFixed(2))
                            : 0,
                    averageNetValue:
                        successfulQuotes > 0
                            ? Number((totalNetValue / successfulQuotes).toFixed(2))
                            : 0,
                    averageVatValue:
                        successfulQuotes > 0
                            ? Number((totalVatValue / successfulQuotes).toFixed(2))
                            : 0
                },
                errors: errors.length > 0 ? errors : undefined,
                processingTime: new Date().toISOString()
            };

            this.logResponse('calculateBulkQuotes', {
                count: results.length,
                successful: successfulQuotes,
                failed: failedQuotes,
                successRate
            });

            return ApiResponseUtil.ok(
                {
                    results,
                    metrics
                },
                `Bulk quotes calculated successfully. ${successfulQuotes}/${totalRequests} quotes processed successfully.`
            );
        } catch (error) {
            this.logError('calculateBulkQuotes', error);
            throw new HttpException(
                `Failed to calculate bulk quotes: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('validate')
    // eslint-disable-next-line @typescript-eslint/require-await
    async validateQuoteRequest(@Body() request: QuoteRequestDto) {
        const issues: string[] = [];

        // Basic validation logic
        if (!request.propertyValue || request.propertyValue <= 0) {
            issues.push('Property value must be greater than 0');
        }

        if (!request.transactionType) {
            issues.push('Transaction type is required');
        }

        if (!request.location?.postcode) {
            issues.push('Property postcode is required');
        }

        const isValid = issues.length === 0;

        return ApiResponseUtil.ok(
            { valid: isValid, issues },
            isValid ? 'Quote request is valid' : 'Quote request validation failed'
        );
    }

    @Post('validate-simple-quote')
    validateSimpleQuote(@Body() request: SimpleSaveQuoteAsLeadDto) {
        try {
            this.logRequest('validateSimpleQuote', { email: request.clientEmail });

            // Basic validation is already handled by the DTO decorators
            // Additional business logic validation can be added here
            const validationResult = {
                isValid: true,
                quoteReference: request.quoteReference,
                clientName: request.clientName,
                clientEmail: request.clientEmail,
                clientPhone: request.clientPhone,
                propertyAddress: request.propertyAddress,
                propertyValue: request.propertyValue,
                transactionType: request.transactionType,
                quoteTotal: request.quoteTotal,
                rateCardProvider: request.rateCardProvider,
                additionalNotes: request.additionalNotes,
                validationMessages: [] as string[]
            };

            // Add any additional business logic validation here
            if (request.propertyValue <= 0) {
                validationResult.isValid = false;
                validationResult.validationMessages.push('Property value must be greater than 0');
            }

            if (request.quoteTotal <= 0) {
                validationResult.isValid = false;
                validationResult.validationMessages.push('Quote total must be greater than 0');
            }

            if (!['buy', 'sell', 'remortgage', 'buy_sell'].includes(request.transactionType)) {
                validationResult.isValid = false;
                validationResult.validationMessages.push('Invalid transaction type');
            }

            this.logResponse('validateSimpleQuote', { isValid: validationResult.isValid });
            return ApiResponseUtil.ok(validationResult, 'Quote validation completed');
        } catch (error) {
            this.logError('validateSimpleQuote', error);
            throw new HttpException(
                `Failed to validate quote: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    // Quote Persistence Endpoints

    @Post('save-as-lead')
    async saveQuoteAsLead(@Body() request: SaveQuoteAsLeadDto, @Req() expressRequest: any) {
        try {
            this.logRequest('saveQuoteAsLead', { email: request.contactDetails.email });

            // First calculate the quote
            const calculatedQuote = await this.quoteCalculationService.calculateQuote(
                request.quoteRequest
            );

            // Then save it as a lead
            const result = await this.quotePersistenceService.saveQuoteAsLead(
                {
                    quoteRequest: request.quoteRequest,
                    calculatedQuote,
                    contactDetails: request.contactDetails
                },
                expressRequest
            );
            this.logResponse('saveQuoteAsLead', { quoteNumber: result.quoteNumber });
            return ApiResponseUtil.created(
                {
                    ...result,
                    expiresAt: result.expiresAt.toISOString()
                },
                'Quote saved as lead successfully'
            );
        } catch (error) {
            this.logError('saveQuoteAsLead', error);
            throw new HttpException(
                `Failed to save quote as lead: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('save-simple-as-lead')
    async saveSimpleQuoteAsLead(@Body() request: SimpleSaveQuoteAsLeadDto) {
        try {
            this.logRequest('saveSimpleQuoteAsLead', { email: request.clientEmail });

            // Convert simple request to a basic quote request for calculation
            const basicQuoteRequest = {
                propertyValue: request.propertyValue,
                propertyType: 'residential' as const,
                transactionType: request.transactionType as
                    | 'buy'
                    | 'sell'
                    | 'remortgage'
                    | 'buy_sell',
                location: {
                    postcode: request.propertyAddress.split(', ').pop() || 'Unknown',
                    region: 'Unknown',
                    country: 'UK'
                },
                propertyConditions: {
                    isNewBuild: false,
                    isLeasehold: false,
                    isWales: false,
                    isScotland: false,
                    isNorthernIreland: false,
                    isFirstTimeBuyer: false,
                    isBuyingWithMortgage: request.transactionType === 'buy',
                    isSharedOwnership: false,
                    isHelpToBuy: false,
                    isGiftedDeposit: false,
                    isCompanyOrTrust: false,
                    isResidingOutsideUK: false,
                    ownsMultipleProperties: false,
                    hasPreviousOwnership: false,
                    isMainResidence: true,
                    buildingOverLimit: false,
                    transferOfEquity: false,
                    mortgageRedemption: false
                },
                clientDetails: {
                    numberOfBuyers: 1,
                    numberOfSellers: 1,
                    numberOfOwners: 1
                },
                useDefaultRateCard: true
            } as any; // Type assertion to bypass strict typing for this conversion

            // Calculate the quote to get proper fee breakdown
            const calculatedQuote =
                await this.quoteCalculationService.calculateQuote(basicQuoteRequest);

            // Save using the existing persistence service
            const result = await this.quotePersistenceService.saveQuoteAsLead({
                quoteRequest: basicQuoteRequest,
                calculatedQuote,
                contactDetails: {
                    name: request.clientName,
                    email: request.clientEmail,
                    phone: request.clientPhone
                }
            });

            this.logResponse('saveSimpleQuoteAsLead', { quoteNumber: result.quoteNumber });
            return ApiResponseUtil.created(
                {
                    ...result,
                    expiresAt: result.expiresAt.toISOString()
                },
                'Quote saved as lead successfully'
            );
        } catch (error) {
            this.logError('saveSimpleQuoteAsLead', error);
            throw new HttpException(
                `Failed to save quote as lead: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('convert-to-case')
    @UseGuards(JwtGuard, RolesGuard)
    async convertQuoteToCase(@Body() request: ConvertQuoteToCaseDto, @Req() expressRequest: any) {
        try {
            // Extract current user ID from JWT guard
            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            this.logRequest('convertQuoteToCase', {
                quoteId: request.quoteId,
                userId: currentUser.systemUserId,
                caseType: request.caseType,
                priority: request.priority
            });

            const result = await this.quotePersistenceService.convertQuoteToCase(
                {
                    quoteId: request.quoteId,
                    acceptedBy: currentUser.systemUserId,
                    acceptedAt: new Date(),
                    caseType: request.caseType,
                    priority: request.priority,
                    assignedTo: request.assignedTo,
                    expectedCompletionDate: request.expectedCompletionDate,
                    caseNotes: request.caseNotes
                },
                expressRequest
            );

            this.logResponse('convertQuoteToCase', { caseNumber: result.caseNumber });
            return ApiResponseUtil.ok(result, 'Quote converted to case successfully');
        } catch (error) {
            this.logError('convertQuoteToCase', error);
            throw new HttpException(
                `Failed to convert quote to case: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':quoteId')
    async getQuoteById(@Param('quoteId') quoteId: string) {
        try {
            const quote = await this.quotePersistenceService.getQuoteById(quoteId);

            if (!quote) {
                throw new HttpException('Quote not found', HttpStatus.NOT_FOUND);
            }

            return ApiResponseUtil.ok(quote, 'Quote retrieved successfully');
        } catch (error) {
            this.logError('getQuoteById', error);
            throw new HttpException(
                `Failed to get quote: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':quoteId/status')
    async getQuoteStatus(@Param('quoteId') quoteId: string) {
        try {
            const quote = await this.quotePersistenceService.getQuoteById(quoteId);

            if (!quote) {
                throw new HttpException('Quote not found', HttpStatus.NOT_FOUND);
            }

            return ApiResponseUtil.ok(
                {
                    quoteId: quote.id,
                    quoteNumber: quote.quoteNumber,
                    status: quote.status,
                    canConvertToCase: quote.status === 'draft',
                    message:
                        quote.status === 'draft'
                            ? 'Quote can be converted to case'
                            : `Quote status is "${quote.status}". Only quotes in "draft" status can be converted to case.`
                },
                'Quote status retrieved successfully'
            );
        } catch (error) {
            this.logError('getQuoteStatus', error);
            throw new HttpException(
                `Failed to get quote status: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('lead/:quoteId')
    async getQuoteLead(@Param('quoteId') quoteId: string) {
        try {
            const quote = await this.quotePersistenceService.getQuoteById(quoteId);

            if (!quote) {
                throw new HttpException('Quote not found', HttpStatus.NOT_FOUND);
            }

            return ApiResponseUtil.ok(quote, 'Quote lead retrieved successfully');
        } catch (error) {
            this.logError('getQuoteLead', error);
            throw new HttpException(
                `Failed to get quote lead: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('number/:quoteNumber')
    async getQuoteByNumberDirect(@Param('quoteNumber') quoteNumber: string) {
        try {
            const quote = await this.quotePersistenceService.getQuoteByNumber(quoteNumber);

            if (!quote) {
                throw new HttpException('Quote not found', HttpStatus.NOT_FOUND);
            }

            return ApiResponseUtil.ok(quote, 'Quote retrieved successfully');
        } catch (error) {
            this.logError('getQuoteByNumberDirect', error);
            throw new HttpException(
                `Failed to get quote by number: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('lead/number/:quoteNumber')
    async getQuoteByNumber(@Param('quoteNumber') quoteNumber: string) {
        try {
            const quote = await this.quotePersistenceService.getQuoteByNumber(quoteNumber);

            if (!quote) {
                throw new HttpException('Quote not found', HttpStatus.NOT_FOUND);
            }

            return ApiResponseUtil.ok(quote, 'Quote retrieved successfully');
        } catch (error) {
            this.logError('getQuoteByNumber', error);
            throw new HttpException(
                `Failed to get quote by number: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('status/:status')
    async getQuotesByStatusDirect(
        @Param('status') status: string,
        @Query('limit') limit: number = 50,
        @Query('offset') offset: number = 0
    ) {
        try {
            const quotes = await this.quotePersistenceService.getQuotesByStatus(
                status as any,
                limit,
                offset
            );

            return ApiResponseUtil.ok(quotes, 'Quotes retrieved successfully');
        } catch (error) {
            this.logError('getQuotesByStatusDirect', error);
            throw new HttpException(
                `Failed to get quotes by status: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('leads/status/:status')
    async getQuotesByStatus(
        @Param('status') status: string,
        @Query('limit') limit: number = 50,
        @Query('offset') offset: number = 0
    ) {
        try {
            const quotes = await this.quotePersistenceService.getQuotesByStatus(
                status as any,
                limit,
                offset
            );

            return ApiResponseUtil.ok(quotes, 'Quotes retrieved successfully');
        } catch (error) {
            this.logError('getQuotesByStatus', error);
            throw new HttpException(
                `Failed to get quotes by status: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('email/:email')
    async getQuotesByEmailDirect(@Param('email') email: string) {
        try {
            const quotes = await this.quotePersistenceService.getQuotesByEmail(email);

            return ApiResponseUtil.ok(quotes, 'Quotes retrieved successfully');
        } catch (error) {
            this.logError('getQuotesByEmailDirect', error);
            throw new HttpException(
                `Failed to get quotes by email: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('leads/email/:email')
    async getQuotesByEmail(@Param('email') email: string) {
        try {
            const quotes = await this.quotePersistenceService.getQuotesByEmail(email);

            return ApiResponseUtil.ok(quotes, 'Quotes retrieved successfully');
        } catch (error) {
            this.logError('getQuotesByEmail', error);
            throw new HttpException(
                `Failed to get quote by email: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('lead/:quoteId/status')
    async updateQuoteStatus(
        @Param('quoteId') quoteId: string,
        @Body() request: QuoteStatusUpdateDto
    ) {
        try {
            await this.quotePersistenceService.updateQuoteStatus(quoteId, request.status);

            return ApiResponseUtil.ok(null, 'Quote status updated successfully');
        } catch (error) {
            this.logError('updateQuoteStatus', error);
            throw new HttpException(
                `Failed to update quote status: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post('rate-cards/:rateCardId/calculate')
    async calculateQuoteWithRateCard(
        @Param('rateCardId') rateCardId: string,
        @Body() request: QuoteRequestDto
    ) {
        try {
            this.logRequest('calculateQuoteWithRateCard', { rateCardId, request });

            // Override the rate card selection to use the specified rate card
            const modifiedRequest = {
                ...request,
                rateCardId,
                useDefaultRateCard: false
            };

            const result = await this.quoteCalculationService.calculateQuote(modifiedRequest);

            this.logResponse('calculateQuoteWithRateCard', result);
            return ApiResponseUtil.ok(
                result,
                'Quote calculated successfully with specified rate card'
            );
        } catch (error) {
            this.logError('calculateQuoteWithRateCard', error);
            throw new HttpException(
                `Failed to calculate quote with rate card: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    // Attachment Management Endpoints
    @Get(':quoteId/attachments')
    async getQuoteAttachments(@Param('quoteId') quoteId: string, @Query() query: any) {
        try {
            this.logRequest('getQuoteAttachments', { quoteId, query });

            let attachments;
            if (query.isPublic === 'true') {
                attachments =
                    await this.quoteAttachmentService.getPublicAttachmentsForQuote(quoteId);
            } else if (query.isRequired === 'true') {
                attachments =
                    await this.quoteAttachmentService.getRequiredAttachmentsForQuote(quoteId);
            } else {
                attachments = await this.quoteAttachmentService.getAttachmentsForQuote(quoteId);
            }

            // Format response with display names
            const formattedAttachments = attachments.map((attachment) => ({
                id: attachment.id,
                quoteId: attachment.quoteId,
                documentId: attachment.documentId,
                filename: attachment.filename,
                url: attachment.url,
                fileSize: attachment.fileSize,
                mimeType: attachment.mimeType,
                uploadedBy: attachment.uploadedBy,
                uploadedByName: attachment.uploadedByName,
                uploadedAt: attachment.uploadedAt,
                description: attachment.description,
                attachmentType: attachment.attachmentType,
                attachmentTypeDisplayName: this.quoteAttachmentService.getAttachmentTypeDisplayName(
                    attachment.attachmentType
                ),
                isPublic: attachment.isPublic,
                isRequired: attachment.isRequired,
                expiresAt: attachment.expiresAt,
                metadata: attachment.metadata,
                createdAt: attachment.createdAt,
                updatedAt: attachment.updatedAt
            }));

            this.logResponse('getQuoteAttachments', {
                quoteId,
                attachmentCount: formattedAttachments.length
            });

            return ApiResponseUtil.ok(formattedAttachments, 'Attachments retrieved successfully');
        } catch (error) {
            this.logError('getQuoteAttachments', error);
            throw new HttpException(
                `Failed to get attachments: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post(':quoteId/attachments')
    async createAttachment(
        @Param('quoteId') quoteId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('createAttachment', { quoteId, filename: request.filename });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            const attachment = await this.quoteAttachmentService.createAttachment(
                {
                    ...request,
                    quoteId
                },
                currentUser.systemUserId,
                currentUser.name || currentUser.email,
                expressRequest
            );

            const formattedAttachment = {
                id: attachment.id,
                quoteId: attachment.quoteId,
                documentId: attachment.documentId,
                filename: attachment.filename,
                url: attachment.url,
                fileSize: attachment.fileSize,
                mimeType: attachment.mimeType,
                uploadedBy: attachment.uploadedBy,
                uploadedByName: attachment.uploadedByName,
                uploadedAt: attachment.uploadedAt,
                description: attachment.description,
                attachmentType: attachment.attachmentType,
                attachmentTypeDisplayName: this.quoteAttachmentService.getAttachmentTypeDisplayName(
                    attachment.attachmentType
                ),
                isPublic: attachment.isPublic,
                isRequired: attachment.isRequired,
                expiresAt: attachment.expiresAt,
                metadata: attachment.metadata,
                createdAt: attachment.createdAt,
                updatedAt: attachment.updatedAt
            };

            this.logResponse('createAttachment', {
                quoteId,
                attachmentId: attachment.id,
                filename: attachment.filename
            });

            return ApiResponseUtil.created(formattedAttachment, 'Attachment created successfully');
        } catch (error) {
            this.logError('createAttachment', error);
            throw new HttpException(
                `Failed to create attachment: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('attachments/:attachmentId')
    async getAttachmentById(@Param('attachmentId') attachmentId: string) {
        try {
            this.logRequest('getAttachmentById', { attachmentId });

            const attachment = await this.quoteAttachmentService.getAttachmentById(attachmentId);

            const formattedAttachment = {
                id: attachment.id,
                quoteId: attachment.quoteId,
                documentId: attachment.documentId,
                filename: attachment.filename,
                url: attachment.url,
                fileSize: attachment.fileSize,
                mimeType: attachment.mimeType,
                uploadedBy: attachment.uploadedBy,
                uploadedByName: attachment.uploadedByName,
                uploadedAt: attachment.uploadedAt,
                description: attachment.description,
                attachmentType: attachment.attachmentType,
                attachmentTypeDisplayName: this.quoteAttachmentService.getAttachmentTypeDisplayName(
                    attachment.attachmentType
                ),
                isPublic: attachment.isPublic,
                isRequired: attachment.isRequired,
                expiresAt: attachment.expiresAt,
                metadata: attachment.metadata,
                createdAt: attachment.createdAt,
                updatedAt: attachment.updatedAt
            };

            this.logResponse('getAttachmentById', {
                attachmentId,
                filename: attachment.filename
            });

            return ApiResponseUtil.ok(formattedAttachment, 'Attachment retrieved successfully');
        } catch (error) {
            this.logError('getAttachmentById', error);
            throw new HttpException(
                `Failed to get attachment: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('attachments/:attachmentId')
    async updateAttachment(
        @Param('attachmentId') attachmentId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('updateAttachment', { attachmentId, updates: request });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            const attachment = await this.quoteAttachmentService.updateAttachment(
                attachmentId,
                request,
                currentUser.systemUserId,
                currentUser.name || currentUser.email,
                expressRequest
            );

            const formattedAttachment = {
                id: attachment.id,
                quoteId: attachment.quoteId,
                documentId: attachment.documentId,
                filename: attachment.filename,
                url: attachment.url,
                fileSize: attachment.fileSize,
                mimeType: attachment.mimeType,
                uploadedBy: attachment.uploadedBy,
                uploadedByName: attachment.uploadedByName,
                uploadedAt: attachment.uploadedAt,
                description: attachment.description,
                attachmentType: attachment.attachmentType,
                attachmentTypeDisplayName: this.quoteAttachmentService.getAttachmentTypeDisplayName(
                    attachment.attachmentType
                ),
                isPublic: attachment.isPublic,
                isRequired: attachment.isRequired,
                expiresAt: attachment.expiresAt,
                metadata: attachment.metadata,
                createdAt: attachment.createdAt,
                updatedAt: attachment.updatedAt
            };

            this.logResponse('updateAttachment', {
                attachmentId,
                filename: attachment.filename
            });

            return ApiResponseUtil.ok(formattedAttachment, 'Attachment updated successfully');
        } catch (error) {
            this.logError('updateAttachment', error);
            throw new HttpException(
                `Failed to update attachment: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Delete('attachments/:attachmentId')
    async deleteAttachment(
        @Param('attachmentId') attachmentId: string,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('deleteAttachment', { attachmentId });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            await this.quoteAttachmentService.deleteAttachment(
                attachmentId,
                currentUser.systemUserId,
                currentUser.name || currentUser.email,
                expressRequest
            );

            this.logResponse('deleteAttachment', { attachmentId });

            return ApiResponseUtil.ok(null, 'Attachment deleted successfully');
        } catch (error) {
            this.logError('deleteAttachment', error);
            throw new HttpException(
                `Failed to delete attachment: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':quoteId/attachments/stats')
    async getAttachmentStats(@Param('quoteId') quoteId: string) {
        try {
            this.logRequest('getAttachmentStats', { quoteId });

            const stats = await this.quoteAttachmentService.getAttachmentStats(quoteId);

            this.logResponse('getAttachmentStats', {
                quoteId,
                totalAttachments: stats.totalAttachments,
                totalFileSize: stats.totalFileSize
            });

            return ApiResponseUtil.ok(stats, 'Attachment statistics retrieved successfully');
        } catch (error) {
            this.logError('getAttachmentStats', error);
            throw new HttpException(
                `Failed to get attachment statistics: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('attachments/:attachmentId/download-url')
    async getAttachmentDownloadUrl(@Param('attachmentId') attachmentId: string) {
        try {
            this.logRequest('getAttachmentDownloadUrl', { attachmentId });

            const downloadUrl =
                await this.quoteAttachmentService.generatePresignedUrl(attachmentId);

            this.logResponse('getAttachmentDownloadUrl', {
                attachmentId,
                hasUrl: !!downloadUrl
            });

            return ApiResponseUtil.ok({ downloadUrl }, 'Download URL generated successfully');
        } catch (error) {
            this.logError('getAttachmentDownloadUrl', error);
            throw new HttpException(
                `Failed to generate download URL: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    // Communication Management Endpoints
    @Get(':quoteId/communications')
    async getQuoteCommunications(@Param('quoteId') quoteId: string, @Query() query: any) {
        try {
            this.logRequest('getQuoteCommunications', { quoteId, query });

            const limit = query.limit ? parseInt(query.limit) : 50;
            const communications = await this.quoteCommunicationService.getCommunicationsForQuote(
                quoteId,
                limit
            );

            // Format response with display names
            const formattedCommunications = communications.map((communication) => ({
                id: communication.id,
                quoteId: communication.quoteId,
                type: communication.type,
                typeDisplayName: this.quoteCommunicationService.getCommunicationTypeDisplayName(
                    communication.type
                ),
                direction: communication.direction,
                directionDisplayName:
                    this.quoteCommunicationService.getCommunicationDirectionDisplayName(
                        communication.direction
                    ),
                priority: communication.priority,
                priorityDisplayName:
                    this.quoteCommunicationService.getCommunicationPriorityDisplayName(
                        communication.priority
                    ),
                subject: communication.subject,
                body: communication.body,
                sender: communication.sender,
                senderName: communication.senderName,
                senderEmail: communication.senderEmail,
                recipient: communication.recipient,
                recipientName: communication.recipientName,
                recipientEmail: communication.recipientEmail,
                sentAt: communication.sentAt,
                isRead: communication.isRead,
                readAt: communication.readAt,
                readBy: communication.readBy,
                readByName: communication.readByName,
                attachments: communication.attachments,
                metadata: communication.metadata,
                isArchived: communication.isArchived,
                archivedAt: communication.archivedAt,
                archivedBy: communication.archivedBy,
                followUpRequired: communication.followUpRequired,
                followUpDate: communication.followUpDate,
                followUpAssignedTo: communication.followUpAssignedTo,
                followUpAssignedToName: communication.followUpAssignedToName,
                createdBy: communication.createdBy,
                createdAt: communication.createdAt,
                updatedAt: communication.updatedAt
            }));

            this.logResponse('getQuoteCommunications', {
                quoteId,
                communicationCount: formattedCommunications.length
            });

            return ApiResponseUtil.ok(
                formattedCommunications,
                'Communications retrieved successfully'
            );
        } catch (error) {
            this.logError('getQuoteCommunications', error);
            throw new HttpException(
                `Failed to get communications: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':quoteId/communications/paginated')
    async getQuoteCommunicationsPaginated(@Param('quoteId') quoteId: string, @Query() query: any) {
        try {
            this.logRequest('getQuoteCommunicationsPaginated', { quoteId, query });

            const page = query.page ? parseInt(query.page) : 1;
            const limit = query.limit ? parseInt(query.limit) : 20;

            const filters = {
                type: query.type,
                direction: query.direction,
                priority: query.priority,
                isRead:
                    query.isRead === 'true' ? true : query.isRead === 'false' ? false : undefined,
                followUpRequired:
                    query.followUpRequired === 'true'
                        ? true
                        : query.followUpRequired === 'false'
                          ? false
                          : undefined,
                startDate: query.startDate ? new Date(query.startDate) : undefined,
                endDate: query.endDate ? new Date(query.endDate) : undefined
            };

            const result = await this.quoteCommunicationService.getCommunicationsPaginated(
                quoteId,
                page,
                limit,
                filters
            );

            // Format communications with display names
            const formattedCommunications = result.communications.map((communication) => ({
                id: communication.id,
                quoteId: communication.quoteId,
                type: communication.type,
                typeDisplayName: this.quoteCommunicationService.getCommunicationTypeDisplayName(
                    communication.type
                ),
                direction: communication.direction,
                directionDisplayName:
                    this.quoteCommunicationService.getCommunicationDirectionDisplayName(
                        communication.direction
                    ),
                priority: communication.priority,
                priorityDisplayName:
                    this.quoteCommunicationService.getCommunicationPriorityDisplayName(
                        communication.priority
                    ),
                subject: communication.subject,
                body: communication.body,
                sender: communication.sender,
                senderName: communication.senderName,
                senderEmail: communication.senderEmail,
                recipient: communication.recipient,
                recipientName: communication.recipientName,
                recipientEmail: communication.recipientEmail,
                sentAt: communication.sentAt,
                isRead: communication.isRead,
                readAt: communication.readAt,
                readBy: communication.readBy,
                readByName: communication.readByName,
                attachments: communication.attachments,
                metadata: communication.metadata,
                isArchived: communication.isArchived,
                archivedAt: communication.archivedAt,
                archivedBy: communication.archivedBy,
                followUpRequired: communication.followUpRequired,
                followUpDate: communication.followUpDate,
                followUpAssignedTo: communication.followUpAssignedTo,
                followUpAssignedToName: communication.followUpAssignedToName,
                createdBy: communication.createdBy,
                createdAt: communication.createdAt,
                updatedAt: communication.updatedAt
            }));

            const response = {
                communications: formattedCommunications,
                pagination: {
                    page: result.page,
                    limit,
                    total: result.total,
                    totalPages: result.totalPages,
                    hasNext: result.page < result.totalPages,
                    hasPrevious: result.page > 1
                },
                filters
            };

            this.logResponse('getQuoteCommunicationsPaginated', {
                quoteId,
                total: result.total,
                page: result.page,
                totalPages: result.totalPages,
                communicationCount: formattedCommunications.length
            });

            return ApiResponseUtil.ok(response, 'Paginated communications retrieved successfully');
        } catch (error) {
            this.logError('getQuoteCommunicationsPaginated', error);
            throw new HttpException(
                `Failed to get paginated communications: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post(':quoteId/communications')
    async createCommunication(
        @Param('quoteId') quoteId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('createCommunication', { quoteId, type: request.type });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            const communication = await this.quoteCommunicationService.createCommunication(
                {
                    ...request,
                    quoteId
                },
                currentUser.systemUserId,
                expressRequest
            );

            const formattedCommunication = {
                id: communication.id,
                quoteId: communication.quoteId,
                type: communication.type,
                typeDisplayName: this.quoteCommunicationService.getCommunicationTypeDisplayName(
                    communication.type
                ),
                direction: communication.direction,
                directionDisplayName:
                    this.quoteCommunicationService.getCommunicationDirectionDisplayName(
                        communication.direction
                    ),
                priority: communication.priority,
                priorityDisplayName:
                    this.quoteCommunicationService.getCommunicationPriorityDisplayName(
                        communication.priority
                    ),
                subject: communication.subject,
                body: communication.body,
                sender: communication.sender,
                senderName: communication.senderName,
                senderEmail: communication.senderEmail,
                recipient: communication.recipient,
                recipientName: communication.recipientName,
                recipientEmail: communication.recipientEmail,
                sentAt: communication.sentAt,
                isRead: communication.isRead,
                readAt: communication.readAt,
                readBy: communication.readBy,
                readByName: communication.readByName,
                attachments: communication.attachments,
                metadata: communication.metadata,
                isArchived: communication.isArchived,
                archivedAt: communication.archivedAt,
                archivedBy: communication.archivedBy,
                followUpRequired: communication.followUpRequired,
                followUpDate: communication.followUpDate,
                followUpAssignedTo: communication.followUpAssignedTo,
                followUpAssignedToName: communication.followUpAssignedToName,
                createdBy: communication.createdBy,
                createdAt: communication.createdAt,
                updatedAt: communication.updatedAt
            };

            this.logResponse('createCommunication', {
                quoteId,
                communicationId: communication.id,
                type: communication.type,
                subject: communication.subject
            });

            return ApiResponseUtil.created(
                formattedCommunication,
                'Communication created successfully'
            );
        } catch (error) {
            this.logError('createCommunication', error);
            throw new HttpException(
                `Failed to create communication: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('communications/:communicationId')
    async getCommunicationById(@Param('communicationId') communicationId: string) {
        try {
            this.logRequest('getCommunicationById', { communicationId });

            const communication =
                await this.quoteCommunicationService.getCommunicationById(communicationId);

            const formattedCommunication = {
                id: communication.id,
                quoteId: communication.quoteId,
                type: communication.type,
                typeDisplayName: this.quoteCommunicationService.getCommunicationTypeDisplayName(
                    communication.type
                ),
                direction: communication.direction,
                directionDisplayName:
                    this.quoteCommunicationService.getCommunicationDirectionDisplayName(
                        communication.direction
                    ),
                priority: communication.priority,
                priorityDisplayName:
                    this.quoteCommunicationService.getCommunicationPriorityDisplayName(
                        communication.priority
                    ),
                subject: communication.subject,
                body: communication.body,
                sender: communication.sender,
                senderName: communication.senderName,
                senderEmail: communication.senderEmail,
                recipient: communication.recipient,
                recipientName: communication.recipientName,
                recipientEmail: communication.recipientEmail,
                sentAt: communication.sentAt,
                isRead: communication.isRead,
                readAt: communication.readAt,
                readBy: communication.readBy,
                readByName: communication.readByName,
                attachments: communication.attachments,
                metadata: communication.metadata,
                isArchived: communication.isArchived,
                archivedAt: communication.archivedAt,
                archivedBy: communication.archivedBy,
                followUpRequired: communication.followUpRequired,
                followUpDate: communication.followUpDate,
                followUpAssignedTo: communication.followUpAssignedTo,
                followUpAssignedToName: communication.followUpAssignedToName,
                createdBy: communication.createdBy,
                createdAt: communication.createdAt,
                updatedAt: communication.updatedAt
            };

            this.logResponse('getCommunicationById', {
                communicationId,
                type: communication.type,
                subject: communication.subject
            });

            return ApiResponseUtil.ok(
                formattedCommunication,
                'Communication retrieved successfully'
            );
        } catch (error) {
            this.logError('getCommunicationById', error);
            throw new HttpException(
                `Failed to get communication: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('communications/:communicationId')
    async updateCommunication(
        @Param('communicationId') communicationId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('updateCommunication', { communicationId, updates: request });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            const communication = await this.quoteCommunicationService.updateCommunication(
                communicationId,
                request,
                currentUser.systemUserId,
                expressRequest
            );

            const formattedCommunication = {
                id: communication.id,
                quoteId: communication.quoteId,
                type: communication.type,
                typeDisplayName: this.quoteCommunicationService.getCommunicationTypeDisplayName(
                    communication.type
                ),
                direction: communication.direction,
                directionDisplayName:
                    this.quoteCommunicationService.getCommunicationDirectionDisplayName(
                        communication.direction
                    ),
                priority: communication.priority,
                priorityDisplayName:
                    this.quoteCommunicationService.getCommunicationPriorityDisplayName(
                        communication.priority
                    ),
                subject: communication.subject,
                body: communication.body,
                sender: communication.sender,
                senderName: communication.senderName,
                senderEmail: communication.senderEmail,
                recipient: communication.recipient,
                recipientName: communication.recipientName,
                recipientEmail: communication.recipientEmail,
                sentAt: communication.sentAt,
                isRead: communication.isRead,
                readAt: communication.readAt,
                readBy: communication.readBy,
                readByName: communication.readByName,
                attachments: communication.attachments,
                metadata: communication.metadata,
                isArchived: communication.isArchived,
                archivedAt: communication.archivedAt,
                archivedBy: communication.archivedBy,
                followUpRequired: communication.followUpRequired,
                followUpDate: communication.followUpDate,
                followUpAssignedTo: communication.followUpAssignedTo,
                followUpAssignedToName: communication.followUpAssignedToName,
                createdBy: communication.createdBy,
                createdAt: communication.createdAt,
                updatedAt: communication.updatedAt
            };

            this.logResponse('updateCommunication', {
                communicationId,
                type: communication.type,
                subject: communication.subject
            });

            return ApiResponseUtil.ok(formattedCommunication, 'Communication updated successfully');
        } catch (error) {
            this.logError('updateCommunication', error);
            throw new HttpException(
                `Failed to update communication: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('communications/:communicationId/mark-read')
    async markCommunicationAsRead(
        @Param('communicationId') communicationId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('markCommunicationAsRead', { communicationId });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            await this.quoteCommunicationService.markAsRead(
                communicationId,
                currentUser.systemUserId,
                currentUser.name || currentUser.email,
                expressRequest
            );

            this.logResponse('markCommunicationAsRead', { communicationId });

            return ApiResponseUtil.ok(null, 'Communication marked as read successfully');
        } catch (error) {
            this.logError('markCommunicationAsRead', error);
            throw new HttpException(
                `Failed to mark communication as read: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('communications/:communicationId/archive')
    async archiveCommunication(
        @Param('communicationId') communicationId: string,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('archiveCommunication', { communicationId });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            await this.quoteCommunicationService.archiveCommunication(
                communicationId,
                currentUser.systemUserId,
                expressRequest
            );

            this.logResponse('archiveCommunication', { communicationId });

            return ApiResponseUtil.ok(null, 'Communication archived successfully');
        } catch (error) {
            this.logError('archiveCommunication', error);
            throw new HttpException(
                `Failed to archive communication: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('communications/:communicationId/follow-up')
    async setFollowUp(
        @Param('communicationId') communicationId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('setFollowUp', { communicationId, followUpDate: request.followUpDate });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            await this.quoteCommunicationService.setFollowUp(
                communicationId,
                new Date(request.followUpDate),
                request.assignedTo,
                request.assignedToName,
                expressRequest
            );

            this.logResponse('setFollowUp', {
                communicationId,
                followUpDate: request.followUpDate,
                assignedTo: request.assignedTo
            });

            return ApiResponseUtil.ok(null, 'Follow-up set successfully');
        } catch (error) {
            this.logError('setFollowUp', error);
            throw new HttpException(
                `Failed to set follow-up: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('communications/:communicationId/complete-follow-up')
    async completeFollowUp(
        @Param('communicationId') communicationId: string,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('completeFollowUp', { communicationId });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            await this.quoteCommunicationService.completeFollowUp(
                communicationId,
                currentUser.systemUserId,
                expressRequest
            );

            this.logResponse('completeFollowUp', { communicationId });

            return ApiResponseUtil.ok(null, 'Follow-up completed successfully');
        } catch (error) {
            this.logError('completeFollowUp', error);
            throw new HttpException(
                `Failed to complete follow-up: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':quoteId/communications/stats')
    async getCommunicationStats(@Param('quoteId') quoteId: string) {
        try {
            this.logRequest('getCommunicationStats', { quoteId });

            const stats = await this.quoteCommunicationService.getCommunicationStats(quoteId);

            this.logResponse('getCommunicationStats', {
                quoteId,
                totalCommunications: stats.totalCommunications,
                unreadCommunications: stats.unreadCommunications,
                followUpRequired: stats.followUpRequired
            });

            return ApiResponseUtil.ok(stats, 'Communication statistics retrieved successfully');
        } catch (error) {
            this.logError('getCommunicationStats', error);
            throw new HttpException(
                `Failed to get communication statistics: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':quoteId/communications/search')
    async searchCommunications(
        @Param('quoteId') quoteId: string,
        @Query('q') searchTerm: string,
        @Query('limit') limit?: number
    ) {
        try {
            this.logRequest('searchCommunications', { quoteId, searchTerm, limit });

            const communications = await this.quoteCommunicationService.searchCommunications(
                quoteId,
                searchTerm,
                limit || 20
            );

            // Format communications with display names
            const formattedCommunications = communications.map((communication) => ({
                id: communication.id,
                quoteId: communication.quoteId,
                type: communication.type,
                typeDisplayName: this.quoteCommunicationService.getCommunicationTypeDisplayName(
                    communication.type
                ),
                direction: communication.direction,
                directionDisplayName:
                    this.quoteCommunicationService.getCommunicationDirectionDisplayName(
                        communication.direction
                    ),
                priority: communication.priority,
                priorityDisplayName:
                    this.quoteCommunicationService.getCommunicationPriorityDisplayName(
                        communication.priority
                    ),
                subject: communication.subject,
                body: communication.body,
                sender: communication.sender,
                senderName: communication.senderName,
                senderEmail: communication.senderEmail,
                recipient: communication.recipient,
                recipientName: communication.recipientName,
                recipientEmail: communication.recipientEmail,
                sentAt: communication.sentAt,
                isRead: communication.isRead,
                readAt: communication.readAt,
                readBy: communication.readBy,
                readByName: communication.readByName,
                attachments: communication.attachments,
                metadata: communication.metadata,
                isArchived: communication.isArchived,
                archivedAt: communication.archivedAt,
                archivedBy: communication.archivedBy,
                followUpRequired: communication.followUpRequired,
                followUpDate: communication.followUpDate,
                followUpAssignedTo: communication.followUpAssignedTo,
                followUpAssignedToName: communication.followUpAssignedToName,
                createdBy: communication.createdBy,
                createdAt: communication.createdAt,
                updatedAt: communication.updatedAt
            }));

            this.logResponse('searchCommunications', {
                quoteId,
                searchTerm,
                resultCount: formattedCommunications.length
            });

            return ApiResponseUtil.ok(
                formattedCommunications,
                'Communication search completed successfully'
            );
        } catch (error) {
            this.logError('searchCommunications', error);
            throw new HttpException(
                `Failed to search communications: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post(':quoteId/communications/system-note')
    async addSystemNote(
        @Param('quoteId') quoteId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('addSystemNote', { quoteId, note: request.note });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            const communication = await this.quoteCommunicationService.addSystemNote(
                quoteId,
                request.note,
                currentUser.systemUserId,
                currentUser.name || currentUser.email,
                expressRequest
            );

            this.logResponse('addSystemNote', {
                quoteId,
                communicationId: communication.id
            });

            return ApiResponseUtil.created(null, 'System note added successfully');
        } catch (error) {
            this.logError('addSystemNote', error);
            throw new HttpException(
                `Failed to add system note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post(':quoteId/communications/log-email')
    async logEmailSent(
        @Param('quoteId') quoteId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('logEmailSent', { quoteId, subject: request.subject });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            const communication = await this.quoteCommunicationService.logEmailSent(
                quoteId,
                request,
                currentUser.systemUserId,
                expressRequest
            );

            this.logResponse('logEmailSent', {
                quoteId,
                communicationId: communication.id,
                recipientEmail: request.recipientEmail
            });

            return ApiResponseUtil.created(null, 'Email logged successfully');
        } catch (error) {
            this.logError('logEmailSent', error);
            throw new HttpException(
                `Failed to log email: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post(':quoteId/communications/log-call')
    async logCall(
        @Param('quoteId') quoteId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('logCall', { quoteId, subject: request.subject });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            const communication = await this.quoteCommunicationService.logCall(
                quoteId,
                request,
                currentUser.systemUserId,
                currentUser.name || currentUser.email,
                expressRequest
            );

            this.logResponse('logCall', {
                quoteId,
                communicationId: communication.id,
                recipientName: request.recipientName
            });

            return ApiResponseUtil.created(null, 'Call logged successfully');
        } catch (error) {
            this.logError('logCall', error);
            throw new HttpException(
                `Failed to log call: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    // Client Call Notes Management Endpoints
    @Get(':quoteId/call-notes')
    async getClientCallNotes(@Param('quoteId') quoteId: string, @Query() query: any) {
        try {
            this.logRequest('getClientCallNotes', { quoteId, query });

            const limit = query.limit ? parseInt(query.limit) : 50;
            const callNotes = await this.quoteClientCallNoteService.getCallNotesForQuote(
                quoteId,
                limit
            );

            // Format response with display names
            const formattedCallNotes = callNotes.map((callNote) => ({
                id: callNote.id,
                quoteId: callNote.quoteId,
                callType: callNote.callType,
                callTypeDisplayName: this.quoteClientCallNoteService.getCallTypeDisplayName(
                    callNote.callType
                ),
                callOutcome: callNote.callOutcome,
                callOutcomeDisplayName: this.quoteClientCallNoteService.getCallOutcomeDisplayName(
                    callNote.callOutcome
                ),
                callPriority: callNote.callPriority,
                callPriorityDisplayName: this.quoteClientCallNoteService.getCallPriorityDisplayName(
                    callNote.callPriority
                ),
                callDate: callNote.callDate,
                callDuration: callNote.callDuration,
                clientName: callNote.clientName,
                clientPhone: callNote.clientPhone,
                clientEmail: callNote.clientEmail,
                staffMember: callNote.staffMember,
                staffMemberName: callNote.staffMemberName,
                callSummary: callNote.callSummary,
                discussionPoints: callNote.discussionPoints,
                clientConcerns: callNote.clientConcerns,
                objectionsRaised: callNote.objectionsRaised,
                objectionsHandled: callNote.objectionsHandled,
                nextSteps: callNote.nextSteps,
                followUpRequired: callNote.followUpRequired,
                followUpDate: callNote.followUpDate,
                followUpAssignedTo: callNote.followUpAssignedTo,
                followUpAssignedToName: callNote.followUpAssignedToName,
                followUpNotes: callNote.followUpNotes,
                quoteRelated: callNote.quoteRelated,
                quoteDiscussed: callNote.quoteDiscussed,
                quoteFeedback: callNote.quoteFeedback,
                clientSatisfaction: callNote.clientSatisfaction,
                clientSatisfactionDescription: callNote.clientSatisfaction
                    ? this.quoteClientCallNoteService.getSatisfactionDescription(
                          callNote.clientSatisfaction
                      )
                    : undefined,
                callMetadata: callNote.callMetadata,
                isArchived: callNote.isArchived,
                archivedAt: callNote.archivedAt,
                archivedBy: callNote.archivedBy,
                createdBy: callNote.createdBy,
                createdAt: callNote.createdAt,
                updatedAt: callNote.updatedAt
            }));

            this.logResponse('getClientCallNotes', {
                quoteId,
                callNoteCount: formattedCallNotes.length
            });

            return ApiResponseUtil.ok(
                formattedCallNotes,
                'Client call notes retrieved successfully'
            );
        } catch (error) {
            this.logError('getClientCallNotes', error);
            throw new HttpException(
                `Failed to get client call notes: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':quoteId/call-notes/paginated')
    async getClientCallNotesPaginated(@Param('quoteId') quoteId: string, @Query() query: any) {
        try {
            this.logRequest('getClientCallNotesPaginated', { quoteId, query });

            const page = query.page ? parseInt(query.page) : 1;
            const limit = query.limit ? parseInt(query.limit) : 20;

            const filters = {
                callType: query.callType,
                callOutcome: query.callOutcome,
                callPriority: query.callPriority,
                followUpRequired:
                    query.followUpRequired === 'true'
                        ? true
                        : query.followUpRequired === 'false'
                          ? false
                          : undefined,
                quoteDiscussed:
                    query.quoteDiscussed === 'true'
                        ? true
                        : query.quoteDiscussed === 'false'
                          ? false
                          : undefined,
                startDate: query.startDate ? new Date(query.startDate) : undefined,
                endDate: query.endDate ? new Date(query.endDate) : undefined,
                staffMember: query.staffMember,
                clientEmail: query.clientEmail
            };

            const result = await this.quoteClientCallNoteService.getCallNotesPaginated(
                quoteId,
                page,
                limit,
                filters
            );

            // Format call notes with display names
            const formattedCallNotes = result.callNotes.map((callNote) => ({
                id: callNote.id,
                quoteId: callNote.quoteId,
                callType: callNote.callType,
                callTypeDisplayName: this.quoteClientCallNoteService.getCallTypeDisplayName(
                    callNote.callType
                ),
                callOutcome: callNote.callOutcome,
                callOutcomeDisplayName: this.quoteClientCallNoteService.getCallOutcomeDisplayName(
                    callNote.callOutcome
                ),
                callPriority: callNote.callPriority,
                callPriorityDisplayName: this.quoteClientCallNoteService.getCallPriorityDisplayName(
                    callNote.callPriority
                ),
                callDate: callNote.callDate,
                callDuration: callNote.callDuration,
                clientName: callNote.clientName,
                clientPhone: callNote.clientPhone,
                clientEmail: callNote.clientEmail,
                staffMember: callNote.staffMember,
                staffMemberName: callNote.staffMemberName,
                callSummary: callNote.callSummary,
                discussionPoints: callNote.discussionPoints,
                clientConcerns: callNote.clientConcerns,
                objectionsRaised: callNote.objectionsRaised,
                objectionsHandled: callNote.objectionsHandled,
                nextSteps: callNote.nextSteps,
                followUpRequired: callNote.followUpRequired,
                followUpDate: callNote.followUpDate,
                followUpAssignedTo: callNote.followUpAssignedTo,
                followUpAssignedToName: callNote.followUpAssignedToName,
                followUpNotes: callNote.followUpNotes,
                quoteRelated: callNote.quoteRelated,
                quoteDiscussed: callNote.quoteDiscussed,
                quoteFeedback: callNote.quoteFeedback,
                clientSatisfaction: callNote.clientSatisfaction,
                clientSatisfactionDescription: callNote.clientSatisfaction
                    ? this.quoteClientCallNoteService.getSatisfactionDescription(
                          callNote.clientSatisfaction
                      )
                    : undefined,
                callMetadata: callNote.callMetadata,
                isArchived: callNote.isArchived,
                archivedAt: callNote.archivedAt,
                archivedBy: callNote.archivedBy,
                createdBy: callNote.createdBy,
                createdAt: callNote.createdAt,
                updatedAt: callNote.updatedAt
            }));

            const response = {
                callNotes: formattedCallNotes,
                pagination: {
                    page: result.page,
                    limit,
                    total: result.total,
                    totalPages: result.totalPages,
                    hasNext: result.page < result.totalPages,
                    hasPrevious: result.page > 1
                },
                filters
            };

            this.logResponse('getClientCallNotesPaginated', {
                quoteId,
                total: result.total,
                page: result.page,
                totalPages: result.totalPages,
                callNoteCount: formattedCallNotes.length
            });

            return ApiResponseUtil.ok(
                response,
                'Paginated client call notes retrieved successfully'
            );
        } catch (error) {
            this.logError('getClientCallNotesPaginated', error);
            throw new HttpException(
                `Failed to get paginated client call notes: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Post(':quoteId/call-notes')
    async createClientCallNote(
        @Param('quoteId') quoteId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('createClientCallNote', { quoteId, callType: request.callType });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            const callNote = await this.quoteClientCallNoteService.createClientCallNote(
                {
                    ...request,
                    quoteId,
                    callDate: new Date(request.callDate),
                    followUpDate: request.followUpDate ? new Date(request.followUpDate) : undefined
                },
                currentUser.systemUserId,
                expressRequest
            );

            const formattedCallNote = {
                id: callNote.id,
                quoteId: callNote.quoteId,
                callType: callNote.callType,
                callTypeDisplayName: this.quoteClientCallNoteService.getCallTypeDisplayName(
                    callNote.callType
                ),
                callOutcome: callNote.callOutcome,
                callOutcomeDisplayName: this.quoteClientCallNoteService.getCallOutcomeDisplayName(
                    callNote.callOutcome
                ),
                callPriority: callNote.callPriority,
                callPriorityDisplayName: this.quoteClientCallNoteService.getCallPriorityDisplayName(
                    callNote.callPriority
                ),
                callDate: callNote.callDate,
                callDuration: callNote.callDuration,
                clientName: callNote.clientName,
                clientPhone: callNote.clientPhone,
                clientEmail: callNote.clientEmail,
                staffMember: callNote.staffMember,
                staffMemberName: callNote.staffMemberName,
                callSummary: callNote.callSummary,
                discussionPoints: callNote.discussionPoints,
                clientConcerns: callNote.clientConcerns,
                objectionsRaised: callNote.objectionsRaised,
                objectionsHandled: callNote.objectionsHandled,
                nextSteps: callNote.nextSteps,
                followUpRequired: callNote.followUpRequired,
                followUpDate: callNote.followUpDate,
                followUpAssignedTo: callNote.followUpAssignedTo,
                followUpAssignedToName: callNote.followUpAssignedToName,
                followUpNotes: callNote.followUpNotes,
                quoteRelated: callNote.quoteRelated,
                quoteDiscussed: callNote.quoteDiscussed,
                quoteFeedback: callNote.quoteFeedback,
                clientSatisfaction: callNote.clientSatisfaction,
                clientSatisfactionDescription: callNote.clientSatisfaction
                    ? this.quoteClientCallNoteService.getSatisfactionDescription(
                          callNote.clientSatisfaction
                      )
                    : undefined,
                callMetadata: callNote.callMetadata,
                isArchived: callNote.isArchived,
                archivedAt: callNote.archivedAt,
                archivedBy: callNote.archivedBy,
                createdBy: callNote.createdBy,
                createdAt: callNote.createdAt,
                updatedAt: callNote.updatedAt
            };

            this.logResponse('createClientCallNote', {
                quoteId,
                callNoteId: callNote.id,
                callType: callNote.callType,
                clientName: callNote.clientName
            });

            return ApiResponseUtil.created(
                formattedCallNote,
                'Client call note created successfully'
            );
        } catch (error) {
            this.logError('createClientCallNote', error);
            throw new HttpException(
                `Failed to create client call note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get('call-notes/:callNoteId')
    async getClientCallNoteById(@Param('callNoteId') callNoteId: string) {
        try {
            this.logRequest('getClientCallNoteById', { callNoteId });

            const callNote = await this.quoteClientCallNoteService.getCallNoteById(callNoteId);

            const formattedCallNote = {
                id: callNote.id,
                quoteId: callNote.quoteId,
                callType: callNote.callType,
                callTypeDisplayName: this.quoteClientCallNoteService.getCallTypeDisplayName(
                    callNote.callType
                ),
                callOutcome: callNote.callOutcome,
                callOutcomeDisplayName: this.quoteClientCallNoteService.getCallOutcomeDisplayName(
                    callNote.callOutcome
                ),
                callPriority: callNote.callPriority,
                callPriorityDisplayName: this.quoteClientCallNoteService.getCallPriorityDisplayName(
                    callNote.callPriority
                ),
                callDate: callNote.callDate,
                callDuration: callNote.callDuration,
                clientName: callNote.clientName,
                clientPhone: callNote.clientPhone,
                clientEmail: callNote.clientEmail,
                staffMember: callNote.staffMember,
                staffMemberName: callNote.staffMemberName,
                callSummary: callNote.callSummary,
                discussionPoints: callNote.discussionPoints,
                clientConcerns: callNote.clientConcerns,
                objectionsRaised: callNote.objectionsRaised,
                objectionsHandled: callNote.objectionsHandled,
                nextSteps: callNote.nextSteps,
                followUpRequired: callNote.followUpRequired,
                followUpDate: callNote.followUpDate,
                followUpAssignedTo: callNote.followUpAssignedTo,
                followUpAssignedToName: callNote.followUpAssignedToName,
                followUpNotes: callNote.followUpNotes,
                quoteRelated: callNote.quoteRelated,
                quoteDiscussed: callNote.quoteDiscussed,
                quoteFeedback: callNote.quoteFeedback,
                clientSatisfaction: callNote.clientSatisfaction,
                clientSatisfactionDescription: callNote.clientSatisfaction
                    ? this.quoteClientCallNoteService.getSatisfactionDescription(
                          callNote.clientSatisfaction
                      )
                    : undefined,
                callMetadata: callNote.callMetadata,
                isArchived: callNote.isArchived,
                archivedAt: callNote.archivedAt,
                archivedBy: callNote.archivedBy,
                createdBy: callNote.createdBy,
                createdAt: callNote.createdAt,
                updatedAt: callNote.updatedAt
            };

            this.logResponse('getClientCallNoteById', {
                callNoteId,
                callType: callNote.callType,
                clientName: callNote.clientName
            });

            return ApiResponseUtil.ok(formattedCallNote, 'Client call note retrieved successfully');
        } catch (error) {
            this.logError('getClientCallNoteById', error);
            throw new HttpException(
                `Failed to get client call note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('call-notes/:callNoteId')
    async updateClientCallNote(
        @Param('callNoteId') callNoteId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('updateClientCallNote', { callNoteId, updates: request });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            const updatedRequest = { ...request };
            if (request.callDate) {
                updatedRequest.callDate = new Date(request.callDate);
            }
            if (request.followUpDate) {
                updatedRequest.followUpDate = new Date(request.followUpDate);
            }

            const callNote = await this.quoteClientCallNoteService.updateCallNote(
                callNoteId,
                updatedRequest,
                currentUser.systemUserId,
                expressRequest
            );

            const formattedCallNote = {
                id: callNote.id,
                quoteId: callNote.quoteId,
                callType: callNote.callType,
                callTypeDisplayName: this.quoteClientCallNoteService.getCallTypeDisplayName(
                    callNote.callType
                ),
                callOutcome: callNote.callOutcome,
                callOutcomeDisplayName: this.quoteClientCallNoteService.getCallOutcomeDisplayName(
                    callNote.callOutcome
                ),
                callPriority: callNote.callPriority,
                callPriorityDisplayName: this.quoteClientCallNoteService.getCallPriorityDisplayName(
                    callNote.callPriority
                ),
                callDate: callNote.callDate,
                callDuration: callNote.callDuration,
                clientName: callNote.clientName,
                clientPhone: callNote.clientPhone,
                clientEmail: callNote.clientEmail,
                staffMember: callNote.staffMember,
                staffMemberName: callNote.staffMemberName,
                callSummary: callNote.callSummary,
                discussionPoints: callNote.discussionPoints,
                clientConcerns: callNote.clientConcerns,
                objectionsRaised: callNote.objectionsRaised,
                objectionsHandled: callNote.objectionsHandled,
                nextSteps: callNote.nextSteps,
                followUpRequired: callNote.followUpRequired,
                followUpDate: callNote.followUpDate,
                followUpAssignedTo: callNote.followUpAssignedTo,
                followUpAssignedToName: callNote.followUpAssignedToName,
                followUpNotes: callNote.followUpNotes,
                quoteRelated: callNote.quoteRelated,
                quoteDiscussed: callNote.quoteDiscussed,
                quoteFeedback: callNote.quoteFeedback,
                clientSatisfaction: callNote.clientSatisfaction,
                clientSatisfactionDescription: callNote.clientSatisfaction
                    ? this.quoteClientCallNoteService.getSatisfactionDescription(
                          callNote.clientSatisfaction
                      )
                    : undefined,
                callMetadata: callNote.callMetadata,
                isArchived: callNote.isArchived,
                archivedAt: callNote.archivedAt,
                archivedBy: callNote.archivedBy,
                createdBy: callNote.createdBy,
                createdAt: callNote.createdAt,
                updatedAt: callNote.updatedAt
            };

            this.logResponse('updateClientCallNote', {
                callNoteId,
                callType: callNote.callType,
                clientName: callNote.clientName
            });

            return ApiResponseUtil.ok(formattedCallNote, 'Client call note updated successfully');
        } catch (error) {
            this.logError('updateClientCallNote', error);
            throw new HttpException(
                `Failed to update client call note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('call-notes/:callNoteId/archive')
    async archiveClientCallNote(
        @Param('callNoteId') callNoteId: string,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('archiveClientCallNote', { callNoteId });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            await this.quoteClientCallNoteService.archiveCallNote(
                callNoteId,
                currentUser.systemUserId,
                expressRequest
            );

            this.logResponse('archiveClientCallNote', { callNoteId });

            return ApiResponseUtil.ok(null, 'Client call note archived successfully');
        } catch (error) {
            this.logError('archiveClientCallNote', error);
            throw new HttpException(
                `Failed to archive client call note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Delete('call-notes/:callNoteId')
    async deleteClientCallNote(
        @Param('callNoteId') callNoteId: string,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('deleteClientCallNote', { callNoteId });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            await this.quoteClientCallNoteService.deleteCallNote(
                callNoteId,
                currentUser.systemUserId,
                expressRequest
            );

            this.logResponse('deleteClientCallNote', { callNoteId });

            return ApiResponseUtil.ok(null, 'Client call note deleted successfully');
        } catch (error) {
            this.logError('deleteClientCallNote', error);
            throw new HttpException(
                `Failed to delete client call note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('call-notes/:callNoteId/follow-up')
    async setCallNoteFollowUp(
        @Param('callNoteId') callNoteId: string,
        @Body() request: any,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('setCallNoteFollowUp', {
                callNoteId,
                followUpDate: request.followUpDate
            });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            await this.quoteClientCallNoteService.setFollowUp(
                callNoteId,
                new Date(request.followUpDate),
                request.assignedTo,
                request.assignedToName,
                request.followUpNotes || '',
                expressRequest
            );

            this.logResponse('setCallNoteFollowUp', {
                callNoteId,
                followUpDate: request.followUpDate,
                assignedTo: request.assignedTo
            });

            return ApiResponseUtil.ok(null, 'Follow-up set successfully');
        } catch (error) {
            this.logError('setCallNoteFollowUp', error);
            throw new HttpException(
                `Failed to set follow-up: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Put('call-notes/:callNoteId/complete-follow-up')
    async completeCallNoteFollowUp(
        @Param('callNoteId') callNoteId: string,
        @Req() expressRequest: any
    ) {
        try {
            this.logRequest('completeCallNoteFollowUp', { callNoteId });

            const currentUser = expressRequest['user'];
            if (!currentUser || !currentUser.systemUserId) {
                throw new HttpException('User authentication required', HttpStatus.UNAUTHORIZED);
            }

            await this.quoteClientCallNoteService.completeFollowUp(
                callNoteId,
                currentUser.systemUserId,
                expressRequest
            );

            this.logResponse('completeCallNoteFollowUp', { callNoteId });

            return ApiResponseUtil.ok(null, 'Follow-up completed successfully');
        } catch (error) {
            this.logError('completeCallNoteFollowUp', error);
            throw new HttpException(
                `Failed to complete follow-up: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':quoteId/call-notes/stats')
    async getCallNotesStats(@Param('quoteId') quoteId: string) {
        try {
            this.logRequest('getCallNotesStats', { quoteId });

            const stats = await this.quoteClientCallNoteService.getCallNotesStats(quoteId);

            this.logResponse('getCallNotesStats', {
                quoteId,
                totalCalls: stats.totalCalls,
                followUpRequired: stats.followUpRequired,
                averageSatisfaction: stats.averageSatisfaction
            });

            return ApiResponseUtil.ok(stats, 'Call notes statistics retrieved successfully');
        } catch (error) {
            this.logError('getCallNotesStats', error);
            throw new HttpException(
                `Failed to get call notes statistics: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':quoteId/call-notes/search')
    async searchCallNotes(
        @Param('quoteId') quoteId: string,
        @Query('q') searchTerm: string,
        @Query('limit') limit?: number
    ) {
        try {
            this.logRequest('searchCallNotes', { quoteId, searchTerm, limit });

            const callNotes = await this.quoteClientCallNoteService.searchCallNotes(
                quoteId,
                searchTerm,
                limit || 20
            );

            // Format call notes with display names
            const formattedCallNotes = callNotes.map((callNote) => ({
                id: callNote.id,
                quoteId: callNote.quoteId,
                callType: callNote.callType,
                callTypeDisplayName: this.quoteClientCallNoteService.getCallTypeDisplayName(
                    callNote.callType
                ),
                callOutcome: callNote.callOutcome,
                callOutcomeDisplayName: this.quoteClientCallNoteService.getCallOutcomeDisplayName(
                    callNote.callOutcome
                ),
                callPriority: callNote.callPriority,
                callPriorityDisplayName: this.quoteClientCallNoteService.getCallPriorityDisplayName(
                    callNote.callPriority
                ),
                callDate: callNote.callDate,
                callDuration: callNote.callDuration,
                clientName: callNote.clientName,
                clientPhone: callNote.clientPhone,
                clientEmail: callNote.clientEmail,
                staffMember: callNote.staffMember,
                staffMemberName: callNote.staffMemberName,
                callSummary: callNote.callSummary,
                discussionPoints: callNote.discussionPoints,
                clientConcerns: callNote.clientConcerns,
                objectionsRaised: callNote.objectionsRaised,
                objectionsHandled: callNote.objectionsHandled,
                nextSteps: callNote.nextSteps,
                followUpRequired: callNote.followUpRequired,
                followUpDate: callNote.followUpDate,
                followUpAssignedTo: callNote.followUpAssignedTo,
                followUpAssignedToName: callNote.followUpAssignedToName,
                followUpNotes: callNote.followUpNotes,
                quoteRelated: callNote.quoteRelated,
                quoteDiscussed: callNote.quoteDiscussed,
                quoteFeedback: callNote.quoteFeedback,
                clientSatisfaction: callNote.clientSatisfaction,
                clientSatisfactionDescription: callNote.clientSatisfaction
                    ? this.quoteClientCallNoteService.getSatisfactionDescription(
                          callNote.clientSatisfaction
                      )
                    : undefined,
                callMetadata: callNote.callMetadata,
                isArchived: callNote.isArchived,
                archivedAt: callNote.archivedAt,
                archivedBy: callNote.archivedBy,
                createdBy: callNote.createdBy,
                createdAt: callNote.createdAt,
                updatedAt: callNote.updatedAt
            }));

            this.logResponse('searchCallNotes', {
                quoteId,
                searchTerm,
                resultCount: formattedCallNotes.length
            });

            return ApiResponseUtil.ok(
                formattedCallNotes,
                'Call notes search completed successfully'
            );
        } catch (error) {
            this.logError('searchCallNotes', error);
            throw new HttpException(
                `Failed to search call notes: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    // Audit Trail Endpoints
    @Get(':quoteId/audit-trail')
    async getQuoteAuditTrail(@Param('quoteId') quoteId: string, @Query('limit') limit?: number) {
        try {
            this.logRequest('getQuoteAuditTrail', { quoteId, limit });

            const auditTrail = await this.quoteAuditService.getQuoteAuditTrail(
                quoteId,
                limit || 100
            );

            this.logResponse('getQuoteAuditTrail', {
                quoteId,
                auditCount: auditTrail.length
            });

            return ApiResponseUtil.ok(auditTrail, 'Audit trail retrieved successfully');
        } catch (error) {
            this.logError('getQuoteAuditTrail', error);
            throw new HttpException(
                `Failed to get audit trail: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':quoteId/audit-trail/paginated')
    async getQuoteAuditTrailPaginated(
        @Param('quoteId') quoteId: string,
        @Query('page') page?: number,
        @Query('limit') limit?: number
    ) {
        try {
            this.logRequest('getQuoteAuditTrailPaginated', { quoteId, page, limit });

            const result = await this.quoteAuditService.getQuoteAuditTrailPaginated(
                quoteId,
                page || 1,
                limit || 20
            );

            this.logResponse('getQuoteAuditTrailPaginated', {
                quoteId,
                total: result.total,
                page: result.page,
                totalPages: result.totalPages,
                auditCount: result.audits.length
            });

            return ApiResponseUtil.ok(result, 'Paginated audit trail retrieved successfully');
        } catch (error) {
            this.logError('getQuoteAuditTrailPaginated', error);
            throw new HttpException(
                `Failed to get paginated audit trail: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @Get(':quoteId/audit-stats')
    async getQuoteAuditStats(@Param('quoteId') quoteId: string) {
        try {
            this.logRequest('getQuoteAuditStats', { quoteId });

            const stats = await this.quoteAuditService.getQuoteAuditStats(quoteId);

            this.logResponse('getQuoteAuditStats', {
                quoteId,
                totalActions: stats.totalActions,
                lastActivity: stats.lastActivity,
                actionCounts: stats.actionCounts
            });

            return ApiResponseUtil.ok(stats, 'Audit statistics retrieved successfully');
        } catch (error) {
            this.logError('getQuoteAuditStats', error);
            throw new HttpException(
                `Failed to get audit statistics: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    // Helper methods for logging
    private logRequest(method: string, data: any): void {
        Logger.log(`[QuoteController] ${method} request:`, JSON.stringify(data, null, 2));
    }

    private logResponse(method: string, data: any): void {
        Logger.log(`[QuoteController] ${method} response:`, JSON.stringify(data, null, 2));
    }

    private logError(method: string, error: any): void {
        Logger.error(`[QuoteController] ${method} error:`, error.message, error.stack);
    }
}
