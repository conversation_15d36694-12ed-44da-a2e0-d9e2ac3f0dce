import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Config } from '@app/common';
import { Logger } from '@nestjs/common';

async function bootstrap() {
    const app = await NestFactory.create(AppModule);
    const logger = new Logger('QuoteEngine');

    // Set global prefix
    app.setGlobalPrefix(Config.QUOTE_ENGINE_PREFIX);

    const port = Config.QUOTE_ENGINE_PORT || 3006;
    await app.listen(port);

    logger.log(`Quote Engine microservice running on ${await app.getUrl()}`);
}

bootstrap();
// Rebuild trigger: HealthAggregator fix in CommonModule - 20251014-022856
