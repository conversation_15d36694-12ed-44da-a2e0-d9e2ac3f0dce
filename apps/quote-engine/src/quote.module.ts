import { Module } from '@nestjs/common';
import { CommonModule, ConfigModule } from '@app/common';
import { QuoteCalculationService } from './services/quote-calculation.service';
import { QuotePersistenceService } from './services/quote-persistence.service';
import { QuoteAuditService } from './services/quote-audit.service';
import { QuoteAttachmentService } from './services/quote-attachment.service';
import { QuoteCommunicationService } from './services/quote-communication.service';
import { QuoteClientCallNoteService } from './services/quote-client-call-note.service';
import { RateCardManagementService } from './services/rate-card-management.service';
import { PromoCodeManagementService } from './services/promo-code-management.service';
import { QuoteController } from './controllers/quote.controller';
import { RateCardController } from './controllers/rate-card.controller';
import { quoteRepositories } from './repositories';
import { CaseServiceModule } from 'apps/case-management/src/case-service.module';

@Module({
    imports: [ConfigModule, CommonModule, CaseServiceModule],
    controllers: [QuoteController, RateCardController],
    providers: [
        QuoteCalculationService,
        QuotePersistenceService,
        QuoteAuditService,
        QuoteAttachmentService,
        QuoteCommunicationService,
        QuoteClientCallNoteService,
        RateCardManagementService,
        PromoCodeManagementService,
        ...quoteRepositories
    ],
    exports: [
        QuoteCalculationService,
        QuotePersistenceService,
        QuoteAuditService,
        QuoteAttachmentService,
        QuoteCommunicationService,
        QuoteClientCallNoteService,
        RateCardManagementService,
        PromoCodeManagementService,
        ...quoteRepositories
    ]
})
export class QuoteModule {}
