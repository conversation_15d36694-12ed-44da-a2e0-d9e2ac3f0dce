import { Module } from '@nestjs/common';
import { CommonModule, ConfigModule } from '@app/common';
import { ApiResponseModule } from '@app/common/api-response';
import { HealthModule } from './health/health.module';
import { QuoteModule } from './quote.module';

@Module({
    imports: [
        ConfigModule,
        CommonModule,
        ApiResponseModule.forRoot({
            excludePaths: ['/health', '/health/db', '/ping', '/metrics']
        }),
        HealthModule,
        QuoteModule
    ],
    controllers: [],
    providers: [],
    exports: []
})
export class AppModule {}
