import { Controller, Get } from '@nestjs/common';
import { ApiResponseUtil } from '@app/common/api-response';

@Controller('health')
export class HealthController {
    @Get()
    getHealth() {
        return ApiResponseUtil.ok(
            {
                service: 'quote-engine',
                status: 'healthy',
                timestamp: new Date().toISOString()
            },
            'Quote Engine service is healthy'
        );
    }

    @Get('ping')
    ping() {
        return { message: 'pong', service: 'quote-engine' };
    }
}
