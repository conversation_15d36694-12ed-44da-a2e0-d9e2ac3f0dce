import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { PromoCode, PromoCodeStatus } from '@app/common/typeorm/entities/tenant';

@Injectable()
export class PromoCodeRepository extends BaseTenantRepository<PromoCode> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(PromoCode, tenantContextService, tenantConnectionService);
    }

    /**
     * Find promo code by code
     */
    async findByCode(code: string): Promise<PromoCode | null> {
        return this.findOne({
            where: { code, status: PromoCodeStatus.ACTIVE }
        });
    }

    /**
     * Find active promo codes
     */
    async findActiveCodes(): Promise<PromoCode[]> {
        return this.find({
            where: { status: PromoCodeStatus.ACTIVE },
            order: { createdAt: 'ASC' }
        });
    }
}
