import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { FeeCategory } from '@app/common/typeorm/entities/tenant';

@Injectable()
export class FeeCategoryRepository extends BaseTenantRepository<FeeCategory> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(FeeCategory, tenantContextService, tenantConnectionService);
    }

    /**
     * Find active fee categories ordered by display order
     */
    async findActiveCategories(): Promise<FeeCategory[]> {
        return this.find({
            where: { active: true },
            order: { displayOrder: 'ASC' }
        });
    }
}
