import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { Quote, QuoteStatus } from '@app/common/typeorm/entities/tenant';

@Injectable()
export class QuoteRepository extends BaseTenantRepository<Quote> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(Quote, tenantContextService, tenantConnectionService);
    }

    /**
     * Find quote by number
     */
    async findByNumber(quoteNumber: string): Promise<Quote | null> {
        return this.findOne({
            where: { quoteNumber }
        });
    }

    /**
     * Find quotes by status
     */
    async findByStatus(
        status: QuoteStatus,
        limit: number = 50,
        offset: number = 0
    ): Promise<Quote[]> {
        return this.find({
            where: { status },
            order: { createdAt: 'DESC' },
            take: limit,
            skip: offset
        });
    }

    /**
     * Find quotes by email
     */
    async findByEmail(email: string): Promise<Quote[]> {
        const repository = await this.getTenantRepository();
        return repository
            .createQueryBuilder('quote')
            .where("quote.client_details->>'email' = :email", { email })
            .orderBy('quote.created_at', 'DESC')
            .getMany();
    }

    /**
     * Find quote by case ID
     */
    async findByCaseId(caseId: string): Promise<Quote | null> {
        return this.findOne({
            where: { caseId }
        });
    }
}
