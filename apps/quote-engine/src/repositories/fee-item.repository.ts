import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { FeeItem } from '@app/common/typeorm/entities/tenant';

@Injectable()
export class FeeItemRepository extends BaseTenantRepository<FeeItem> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(FeeItem, tenantContextService, tenantConnectionService);
    }

    /**
     * Find fee items by category ID
     */
    async findByCategoryId(categoryId: string): Promise<FeeItem[]> {
        return this.find({
            where: { categoryId },
            order: { createdAt: 'ASC' }
        });
    }

    /**
     * Find active fee items
     */
    async findActiveItems(): Promise<FeeItem[]> {
        return this.find({
            where: { active: true },
            order: { createdAt: 'ASC' }
        });
    }
}
