import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { RateCardFeeItem } from '@app/common/typeorm/entities/tenant/rate-card-fee-item.entity';

@Injectable()
export class RateCardFeeItemRepository extends BaseTenantRepository<RateCardFeeItem> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(RateCardFeeItem, tenantContextService, tenantConnectionService);
    }
    async findByRateCardId(rateCardId: string): Promise<RateCardFeeItem[]> {
        return await this.find({
            where: { rateCardId, active: true },
            order: { displayOrder: 'ASC', label: 'ASC' }
        });
    }

    async deleteByRateCardId(rateCardId: string): Promise<void> {
        const repository = await this.getTenantRepository();
        await repository.delete({ rateCardId });
    }

    async findByRateCardIdAndType(rateCardId: string, feeType: string): Promise<RateCardFeeItem[]> {
        return await this.find({
            where: { rateCardId, feeType: feeType as any, active: true },
            order: { displayOrder: 'ASC' }
        });
    }

    async findByRateCardIdAndApplicableFor(
        rateCardId: string,
        applicableFor: string
    ): Promise<RateCardFeeItem[]> {
        const repository = await this.getTenantRepository();
        return await repository
            .createQueryBuilder('feeItem')
            .where('feeItem.rateCardId = :rateCardId', { rateCardId })
            .andWhere('feeItem.active = :active', { active: true })
            .andWhere('feeItem.applicableFor LIKE :applicableFor', {
                applicableFor: `%${applicableFor}%`
            })
            .orderBy('feeItem.displayOrder', 'ASC')
            .getMany();
    }

    async findActiveByRateCardId(rateCardId: string): Promise<RateCardFeeItem[]> {
        return await this.find({
            where: { rateCardId, active: true },
            order: { displayOrder: 'ASC', label: 'ASC' }
        });
    }
}
