import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { QuoteAudit, QuoteAuditAction } from '@app/common/typeorm/entities/tenant';

@Injectable()
export class QuoteAuditRepository extends BaseTenantRepository<QuoteAudit> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(QuoteAudit, tenantContextService, tenantConnectionService);
    }

    /**
     * Find audit entries for a specific quote
     */
    async findByQuoteId(quoteId: string, limit: number = 50): Promise<QuoteAudit[]> {
        return this.find({
            where: {
                quoteId
            },
            order: {
                performedAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find audit entries by action type
     */
    async findByAction(action: QuoteAuditAction, limit: number = 50): Promise<QuoteAudit[]> {
        return this.find({
            where: {
                action
            },
            order: {
                performedAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find audit entries by user
     */
    async findByUser(performedBy: string, limit: number = 50): Promise<QuoteAudit[]> {
        return this.find({
            where: {
                performedBy
            },
            order: {
                performedAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Log an audit action
     */
    async logAction(
        quoteId: string,
        action: QuoteAuditAction,
        performedBy: string,
        performedByName?: string,
        ipAddress?: string,
        details?: Record<string, any>,
        userAgent?: string,
        sessionId?: string
    ): Promise<QuoteAudit> {
        const auditEntry = await this.create({
            quoteId,
            action,
            performedBy,
            performedByName,
            ipAddress,
            details,
            userAgent,
            sessionId,
            performedAt: new Date()
        });

        return this.save(auditEntry);
    }

    /**
     * Get audit trail for a quote with pagination
     */
    async getAuditTrail(
        quoteId: string,
        page: number = 1,
        limit: number = 20
    ): Promise<{ audits: QuoteAudit[]; total: number; page: number; totalPages: number }> {
        const offset = (page - 1) * limit;

        const [audits, total] = await Promise.all([
            this.find({
                where: { quoteId },
                order: { performedAt: 'DESC' },
                skip: offset,
                take: limit
            }),
            this.count({ where: { quoteId } })
        ]);

        const totalPages = Math.ceil(total / limit);

        return {
            audits,
            total,
            page,
            totalPages
        };
    }

    /**
     * Get recent activity across all quotes for a user
     */
    async getRecentActivityForUser(performedBy: string, limit: number = 20): Promise<QuoteAudit[]> {
        return this.find({
            where: {
                performedBy
            },
            order: {
                performedAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Get audit statistics for a quote
     */
    async getAuditStats(quoteId: string): Promise<{
        totalActions: number;
        lastActivity: Date | null;
        actionCounts: Record<string, number>;
    }> {
        const audits = await this.find({
            where: { quoteId },
            order: { performedAt: 'DESC' }
        });

        const actionCounts: Record<string, number> = {};
        audits.forEach((audit) => {
            actionCounts[audit.action] = (actionCounts[audit.action] || 0) + 1;
        });

        return {
            totalActions: audits.length,
            lastActivity: audits.length > 0 ? audits[0].performedAt : null,
            actionCounts
        };
    }
}
