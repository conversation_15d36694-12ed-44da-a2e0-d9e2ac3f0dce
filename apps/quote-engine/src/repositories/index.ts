import { FeeCategoryRepository } from './fee-category.repository';
import { FeeItemRepository } from './fee-item.repository';
import { PromoCodeRepository } from './promo-code.repository';
import { QuoteRepository } from './quote.repository';
import { QuoteAuditRepository } from './quote-audit.repository';
import { QuoteAttachmentRepository } from './quote-attachment.repository';
import { QuoteCommunicationRepository } from './quote-communication.repository';
import { QuoteClientCallNoteRepository } from './quote-client-call-note.repository';
import { RateCardRepository } from './rate-card.repository';
import { RateCardFeeItemRepository } from './rate-card-fee-item.repository';

export {
    FeeCategoryRepository,
    FeeItemRepository,
    PromoCodeRepository,
    QuoteRepository,
    QuoteAuditRepository,
    QuoteAttachmentRepository,
    QuoteCommunicationRepository,
    QuoteClientCallNoteRepository,
    RateCardRepository,
    RateCardFeeItemRepository
};

export const quoteRepositories = [
    FeeCategoryRepository,
    FeeItemRepository,
    PromoCodeRepository,
    QuoteRepository,
    QuoteAuditRepository,
    QuoteAttachmentRepository,
    QuoteCommunicationRepository,
    QuoteClientCallNoteRepository,
    RateCardRepository,
    RateCardFeeItemRepository
];
