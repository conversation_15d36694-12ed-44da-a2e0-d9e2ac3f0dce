import { Injectable, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>, Between } from 'typeorm';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import {
    QuoteClientCallNote,
    CallType,
    CallOutcome,
    CallPriority
} from '@app/common/typeorm/entities/tenant';

@Injectable()
export class QuoteClientCallNoteRepository extends BaseTenantRepository<QuoteClientCallNote> {
    logger = new Logger(QuoteClientCallNoteRepository.name);

    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(QuoteClientCallNote, tenantContextService, tenantConnectionService);
    }

    /**
     * Find call notes for a specific quote
     */
    async findByQuoteId(quoteId: string, limit: number = 50): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                quoteId,
                isArchived: false
            },
            order: {
                callDate: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find call notes by call type
     */
    async findByCallType(callType: CallType, limit: number = 50): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                callType,
                isArchived: false
            },
            order: {
                callDate: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find call notes by call outcome
     */
    async findByCallOutcome(
        callOutcome: CallOutcome,
        limit: number = 50
    ): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                callOutcome,
                isArchived: false
            },
            order: {
                callDate: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find call notes by staff member
     */
    async findByStaffMember(
        staffMember: string,
        limit: number = 50
    ): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                staffMember,
                isArchived: false
            },
            order: {
                callDate: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find call notes by client
     */
    async findByClient(clientEmail: string, limit: number = 50): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                clientEmail,
                isArchived: false
            },
            order: {
                callDate: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find call notes requiring follow-up
     */
    async findFollowUpRequired(): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                followUpRequired: true,
                isArchived: false
            },
            order: {
                followUpDate: 'ASC'
            }
        });
    }

    /**
     * Find overdue follow-ups
     */
    async findOverdueFollowUps(): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                followUpRequired: true,
                followUpDate: LessThan(new Date()),
                isArchived: false
            },
            order: {
                followUpDate: 'ASC'
            }
        });
    }

    /**
     * Find call notes by priority
     */
    async findByPriority(
        priority: CallPriority,
        limit: number = 50
    ): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                callPriority: priority,
                isArchived: false
            },
            order: {
                callDate: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find call notes within date range
     */
    async findByDateRange(
        startDate: Date,
        endDate: Date,
        limit: number = 100
    ): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                callDate: Between(startDate, endDate),
                isArchived: false
            },
            order: {
                callDate: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find call notes where quote was discussed
     */
    async findQuoteDiscussed(limit: number = 50): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                quoteDiscussed: true,
                isArchived: false
            },
            order: {
                callDate: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find call notes with low client satisfaction
     */
    async findLowSatisfaction(
        maxRating: number = 3,
        limit: number = 50
    ): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                clientSatisfaction: LessThan(maxRating),
                isArchived: false
            },
            order: {
                callDate: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Get call notes with pagination
     */
    async getCallNotesPaginated(
        quoteId: string,
        page: number = 1,
        limit: number = 20,
        filters?: {
            callType?: CallType;
            callOutcome?: CallOutcome;
            callPriority?: CallPriority;
            followUpRequired?: boolean;
            quoteDiscussed?: boolean;
            startDate?: Date;
            endDate?: Date;
        }
    ): Promise<{
        callNotes: QuoteClientCallNote[];
        total: number;
        page: number;
        totalPages: number;
    }> {
        const offset = (page - 1) * limit;

        const whereConditions: any = {
            quoteId,
            isArchived: false
        };

        if (filters) {
            if (filters.callType) whereConditions.callType = filters.callType;
            if (filters.callOutcome) whereConditions.callOutcome = filters.callOutcome;
            if (filters.callPriority) whereConditions.callPriority = filters.callPriority;
            if (filters.followUpRequired !== undefined)
                whereConditions.followUpRequired = filters.followUpRequired;
            if (filters.quoteDiscussed !== undefined)
                whereConditions.quoteDiscussed = filters.quoteDiscussed;
            if (filters.startDate || filters.endDate) {
                const start = filters.startDate || new Date('1900-01-01');
                const end = filters.endDate || new Date();
                whereConditions.callDate = Between(start, end);
            }
        }

        const [callNotes, total] = await Promise.all([
            this.find({
                where: whereConditions,
                order: { callDate: 'DESC' },
                skip: offset,
                take: limit
            }),
            this.count({ where: whereConditions })
        ]);

        const totalPages = Math.ceil(total / limit);

        return {
            callNotes,
            total,
            page,
            totalPages
        };
    }

    /**
     * Get call notes statistics for a quote
     */
    async getCallNotesStats(quoteId: string): Promise<{
        totalCalls: number;
        followUpRequired: number;
        overdueFollowUps: number;
        averageSatisfaction: number;
        callTypeCounts: Record<string, number>;
        callOutcomeCounts: Record<string, number>;
        priorityCounts: Record<string, number>;
        lastCallDate: Date | null;
        totalCallDuration: number;
        averageCallDuration: number;
    }> {
        const callNotes = await this.findByQuoteId(quoteId, 1000); // Get more for stats

        const stats = {
            totalCalls: callNotes.length,
            followUpRequired: callNotes.filter((c) => c.followUpRequired).length,
            overdueFollowUps: callNotes.filter(
                (c) => c.followUpRequired && c.followUpDate && c.followUpDate < new Date()
            ).length,
            averageSatisfaction: 0,
            callTypeCounts: {} as Record<string, number>,
            callOutcomeCounts: {} as Record<string, number>,
            priorityCounts: {} as Record<string, number>,
            lastCallDate: callNotes.length > 0 ? callNotes[0].callDate : null,
            totalCallDuration: callNotes.reduce((sum, c) => sum + (c.callDuration || 0), 0),
            averageCallDuration: 0
        };

        // Calculate average satisfaction
        const satisfactionRatings = callNotes
            .filter((c) => c.clientSatisfaction !== null)
            .map((c) => c.clientSatisfaction);
        if (satisfactionRatings.length > 0) {
            stats.averageSatisfaction =
                satisfactionRatings.reduce((sum, rating) => sum + rating, 0) /
                satisfactionRatings.length;
        }

        // Calculate average call duration
        const callDurations = callNotes
            .filter((c) => c.callDuration !== null)
            .map((c) => c.callDuration);
        if (callDurations.length > 0) {
            stats.averageCallDuration =
                callDurations.reduce((sum, duration) => sum + duration, 0) / callDurations.length;
        }

        // Count by call type, outcome, and priority
        callNotes.forEach((callNote) => {
            const callType = callNote.callType;
            const callOutcome = callNote.callOutcome;
            const priority = callNote.callPriority;

            stats.callTypeCounts[callType] = (stats.callTypeCounts[callType] || 0) + 1;
            stats.callOutcomeCounts[callOutcome] = (stats.callOutcomeCounts[callOutcome] || 0) + 1;
            stats.priorityCounts[priority] = (stats.priorityCounts[priority] || 0) + 1;
        });

        return stats;
    }

    /**
     * Archive call note
     */
    async archiveCallNote(callNoteId: string, archivedBy: string): Promise<QuoteClientCallNote> {
        const callNote = await this.findOne({
            where: { id: callNoteId }
        });

        if (!callNote) {
            throw new Error('Call note not found');
        }

        callNote.isArchived = true;
        callNote.archivedAt = new Date();
        callNote.archivedBy = archivedBy;
        callNote.updatedAt = new Date();

        return this.save(callNote);
    }

    /**
     * Set follow-up for call note
     */
    async setFollowUp(
        callNoteId: string,
        followUpDate: Date,
        assignedTo: string,
        assignedToName: string,
        followUpNotes?: string
    ): Promise<QuoteClientCallNote> {
        const callNote = await this.findOne({
            where: { id: callNoteId }
        });

        if (!callNote) {
            throw new Error('Call note not found');
        }

        callNote.followUpRequired = true;
        callNote.followUpDate = followUpDate;
        callNote.followUpAssignedTo = assignedTo;
        callNote.followUpAssignedToName = assignedToName;
        if (followUpNotes) {
            callNote.followUpNotes = followUpNotes;
        }
        callNote.updatedAt = new Date();

        return this.save(callNote);
    }

    /**
     * Complete follow-up
     */
    async completeFollowUp(callNoteId: string): Promise<QuoteClientCallNote> {
        const callNote = await this.findOne({
            where: { id: callNoteId }
        });

        if (!callNote) {
            throw new Error('Call note not found');
        }

        callNote.followUpRequired = false;
        callNote.followUpDate = new Date();
        callNote.followUpAssignedTo = '';
        callNote.followUpAssignedToName = '';
        callNote.followUpNotes = '';
        callNote.updatedAt = new Date();

        return this.save(callNote);
    }

    /**
     * Search call notes by content
     */
    async searchCallNotes(
        quoteId: string,
        searchTerm: string,
        limit: number = 20
    ): Promise<QuoteClientCallNote[]> {
        // Using raw query for better text search performance
        const query = `
            SELECT * FROM quote_client_call_notes 
            WHERE quote_id = $1 
            AND is_archived = false
            AND (
                call_summary ILIKE $2 
                OR discussion_points ILIKE $2 
                OR client_concerns ILIKE $2 
                OR objections_raised ILIKE $2 
                OR objections_handled ILIKE $2 
                OR next_steps ILIKE $2 
                OR quote_feedback ILIKE $2
                OR client_name ILIKE $2
                OR staff_member_name ILIKE $2
            )
            ORDER BY call_date DESC
            LIMIT $3
        `;

        return this.query(query, [quoteId, `%${searchTerm}%`, limit]);
    }

    /**
     * Get recent call activity for a staff member
     */
    async getRecentCallsForStaff(
        staffMember: string,
        limit: number = 20
    ): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                staffMember,
                isArchived: false
            },
            order: {
                callDate: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Get call notes by client across all quotes
     */
    async getCallsByClient(
        clientEmail: string,
        limit: number = 50
    ): Promise<QuoteClientCallNote[]> {
        return this.find({
            where: {
                clientEmail,
                isArchived: false
            },
            order: {
                callDate: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Update call note
     */
    async updateCallNote(
        callNoteId: string,
        updates: Partial<QuoteClientCallNote>
    ): Promise<QuoteClientCallNote> {
        const callNote = await this.findOne({
            where: { id: callNoteId }
        });

        if (!callNote) {
            throw new Error('Call note not found');
        }

        Object.assign(callNote, updates);
        callNote.updatedAt = new Date();

        return this.save(callNote);
    }

    /**
     * Delete call note
     */
    async deleteCallNote(callNoteId: string): Promise<void> {
        const callNote = await this.findOne({
            where: { id: callNoteId }
        });

        if (!callNote) {
            throw new Error('Call note not found');
        }

        await this.remove(callNote);
    }

    /**
     * Delete all call notes for a quote
     */
    async deleteByQuoteId(quoteId: string): Promise<void> {
        const callNotes = await this.findByQuoteId(quoteId);
        Logger.log(`Deleting ${callNotes.length} call notes for quote ${quoteId}`);
        for (const callNote of callNotes) {
            await this.remove(callNote);
        }
    }
}
