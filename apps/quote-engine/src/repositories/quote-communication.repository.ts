import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>, Between } from 'typeorm';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import {
    QuoteCommunication,
    CommunicationType,
    CommunicationDirection,
    CommunicationPriority
} from '@app/common/typeorm/entities/tenant';

@Injectable()
export class QuoteCommunicationRepository extends BaseTenantRepository<QuoteCommunication> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(QuoteCommunication, tenantContextService, tenantConnectionService);
    }

    /**
     * Find communications for a specific quote
     */
    async findByQuoteId(quoteId: string, limit: number = 50): Promise<QuoteCommunication[]> {
        return this.find({
            where: {
                quoteId,
                isArchived: false
            },
            order: {
                sentAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find communications by type
     */
    async findByType(type: CommunicationType, limit: number = 50): Promise<QuoteCommunication[]> {
        return this.find({
            where: {
                type,
                isArchived: false
            },
            order: {
                sentAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find communications by direction
     */
    async findByDirection(
        direction: CommunicationDirection,
        limit: number = 50
    ): Promise<QuoteCommunication[]> {
        return this.find({
            where: {
                direction,
                isArchived: false
            },
            order: {
                sentAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find communications by sender
     */
    async findBySender(sender: string, limit: number = 50): Promise<QuoteCommunication[]> {
        return this.find({
            where: {
                sender,
                isArchived: false
            },
            order: {
                sentAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find communications by recipient
     */
    async findByRecipient(recipient: string, limit: number = 50): Promise<QuoteCommunication[]> {
        return this.find({
            where: {
                recipient,
                isArchived: false
            },
            order: {
                sentAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find unread communications for a user
     */
    async findUnreadForUser(userId: string): Promise<QuoteCommunication[]> {
        return this.find({
            where: {
                recipient: userId,
                isRead: false,
                isArchived: false
            },
            order: {
                sentAt: 'DESC'
            }
        });
    }

    /**
     * Find communications requiring follow-up
     */
    async findFollowUpRequired(): Promise<QuoteCommunication[]> {
        return this.find({
            where: {
                followUpRequired: true,
                isArchived: false
            },
            order: {
                followUpDate: 'ASC'
            }
        });
    }

    /**
     * Find overdue follow-ups
     */
    async findOverdueFollowUps(): Promise<QuoteCommunication[]> {
        return this.find({
            where: {
                followUpRequired: true,
                followUpDate: LessThan(new Date()),
                isArchived: false
            },
            order: {
                followUpDate: 'ASC'
            }
        });
    }

    /**
     * Find communications by priority
     */
    async findByPriority(
        priority: CommunicationPriority,
        limit: number = 50
    ): Promise<QuoteCommunication[]> {
        return this.find({
            where: {
                priority,
                isArchived: false
            },
            order: {
                sentAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Find communications within date range
     */
    async findByDateRange(
        startDate: Date,
        endDate: Date,
        limit: number = 100
    ): Promise<QuoteCommunication[]> {
        return this.find({
            where: {
                sentAt: Between(startDate, endDate),
                isArchived: false
            },
            order: {
                sentAt: 'DESC'
            },
            take: limit
        });
    }

    /**
     * Get communications with pagination
     */
    async getCommunicationsPaginated(
        quoteId: string,
        page: number = 1,
        limit: number = 20,
        filters?: {
            type?: CommunicationType;
            direction?: CommunicationDirection;
            priority?: CommunicationPriority;
            isRead?: boolean;
            followUpRequired?: boolean;
        }
    ): Promise<{
        communications: QuoteCommunication[];
        total: number;
        page: number;
        totalPages: number;
    }> {
        const offset = (page - 1) * limit;

        const whereConditions: any = {
            quoteId,
            isArchived: false
        };

        if (filters) {
            if (filters.type) whereConditions.type = filters.type;
            if (filters.direction) whereConditions.direction = filters.direction;
            if (filters.priority) whereConditions.priority = filters.priority;
            if (filters.isRead !== undefined) whereConditions.isRead = filters.isRead;
            if (filters.followUpRequired !== undefined)
                whereConditions.followUpRequired = filters.followUpRequired;
        }

        const [communications, total] = await Promise.all([
            this.find({
                where: whereConditions,
                order: { sentAt: 'DESC' },
                skip: offset,
                take: limit
            }),
            this.count({ where: whereConditions })
        ]);

        const totalPages = Math.ceil(total / limit);

        return {
            communications,
            total,
            page,
            totalPages
        };
    }

    /**
     * Get communication statistics for a quote
     */
    async getCommunicationStats(quoteId: string): Promise<{
        totalCommunications: number;
        unreadCommunications: number;
        followUpRequired: number;
        overdueFollowUps: number;
        typeCounts: Record<string, number>;
        directionCounts: Record<string, number>;
        priorityCounts: Record<string, number>;
        lastCommunication: Date | null;
    }> {
        const communications = await this.findByQuoteId(quoteId, 1000); // Get more for stats

        const stats = {
            totalCommunications: communications.length,
            unreadCommunications: communications.filter((c) => !c.isRead).length,
            followUpRequired: communications.filter((c) => c.followUpRequired).length,
            overdueFollowUps: communications.filter(
                (c) => c.followUpRequired && c.followUpDate && c.followUpDate < new Date()
            ).length,
            typeCounts: {} as Record<string, number>,
            directionCounts: {} as Record<string, number>,
            priorityCounts: {} as Record<string, number>,
            lastCommunication: communications.length > 0 ? communications[0].sentAt : null
        };

        // Count by type, direction, and priority
        communications.forEach((communication) => {
            const type = communication.type;
            const direction = communication.direction;
            const priority = communication.priority;

            stats.typeCounts[type] = (stats.typeCounts[type] || 0) + 1;
            stats.directionCounts[direction] = (stats.directionCounts[direction] || 0) + 1;
            stats.priorityCounts[priority] = (stats.priorityCounts[priority] || 0) + 1;
        });

        return stats;
    }

    /**
     * Mark communication as read
     */
    async markAsRead(
        communicationId: string,
        readBy: string,
        readByName: string
    ): Promise<QuoteCommunication> {
        const communication = await this.findOne({
            where: { id: communicationId }
        });

        if (!communication) {
            throw new Error('Communication not found');
        }

        communication.isRead = true;
        communication.readAt = new Date();
        communication.readBy = readBy;
        communication.readByName = readByName;
        communication.updatedAt = new Date();

        return this.save(communication);
    }

    /**
     * Archive communication
     */
    async archiveCommunication(
        communicationId: string,
        archivedBy: string
    ): Promise<QuoteCommunication> {
        const communication = await this.findOne({
            where: { id: communicationId }
        });

        if (!communication) {
            throw new Error('Communication not found');
        }

        communication.isArchived = true;
        communication.archivedAt = new Date();
        communication.archivedBy = archivedBy;
        communication.updatedAt = new Date();

        return this.save(communication);
    }

    /**
     * Set follow-up for communication
     */
    async setFollowUp(
        communicationId: string,
        followUpDate: Date,
        assignedTo: string,
        assignedToName: string
    ): Promise<QuoteCommunication> {
        const communication = await this.findOne({
            where: { id: communicationId }
        });

        if (!communication) {
            throw new Error('Communication not found');
        }

        communication.followUpRequired = true;
        communication.followUpDate = followUpDate;
        communication.followUpAssignedTo = assignedTo;
        communication.followUpAssignedToName = assignedToName;
        communication.updatedAt = new Date();

        return this.save(communication);
    }

    /**
     * Complete follow-up
     */
    async completeFollowUp(communicationId: string): Promise<QuoteCommunication> {
        const communication = await this.findOne({
            where: { id: communicationId }
        });

        if (!communication) {
            throw new Error('Communication not found');
        }

        communication.followUpRequired = false;
        communication.followUpDate = new Date();
        communication.followUpAssignedTo = '';
        communication.followUpAssignedToName = '';
        communication.updatedAt = new Date();

        return this.save(communication);
    }

    /**
     * Search communications by content
     */
    async searchCommunications(
        quoteId: string,
        searchTerm: string,
        limit: number = 20
    ): Promise<QuoteCommunication[]> {
        // Using raw query for better text search performance
        const query = `
            SELECT * FROM quote_communications 
            WHERE quote_id = $1 
            AND is_archived = false
            AND (
                subject ILIKE $2 
                OR body ILIKE $2 
                OR sender_name ILIKE $2 
                OR recipient_name ILIKE $2
            )
            ORDER BY sent_at DESC
            LIMIT $3
        `;

        return this.query(query, [quoteId, `%${searchTerm}%`, limit]);
    }

    /**
     * Get recent activity across all quotes for a user
     */
    async getRecentActivityForUser(
        userId: string,
        limit: number = 20
    ): Promise<QuoteCommunication[]> {
        return this.find({
            where: [{ sender: userId }, { recipient: userId }],
            order: {
                sentAt: 'DESC'
            },
            take: limit
        });
    }
}
