import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { RateCard, RateCardStatus } from '@app/common/typeorm/entities/tenant/rate-card.entity';

@Injectable()
export class RateCardRepository extends BaseTenantRepository<RateCard> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(RateCard, tenantContextService, tenantConnectionService);
    }
    async findByProvider(providerName: string): Promise<RateCard | null> {
        return await this.findOne({
            where: { providerName, status: RateCardStatus.ACTIVE }
        });
    }

    async findDefault(): Promise<RateCard | null> {
        return await this.findOne({
            where: { isDefault: true, status: RateCardStatus.ACTIVE }
        });
    }

    async clearDefault(): Promise<void> {
        const repository = await this.getTenantRepository();
        await repository.update({ isDefault: true }, { isDefault: false });
    }

    async findByStatus(status: RateCardStatus): Promise<RateCard[]> {
        return await this.find({
            where: { status },
            order: { priority: 'ASC', displayName: 'ASC' }
        });
    }

    async findActiveRateCards(): Promise<RateCard[]> {
        return await this.find({
            where: { status: RateCardStatus.ACTIVE },
            order: { priority: 'ASC', displayName: 'ASC' }
        });
    }
}
