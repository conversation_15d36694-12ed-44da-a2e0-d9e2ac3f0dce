import { Injectable, Logger } from '@nestjs/common';
import { <PERSON><PERSON>han, Between } from 'typeorm';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { QuoteAttachment, QuoteAttachmentType } from '@app/common/typeorm/entities/tenant';

@Injectable()
export class QuoteAttachmentRepository extends BaseTenantRepository<QuoteAttachment> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(QuoteAttachment, tenantContextService, tenantConnectionService);
    }

    /**
     * Find attachments for a specific quote
     */
    async findByQuoteId(quoteId: string): Promise<QuoteAttachment[]> {
        return this.find({
            where: {
                quoteId
            },
            order: {
                uploadedAt: 'DESC'
            }
        });
    }

    /**
     * Find attachments by type
     */
    async findByAttachmentType(attachmentType: QuoteAttachmentType): Promise<QuoteAttachment[]> {
        return this.find({
            where: {
                attachmentType
            },
            order: {
                uploadedAt: 'DESC'
            }
        });
    }

    /**
     * Find public attachments for a quote (visible to clients)
     */
    async findPublicByQuoteId(quoteId: string): Promise<QuoteAttachment[]> {
        return this.find({
            where: {
                quoteId,
                isPublic: true
            },
            order: {
                uploadedAt: 'DESC'
            }
        });
    }

    /**
     * Find required attachments for a quote
     */
    async findRequiredByQuoteId(quoteId: string): Promise<QuoteAttachment[]> {
        return this.find({
            where: {
                quoteId,
                isRequired: true
            },
            order: {
                uploadedAt: 'DESC'
            }
        });
    }

    /**
     * Find attachments by uploader
     */
    async findByUploader(uploadedBy: string): Promise<QuoteAttachment[]> {
        return this.find({
            where: {
                uploadedBy
            },
            order: {
                uploadedAt: 'DESC'
            }
        });
    }

    /**
     * Find expired attachments
     */
    async findExpired(): Promise<QuoteAttachment[]> {
        return this.find({
            where: {
                expiresAt: LessThan(new Date())
            },
            order: {
                expiresAt: 'ASC'
            }
        });
    }

    /**
     * Find attachments expiring soon (within specified days)
     */
    async findExpiringSoon(days: number = 7): Promise<QuoteAttachment[]> {
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + days);

        return this.find({
            where: {
                expiresAt: Between(new Date(), futureDate)
            },
            order: {
                expiresAt: 'ASC'
            }
        });
    }

    /**
     * Get attachment statistics for a quote
     */
    async getAttachmentStats(quoteId: string): Promise<{
        totalAttachments: number;
        publicAttachments: number;
        requiredAttachments: number;
        expiredAttachments: number;
        totalFileSize: number;
        attachmentTypeCounts: Record<string, number>;
    }> {
        const attachments = await this.findByQuoteId(quoteId);

        const stats = {
            totalAttachments: attachments.length,
            publicAttachments: attachments.filter((a) => a.isPublic).length,
            requiredAttachments: attachments.filter((a) => a.isRequired).length,
            expiredAttachments: attachments.filter((a) => a.expiresAt && a.expiresAt < new Date())
                .length,
            totalFileSize: attachments.reduce((sum, a) => sum + (a.fileSize || 0), 0),
            attachmentTypeCounts: {} as Record<string, number>
        };

        // Count by attachment type
        attachments.forEach((attachment) => {
            const type = attachment.attachmentType;
            stats.attachmentTypeCounts[type] = (stats.attachmentTypeCounts[type] || 0) + 1;
        });

        return stats;
    }

    /**
     * Find attachments by document ID (from document microservice)
     */
    async findByDocumentId(documentId: string): Promise<QuoteAttachment | null> {
        return this.findOne({
            where: {
                documentId
            }
        });
    }

    /**
     * Update attachment metadata
     */
    async updateAttachmentMetadata(
        attachmentId: string,
        updates: Partial<QuoteAttachment>
    ): Promise<QuoteAttachment> {
        const attachment = await this.findOne({
            where: { id: attachmentId }
        });

        if (!attachment) {
            throw new Error('Attachment not found');
        }

        Object.assign(attachment, updates);
        attachment.updatedAt = new Date();

        return this.save(attachment);
    }

    /**
     * Delete attachment by ID
     */
    async deleteAttachment(attachmentId: string): Promise<void> {
        const attachment = await this.findOne({
            where: { id: attachmentId }
        });

        if (!attachment) {
            throw new Error('Attachment not found');
        }

        await this.remove(attachment);
    }

    /**
     * Delete all attachments for a quote
     */
    async deleteByQuoteId(quoteId: string): Promise<void> {
        const attachments = await this.findByQuoteId(quoteId);
        Logger.log(`Deleting ${attachments.length} attachments for quote ${quoteId}`);
        for (const attachment of attachments) {
            await this.remove(attachment);
        }
    }
}
