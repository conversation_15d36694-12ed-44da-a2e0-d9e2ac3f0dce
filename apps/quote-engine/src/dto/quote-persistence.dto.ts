import {
    IsString,
    IsEmail,
    IsO<PERSON>al,
    IsNumber,
    IsEnum,
    ValidateNested,
    IsDateString,
    IsInt,
    Min,
    Max
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { QuoteRequestDto } from './quote-request.dto';
import { QuoteStatus } from '@app/common/typeorm/entities/tenant';

export class ContactDetailsDto {
    @IsEmail()
    email: string;

    @IsString()
    phone: string;

    @IsString()
    name: string;
}

export class SimpleSaveQuoteAsLeadDto {
    @IsString()
    quoteReference: string;

    @IsString()
    clientName: string;

    @IsEmail()
    clientEmail: string;

    @IsString()
    clientPhone: string;

    @IsString()
    propertyAddress: string;

    @IsNumber()
    propertyValue: number;

    @IsString()
    transactionType: string;

    @IsNumber()
    quoteTotal: number;

    @IsString()
    rateCardProvider: string;

    @IsOptional()
    @IsString()
    additionalNotes?: string;
}

export class SaveQuoteAsLeadDto {
    @ValidateNested()
    @Type(() => QuoteRequestDto)
    quoteRequest: QuoteRequestDto;

    @IsOptional()
    @IsString()
    leadSource?: string;

    @ValidateNested()
    @Type(() => ContactDetailsDto)
    contactDetails: ContactDetailsDto;

    @IsOptional()
    @IsString()
    additionalNotes?: string;
}

export class ConvertQuoteToCaseDto {
    @IsString()
    quoteId: string;

    @IsString()
    @IsOptional()
    caseType?: string;

    @IsString()
    @IsOptional()
    priority?: string;

    @IsString()
    @IsOptional()
    assignedTo?: string;

    @IsString()
    @IsOptional()
    expectedCompletionDate?: string;

    @IsString()
    @IsOptional()
    caseNotes?: string;
}

export class QuoteStatusUpdateDto {
    @IsEnum(QuoteStatus)
    status: QuoteStatus;
}

export class PaginatedQuotesQueryDto {
    @IsOptional()
    @IsInt()
    @Min(1)
    @Max(100)
    @Transform(({ value }) => parseInt(value))
    page?: number = 1;

    @IsOptional()
    @IsInt()
    @Min(1)
    @Max(100)
    @Transform(({ value }) => parseInt(value))
    limit?: number = 20;

    @IsOptional()
    @IsEnum(QuoteStatus)
    status?: QuoteStatus;

    @IsOptional()
    @IsDateString()
    startDate?: string;

    @IsOptional()
    @IsDateString()
    endDate?: string;

    @IsOptional()
    @IsString()
    search?: string; // For searching by quote number, client name, or email
}

export class PaginatedQuotesResponseDto {
    quotes: any[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrevious: boolean;
    };
    filters: {
        status?: QuoteStatus;
        startDate?: string;
        endDate?: string;
        search?: string;
    };
}

export class LeadConversionResult {
    caseId: string;
    caseNumber: string;
    quoteId: string;
    assignedSolicitor?: string;
    nextSteps: string[];
}
