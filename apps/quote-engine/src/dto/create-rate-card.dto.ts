import {
    IsString,
    IsEnum,
    IsOptional,
    IsDate,
    IsNumber,
    IsBoolean,
    IsObject
} from 'class-validator';
import { RateCardProvider } from '@app/common/typeorm/entities/tenant/rate-card.entity';

export class CreateRateCardDto {
    @IsString()
    providerName: string;

    @IsEnum(RateCardProvider)
    providerCode: RateCardProvider;

    @IsString()
    displayName: string;

    @IsOptional()
    @IsString()
    description?: string;

    @IsString()
    version: string;

    @IsDate()
    effectiveDate: Date;

    @IsOptional()
    @IsDate()
    expiryDate?: Date;

    @IsNumber()
    priority: number;

    @IsBoolean()
    isDefault: boolean;

    @IsOptional()
    @IsObject()
    metadata?: Record<string, any>;

    @IsString()
    tenantId: string;
}
