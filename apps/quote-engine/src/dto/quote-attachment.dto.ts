import {
    IsString,
    IsOptional,
    IsN<PERSON>ber,
    IsEnum,
    IsBoolean,
    IsDateString,
    IsObject
} from 'class-validator';
import { QuoteAttachmentType } from '@app/common/typeorm/entities/tenant';

export class CreateAttachmentDto {
    @IsString()
    quoteId: string;

    @IsString()
    filename: string;

    @IsString()
    url: string;

    @IsOptional()
    @IsNumber()
    fileSize?: number;

    @IsOptional()
    @IsString()
    mimeType?: string;

    @IsOptional()
    @IsString()
    description?: string;

    @IsEnum(QuoteAttachmentType)
    attachmentType: QuoteAttachmentType;

    @IsOptional()
    @IsBoolean()
    isPublic?: boolean;

    @IsOptional()
    @IsBoolean()
    isRequired?: boolean;

    @IsOptional()
    @IsDateString()
    expiresAt?: string;

    @IsOptional()
    @IsObject()
    metadata?: Record<string, any>;
}

export class UpdateAttachmentDto {
    @IsOptional()
    @IsString()
    filename?: string;

    @IsOptional()
    @IsString()
    description?: string;

    @IsOptional()
    @IsEnum(QuoteAttachmentType)
    attachmentType?: QuoteAttachmentType;

    @IsOptional()
    @IsBoolean()
    isPublic?: boolean;

    @IsOptional()
    @IsBoolean()
    isRequired?: boolean;

    @IsOptional()
    @IsDateString()
    expiresAt?: string;

    @IsOptional()
    @IsObject()
    metadata?: Record<string, any>;
}

export class AttachmentResponseDto {
    id: string;
    quoteId: string;
    documentId?: string;
    filename: string;
    url: string;
    fileSize?: number;
    mimeType?: string;
    uploadedBy: string;
    uploadedByName?: string;
    uploadedAt: Date;
    description?: string;
    attachmentType: QuoteAttachmentType;
    attachmentTypeDisplayName: string;
    isPublic: boolean;
    isRequired: boolean;
    expiresAt?: Date;
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
}

export class AttachmentStatsDto {
    totalAttachments: number;
    publicAttachments: number;
    requiredAttachments: number;
    expiredAttachments: number;
    totalFileSize: number;
    attachmentTypeCounts: Record<string, number>;
}

export class AttachmentListQueryDto {
    @IsOptional()
    @IsEnum(QuoteAttachmentType)
    attachmentType?: QuoteAttachmentType;

    @IsOptional()
    @IsBoolean()
    isPublic?: boolean;

    @IsOptional()
    @IsBoolean()
    isRequired?: boolean;

    @IsOptional()
    @IsBoolean()
    includeExpired?: boolean;
}
