import { IsString, <PERSON><PERSON><PERSON>, Is<PERSON>ptional, IsN<PERSON>ber, IsBoolean } from 'class-validator';
import { FeeItemType } from '@app/common/typeorm/entities/tenant/rate-card-fee-item.entity';
import { VatType } from '@app/common/typeorm/entities/tenant/fee-item.entity';

export class CreateRateCardFeeItemDto {
    @IsString()
    rateCardId: string;

    @IsString()
    label: string;

    @IsEnum(FeeItemType)
    feeType: FeeItemType;

    @IsString()
    categoryName: string;

    @IsOptional()
    @IsNumber()
    rangeStart?: number;

    @IsOptional()
    @IsNumber()
    rangeEnd?: number;

    @IsOptional()
    @IsString()
    conditionSlug?: string;

    @IsNumber()
    netFee: number;

    @IsNumber()
    vatFee: number;

    @IsNumber()
    totalFee: number;

    @IsEnum(VatType)
    vatType: VatType;

    @IsString()
    applicableFor: string;

    @IsBoolean()
    perParty: boolean;

    @IsBoolean()
    dynamic: boolean;

    @IsNumber()
    displayOrder: number;

    @IsOptional()
    @IsString()
    notes?: string;

    @IsString()
    tenantId: string;
}
