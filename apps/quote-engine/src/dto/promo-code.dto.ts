import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, <PERSON><PERSON>num, IsDateString, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { PromoCodeStatus } from '@app/common/typeorm/entities/tenant';

export class CreatePromoCodeDto {
    @IsString()
    name: string;

    @IsString()
    code: string;

    @IsNumber()
    @Min(1)
    @Max(100)
    discount: number;

    @IsOptional()
    @IsDateString()
    expirationDate?: string;

    @IsOptional()
    @IsNumber()
    @Min(1)
    usageLimit?: number;
}

export class UpdatePromoCodeDto {
    @IsOptional()
    @IsString()
    name?: string;

    @IsOptional()
    @IsString()
    code?: string;

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(100)
    discount?: number;

    @IsOptional()
    @IsDateString()
    expirationDate?: string;

    @IsOptional()
    @IsNumber()
    @Min(1)
    usageLimit?: number;

    @IsOptional()
    @IsEnum(PromoCodeStatus)
    status?: PromoCodeStatus;
}

export class ValidatePromoCodeDto {
    @IsString()
    code: string;
}

export class PromoCodeResponseDto {
    id: string;
    name: string;
    code: string;
    discount: number;
    expirationDate?: Date;
    status: PromoCodeStatus;
    usageLimit?: number;
    usageCount: number;
    createdAt: Date;
    updatedAt: Date;
}

export class PromoCodeValidationResponseDto {
    isValid: boolean;
    discount: number;
    promoCodeId: string;
    message?: string;
}

export class PromoCodeUsageStatsDto {
    totalUsage: number;
    remainingUsage: number;
    isExpired: boolean;
    isUsageLimitReached: boolean;
}

export class PromoCodeListQueryDto {
    @IsOptional()
    @IsEnum(PromoCodeStatus)
    status?: PromoCodeStatus;

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(100)
    @Type(() => Number)
    limit?: number = 20;

    @IsOptional()
    @IsNumber()
    @Min(0)
    @Type(() => Number)
    offset?: number = 0;

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Type(() => Number)
    page?: number = 1;

    @IsOptional()
    @IsString()
    search?: string;
}
