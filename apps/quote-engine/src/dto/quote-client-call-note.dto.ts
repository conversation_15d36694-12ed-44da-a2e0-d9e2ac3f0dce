import {
    IsString,
    IsOptional,
    IsEnum,
    IsBoolean,
    IsDateString,
    IsNumber,
    IsObject,
    Min,
    Max
} from 'class-validator';
import { CallType, CallOutcome, CallPriority } from '@app/common/typeorm/entities/tenant';

export class CreateClientCallNoteDto {
    @IsString()
    quoteId: string;

    @IsEnum(CallType)
    callType: CallType;

    @IsEnum(CallOutcome)
    callOutcome: CallOutcome;

    @IsOptional()
    @IsEnum(CallPriority)
    callPriority?: CallPriority;

    @IsDateString()
    callDate: string;

    @IsOptional()
    @IsNumber()
    @Min(1)
    callDuration?: number;

    @IsString()
    clientName: string;

    @IsOptional()
    @IsString()
    clientPhone?: string;

    @IsOptional()
    @IsString()
    clientEmail?: string;

    @IsString()
    staffMember: string;

    @IsString()
    staffMemberName: string;

    @IsString()
    callSummary: string;

    @IsString()
    discussionPoints: string;

    @IsOptional()
    @IsString()
    clientConcerns?: string;

    @IsOptional()
    @IsString()
    objectionsRaised?: string;

    @IsOptional()
    @IsString()
    objectionsHandled?: string;

    @IsOptional()
    @IsString()
    nextSteps?: string;

    @IsOptional()
    @IsBoolean()
    followUpRequired?: boolean;

    @IsOptional()
    @IsDateString()
    followUpDate?: string;

    @IsOptional()
    @IsString()
    followUpAssignedTo?: string;

    @IsOptional()
    @IsString()
    followUpAssignedToName?: string;

    @IsOptional()
    @IsString()
    followUpNotes?: string;

    @IsOptional()
    @IsBoolean()
    quoteRelated?: boolean;

    @IsOptional()
    @IsBoolean()
    quoteDiscussed?: boolean;

    @IsOptional()
    @IsString()
    quoteFeedback?: string;

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(5)
    clientSatisfaction?: number;

    @IsOptional()
    @IsObject()
    callMetadata?: Record<string, any>;
}

export class UpdateClientCallNoteDto {
    @IsOptional()
    @IsEnum(CallType)
    callType?: CallType;

    @IsOptional()
    @IsEnum(CallOutcome)
    callOutcome?: CallOutcome;

    @IsOptional()
    @IsEnum(CallPriority)
    callPriority?: CallPriority;

    @IsOptional()
    @IsDateString()
    callDate?: string;

    @IsOptional()
    @IsNumber()
    @Min(1)
    callDuration?: number;

    @IsOptional()
    @IsString()
    clientName?: string;

    @IsOptional()
    @IsString()
    clientPhone?: string;

    @IsOptional()
    @IsString()
    clientEmail?: string;

    @IsOptional()
    @IsString()
    callSummary?: string;

    @IsOptional()
    @IsString()
    discussionPoints?: string;

    @IsOptional()
    @IsString()
    clientConcerns?: string;

    @IsOptional()
    @IsString()
    objectionsRaised?: string;

    @IsOptional()
    @IsString()
    objectionsHandled?: string;

    @IsOptional()
    @IsString()
    nextSteps?: string;

    @IsOptional()
    @IsBoolean()
    followUpRequired?: boolean;

    @IsOptional()
    @IsDateString()
    followUpDate?: string;

    @IsOptional()
    @IsString()
    followUpAssignedTo?: string;

    @IsOptional()
    @IsString()
    followUpAssignedToName?: string;

    @IsOptional()
    @IsString()
    followUpNotes?: string;

    @IsOptional()
    @IsBoolean()
    quoteRelated?: boolean;

    @IsOptional()
    @IsBoolean()
    quoteDiscussed?: boolean;

    @IsOptional()
    @IsString()
    quoteFeedback?: string;

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(5)
    clientSatisfaction?: number;

    @IsOptional()
    @IsObject()
    callMetadata?: Record<string, any>;
}

export class ClientCallNoteResponseDto {
    id: string;
    quoteId: string;
    callType: CallType;
    callTypeDisplayName: string;
    callOutcome: CallOutcome;
    callOutcomeDisplayName: string;
    callPriority: CallPriority;
    callPriorityDisplayName: string;
    callDate: Date;
    callDuration?: number;
    clientName: string;
    clientPhone?: string;
    clientEmail?: string;
    staffMember: string;
    staffMemberName: string;
    callSummary: string;
    discussionPoints: string;
    clientConcerns?: string;
    objectionsRaised?: string;
    objectionsHandled?: string;
    nextSteps?: string;
    followUpRequired: boolean;
    followUpDate?: Date;
    followUpAssignedTo?: string;
    followUpAssignedToName?: string;
    followUpNotes?: string;
    quoteRelated: boolean;
    quoteDiscussed: boolean;
    quoteFeedback?: string;
    clientSatisfaction?: number;
    clientSatisfactionDescription?: string;
    callMetadata?: Record<string, any>;
    isArchived: boolean;
    archivedAt?: Date;
    archivedBy?: string;
    createdBy: string;
    createdAt: Date;
    updatedAt: Date;
}

export class CallNoteStatsDto {
    totalCalls: number;
    followUpRequired: number;
    overdueFollowUps: number;
    averageSatisfaction: number;
    callTypeCounts: Record<string, number>;
    callOutcomeCounts: Record<string, number>;
    priorityCounts: Record<string, number>;
    lastCallDate: Date | null;
    totalCallDuration: number;
    averageCallDuration: number;
}

export class CallNoteListQueryDto {
    @IsOptional()
    @IsEnum(CallType)
    callType?: CallType;

    @IsOptional()
    @IsEnum(CallOutcome)
    callOutcome?: CallOutcome;

    @IsOptional()
    @IsEnum(CallPriority)
    callPriority?: CallPriority;

    @IsOptional()
    @IsBoolean()
    followUpRequired?: boolean;

    @IsOptional()
    @IsBoolean()
    quoteDiscussed?: boolean;

    @IsOptional()
    @IsDateString()
    startDate?: string;

    @IsOptional()
    @IsDateString()
    endDate?: string;

    @IsOptional()
    @IsString()
    staffMember?: string;

    @IsOptional()
    @IsString()
    clientEmail?: string;

    @IsOptional()
    @IsString()
    search?: string;
}

export class SetFollowUpDto {
    @IsDateString()
    followUpDate: string;

    @IsString()
    assignedTo: string;

    @IsString()
    assignedToName: string;

    @IsOptional()
    @IsString()
    followUpNotes?: string;
}

export class CallNoteFiltersDto {
    @IsOptional()
    @IsEnum(CallType)
    callType?: CallType;

    @IsOptional()
    @IsEnum(CallOutcome)
    callOutcome?: CallOutcome;

    @IsOptional()
    @IsEnum(CallPriority)
    callPriority?: CallPriority;

    @IsOptional()
    @IsBoolean()
    followUpRequired?: boolean;

    @IsOptional()
    @IsBoolean()
    quoteDiscussed?: boolean;

    @IsOptional()
    @IsDateString()
    startDate?: string;

    @IsOptional()
    @IsDateString()
    endDate?: string;

    @IsOptional()
    @IsString()
    staffMember?: string;

    @IsOptional()
    @IsString()
    clientEmail?: string;
}
