import {
    Is<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ber,
    IsEnum,
    IsBoolean,
    IsOptional,
    IsObject,
    ValidateNested,
    Min,
    Max,
    IsArray,
    ArrayMinSize
} from 'class-validator';
import { Type } from 'class-transformer';

export enum PropertyType {
    RESIDENTIAL = 'residential',
    COMMERCIAL = 'commercial',
    MIXED = 'mixed'
}

export enum TransactionType {
    BUY = 'buy',
    SELL = 'sell',
    REMORTGAGE = 'remortgage',
    BUY_SELL = 'buy_sell'
}

export class LocationDto {
    @IsString()
    postcode: string;

    @IsString()
    region: string;

    @IsString()
    country: string;
}

export class PropertyConditionsDto {
    @IsBoolean()
    isNewBuild: boolean;

    @IsBoolean()
    isLeasehold: boolean;

    @IsBoolean()
    isWales: boolean;

    @IsBoolean()
    isScotland: boolean;

    @IsBoolean()
    isNorthernIreland: boolean;

    @IsBoolean()
    isFirstTimeBuyer: boolean;

    @IsBoolean()
    isBuyingWithMortgage: boolean;

    @IsBoolean()
    isSharedOwnership: boolean;

    @IsBoolean()
    isHelpToBuy: boolean;

    @IsBoolean()
    isGiftedDeposit: boolean;

    @IsBoolean()
    isCompanyOrTrust: boolean;

    @IsBoolean()
    isResidingOutsideUK: boolean;

    @IsBoolean()
    ownsMultipleProperties: boolean;

    @IsBoolean()
    hasPreviousOwnership: boolean;

    @IsBoolean()
    isMainResidence: boolean;

    @IsBoolean()
    buildingOverLimit: boolean;

    @IsBoolean()
    transferOfEquity: boolean;

    @IsBoolean()
    mortgageRedemption: boolean;
}

export class ClientDetailsDto {
    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(10)
    numberOfBuyers?: number;

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(10)
    numberOfSellers?: number;

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(10)
    numberOfOwners?: number;
}

export class QuoteRequestDto {
    @IsNumber()
    @Min(1000)
    @Max(*********)
    propertyValue: number;

    @IsEnum(PropertyType)
    propertyType: PropertyType;

    @IsEnum(TransactionType)
    transactionType: TransactionType;

    @IsObject()
    @ValidateNested()
    @Type(() => LocationDto)
    location: LocationDto;

    @IsObject()
    @ValidateNested()
    @Type(() => PropertyConditionsDto)
    propertyConditions: PropertyConditionsDto;

    @IsObject()
    @ValidateNested()
    @Type(() => ClientDetailsDto)
    clientDetails: ClientDetailsDto;

    @IsOptional()
    @IsString()
    promoCode?: string;

    // Rate Card Selection
    @IsOptional()
    @IsString()
    rateCardId?: string;

    @IsOptional()
    @IsString()
    rateCardProvider?: string;

    @IsOptional()
    @IsBoolean()
    useDefaultRateCard?: boolean = true;
}

export class BulkQuoteRequestDto {
    @IsArray()
    @ArrayMinSize(1)
    @ValidateNested({ each: true })
    @Type(() => QuoteRequestDto)
    requests: QuoteRequestDto[];
}

export class QuoteEstimateDto {
    @IsString()
    reference: string;

    @IsNumber()
    estimatedTotal: number;

    @IsNumber()
    estimatedNet: number;

    @IsNumber()
    estimatedVat: number;

    @IsOptional()
    @IsNumber()
    discount?: number;

    @IsString()
    calculationTimestamp: string;
}
