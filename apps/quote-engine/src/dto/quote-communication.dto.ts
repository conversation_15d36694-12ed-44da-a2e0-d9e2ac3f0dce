import {
    IsString,
    IsOptional,
    IsEnum,
    IsBoolean,
    IsDateString,
    IsArray,
    IsObject
} from 'class-validator';
import {
    CommunicationType,
    CommunicationDirection,
    CommunicationPriority
} from '@app/common/typeorm/entities/tenant';

export class CreateCommunicationDto {
    @IsString()
    quoteId: string;

    @IsEnum(CommunicationType)
    type: CommunicationType;

    @IsEnum(CommunicationDirection)
    direction: CommunicationDirection;

    @IsOptional()
    @IsEnum(CommunicationPriority)
    priority?: CommunicationPriority;

    @IsString()
    subject: string;

    @IsString()
    body: string;

    @IsString()
    sender: string;

    @IsOptional()
    @IsString()
    senderName?: string;

    @IsOptional()
    @IsString()
    senderEmail?: string;

    @IsOptional()
    @IsString()
    recipient?: string;

    @IsOptional()
    @IsString()
    recipientName?: string;

    @IsOptional()
    @IsString()
    recipientEmail?: string;

    @IsOptional()
    @IsArray()
    attachments?: string[];

    @IsOptional()
    @IsObject()
    metadata?: Record<string, any>;

    @IsOptional()
    @IsBoolean()
    followUpRequired?: boolean;

    @IsOptional()
    @IsDateString()
    followUpDate?: string;

    @IsOptional()
    @IsString()
    followUpAssignedTo?: string;

    @IsOptional()
    @IsString()
    followUpAssignedToName?: string;
}

export class UpdateCommunicationDto {
    @IsOptional()
    @IsString()
    subject?: string;

    @IsOptional()
    @IsString()
    body?: string;

    @IsOptional()
    @IsEnum(CommunicationPriority)
    priority?: CommunicationPriority;

    @IsOptional()
    @IsObject()
    metadata?: Record<string, any>;

    @IsOptional()
    @IsBoolean()
    followUpRequired?: boolean;

    @IsOptional()
    @IsDateString()
    followUpDate?: string;

    @IsOptional()
    @IsString()
    followUpAssignedTo?: string;

    @IsOptional()
    @IsString()
    followUpAssignedToName?: string;
}

export class CommunicationResponseDto {
    id: string;
    quoteId: string;
    type: CommunicationType;
    typeDisplayName: string;
    direction: CommunicationDirection;
    directionDisplayName: string;
    priority: CommunicationPriority;
    priorityDisplayName: string;
    subject: string;
    body: string;
    sender: string;
    senderName?: string;
    senderEmail?: string;
    recipient?: string;
    recipientName?: string;
    recipientEmail?: string;
    sentAt: Date;
    isRead: boolean;
    readAt?: Date;
    readBy?: string;
    readByName?: string;
    attachments?: string[];
    metadata?: Record<string, any>;
    isArchived: boolean;
    archivedAt?: Date;
    archivedBy?: string;
    followUpRequired: boolean;
    followUpDate?: Date;
    followUpAssignedTo?: string;
    followUpAssignedToName?: string;
    createdBy: string;
    createdAt: Date;
    updatedAt: Date;
}

export class CommunicationStatsDto {
    totalCommunications: number;
    unreadCommunications: number;
    followUpRequired: number;
    overdueFollowUps: number;
    typeCounts: Record<string, number>;
    directionCounts: Record<string, number>;
    priorityCounts: Record<string, number>;
    lastCommunication: Date | null;
}

export class CommunicationListQueryDto {
    @IsOptional()
    @IsEnum(CommunicationType)
    type?: CommunicationType;

    @IsOptional()
    @IsEnum(CommunicationDirection)
    direction?: CommunicationDirection;

    @IsOptional()
    @IsEnum(CommunicationPriority)
    priority?: CommunicationPriority;

    @IsOptional()
    @IsBoolean()
    isRead?: boolean;

    @IsOptional()
    @IsBoolean()
    followUpRequired?: boolean;

    @IsOptional()
    @IsDateString()
    startDate?: string;

    @IsOptional()
    @IsDateString()
    endDate?: string;

    @IsOptional()
    @IsString()
    search?: string;
}

export class MarkAsReadDto {
    @IsString()
    readBy: string;

    @IsString()
    readByName: string;
}

export class SetFollowUpDto {
    @IsDateString()
    followUpDate: string;

    @IsString()
    assignedTo: string;

    @IsString()
    assignedToName: string;
}

export class LogEmailDto {
    @IsString()
    subject: string;

    @IsString()
    body: string;

    @IsString()
    recipientEmail: string;

    @IsOptional()
    @IsString()
    recipientName?: string;

    @IsString()
    senderEmail: string;

    @IsString()
    senderName: string;
}

export class LogCallDto {
    @IsString()
    subject: string;

    @IsString()
    notes: string;

    @IsOptional()
    @IsString()
    recipientName?: string;

    @IsOptional()
    @IsString()
    recipientPhone?: string;

    @IsOptional()
    @IsString()
    duration?: number;

    @IsOptional()
    @IsString()
    outcome?: string;
}

export class AddSystemNoteDto {
    @IsString()
    note: string;
}
