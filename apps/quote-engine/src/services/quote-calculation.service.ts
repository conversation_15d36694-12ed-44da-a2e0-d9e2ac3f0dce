import { Injectable, Logger } from '@nestjs/common';
import { FeeCategoryRepository, FeeItemRepository, PromoCodeRepository } from '../repositories';
import { RateCardRepository } from '../repositories/rate-card.repository';
import { RateCardFeeItemRepository } from '../repositories/rate-card-fee-item.repository';
import { TenantConnectionService } from '@app/common/multi-tenancy';
import { RateCardStatus } from '@app/common/typeorm/entities/tenant/rate-card.entity';
import { PromoCodeManagementService } from './promo-code-management.service';
import { QuoteAuditService } from './quote-audit.service';

export interface QuoteBreakdownItem {
    category: string;
    label: string;
    net: number;
    vat: number;
    total: number;
    dynamic?: boolean;
    calculationMethod?: string;
}

export interface CategoryBreakdown {
    name: string;
    items: QuoteBreakdownItem[];
}

export interface QuoteBreakdown {
    categories: CategoryBreakdown[];
    grandTotalNet: number;
    grandTotalVat: number;
    grandTotal: number;
    reference: string;
    discount: number;
    calculationTimestamp: Date;
    // Rate Card Information
    rateCard: {
        id: string;
        providerName: string;
        displayName: string;
        description: string;
        version: string;
        effectiveDate: Date;
    };
    appliedFeeItems: number;
    totalFeeItems: number;
}

export interface QuoteRequest {
    propertyValue: number;
    propertyType: 'residential' | 'commercial' | 'mixed';
    transactionType: 'buy' | 'sell' | 'remortgage' | 'buy_sell';
    location: {
        postcode: string;
        region: string;
        country: string;
    };
    propertyConditions: {
        isNewBuild: boolean;
        isLeasehold: boolean;
        isWales: boolean;
        isScotland: boolean;
        isNorthernIreland: boolean;
        isFirstTimeBuyer: boolean;
        isBuyingWithMortgage: boolean;
        isSharedOwnership: boolean;
        isHelpToBuy: boolean;
        isGiftedDeposit: boolean;
        isCompanyOrTrust: boolean;
        isResidingOutsideUK: boolean;
        ownsMultipleProperties: boolean;
        hasPreviousOwnership: boolean;
        isMainResidence: boolean;
        buildingOverLimit: boolean;
        transferOfEquity: boolean;
        mortgageRedemption: boolean;
    };
    clientDetails: {
        numberOfBuyers?: number;
        numberOfSellers?: number;
        numberOfOwners?: number;
    };
    promoCode?: string;
    // Rate Card Selection
    rateCardId?: string;
    rateCardProvider?: string;
    useDefaultRateCard?: boolean;
}

@Injectable()
export class QuoteCalculationService {
    private readonly logger = new Logger(QuoteCalculationService.name);

    constructor(
        private readonly feeCategoryRepository: FeeCategoryRepository,
        private readonly feeItemRepository: FeeItemRepository,
        private readonly promoCodeRepository: PromoCodeRepository,
        private readonly rateCardRepository: RateCardRepository,
        private readonly rateCardFeeItemRepository: RateCardFeeItemRepository,
        private readonly tenantConnectionService: TenantConnectionService,
        private readonly promoCodeManagementService: PromoCodeManagementService,
        private readonly quoteAuditService: QuoteAuditService
    ) {}

    /**
     * Calculate a quote based on the provided request using rate cards
     */
    async calculateQuote(
        request: QuoteRequest,
        quoteId?: string,
        httpRequest?: any
    ): Promise<QuoteBreakdown> {
        try {
            this.logger.log(`Calculating quote for ${request.transactionType} transaction`);
            this.logger.debug(
                `Request details: propertyValue=${request.propertyValue}, transactionType=${request.transactionType}`
            );
            this.logger.debug(`Property conditions: ${JSON.stringify(request.propertyConditions)}`);

            // Determine which rate card to use
            const rateCard = await this.determineRateCard(request);
            if (!rateCard) {
                throw new Error('No suitable rate card found for this transaction');
            }

            this.logger.log(`Using rate card: ${rateCard.displayName} (${rateCard.providerName})`);

            // Get all fee items for this rate card
            const feeItems = await this.rateCardFeeItemRepository.findByRateCardId(rateCard.id);
            if (!feeItems || feeItems.length === 0) {
                throw new Error(`No fee items found for rate card: ${rateCard.displayName}`);
            }

            // Group fee items by category
            const categoriesMap = new Map<string, QuoteBreakdownItem[]>();
            let grandTotalNet = 0;
            let grandTotalVat = 0;
            let grandTotal = 0;
            let appliedFeeItemsCount = 0;

            // Process each fee item
            for (const item of feeItems) {
                this.logger.debug(`Processing fee item: ${item.label} (${item.categoryName})`);
                this.logger.debug(`  - applicableFor: ${item.applicableFor}`);
                this.logger.debug(`  - rangeStart: ${item.rangeStart}, rangeEnd: ${item.rangeEnd}`);
                this.logger.debug(`  - conditionSlug: ${item.conditionSlug}`);
                this.logger.debug(`  - active: ${item.active}`);
                this.logger.debug(
                    `  - netFee: ${item.netFee}, vatFee: ${item.vatFee}, totalFee: ${item.totalFee}`
                );

                // Check if item applies using the same logic as debug endpoint
                const itemApplies = this.rateCardItemApplies(item, request);
                this.logger.debug(`  - rateCardItemApplies result: ${itemApplies}`);

                const calculatedItem = await this.calculateRateCardFeeItem(item, request);
                this.logger.debug(
                    `  - calculateRateCardFeeItem result: ${calculatedItem ? 'SUCCESS' : 'NULL'}`
                );

                if (
                    calculatedItem &&
                    typeof calculatedItem.net === 'number' &&
                    typeof calculatedItem.vat === 'number' &&
                    typeof calculatedItem.total === 'number'
                ) {
                    this.logger.debug(`  ✓ Item applied successfully`);
                    const categoryName = item.categoryName || 'Other Fees';

                    if (!categoriesMap.has(categoryName)) {
                        categoriesMap.set(categoryName, []);
                    }

                    categoriesMap.get(categoryName)!.push(calculatedItem);

                    grandTotalNet += calculatedItem.net;
                    grandTotalVat += calculatedItem.vat;
                    grandTotal += calculatedItem.total;
                    appliedFeeItemsCount++;
                } else {
                    this.logger.debug(`  ✗ Item not applied`);
                }
            }

            // Convert categories map to array format
            const resultCategories: CategoryBreakdown[] = Array.from(categoriesMap.entries()).map(
                ([name, items]) => ({
                    name,
                    items
                })
            );

            // Ensure all totals are valid numbers
            grandTotalNet = Number(grandTotalNet) || 0;
            grandTotalVat = Number(grandTotalVat) || 0;
            grandTotal = Number(grandTotal) || 0;

            // Apply promo code discount if provided
            let discount = 0;
            if (request.promoCode) {
                this.logger.log(`Validating promo code: ${request.promoCode}`);
                const promoCodeValidation = await this.promoCodeManagementService.validatePromoCode(
                    request.promoCode
                );

                if (promoCodeValidation.isValid) {
                    discount = (grandTotal * promoCodeValidation.discount) / 100;
                    grandTotal -= discount;
                    this.logger.log(
                        `Promo code applied: ${request.promoCode}, discount: ${promoCodeValidation.discount}%, amount: £${discount.toFixed(2)}`
                    );

                    // Log audit trail for promo code application
                    if (quoteId && httpRequest) {
                        await this.quoteAuditService.logPromoCodeApplied(
                            quoteId,
                            request.promoCode,
                            discount,
                            httpRequest.user?.systemUserId || 'system',
                            httpRequest.user?.name || 'System',
                            httpRequest
                        );
                    }
                } else {
                    this.logger.warn(
                        `Invalid promo code: ${request.promoCode}, reason: ${promoCodeValidation.message}`
                    );
                }
            }

            const result: QuoteBreakdown = {
                categories: resultCategories,
                grandTotalNet,
                grandTotalVat,
                grandTotal,
                reference: this.generateQuoteReference(),
                discount,
                calculationTimestamp: new Date(),
                // Rate Card Information
                rateCard: {
                    id: rateCard.id,
                    providerName: rateCard.providerName,
                    displayName: rateCard.displayName,
                    description: rateCard.description,
                    version: rateCard.version,
                    effectiveDate: rateCard.effectiveDate
                },
                appliedFeeItems: appliedFeeItemsCount,
                totalFeeItems: feeItems.length
            };

            this.logger.log(
                `Quote calculated successfully. Total: £${grandTotal.toFixed(2)}, Net: £${grandTotalNet.toFixed(2)}, VAT: £${grandTotalVat.toFixed(2)}`
            );
            this.logger.log(
                `Applied ${appliedFeeItemsCount} fee items out of ${feeItems.length} total items`
            );

            // If no fee items were applied, log a warning and provide debugging info
            if (appliedFeeItemsCount === 0) {
                this.logger.warn(
                    `No fee items were applied! This usually indicates a mismatch between the request and the rate card.`
                );
                this.logger.warn(
                    `Request: transactionType=${request.transactionType}, propertyValue=${request.propertyValue}`
                );
                this.logger.warn(`Rate Card: ${rateCard.providerName} (${rateCard.providerCode})`);
                this.logger.warn(`Available fee items: ${feeItems.length}`);

                // Log some sample fee items for debugging
                const sampleItems = feeItems.slice(0, 5);
                sampleItems.forEach((item, index) => {
                    this.logger.warn(
                        `Sample item ${index + 1}: ${item.label} - applicableFor: ${item.applicableFor}, range: ${item.rangeStart}-${item.rangeEnd}, condition: ${item.conditionSlug}`
                    );
                });
            }

            this.logger.log(
                `Quote calculation completed: appliedFeeItems=${appliedFeeItemsCount}, grandTotal=£${grandTotal.toFixed(2)}`
            );

            // Log audit trail for quote calculation
            if (quoteId && httpRequest) {
                await this.quoteAuditService.logQuoteCalculated(
                    quoteId,
                    httpRequest.user?.systemUserId || 'system',
                    httpRequest.user?.name || 'System',
                    httpRequest,
                    {
                        transactionType: request.transactionType,
                        propertyValue: request.propertyValue,
                        appliedFeeItems: appliedFeeItemsCount,
                        totalFeeItems: feeItems.length,
                        grandTotal: grandTotal,
                        discount: discount,
                        promoCode: request.promoCode,
                        rateCardProvider: rateCard.providerName
                    }
                );
            }

            return result;
        } catch (error) {
            this.logger.error(`Error calculating quote: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Determine which rate card to use based on the request
     */
    private async determineRateCard(request: QuoteRequest): Promise<any> {
        // If specific rate card ID is provided, use that
        if (request.rateCardId) {
            const rateCard = await this.rateCardRepository.findOne({
                where: { id: request.rateCardId, status: RateCardStatus.ACTIVE }
            });
            if (rateCard) {
                return rateCard;
            }
        }

        // If specific provider is requested, find their active rate card
        if (request.rateCardProvider) {
            const rateCard = await this.rateCardRepository.findByProvider(request.rateCardProvider);
            if (rateCard) {
                return rateCard;
            }
        }

        // Use default rate card if requested or if no specific selection
        if (request.useDefaultRateCard !== false) {
            const defaultRateCard = await this.rateCardRepository.findDefault();
            if (defaultRateCard) {
                return defaultRateCard;
            }
        }

        // Fallback: find the first active rate card
        const activeRateCards = await this.rateCardRepository.findActiveRateCards();
        if (activeRateCards.length > 0) {
            return activeRateCards[0];
        }

        return null;
    }

    /**
     * Calculate a fee item from a rate card
     */
    private async calculateRateCardFeeItem(
        item: any, // RateCardFeeItem type
        request: QuoteRequest
    ): Promise<QuoteBreakdownItem | null> {
        try {
            // Check if this item applies to the current transaction
            if (!this.rateCardItemApplies(item, request)) {
                return null;
            }

            // Validate and convert input values
            let netFee = typeof item.netFee === 'number' ? item.netFee : parseFloat(item.netFee);
            let vatFee = typeof item.vatFee === 'number' ? item.vatFee : parseFloat(item.vatFee);
            let totalFee =
                typeof item.totalFee === 'number' ? item.totalFee : parseFloat(item.totalFee);

            if (isNaN(netFee) || isNaN(vatFee) || isNaN(totalFee)) {
                this.logger.warn(
                    `Invalid fee values for item ${item.label}: netFee=${item.netFee}, vatFee=${item.vatFee}, totalFee=${item.totalFee}`
                );
                return null;
            }

            let calculationMethod = 'Standard';

            // Handle dynamic calculations if needed
            if (item.dynamic) {
                const dynamicResult = await this.performDynamicCalculation(item, request);
                if (dynamicResult) {
                    netFee = dynamicResult.net;
                    vatFee = dynamicResult.vat;
                    totalFee = netFee + vatFee;
                    calculationMethod = dynamicResult.method;
                }
            }

            // Apply party multipliers if applicable
            if (item.perParty) {
                const partyCount = this.getPartyCount(request);
                netFee = netFee * partyCount;
                vatFee = vatFee * partyCount;
                totalFee = totalFee * partyCount;
                calculationMethod += ' (per party)';
            }

            // Range checking is already handled by rateCardItemApplies method
            // No need for duplicate range validation here

            // Ensure all fee values are valid numbers
            const finalNetFee = Number(netFee) || 0;
            const finalVatFee = Number(vatFee) || 0;
            const finalTotalFee = Number(totalFee) || 0;

            return {
                category: item.categoryName || 'Other Fees',
                label: item.label,
                net: finalNetFee,
                vat: finalVatFee,
                total: finalTotalFee,
                dynamic: item.dynamic,
                calculationMethod
            };
        } catch (error) {
            this.logger.error(
                `Error calculating rate card fee item ${item.label}: ${error.message}`
            );
            return null;
        }
    }

    /**
     * Check if a rate card fee item applies to the current request
     */
    private rateCardItemApplies(item: any, request: QuoteRequest): boolean {
        this.logger.debug(`Checking if item applies: ${item.label}`);

        // Check if item is active
        if (!item.active) {
            this.logger.debug(`  ✗ Item not active`);
            return false;
        }

        // Check if item applies to the current transaction type
        if (item.applicableFor && !item.applicableFor.includes(request.transactionType)) {
            this.logger.debug(
                `  ✗ Transaction type mismatch: item.applicableFor=${item.applicableFor}, request.transactionType=${request.transactionType}`
            );
            return false;
        }

        // Check property value range
        if (
            item.rangeStart !== null &&
            item.rangeStart !== undefined &&
            item.rangeEnd !== null &&
            item.rangeEnd !== undefined
        ) {
            if (request.propertyValue < item.rangeStart || request.propertyValue > item.rangeEnd) {
                this.logger.debug(
                    `  ✗ Property value out of range: request.propertyValue=${request.propertyValue}, range=${item.rangeStart}-${item.rangeEnd}`
                );
                return false;
            }
        }

        // Check conditions
        if (item.conditionSlug) {
            if (!this.evaluateRateCardCondition(item.conditionSlug, request)) {
                this.logger.debug(`  ✗ Condition failed: conditionSlug=${item.conditionSlug}`);
                return false;
            }
        }

        this.logger.debug(`  ✓ Item applies successfully`);
        return true;
    }

    /**
     * Evaluate rate card specific conditions
     */
    private evaluateRateCardCondition(conditionSlug: string, request: QuoteRequest): boolean {
        const conditions = request.propertyConditions;

        switch (conditionSlug) {
            case 'freehold':
                return !conditions.isLeasehold;
            case 'leasehold':
                return conditions.isLeasehold;
            case 'buy_to_let':
                return !conditions.isMainResidence;
            case 'leasehold_buy_to_let':
                return conditions.isLeasehold && !conditions.isMainResidence;
            case 'is_new_build':
                return conditions.isNewBuild;
            case 'first_time_buyer':
                return conditions.isFirstTimeBuyer;
            case 'gifted_deposit':
                return conditions.isGiftedDeposit;
            case 'help_to_buy_equity':
                return conditions.isHelpToBuy;
            case 'help_to_buy_isa':
                return conditions.isHelpToBuy;
            case 'shared_ownership':
                return conditions.isSharedOwnership;
            case 'right_to_buy':
                return conditions.isMainResidence && conditions.isFirstTimeBuyer;
            case 'unregistered':
                return false; // Would need additional property condition
            case 'mortgage_redemption':
                return conditions.mortgageRedemption;
            case 'auction':
                return false; // Would need additional property condition
            case 'hmo':
                return false; // Would need additional property condition
            case 'flying_freehold':
                return false; // Would need additional property condition
            case 'solar_panel_lease':
                return false; // Would need additional property condition
            case 'staircasing':
                return conditions.isSharedOwnership;
            case 'trust_deed':
                return conditions.isCompanyOrTrust;
            case 'lease_extension':
                return conditions.isLeasehold;
            case 'lease_extension_statutory':
                return conditions.isLeasehold;
            case 'dual_representation':
                return false; // Would need additional property condition
            case 'concessionary':
                return conditions.isFirstTimeBuyer;
            case 'company_or_trust':
                return conditions.isCompanyOrTrust;
            case 'pepper_remortgage':
                return request.transactionType === 'remortgage';
            case 'sep_rep_individual':
            case 'sep_rep_ltd_company':
            case 'dual_rep_individual':
            case 'dual_rep_ltd_company':
            case 'independent_legal_advice':
                return false; // Molo-specific conditions, would need additional data
            case 'cash_buyer':
                return !conditions.isBuyingWithMortgage;
            case 'buying_with_mortgage':
                return conditions.isBuyingWithMortgage;
            default:
                return true; // Default to true for unknown conditions
        }
    }

    /**
     * Calculate a specific fee item
     */
    private async calculateFeeItem(
        item: any, // FeeItem type was removed, so using 'any' for now
        request: QuoteRequest
    ): Promise<QuoteBreakdownItem | null> {
        try {
            let netFee = item.netFee;
            let vatFee = item.vatFee;
            let totalFee = item.totalFee;
            let calculationMethod = 'static';

            // Handle dynamic calculations
            if (item.dynamic) {
                const dynamicResult = await this.performDynamicCalculation(item, request);
                if (dynamicResult) {
                    netFee = dynamicResult.net;
                    vatFee = dynamicResult.vat;
                    totalFee = netFee + vatFee;
                    calculationMethod = dynamicResult.method;
                }
            }

            // Apply per-party multiplier if applicable
            if (item.perParty) {
                const partyCount = this.getPartyCount(request);
                netFee *= partyCount;
                vatFee *= partyCount;
                totalFee *= partyCount;
            }

            return {
                category: item.category?.name || 'Unknown Category',
                label: item.label,
                net: Math.round(netFee * 100) / 100,
                vat: Math.round(vatFee * 100) / 100,
                total: Math.round(totalFee * 100) / 100,
                dynamic: item.dynamic,
                calculationMethod
            };
        } catch (error) {
            this.logger.error(`Failed to calculate fee item ${item.label}:`, error);
            return null;
        }
    }

    /**
     * Perform dynamic calculations for specific fee types
     */
    // eslint-disable-next-line @typescript-eslint/require-await
    private async performDynamicCalculation(
        item: any, // FeeItem type was removed, so using 'any' for now
        request: QuoteRequest
    ): Promise<{ net: number; vat: number; method: string } | null> {
        try {
            switch (item.conditionSlug) {
                case 'ltt_calculation':
                    if (request.propertyConditions.isWales) {
                        const ltt = this.calculateLTT(
                            request.propertyValue,
                            request.propertyConditions.isNewBuild
                        );
                        return { net: ltt, vat: 0, method: 'Welsh LTT' };
                    }
                    break;

                case 'sdlt_calculation':
                    if (
                        !request.propertyConditions.isWales &&
                        !request.propertyConditions.isScotland
                    ) {
                        const sdlt = this.calculateSDLT(
                            request.propertyValue,
                            request.propertyConditions.isFirstTimeBuyer
                        );
                        return { net: sdlt, vat: 0, method: 'English SDLT' };
                    }
                    break;

                case 'land_registry_fee': {
                    const landRegistryFee = this.calculateLandRegistryFee(request.propertyValue);
                    return { net: landRegistryFee, vat: 0, method: 'Land Registry Fee' };
                }

                case 'search_fees': {
                    const searchFees = this.calculateSearchFees(
                        request.location,
                        request.propertyConditions
                    );
                    return { net: searchFees, vat: 0, method: 'Property Searches' };
                }

                case 'legal_fees': {
                    const legalFees = this.calculateLegalFees(request);
                    return { net: legalFees, vat: legalFees * 0.2, method: 'Legal Fees' };
                }

                case 'mortgage_fees': {
                    const mortgageFees = this.calculateMortgageFees(request.propertyValue);
                    return { net: mortgageFees, vat: 0, method: 'Mortgage Fees' };
                }

                case 'survey_fees': {
                    const surveyFees = this.calculateSurveyFees(
                        request.propertyValue,
                        request.propertyType
                    );
                    return { net: surveyFees, vat: 0, method: 'Survey Fees' };
                }

                case 'insurance_fees': {
                    const insuranceFees = this.calculateInsuranceFees(
                        request.propertyValue,
                        request.propertyConditions
                    );
                    return { net: insuranceFees, vat: 0, method: 'Insurance Fees' };
                }

                default:
                    return null;
            }
        } catch (error) {
            this.logger.error(`Dynamic calculation failed for ${item.conditionSlug}:`, error);
            return null;
        }

        return null;
    }

    /**
     * Check if a fee item applies to the current request
     */
    private itemApplies(item: any, request: QuoteRequest): boolean {
        // FeeItem type was removed, so using 'any' for now
        // Check applicableFor
        if (item.applicableFor && item.applicableFor.length > 0) {
            if (!item.applicableFor.includes(request.transactionType)) {
                return false;
            }
        }

        // Check price range
        if (item.rangeStart != null && item.rangeEnd != null) {
            if (request.propertyValue < item.rangeStart || request.propertyValue > item.rangeEnd) {
                return false;
            }
        }

        // Check conditions - handle dynamic calculation slugs separately
        if (item.conditionSlug && item.conditionSlug !== 'always') {
            // For dynamic calculations, check if the calculation should apply
            if (item.dynamic) {
                return this.evaluateDynamicCondition(item.conditionSlug, request);
            } else {
                return this.evaluateCondition(item.conditionSlug, request);
            }
        }

        return true;
    }

    /**
     * Evaluate a dynamic calculation condition
     */
    private evaluateDynamicCondition(conditionSlug: string, request: QuoteRequest): boolean {
        switch (conditionSlug) {
            case 'ltt_calculation':
                return request.propertyConditions.isWales;
            case 'sdlt_calculation':
                return (
                    !request.propertyConditions.isWales && !request.propertyConditions.isScotland
                );
            case 'land_registry_fee':
                return true; // Always applies
            case 'search_fees':
                return true; // Always applies
            case 'legal_fees':
                return true; // Always applies
            case 'mortgage_fees':
                return request.propertyConditions.isBuyingWithMortgage;
            case 'survey_fees':
                return true; // Always applies
            case 'insurance_fees':
                return true; // Always applies
            default:
                return false;
        }
    }

    /**
     * Evaluate a condition against the request
     */
    private evaluateCondition(conditionSlug: string, request: QuoteRequest): boolean {
        const conditions = request.propertyConditions;

        switch (conditionSlug) {
            case 'is_new_build':
                return conditions.isNewBuild;
            case 'leasehold':
                return conditions.isLeasehold;
            case 'property_in_wales':
                return conditions.isWales;
            case 'not_property_in_wales':
                return !conditions.isWales;
            case 'property_in_scotland':
                return conditions.isScotland;
            case 'property_in_northern_ireland':
                return conditions.isNorthernIreland;
            case 'first_time_buyer':
                return conditions.isFirstTimeBuyer;
            case 'buying_with_mortgage':
                return conditions.isBuyingWithMortgage;
            case 'shared_ownership':
                return conditions.isSharedOwnership;
            case 'help_to_buy':
                return conditions.isHelpToBuy;
            case 'gifted_deposit':
                return conditions.isGiftedDeposit;
            case 'company_or_trust':
                return conditions.isCompanyOrTrust;
            case 'reside_outside_uk':
                return conditions.isResidingOutsideUK;
            case 'own_multiple_properties':
                return conditions.ownsMultipleProperties;
            case 'previous_property_ownership':
                return conditions.hasPreviousOwnership;
            case 'main_residence':
                return conditions.isMainResidence;
            case 'building_over_limit':
                return conditions.buildingOverLimit;
            case 'transfer_of_equity':
                return conditions.transferOfEquity;
            case 'mortgage_redemption':
                return conditions.mortgageRedemption;
            case 'direct_purchase':
                return !conditions.isBuyingWithMortgage;
            default:
                return false;
        }
    }

    /**
     * Get party count based on transaction type
     */
    private getPartyCount(request: QuoteRequest): number {
        switch (request.transactionType) {
            case 'buy':
                return request.clientDetails.numberOfBuyers || 1;
            case 'sell':
                return request.clientDetails.numberOfSellers || 1;
            case 'remortgage':
                return request.clientDetails.numberOfOwners || 1;
            default:
                return 1;
        }
    }

    /**
     * Calculate Land Transaction Tax (LTT) for Wales
     */
    private calculateLTT(purchasePrice: number, isNewBuild: boolean): number {
        if (isNewBuild && purchasePrice <= 500000) {
            return purchasePrice * 0.05;
        }

        let totalTax = 0;
        let remainder = purchasePrice;

        // Band 1: 0% on the first £225,000
        if (remainder > 0) {
            const bandMax = 225000;
            const amount = Math.min(remainder, bandMax);
            totalTax += amount * 0;
            remainder -= amount;
        }

        // Band 2: 6% on the portion from £225,001 to £400,000
        if (remainder > 0) {
            const bandSize = 400000 - 225000;
            const amount = Math.min(remainder, bandSize);
            totalTax += amount * 0.06;
            remainder -= amount;
        }

        // Band 3: 7.5% on the portion from £400,001 to £750,000
        if (remainder > 0) {
            const bandSize = 750000 - 400000;
            const amount = Math.min(remainder, bandSize);
            totalTax += amount * 0.075;
            remainder -= amount;
        }

        // Band 4: 10% on the portion from £750,001 to £1,500,000
        if (remainder > 0) {
            const bandSize = 1500000 - 750000;
            const amount = Math.min(remainder, bandSize);
            totalTax += amount * 0.1;
            remainder -= amount;
        }

        // Band 5: 12% on the portion above £1,500,000
        if (remainder > 0) {
            totalTax += remainder * 0.12;
        }

        return Math.round(totalTax * 100) / 100;
    }

    /**
     * Calculate Stamp Duty Land Tax (SDLT) for England/Northern Ireland
     */
    private calculateSDLT(purchasePrice: number, firstTimeBuyer: boolean): number {
        let tax = 0;

        if (firstTimeBuyer && purchasePrice <= 500000) {
            if (purchasePrice <= 300000) {
                tax = 0;
            } else {
                tax = (purchasePrice - 300000) * 0.05;
            }
        } else {
            if (purchasePrice <= 125000) {
                tax = 0;
            } else if (purchasePrice <= 250000) {
                tax = (purchasePrice - 125000) * 0.02;
            } else if (purchasePrice <= 925000) {
                tax = 125000 * 0.02 + (purchasePrice - 250000) * 0.05;
            } else if (purchasePrice <= 1500000) {
                tax = 125000 * 0.02 + 675000 * 0.05 + (purchasePrice - 925000) * 0.1;
            } else {
                tax =
                    125000 * 0.02 + 675000 * 0.05 + 575000 * 0.1 + (purchasePrice - 1500000) * 0.12;
            }
        }

        return Math.round(tax * 100) / 100;
    }

    /**
     * Calculate Land Registry fees
     */
    private calculateLandRegistryFee(propertyValue: number): number {
        if (propertyValue <= 80000) return 40;
        if (propertyValue <= 100000) return 60;
        if (propertyValue <= 200000) return 120;
        if (propertyValue <= 500000) return 200;
        if (propertyValue <= 1000000) return 300;
        return 455;
    }

    /**
     * Calculate search fees
     */
    private calculateSearchFees(
        location: QuoteRequest['location'],
        conditions: QuoteRequest['propertyConditions']
    ): number {
        let baseFee = 200; // Base search fee

        // Add regional variations
        if (location.region.toLowerCase().includes('london')) {
            baseFee += 50;
        }

        // Add condition-based fees
        if (conditions.isLeasehold) {
            baseFee += 75; // Additional leasehold searches
        }

        if (conditions.isNewBuild) {
            baseFee += 100; // New build specific searches
        }

        return baseFee;
    }

    /**
     * Calculate legal fees
     */
    private calculateLegalFees(request: QuoteRequest): number {
        let baseFee = 800;

        // Adjust for property value
        if (request.propertyValue > 500000) {
            baseFee += (request.propertyValue - 500000) * 0.001;
        }

        // Adjust for transaction type
        switch (request.transactionType) {
            case 'buy':
                baseFee *= 1.0;
                break;
            case 'sell':
                baseFee *= 0.8;
                break;
            case 'remortgage':
                baseFee *= 0.6;
                break;
        }

        // Adjust for complexity
        if (request.propertyConditions.isLeasehold) {
            baseFee *= 1.2;
        }

        if (request.propertyConditions.isSharedOwnership) {
            baseFee *= 1.3;
        }

        return Math.round(baseFee);
    }

    /**
     * Calculate mortgage fees
     */
    private calculateMortgageFees(propertyValue: number): number {
        return Math.round(propertyValue * 0.001); // 0.1% of property value
    }

    /**
     * Calculate survey fees
     */
    private calculateSurveyFees(propertyValue: number, propertyType: string): number {
        let baseFee = 400;

        // Adjust for property value
        if (propertyValue > 500000) {
            baseFee += (propertyValue - 500000) * 0.0005;
        }

        // Adjust for property type
        if (propertyType === 'commercial') {
            baseFee *= 1.5;
        }

        return Math.round(baseFee);
    }

    /**
     * Calculate insurance fees
     */
    private calculateInsuranceFees(
        propertyValue: number,
        conditions: QuoteRequest['propertyConditions']
    ): number {
        let baseFee = 150;

        // Adjust for property value
        baseFee += propertyValue * 0.0002; // 0.02% of property value

        // Adjust for conditions
        if (conditions.isNewBuild) {
            baseFee *= 0.8; // New builds often have warranties
        }

        if (conditions.isLeasehold) {
            baseFee *= 1.1; // Leasehold may require additional insurance
        }

        return Math.round(baseFee);
    }

    /**
     * Generate a unique quote reference
     */
    private generateQuoteReference(): string {
        const timestamp = Date.now().toString().slice(-8);
        const random = Math.floor(Math.random() * 1000)
            .toString()
            .padStart(3, '0');
        return `Q${timestamp}${random}`;
    }
}
