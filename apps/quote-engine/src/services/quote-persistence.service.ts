import { Injectable, Logger } from '@nestjs/common';
import { QuoteRepository } from '../repositories';
import { QuoteCalculationService } from './quote-calculation.service';
import { QuoteAuditService } from './quote-audit.service';
import { TenantConnectionService, TenantContextService } from '@app/common/multi-tenancy';
import { Quote, QuoteStatus, TransactionType } from '@app/common/typeorm/entities/tenant';
import { QuoteRequestDto } from '../dto/quote-request.dto';
import { QuoteBreakdown } from './quote-calculation.service';
import { CaseType, CasePriority, Case } from '@app/common/typeorm/entities/tenant/case.entity';
import { CaseStatus } from '@app/common/enums/case-status.enum';
import { Client } from '@app/common/typeorm/entities/tenant/client.entity';
import { CaseAssignment } from '@app/common/typeorm/entities/tenant/case-assignment.entity';
import {
    PaginatedQuotesQueryDto,
    PaginatedQuotesResponseDto,
    LeadConversionResult
} from '../dto/quote-persistence.dto';
import { Between, Like, FindManyOptions } from 'typeorm';
import { ConveyancingMilestoneService } from 'apps/case-management/src/services/conveyancing-milestone.service';
import { Request } from 'express';
interface QuotePersistenceRequest {
    quoteRequest: QuoteRequestDto;
    calculatedQuote?: QuoteBreakdown; // Optional pre-calculated quote
    contactDetails: {
        name: string;
        email: string;
        phone: string;
    };
}

interface QuotePersistenceResult {
    quoteId: string;
    quoteNumber: string;
    status: QuoteStatus;
    totalAmount: number;
    expiresAt: Date;
    leadId: string;
}

interface LeadConversionRequest {
    quoteId: string;
    acceptedBy: string;
    acceptedAt: Date;
    caseType?: string;
    priority?: string;
    assignedTo?: string;
    expectedCompletionDate?: string;
    caseNotes?: string;
}

@Injectable()
export class QuotePersistenceService {
    private readonly logger = new Logger(QuotePersistenceService.name);

    constructor(
        private readonly quoteRepository: QuoteRepository,
        private readonly quoteCalculationService: QuoteCalculationService,
        private readonly quoteAuditService: QuoteAuditService,
        private readonly tenantConnectionService: TenantConnectionService,
        private readonly tenantContextService: TenantContextService,
        private readonly conveyancingMilestoneService: ConveyancingMilestoneService
    ) {}

    /**
     * Save a calculated quote as a lead
     */
    async saveQuoteAsLead(
        request: QuotePersistenceRequest,
        httpRequest?: Request
    ): Promise<QuotePersistenceResult> {
        try {
            // Determine if a pre-calculated quote is provided
            const calculatedQuote =
                request.calculatedQuote ||
                (await this.quoteCalculationService.calculateQuote(request.quoteRequest));

            // Create quote entity
            const quoteData = {
                quoteNumber: this.generateQuoteNumber(),
                transactionType: request.quoteRequest.transactionType as TransactionType,
                propertyValue: request.quoteRequest.propertyValue,
                propertyAddress: {
                    postCode: request.quoteRequest.location.postcode,
                    street: request.quoteRequest.location.region,
                    town: request.quoteRequest.location.country
                },
                propertyConditions: request.quoteRequest.propertyConditions,
                clientDetails: {
                    email: request.contactDetails.email,
                    firstName: request.contactDetails.name.split(' ')[0],
                    lastName: request.contactDetails.name.split(' ').slice(1).join(' '),
                    phone: request.contactDetails.phone,
                    numberOfBuyers: request.quoteRequest.clientDetails?.numberOfBuyers || 1
                },
                quoteBreakdown: calculatedQuote,
                totalAmount: calculatedQuote.grandTotal,
                promoCode: request.quoteRequest.promoCode,
                discountAmount: calculatedQuote.discount,
                status: QuoteStatus.DRAFT,
                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
                createdBy: request.contactDetails.email
            };

            // Create and save the quote
            const quote = await this.quoteRepository.create(quoteData);
            const savedQuote = await this.quoteRepository.save(quote);
            this.logger.log(`Quote saved as lead: ${savedQuote.quoteNumber}`);

            // Log audit trail
            if (httpRequest) {
                await this.quoteAuditService.logQuoteCreated(
                    savedQuote.id,
                    request.contactDetails.email,
                    request.contactDetails.name,
                    httpRequest,
                    {
                        quoteNumber: savedQuote.quoteNumber,
                        transactionType: request.quoteRequest.transactionType,
                        propertyValue: request.quoteRequest.propertyValue,
                        totalAmount: savedQuote.totalAmount,
                        promoCode: request.quoteRequest.promoCode,
                        discountAmount: savedQuote.discountAmount
                    }
                );
            }

            return {
                quoteId: savedQuote.id,
                quoteNumber: savedQuote.quoteNumber,
                status: savedQuote.status,
                totalAmount: savedQuote.totalAmount,
                expiresAt: savedQuote.expiresAt,
                leadId: savedQuote.id
            };
        } catch (error) {
            this.logger.error(`Failed to save quote as lead: ${error.message}`, error.stack);
            throw new Error(`Failed to save quote as lead: ${error.message}`);
        }
    }

    /**
     * Convert a quote/lead to a case
     */
    async convertQuoteToCase(
        request: LeadConversionRequest,
        httpRequest?: Request
    ): Promise<LeadConversionResult> {
        try {
            this.logger.log(`Starting quote to case conversion for quote ID: ${request.quoteId}`);

            // Validate required fields
            if (!request.acceptedBy || request.acceptedBy.trim() === '') {
                throw new Error('acceptedBy field is required and cannot be empty');
            }

            if (!request.acceptedAt) {
                request.acceptedAt = new Date();
                this.logger.log('acceptedAt not provided, using current timestamp');
            }

            this.logger.log(
                `Conversion request: acceptedBy=${request.acceptedBy}, acceptedAt=${request.acceptedAt}`
            );

            // Find the quote
            const quote = await this.quoteRepository.findOne({
                where: { id: request.quoteId }
            });

            if (!quote) {
                throw new Error('Quote not found');
            }

            this.logger.log(`Found quote: ${quote.quoteNumber}, current status: ${quote.status}`);

            if (quote.status !== QuoteStatus.DRAFT) {
                throw new Error(`Quote is not in draft status. Current status: ${quote.status}`);
            }

            // Update quote status to accepted
            quote.status = QuoteStatus.ACCEPTED;
            await this.quoteRepository.save(quote);
            this.logger.log(`Quote status updated to ACCEPTED`);

            // Create case in case management system
            this.logger.log(`Creating case from quote...`);
            const caseData = await this.createCaseFromQuote(quote, request);
            this.logger.log(`Case created successfully: ${caseData.caseNumber}`);

            // Link quote to the created case
            quote.caseId = caseData.caseId;
            await this.quoteRepository.save(quote);
            this.logger.log(`Quote ${quote.quoteNumber} linked to case ${caseData.caseId}`);

            // Get solicitor assignment
            const assignedSolicitor = await this.assignSolicitorToCase(caseData.caseId);
            this.logger.log(`Solicitor assignment retrieved: ${assignedSolicitor || 'None'}`);

            // Generate next steps
            const nextSteps = this.generateNextSteps(caseData.caseNumber, assignedSolicitor);

            this.logger.log(
                `Quote ${quote.quoteNumber} successfully converted to case ${caseData.caseNumber}`
            );

            // Log audit trail
            if (httpRequest) {
                await this.quoteAuditService.logQuoteConverted(
                    quote.id,
                    caseData.caseId,
                    request.acceptedBy,
                    request.acceptedBy, // Using acceptedBy as userName for now
                    httpRequest,
                    {
                        caseNumber: caseData.caseNumber,
                        assignedSolicitor,
                        caseType: request.caseType,
                        priority: request.priority,
                        expectedCompletionDate: request.expectedCompletionDate,
                        caseNotes: request.caseNotes
                    }
                );
            }

            return {
                caseId: caseData.caseId,
                caseNumber: caseData.caseNumber,
                quoteId: quote.id,
                assignedSolicitor,
                nextSteps
            };
        } catch (error) {
            this.logger.error(`Failed to convert quote to case: ${error.message}`, error.stack);
            throw new Error(`Failed to convert quote to case: ${error.message}`);
        }
    }

    /**
     * Get quote by ID
     */
    async getQuoteById(quoteId: string): Promise<Quote | null> {
        return await this.quoteRepository.findOne({
            where: { id: quoteId }
        });
    }

    /**
     * Get quote by quote number
     */
    async getQuoteByNumber(quoteNumber: string): Promise<Quote | null> {
        return await this.quoteRepository.findOne({
            where: { quoteNumber }
        });
    }

    /**
     * Get quotes by status
     */
    async getQuotesByStatus(
        status: QuoteStatus,
        limit: number = 50,
        offset: number = 0
    ): Promise<Quote[]> {
        return await this.quoteRepository.find({
            where: { status },
            order: { createdAt: 'DESC' },
            skip: offset,
            take: limit
        });
    }

    /**
     * Get quotes by email
     */
    async getQuotesByEmail(email: string): Promise<Quote[]> {
        return await this.quoteRepository.find({
            where: { clientDetails: { email } },
            order: { createdAt: 'DESC' }
        });
    }

    /**
     * Get quote by case ID
     */
    async getQuoteByCaseId(caseId: string): Promise<Quote | null> {
        return await this.quoteRepository.findByCaseId(caseId);
    }

    /**
     * Update quote status
     */
    async updateQuoteStatus(quoteId: string, status: QuoteStatus): Promise<void> {
        const quote = await this.quoteRepository.findOne({
            where: { id: quoteId }
        });

        if (!quote) {
            throw new Error('Quote not found');
        }

        quote.status = status;
        await this.quoteRepository.save(quote);
    }

    /**
     * Get paginated quotes with filtering
     */
    async getPaginatedQuotes(query: PaginatedQuotesQueryDto): Promise<PaginatedQuotesResponseDto> {
        try {
            this.logger.log(`Getting paginated quotes with filters:`, query);

            const { page = 1, limit = 20, status, startDate, endDate, search } = query;
            const offset = (page - 1) * limit;

            // Build where conditions
            const whereConditions: any = {};

            // Status filter
            if (status) {
                whereConditions.status = status;
            }

            // Date range filter
            if (startDate || endDate) {
                const start = startDate ? new Date(startDate) : undefined;
                const end = endDate ? new Date(endDate) : undefined;

                if (start && end) {
                    whereConditions.createdAt = Between(start, end);
                } else if (start) {
                    whereConditions.createdAt = Between(start, new Date());
                } else if (end) {
                    whereConditions.createdAt = Between(new Date('1900-01-01'), end);
                }
            }

            // Search filter (quote number, client name, or email)
            if (search) {
                whereConditions.quoteNumber = Like(`%${search}%`);
            }

            // Build find options
            const findOptions: FindManyOptions<Quote> = {
                where: whereConditions,
                order: { createdAt: 'DESC' },
                skip: offset,
                take: limit
            };

            // If search is provided, we need to search in JSON fields too
            if (search) {
                // Use raw query for complex search across JSON fields
                const searchQuery = `
                    SELECT * FROM quotes 
                    WHERE (
                        quote_number ILIKE $1 OR 
                        client_details->>'firstName' ILIKE $1 OR 
                        client_details->>'lastName' ILIKE $1 OR 
                        client_details->>'email' ILIKE $1
                    )
                    ${status ? 'AND status = $2' : ''}
                    ${startDate ? `AND created_at >= $${status ? '3' : '2'}` : ''}
                    ${endDate ? `AND created_at <= $${status ? (startDate ? '4' : '3') : startDate ? '3' : '2'}` : ''}
                    ORDER BY created_at DESC
                    LIMIT $${status ? (startDate ? (endDate ? '5' : '4') : '3') : startDate ? (endDate ? '4' : '3') : '2'} 
                    OFFSET $${status ? (startDate ? (endDate ? '6' : '5') : '4') : startDate ? (endDate ? '5' : '4') : '3'}
                `;

                const countQuery = `
                    SELECT COUNT(*) as total FROM quotes 
                    WHERE (
                        quote_number ILIKE $1 OR 
                        client_details->>'firstName' ILIKE $1 OR 
                        client_details->>'lastName' ILIKE $1 OR 
                        client_details->>'email' ILIKE $1
                    )
                    ${status ? 'AND status = $2' : ''}
                    ${startDate ? `AND created_at >= $${status ? '3' : '2'}` : ''}
                    ${endDate ? `AND created_at <= $${status ? (startDate ? '4' : '3') : startDate ? '3' : '2'}` : ''}
                `;

                const queryParams = [`%${search}%`];
                if (status) queryParams.push(status);
                if (startDate) queryParams.push(startDate);
                if (endDate) queryParams.push(endDate);
                queryParams.push(limit.toString(), offset.toString());

                const [quotes, countResult] = await Promise.all([
                    this.quoteRepository.query(searchQuery, queryParams),
                    this.quoteRepository.query(countQuery, queryParams.slice(0, -2))
                ]);

                const total = parseInt(countResult[0]?.total || '0');
                const totalPages = Math.ceil(total / limit);

                return {
                    quotes: quotes.map((quote) => this.formatQuoteForResponse(quote)),
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages,
                        hasNext: page < totalPages,
                        hasPrevious: page > 1
                    },
                    filters: {
                        status,
                        startDate,
                        endDate,
                        search
                    }
                };
            }

            // Standard query without search
            const [quotes, total] = await Promise.all([
                this.quoteRepository.find(findOptions),
                this.quoteRepository.count({ where: whereConditions })
            ]);

            const totalPages = Math.ceil(total / limit);

            this.logger.log(`Retrieved ${quotes.length} quotes out of ${total} total`);

            return {
                quotes: quotes.map((quote) => this.formatQuoteForResponse(quote)),
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages,
                    hasNext: page < totalPages,
                    hasPrevious: page > 1
                },
                filters: {
                    status,
                    startDate,
                    endDate,
                    search
                }
            };
        } catch (error) {
            this.logger.error(`Failed to get paginated quotes: ${error.message}`, error.stack);
            throw new Error(`Failed to get paginated quotes: ${error.message}`);
        }
    }

    /**
     * Get quote analytics
     */
    async getQuoteAnalytics(): Promise<{
        totalQuotes: number;
        totalValue: number;
        byStatus: {
            draft: number;
            sent: number;
            accepted: number;
            expired: number;
        };
        conversionRate: number;
    }> {
        try {
            // Get total quotes
            const totalQuotes = await this.quoteRepository.count();

            // Get quotes by status
            const draftQuotes = await this.quoteRepository.count({
                where: { status: QuoteStatus.DRAFT }
            });
            const sentQuotes = await this.quoteRepository.count({
                where: { status: QuoteStatus.SENT }
            });
            const acceptedQuotes = await this.quoteRepository.count({
                where: { status: QuoteStatus.ACCEPTED }
            });
            const expiredQuotes = await this.quoteRepository.count({
                where: { status: QuoteStatus.EXPIRED }
            });

            // Get total value using query
            const totalValueResult = await this.quoteRepository.query(
                'SELECT SUM(total_amount) as total_value FROM quotes'
            );
            const totalValue = parseFloat(totalValueResult?.[0]?.total_value || '0');

            return {
                totalQuotes,
                totalValue,
                byStatus: {
                    draft: draftQuotes,
                    sent: sentQuotes,
                    accepted: acceptedQuotes,
                    expired: expiredQuotes
                },
                conversionRate: totalQuotes > 0 ? (acceptedQuotes / totalQuotes) * 100 : 0
            };
        } catch (error) {
            this.logger.error(`Failed to get quote analytics: ${error.message}`, error.stack);
            throw new Error(`Failed to get quote analytics: ${error.message}`);
        }
    }

    // Private helper methods

    private generateQuoteNumber(): string {
        const timestamp = Date.now().toString().slice(-8);
        const random = Math.floor(Math.random() * 1000)
            .toString()
            .padStart(3, '0');
        return `Q${timestamp}${random}`;
    }

    private formatQuoteForResponse(quote: any): any {
        return {
            id: quote.id,
            quoteNumber: quote.quote_number || quote.quoteNumber,
            caseId: quote.case_id || quote.caseId,
            transactionType: quote.transaction_type || quote.transactionType,
            propertyValue: parseFloat(quote.property_value || quote.propertyValue || '0'),
            propertyAddress: quote.property_address || quote.propertyAddress,
            propertyConditions: quote.property_conditions || quote.propertyConditions,
            clientDetails: quote.client_details || quote.clientDetails,
            quoteBreakdown: quote.quote_breakdown || quote.quoteBreakdown,
            totalAmount: parseFloat(quote.total_amount || quote.totalAmount || '0'),
            promoCode: quote.promo_code || quote.promoCode,
            discountAmount: parseFloat(quote.discount_amount || quote.discountAmount || '0'),
            status: quote.status,
            expiresAt: quote.expires_at || quote.expiresAt,
            createdBy: quote.created_by || quote.createdBy,
            createdAt: quote.created_at || quote.createdAt,
            updatedAt: quote.updated_at || quote.updatedAt
        };
    }

    private async createCaseFromQuote(
        quote: Quote,
        conversionRequest: LeadConversionRequest
    ): Promise<{
        caseId: string;
        caseNumber: string;
    }> {
        try {
            this.logger.log(`Creating case from quote: ${quote.quoteNumber}`);
            this.logger.log(
                `Conversion request details: acceptedBy=${conversionRequest.acceptedBy}, acceptedAt=${conversionRequest.acceptedAt}`
            );

            // Get the tenant data source to access case management entities
            const tenantId = this.tenantContextService.getTenantId();
            const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);

            // Get repositories for case management
            const caseRepository = dataSource.getRepository(Case);
            const clientRepository = dataSource.getRepository(Client);
            const caseAssignmentRepository = dataSource.getRepository(CaseAssignment);

            // Create or find client
            let clientId: string;
            const existingClient = await clientRepository.findOne({
                where: { email: quote.clientDetails.email }
            });

            if (existingClient) {
                clientId = existingClient.id;
                this.logger.log(`Found existing client: ${clientId}`);
                // Update client information if needed
                existingClient.name =
                    quote.clientDetails.firstName + ' ' + quote.clientDetails.lastName;
                existingClient.phone = quote.clientDetails.phone || '';
                await clientRepository.save(existingClient);
            } else {
                this.logger.log(`Creating new client for email: ${quote.clientDetails.email}`);
                this.logger.log(
                    `Client creation data: name=${quote.clientDetails.firstName + ' ' + quote.clientDetails.lastName}, createdBy=${conversionRequest.acceptedBy}`
                );

                // Create new client
                const createdBy = conversionRequest.acceptedBy || quote.createdBy || 'system';
                this.logger.log(`Using createdBy: ${createdBy} for client creation`);

                const newClient = clientRepository.create({
                    name: quote.clientDetails.firstName + ' ' + quote.clientDetails.lastName,
                    email: quote.clientDetails.email,
                    phone: quote.clientDetails.phone,
                    address: JSON.stringify(quote.propertyAddress),
                    additionalInfo: {
                        numberOfBuyers: quote.clientDetails.numberOfBuyers,
                        quoteId: quote.id,
                        quoteNumber: quote.quoteNumber
                    },
                    createdBy: createdBy
                });

                const savedClient = await clientRepository.save(newClient);
                clientId = savedClient.id;
                this.logger.log(`New client created successfully: ${clientId}`);
            }

            // Generate case number (using timestamp + random for uniqueness)
            const caseNumber = `CASE-${Date.now()}${Math.floor(Math.random() * 1000)
                .toString()
                .padStart(3, '0')}`;

            // Create case
            const caseCreatedBy = conversionRequest.acceptedBy || quote.createdBy || 'system';
            this.logger.log(`Using createdBy: ${caseCreatedBy} for case creation`);

            // Map priority from request to CasePriority enum
            const casePriority = this.mapPriorityToEnum(conversionRequest.priority);

            // Map case type from request to CaseType enum
            const caseType = this.mapCaseTypeToEnum(conversionRequest.caseType);

            // Add case notes if provided
            let description = `Conveyancing case created from quote ${quote.quoteNumber}. Property value: £${quote.propertyValue.toLocaleString()}. Transaction type: ${quote.transactionType}.`;
            if (conversionRequest.caseNotes) {
                description += `\n\nCase Notes: ${conversionRequest.caseNotes}`;
            }

            const newCase = caseRepository.create({
                caseNumber,
                title: `Conveyancing Case - ${quote.transactionType} - ${quote.quoteNumber}`,
                description,
                priority: casePriority,
                type: caseType,
                status: CaseStatus.DRAFT,
                clientId,
                createdBy: caseCreatedBy
            });

            const savedCase = await caseRepository.save(newCase);

            // Create initial case assignment (assign to the user who accepted the quote)
            const assignmentUserId = conversionRequest.acceptedBy || quote.createdBy || 'system';
            const assignmentUserName = conversionRequest.assignedTo || assignmentUserId;
            this.logger.log(`Using userId: ${assignmentUserId} for case assignment`);

            const assignment = caseAssignmentRepository.create({
                caseId: savedCase.id,
                userId: assignmentUserId,
                userName: assignmentUserName,
                assignedBy: assignmentUserId,
                notes: `Case created from quote ${quote.quoteNumber}. Quote accepted on ${conversionRequest.acceptedAt.toISOString()}.${conversionRequest.assignedTo ? ` Assigned to: ${conversionRequest.assignedTo}` : ''}`,
                isActive: true
            });

            await caseAssignmentRepository.save(assignment);

            // Create case-specific milestones and tasks from seeded templates
            if (
                savedCase.type === CaseType.CONVEYANCING ||
                savedCase.type === CaseType.REAL_ESTATE
            ) {
                try {
                    await this.conveyancingMilestoneService.createDefaultMilestonesForCase(
                        savedCase.id,
                        caseCreatedBy
                    );
                    this.logger.log(`Created default milestones for case: ${savedCase.id}`);
                } catch (error) {
                    this.logger.error(
                        `Failed to create milestones for case ${savedCase.id}:`,
                        error
                    );
                    // Don't fail case creation if milestone creation fails
                }
            }

            this.logger.log(
                `Case created successfully: ${savedCase.caseNumber} for quote ${quote.quoteNumber}`
            );

            return {
                caseId: savedCase.id,
                caseNumber: savedCase.caseNumber
            };
        } catch (error) {
            this.logger.error(`Failed to create case from quote: ${error.message}`, error.stack);
            throw new Error(`Failed to create case from quote: ${error.message}`);
        }
    }

    private async assignSolicitorToCase(caseId: string): Promise<string | undefined> {
        try {
            // Get the tenant data source to access case management entities
            const tenantId = this.tenantContextService.getTenantId();
            const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);

            // Get the case assignment repository
            const caseAssignmentRepository = dataSource.getRepository(CaseAssignment);

            // Find the current assignment for this case
            const currentAssignment = await caseAssignmentRepository.findOne({
                where: { caseId, isActive: true }
            });

            if (currentAssignment) {
                // Return the currently assigned user ID
                return currentAssignment.userId;
            }

            // If no assignment found, this shouldn't happen as we create one in createCaseFromQuote
            this.logger.warn(`No active assignment found for case ${caseId}`);
            return undefined;
        } catch (error) {
            this.logger.error(
                `Failed to get solicitor assignment for case: ${error.message}`,
                error.stack
            );
            return undefined;
        }
    }

    private generateNextSteps(caseNumber: string, solicitorId?: string): string[] {
        const steps = [
            'Conveyancing case created from accepted quote',
            'Initial client consultation scheduled',
            'Document collection process initiated',
            'Property searches ordered',
            'Local authority searches requested',
            'Land registry searches initiated',
            'Client identity verification process started',
            'Anti-money laundering checks initiated',
            'Draft contract preparation begun',
            'Exchange of contracts preparation'
        ];

        if (solicitorId) {
            steps.unshift(`Solicitor ${solicitorId} assigned to case ${caseNumber}`);
        }

        return steps;
    }

    /**
     * Map priority string to CasePriority enum
     */
    private mapPriorityToEnum(priority?: string): CasePriority {
        if (!priority) return CasePriority.MEDIUM;

        switch (priority.toLowerCase()) {
            case 'low':
                return CasePriority.LOW;
            case 'medium':
                return CasePriority.MEDIUM;
            case 'high':
                return CasePriority.HIGH;
            case 'urgent':
                return CasePriority.URGENT;
            default:
                return CasePriority.MEDIUM;
        }
    }

    /**
     * Map case type string to CaseType enum
     */
    private mapCaseTypeToEnum(caseType?: string): CaseType {
        if (!caseType) return CaseType.CONVEYANCING;

        switch (caseType.toLowerCase()) {
            case 'conveyancing':
            case 'real_estate':
                return CaseType.CONVEYANCING;
            case 'litigation':
                return CaseType.LITIGATION;
            case 'corporate':
                return CaseType.CORPORATE;
            case 'intellectual_property':
                return CaseType.INTELLECTUAL_PROPERTY;
            case 'family':
                return CaseType.FAMILY;
            case 'criminal':
                return CaseType.CRIMINAL;
            default:
                return CaseType.CONVEYANCING;
        }
    }
}
