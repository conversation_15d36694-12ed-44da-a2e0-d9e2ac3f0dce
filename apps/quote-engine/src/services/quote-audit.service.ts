import { Injectable, Logger } from '@nestjs/common';
import { Request } from 'express';
import { QuoteAuditRepository } from '../repositories/quote-audit.repository';
import { QuoteAuditAction, QuoteStatus } from '@app/common/typeorm/entities/tenant';

declare module 'express' {
    interface Request {
        sessionID?: string;
        connection?: {
            remoteAddress?: string;
        };
    }
}

@Injectable()
export class QuoteAuditService {
    private readonly logger = new Logger(QuoteAuditService.name);

    constructor(private readonly quoteAuditRepository: QuoteAuditRepository) {}

    /**
     * Extract request information for audit logging
     */
    private extractRequestInfo(request: Request): {
        ipAddress: string;
        userAgent: string;
        sessionId?: string;
    } {
        return {
            ipAddress: request.ip || request?.connection?.remoteAddress || 'unknown',
            userAgent: request.get('User-Agent') || 'unknown',
            sessionId: request?.sessionID || 'unknown'
        };
    }

    /**
     * Log quote creation
     */
    async logQuoteCreated(
        quoteId: string,
        userId: string,
        userName: string,
        request: Request,
        details?: Record<string, any>
    ): Promise<void> {
        try {
            const requestInfo = this.extractRequestInfo(request);
            await this.quoteAuditRepository.logAction(
                quoteId,
                QuoteAuditAction.CREATED,
                userId,
                userName,
                requestInfo.ipAddress,
                {
                    ...details,
                    timestamp: new Date().toISOString()
                },
                requestInfo.userAgent,
                requestInfo.sessionId
            );

            this.logger.debug(
                `Audit log created for quote ${quoteId}: CREATED by ${userName} (${userId})`
            );
        } catch (error) {
            this.logger.error(
                `Failed to create audit log for quote ${quoteId}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Log quote view
     */
    async logQuoteViewed(
        quoteId: string,
        userId: string,
        userName: string,
        request: Request
    ): Promise<void> {
        try {
            const requestInfo = this.extractRequestInfo(request);
            await this.quoteAuditRepository.logAction(
                quoteId,
                QuoteAuditAction.VIEWED,
                userId,
                userName,
                requestInfo.ipAddress,
                { timestamp: new Date().toISOString() },
                requestInfo.userAgent,
                requestInfo.sessionId
            );
        } catch (error) {
            this.logger.error(
                `Failed to log quote view for ${quoteId}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Log status change
     */
    async logStatusChange(
        quoteId: string,
        oldStatus: QuoteStatus,
        newStatus: QuoteStatus,
        userId: string,
        userName: string,
        request: Request,
        details?: Record<string, any>
    ): Promise<void> {
        try {
            const requestInfo = this.extractRequestInfo(request);
            const action = this.getStatusChangeAction(newStatus);

            await this.quoteAuditRepository.logAction(
                quoteId,
                action,
                userId,
                userName,
                requestInfo.ipAddress,
                {
                    oldStatus,
                    newStatus,
                    ...details,
                    timestamp: new Date().toISOString()
                },
                requestInfo.userAgent,
                requestInfo.sessionId
            );

            this.logger.debug(
                `Audit log created for quote ${quoteId}: ${action} by ${userName} (${userId})`
            );
        } catch (error) {
            this.logger.error(
                `Failed to log status change for quote ${quoteId}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Log promo code application
     */
    async logPromoCodeApplied(
        quoteId: string,
        promoCode: string,
        discount: number,
        userId: string,
        userName: string,
        request: Request
    ): Promise<void> {
        try {
            const requestInfo = this.extractRequestInfo(request);
            await this.quoteAuditRepository.logAction(
                quoteId,
                QuoteAuditAction.PROMO_CODE_APPLIED,
                userId,
                userName,
                requestInfo.ipAddress,
                {
                    promoCode,
                    discount,
                    timestamp: new Date().toISOString()
                },
                requestInfo.userAgent,
                requestInfo.sessionId
            );
        } catch (error) {
            this.logger.error(
                `Failed to log promo code application for quote ${quoteId}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Log quote calculation
     */
    async logQuoteCalculated(
        quoteId: string,
        userId: string,
        userName: string,
        request: Request,
        calculationDetails?: Record<string, any>
    ): Promise<void> {
        try {
            const requestInfo = this.extractRequestInfo(request);
            await this.quoteAuditRepository.logAction(
                quoteId,
                QuoteAuditAction.CALCULATED,
                userId,
                userName,
                requestInfo.ipAddress,
                {
                    ...calculationDetails,
                    timestamp: new Date().toISOString()
                },
                requestInfo.userAgent,
                requestInfo.sessionId
            );
        } catch (error) {
            this.logger.error(
                `Failed to log quote calculation for ${quoteId}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Log quote conversion to case
     */
    async logQuoteConverted(
        quoteId: string,
        caseId: string,
        userId: string,
        userName: string,
        request: Request,
        details?: Record<string, any>
    ): Promise<void> {
        try {
            const requestInfo = this.extractRequestInfo(request);
            await this.quoteAuditRepository.logAction(
                quoteId,
                QuoteAuditAction.CONVERTED_TO_CASE,
                userId,
                userName,
                requestInfo.ipAddress,
                {
                    caseId,
                    ...details,
                    timestamp: new Date().toISOString()
                },
                requestInfo.userAgent,
                requestInfo.sessionId
            );
        } catch (error) {
            this.logger.error(
                `Failed to log quote conversion for ${quoteId}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Log email sent
     */
    async logEmailSent(
        quoteId: string,
        recipientEmail: string,
        emailType: string,
        userId: string,
        userName: string,
        request: Request,
        details?: Record<string, any>
    ): Promise<void> {
        try {
            const requestInfo = this.extractRequestInfo(request);
            await this.quoteAuditRepository.logAction(
                quoteId,
                QuoteAuditAction.EMAIL_SENT,
                userId,
                userName,
                requestInfo.ipAddress,
                {
                    recipientEmail,
                    emailType,
                    ...details,
                    timestamp: new Date().toISOString()
                },
                requestInfo.userAgent,
                requestInfo.sessionId
            );
        } catch (error) {
            this.logger.error(
                `Failed to log email sent for quote ${quoteId}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Log attachment added
     */
    async logAttachmentAdded(
        quoteId: string,
        attachmentId: string,
        filename: string,
        userId: string,
        userName: string,
        request: Request
    ): Promise<void> {
        try {
            const requestInfo = this.extractRequestInfo(request);
            await this.quoteAuditRepository.logAction(
                quoteId,
                QuoteAuditAction.ATTACHMENT_ADDED,
                userId,
                userName,
                requestInfo.ipAddress,
                {
                    attachmentId,
                    filename,
                    timestamp: new Date().toISOString()
                },
                requestInfo.userAgent,
                requestInfo.sessionId
            );
        } catch (error) {
            this.logger.error(
                `Failed to log attachment added for quote ${quoteId}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Log note added
     */
    async logNoteAdded(
        quoteId: string,
        noteId: string,
        noteContent: string,
        userId: string,
        userName: string,
        request: Request
    ): Promise<void> {
        try {
            const requestInfo = this.extractRequestInfo(request);
            await this.quoteAuditRepository.logAction(
                quoteId,
                QuoteAuditAction.NOTE_ADDED,
                userId,
                userName,
                requestInfo.ipAddress,
                {
                    noteId,
                    noteContent: noteContent.substring(0, 100), // Truncate for storage
                    timestamp: new Date().toISOString()
                },
                requestInfo.userAgent,
                requestInfo.sessionId
            );
        } catch (error) {
            this.logger.error(
                `Failed to log note added for quote ${quoteId}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Get audit trail for a quote
     */
    async getQuoteAuditTrail(quoteId: string, limit: number = 100): Promise<any[]> {
        try {
            const audits = await this.quoteAuditRepository.findByQuoteId(quoteId, limit);
            return audits.map((audit) => ({
                id: audit.id,
                action: audit.action,
                performedBy: audit.performedBy,
                performedByName: audit.performedByName,
                performedAt: audit.performedAt,
                details: audit.details,
                ipAddress: audit.ipAddress
            }));
        } catch (error) {
            this.logger.error(
                `Failed to get audit trail for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            return [];
        }
    }

    /**
     * Get audit trail with pagination
     */
    async getQuoteAuditTrailPaginated(
        quoteId: string,
        page: number = 1,
        limit: number = 20
    ): Promise<{
        audits: any[];
        total: number;
        page: number;
        totalPages: number;
    }> {
        try {
            return await this.quoteAuditRepository.getAuditTrail(quoteId, page, limit);
        } catch (error) {
            this.logger.error(
                `Failed to get paginated audit trail for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            return {
                audits: [],
                total: 0,
                page,
                totalPages: 0
            };
        }
    }

    /**
     * Get audit statistics for a quote
     */
    async getQuoteAuditStats(quoteId: string): Promise<{
        totalActions: number;
        lastActivity: Date | null;
        actionCounts: Record<string, number>;
    }> {
        try {
            return await this.quoteAuditRepository.getAuditStats(quoteId);
        } catch (error) {
            this.logger.error(
                `Failed to get audit stats for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            return {
                totalActions: 0,
                lastActivity: null,
                actionCounts: {}
            };
        }
    }

    /**
     * Generic method to log an action
     */
    async logAction(
        quoteId: string,
        action: QuoteAuditAction,
        performedBy: string,
        performedByName: string,
        request: Request,
        details?: Record<string, any>
    ): Promise<void> {
        try {
            const requestInfo = this.extractRequestInfo(request);
            await this.quoteAuditRepository.logAction(
                quoteId,
                action,
                performedBy,
                performedByName,
                requestInfo.ipAddress,
                details,
                requestInfo.userAgent,
                requestInfo.sessionId
            );

            this.logger.debug(
                `Audit log created for quote ${quoteId}: ${action} by ${performedByName} (${performedBy})`
            );
        } catch (error) {
            this.logger.error(
                `Failed to create audit log for quote ${quoteId}: ${error.message}`,
                error.stack
            );
        }
    }

    /**
     * Map status to audit action
     */
    private getStatusChangeAction(status: QuoteStatus): QuoteAuditAction {
        switch (status) {
            case QuoteStatus.DRAFT:
                return QuoteAuditAction.STATUS_DRAFT;
            case QuoteStatus.SENT:
                return QuoteAuditAction.STATUS_SENT;
            case QuoteStatus.ACCEPTED:
                return QuoteAuditAction.STATUS_ACCEPTED;
            case QuoteStatus.EXPIRED:
                return QuoteAuditAction.STATUS_EXPIRED;
            default:
                return QuoteAuditAction.STATUS_CHANGED;
        }
    }
}
