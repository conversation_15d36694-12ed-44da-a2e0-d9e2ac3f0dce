import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Request } from 'express';
import { QuoteCommunicationRepository } from '../repositories/quote-communication.repository';
import { QuoteAuditService } from './quote-audit.service';
import {
    QuoteCommunication,
    CommunicationType,
    CommunicationDirection,
    CommunicationPriority,
    QuoteAuditAction
} from '@app/common/typeorm/entities/tenant';

export interface CreateCommunicationDto {
    quoteId: string;
    type: CommunicationType;
    direction: CommunicationDirection;
    priority?: CommunicationPriority;
    subject: string;
    body: string;
    sender: string;
    senderName?: string;
    senderEmail?: string;
    recipient?: string;
    recipientName?: string;
    recipientEmail?: string;
    attachments?: string[];
    metadata?: Record<string, any>;
    followUpRequired?: boolean;
    followUpDate?: Date;
    followUpAssignedTo?: string;
    followUpAssignedToName?: string;
}

export interface UpdateCommunicationDto {
    subject?: string;
    body?: string;
    priority?: CommunicationPriority;
    metadata?: Record<string, any>;
    followUpRequired?: boolean;
    followUpDate?: Date;
    followUpAssignedTo?: string;
    followUpAssignedToName?: string;
}

export interface CommunicationFilters {
    type?: CommunicationType;
    direction?: CommunicationDirection;
    priority?: CommunicationPriority;
    isRead?: boolean;
    followUpRequired?: boolean;
    startDate?: Date;
    endDate?: Date;
}

@Injectable()
export class QuoteCommunicationService {
    private readonly logger = new Logger(QuoteCommunicationService.name);

    constructor(
        private readonly quoteCommunicationRepository: QuoteCommunicationRepository,
        private readonly quoteAuditService: QuoteAuditService
    ) {}

    /**
     * Create a new communication entry
     */
    async createCommunication(
        createDto: CreateCommunicationDto,
        createdBy: string,
        request: Request
    ): Promise<QuoteCommunication> {
        try {
            this.logger.log(
                `Creating communication for quote ${createDto.quoteId}: ${createDto.type}`
            );

            const communication = await this.quoteCommunicationRepository.create({
                quoteId: createDto.quoteId,
                type: createDto.type,
                direction: createDto.direction,
                priority: createDto.priority || CommunicationPriority.MEDIUM,
                subject: createDto.subject,
                body: createDto.body,
                sender: createDto.sender,
                senderName: createDto.senderName,
                senderEmail: createDto.senderEmail,
                recipient: createDto.recipient,
                recipientName: createDto.recipientName,
                recipientEmail: createDto.recipientEmail,
                attachments: createDto.attachments || [],
                metadata: createDto.metadata || {},
                followUpRequired: createDto.followUpRequired || false,
                followUpDate: createDto.followUpDate,
                followUpAssignedTo: createDto.followUpAssignedTo,
                followUpAssignedToName: createDto.followUpAssignedToName,
                createdBy,
                sentAt: new Date(),
                createdAt: new Date(),
                updatedAt: new Date()
            });

            const savedCommunication = await this.quoteCommunicationRepository.save(communication);

            // Log audit trail
            await this.quoteAuditService.logAction(
                createDto.quoteId,
                QuoteAuditAction.NOTE_ADDED,
                createdBy,
                createDto.senderName || createdBy,
                request,
                {
                    communicationId: savedCommunication.id,
                    communicationType: createDto.type,
                    subject: createDto.subject,
                    direction: createDto.direction,
                    priority: createDto.priority
                }
            );

            this.logger.log(`Communication created successfully: ${savedCommunication.id}`);
            return savedCommunication;
        } catch (error) {
            this.logger.error(`Failed to create communication: ${error.message}`, error.stack);
            throw new HttpException(
                `Failed to create communication: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get communications for a quote
     */
    async getCommunicationsForQuote(
        quoteId: string,
        limit: number = 50
    ): Promise<QuoteCommunication[]> {
        try {
            this.logger.log(`Getting communications for quote ${quoteId}`);
            return await this.quoteCommunicationRepository.findByQuoteId(quoteId, limit);
        } catch (error) {
            this.logger.error(
                `Failed to get communications for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get communications: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get communications with pagination and filters
     */
    async getCommunicationsPaginated(
        quoteId: string,
        page: number = 1,
        limit: number = 20,
        filters?: CommunicationFilters
    ): Promise<{
        communications: QuoteCommunication[];
        total: number;
        page: number;
        totalPages: number;
    }> {
        try {
            this.logger.log(`Getting paginated communications for quote ${quoteId}`);

            const filterOptions = filters
                ? {
                      type: filters.type,
                      direction: filters.direction,
                      priority: filters.priority,
                      isRead: filters.isRead,
                      followUpRequired: filters.followUpRequired
                  }
                : undefined;

            return await this.quoteCommunicationRepository.getCommunicationsPaginated(
                quoteId,
                page,
                limit,
                filterOptions
            );
        } catch (error) {
            this.logger.error(
                `Failed to get paginated communications for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get paginated communications: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get communication by ID
     */
    async getCommunicationById(communicationId: string): Promise<QuoteCommunication> {
        try {
            const communication = await this.quoteCommunicationRepository.findOne({
                where: { id: communicationId }
            });

            if (!communication) {
                throw new HttpException('Communication not found', HttpStatus.NOT_FOUND);
            }

            return communication;
        } catch (error) {
            this.logger.error(
                `Failed to get communication ${communicationId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get communication: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Update communication
     */
    async updateCommunication(
        communicationId: string,
        updateDto: UpdateCommunicationDto,
        updatedBy: string,
        request: Request
    ): Promise<QuoteCommunication> {
        try {
            this.logger.log(`Updating communication ${communicationId}`);

            const communication = await this.getCommunicationById(communicationId);

            Object.assign(communication, updateDto);
            communication.updatedAt = new Date();

            const updatedCommunication =
                await this.quoteCommunicationRepository.save(communication);

            // Log audit trail
            await this.quoteAuditService.logAction(
                communication.quoteId,
                QuoteAuditAction.NOTE_UPDATED,
                updatedBy,
                updatedBy,
                request,
                {
                    communicationId,
                    changes: updateDto
                }
            );

            this.logger.log(`Communication updated successfully: ${communicationId}`);
            return updatedCommunication;
        } catch (error) {
            this.logger.error(
                `Failed to update communication ${communicationId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to update communication: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Mark communication as read
     */
    async markAsRead(
        communicationId: string,
        readBy: string,
        readByName: string,
        request: Request
    ): Promise<QuoteCommunication> {
        try {
            this.logger.log(`Marking communication ${communicationId} as read`);

            const communication = await this.quoteCommunicationRepository.markAsRead(
                communicationId,
                readBy,
                readByName
            );

            // Log audit trail
            await this.quoteAuditService.logAction(
                communication.quoteId,
                QuoteAuditAction.VIEWED,
                readBy,
                readByName,
                request,
                {
                    communicationId,
                    communicationType: communication.type,
                    subject: communication.subject
                }
            );

            this.logger.log(`Communication marked as read: ${communicationId}`);
            return communication;
        } catch (error) {
            this.logger.error(
                `Failed to mark communication ${communicationId} as read: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to mark communication as read: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Archive communication
     */
    async archiveCommunication(
        communicationId: string,
        archivedBy: string,
        request: Request
    ): Promise<QuoteCommunication> {
        try {
            this.logger.log(`Archiving communication ${communicationId}`);

            const communication = await this.quoteCommunicationRepository.archiveCommunication(
                communicationId,
                archivedBy
            );

            // Log audit trail
            await this.quoteAuditService.logAction(
                communication.quoteId,
                QuoteAuditAction.NOTE_DELETED,
                archivedBy,
                archivedBy,
                request,
                {
                    communicationId,
                    communicationType: communication.type,
                    subject: communication.subject
                }
            );

            this.logger.log(`Communication archived: ${communicationId}`);
            return communication;
        } catch (error) {
            this.logger.error(
                `Failed to archive communication ${communicationId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to archive communication: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Set follow-up for communication
     */
    async setFollowUp(
        communicationId: string,
        followUpDate: Date,
        assignedTo: string,
        assignedToName: string,
        request: Request
    ): Promise<QuoteCommunication> {
        try {
            this.logger.log(`Setting follow-up for communication ${communicationId}`);

            const communication = await this.quoteCommunicationRepository.setFollowUp(
                communicationId,
                followUpDate,
                assignedTo,
                assignedToName
            );

            // Log audit trail
            await this.quoteAuditService.logAction(
                communication.quoteId,
                QuoteAuditAction.REMINDER_SET,
                assignedTo,
                assignedToName,
                request,
                {
                    communicationId,
                    followUpDate,
                    assignedTo
                }
            );

            this.logger.log(`Follow-up set for communication: ${communicationId}`);
            return communication;
        } catch (error) {
            this.logger.error(
                `Failed to set follow-up for communication ${communicationId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to set follow-up: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Complete follow-up
     */
    async completeFollowUp(
        communicationId: string,
        completedBy: string,
        request: Request
    ): Promise<QuoteCommunication> {
        try {
            this.logger.log(`Completing follow-up for communication ${communicationId}`);

            const communication =
                await this.quoteCommunicationRepository.completeFollowUp(communicationId);

            // Log audit trail
            await this.quoteAuditService.logAction(
                communication.quoteId,
                QuoteAuditAction.FOLLOW_UP_SENT,
                completedBy,
                completedBy,
                request,
                {
                    communicationId,
                    completedAt: new Date()
                }
            );

            this.logger.log(`Follow-up completed for communication: ${communicationId}`);
            return communication;
        } catch (error) {
            this.logger.error(
                `Failed to complete follow-up for communication ${communicationId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to complete follow-up: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get communication statistics for a quote
     */
    async getCommunicationStats(quoteId: string): Promise<{
        totalCommunications: number;
        unreadCommunications: number;
        followUpRequired: number;
        overdueFollowUps: number;
        typeCounts: Record<string, number>;
        directionCounts: Record<string, number>;
        priorityCounts: Record<string, number>;
        lastCommunication: Date | null;
    }> {
        try {
            this.logger.log(`Getting communication stats for quote ${quoteId}`);
            return await this.quoteCommunicationRepository.getCommunicationStats(quoteId);
        } catch (error) {
            this.logger.error(
                `Failed to get communication stats for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get communication stats: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get unread communications for a user
     */
    async getUnreadCommunicationsForUser(userId: string): Promise<QuoteCommunication[]> {
        try {
            this.logger.log(`Getting unread communications for user ${userId}`);
            return await this.quoteCommunicationRepository.findUnreadForUser(userId);
        } catch (error) {
            this.logger.error(
                `Failed to get unread communications for user ${userId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get unread communications: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get communications requiring follow-up
     */
    async getFollowUpRequired(): Promise<QuoteCommunication[]> {
        try {
            this.logger.log('Getting communications requiring follow-up');
            return await this.quoteCommunicationRepository.findFollowUpRequired();
        } catch (error) {
            this.logger.error(
                `Failed to get follow-up required communications: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get follow-up required communications: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get overdue follow-ups
     */
    async getOverdueFollowUps(): Promise<QuoteCommunication[]> {
        try {
            this.logger.log('Getting overdue follow-ups');
            return await this.quoteCommunicationRepository.findOverdueFollowUps();
        } catch (error) {
            this.logger.error(`Failed to get overdue follow-ups: ${error.message}`, error.stack);
            throw new HttpException(
                `Failed to get overdue follow-ups: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Search communications by content
     */
    async searchCommunications(
        quoteId: string,
        searchTerm: string,
        limit: number = 20
    ): Promise<QuoteCommunication[]> {
        try {
            this.logger.log(
                `Searching communications for quote ${quoteId} with term: ${searchTerm}`
            );
            return await this.quoteCommunicationRepository.searchCommunications(
                quoteId,
                searchTerm,
                limit
            );
        } catch (error) {
            this.logger.error(
                `Failed to search communications for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to search communications: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get recent activity for a user
     */
    async getRecentActivityForUser(
        userId: string,
        limit: number = 20
    ): Promise<QuoteCommunication[]> {
        try {
            this.logger.log(`Getting recent activity for user ${userId}`);
            return await this.quoteCommunicationRepository.getRecentActivityForUser(userId, limit);
        } catch (error) {
            this.logger.error(
                `Failed to get recent activity for user ${userId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get recent activity: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Add system note
     */
    async addSystemNote(
        quoteId: string,
        note: string,
        userId: string,
        userName: string,
        request: Request
    ): Promise<QuoteCommunication> {
        try {
            this.logger.log(`Adding system note for quote ${quoteId}`);

            return await this.createCommunication(
                {
                    quoteId,
                    type: CommunicationType.SYSTEM_UPDATE,
                    direction: CommunicationDirection.SYSTEM,
                    priority: CommunicationPriority.MEDIUM,
                    subject: 'System Note',
                    body: note,
                    sender: userId,
                    senderName: userName
                },
                userId,
                request
            );
        } catch (error) {
            this.logger.error(
                `Failed to add system note for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to add system note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Log email sent
     */
    async logEmailSent(
        quoteId: string,
        emailDetails: {
            subject: string;
            body: string;
            recipientEmail: string;
            recipientName?: string;
            senderEmail: string;
            senderName: string;
        },
        userId: string,
        request: Request
    ): Promise<QuoteCommunication> {
        try {
            this.logger.log(`Logging email sent for quote ${quoteId}`);

            return await this.createCommunication(
                {
                    quoteId,
                    type: CommunicationType.EMAIL,
                    direction: CommunicationDirection.OUTBOUND,
                    priority: CommunicationPriority.MEDIUM,
                    subject: emailDetails.subject,
                    body: emailDetails.body,
                    sender: userId,
                    senderName: emailDetails.senderName,
                    senderEmail: emailDetails.senderEmail,
                    recipient: emailDetails.recipientEmail,
                    recipientName: emailDetails.recipientName,
                    recipientEmail: emailDetails.recipientEmail,
                    metadata: {
                        emailType: 'quote_communication',
                        sentAt: new Date().toISOString()
                    }
                },
                userId,
                request
            );
        } catch (error) {
            this.logger.error(
                `Failed to log email sent for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to log email sent: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Log call made
     */
    async logCall(
        quoteId: string,
        callDetails: {
            subject: string;
            notes?: string;
            body?: string; // Add support for 'body' field
            recipientName?: string;
            recipientPhone?: string;
            duration?: number;
            outcome?: string;
        },
        userId: string,
        userName: string,
        request: Request
    ): Promise<QuoteCommunication> {
        try {
            this.logger.log(`Logging call for quote ${quoteId}`);

            // Use 'notes' if provided, otherwise fall back to 'body'
            const callBody = callDetails.notes || callDetails.body || '';

            if (!callBody) {
                throw new Error('Call notes/body is required');
            }

            return await this.createCommunication(
                {
                    quoteId,
                    type: CommunicationType.CALL,
                    direction: CommunicationDirection.OUTBOUND,
                    priority: CommunicationPriority.MEDIUM,
                    subject: callDetails.subject,
                    body: callBody,
                    sender: userId,
                    senderName: userName,
                    recipient: callDetails.recipientPhone,
                    recipientName: callDetails.recipientName,
                    metadata: {
                        callDuration: callDetails.duration,
                        callOutcome: callDetails.outcome,
                        callTime: new Date().toISOString()
                    }
                },
                userId,
                request
            );
        } catch (error) {
            this.logger.error(
                `Failed to log call for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to log call: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get communication type display name
     */
    getCommunicationTypeDisplayName(type: CommunicationType): string {
        const displayNames: Record<CommunicationType, string> = {
            [CommunicationType.EMAIL]: 'Email',
            [CommunicationType.CALL]: 'Phone Call',
            [CommunicationType.SMS]: 'SMS',
            [CommunicationType.NOTE]: 'Note',
            [CommunicationType.SYSTEM_UPDATE]: 'System Update',
            [CommunicationType.DOCUMENT_UPLOAD]: 'Document Upload',
            [CommunicationType.STATUS_CHANGE]: 'Status Change',
            [CommunicationType.REMINDER]: 'Reminder',
            [CommunicationType.FOLLOW_UP]: 'Follow-up',
            [CommunicationType.QUOTE_SENT]: 'Quote Sent',
            [CommunicationType.QUOTE_VIEWED]: 'Quote Viewed',
            [CommunicationType.QUOTE_ACCEPTED]: 'Quote Accepted',
            [CommunicationType.QUOTE_REJECTED]: 'Quote Rejected',
            [CommunicationType.PAYMENT_RECEIVED]: 'Payment Received',
            [CommunicationType.APPOINTMENT_SCHEDULED]: 'Appointment Scheduled',
            [CommunicationType.APPOINTMENT_COMPLETED]: 'Appointment Completed',
            [CommunicationType.CLIENT_INQUIRY]: 'Client Inquiry',
            [CommunicationType.STAFF_RESPONSE]: 'Staff Response',
            [CommunicationType.ESCALATION]: 'Escalation',
            [CommunicationType.RESOLUTION]: 'Resolution'
        };

        return displayNames[type] || 'Unknown';
    }

    /**
     * Get communication direction display name
     */
    getCommunicationDirectionDisplayName(direction: CommunicationDirection): string {
        const displayNames: Record<CommunicationDirection, string> = {
            [CommunicationDirection.INBOUND]: 'Inbound',
            [CommunicationDirection.OUTBOUND]: 'Outbound',
            [CommunicationDirection.INTERNAL]: 'Internal',
            [CommunicationDirection.SYSTEM]: 'System'
        };

        return displayNames[direction] || 'Unknown';
    }

    /**
     * Get communication priority display name
     */
    getCommunicationPriorityDisplayName(priority: CommunicationPriority): string {
        const displayNames: Record<CommunicationPriority, string> = {
            [CommunicationPriority.LOW]: 'Low',
            [CommunicationPriority.MEDIUM]: 'Medium',
            [CommunicationPriority.HIGH]: 'High',
            [CommunicationPriority.URGENT]: 'Urgent'
        };

        return displayNames[priority] || 'Unknown';
    }
}
