import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { RateCardRepository } from '../repositories/rate-card.repository';
import { RateCardFeeItemRepository } from '../repositories/rate-card-fee-item.repository';
import {
    RateCard,
    RateCardStatus,
    RateCardProvider
} from '@app/common/typeorm/entities/tenant/rate-card.entity';
import {
    RateCardFeeItem,
    FeeItemType
} from '@app/common/typeorm/entities/tenant/rate-card-fee-item.entity';
import { TenantConnectionService } from '@app/common/multi-tenancy';

export interface CreateRateCardDto {
    providerName: string;
    providerCode: RateCardProvider;
    displayName: string;
    description?: string;
    version: string;
    effectiveDate: Date;
    expiryDate?: Date;
    priority: number;
    isDefault?: boolean;
    metadata?: Record<string, any>;
}

export interface UpdateRateCardDto {
    displayName?: string;
    description?: string;
    version?: string;
    effectiveDate?: Date;
    expiryDate?: Date;
    priority?: number;
    isDefault?: boolean;
    status?: RateCardStatus;
    metadata?: Record<string, any>;
}

export interface CreateFeeItemDto {
    label: string;
    categoryName: string;
    feeType: FeeItemType;
    netFee: number;
    vatFee: number;
    totalFee: number;
    displayOrder: number;
    active: boolean;
    perParty?: boolean;
    rangeStart?: number;
    rangeEnd?: number;
    applicableFor?: string;
    conditionSlug?: string;
    dynamic?: boolean;
    calculationFormula?: string;
}

export interface RateCardComparisonResult {
    rateCardId: string;
    providerName: string;
    displayName: string;
    totalFees: number;
    feeBreakdown: {
        category: string;
        items: Array<{
            label: string;
            netFee: number;
            vatFee: number;
            totalFee: number;
        }>;
    }[];
    applicableFeeItems: number;
    totalFeeItems: number;
    priority: number;
    isDefault: boolean;
}

@Injectable()
export class RateCardManagementService {
    private readonly logger = new Logger(RateCardManagementService.name);

    constructor(
        private readonly rateCardRepository: RateCardRepository,
        private readonly rateCardFeeItemRepository: RateCardFeeItemRepository,
        private readonly tenantConnectionService: TenantConnectionService
    ) {}

    /**
     * Create a new rate card
     */
    async createRateCard(dto: CreateRateCardDto): Promise<RateCard> {
        try {
            // If this is set as default, clear other defaults first
            if (dto.isDefault) {
                await this.rateCardRepository.clearDefault();
            }

            const rateCardData = {
                providerName: dto.providerName,
                providerCode: dto.providerCode,
                displayName: dto.displayName,
                description: dto.description,
                version: dto.version,
                effectiveDate: dto.effectiveDate,
                expiryDate: dto.expiryDate,
                priority: dto.priority,
                isDefault: dto.isDefault || false,
                status: RateCardStatus.ACTIVE,
                metadata: dto.metadata || {},
                createdAt: new Date(),
                updatedAt: new Date()
            };

            const rateCard = this.rateCardRepository.create(rateCardData);
            const savedRateCard = await this.rateCardRepository.save(rateCard as any);
            this.logger.log(
                `Created rate card: ${savedRateCard.displayName} for provider: ${savedRateCard.providerName}`
            );

            return savedRateCard;
        } catch (error) {
            this.logger.error(`Error creating rate card: ${error.message}`, error.stack);
            throw new BadRequestException(`Failed to create rate card: ${error.message}`);
        }
    }

    /**
     * Update an existing rate card
     */
    async updateRateCard(rateCardId: string, dto: UpdateRateCardDto): Promise<RateCard> {
        try {
            const rateCard = await this.rateCardRepository.findOne({
                where: { id: rateCardId }
            });

            if (!rateCard) {
                throw new NotFoundException(`Rate card with ID ${rateCardId} not found`);
            }

            // If setting as default, clear other defaults first
            if (dto.isDefault) {
                await this.rateCardRepository.clearDefault();
            }

            // Update the rate card
            Object.assign(rateCard, {
                ...dto,
                updatedAt: new Date()
            });

            const updatedRateCard = await this.rateCardRepository.save(rateCard);
            this.logger.log(`Updated rate card: ${updatedRateCard.displayName}`);

            return updatedRateCard;
        } catch (error) {
            this.logger.error(
                `Error updating rate card ${rateCardId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Delete a rate card and all its fee items
     */
    async deleteRateCard(rateCardId: string): Promise<void> {
        try {
            const rateCard = await this.rateCardRepository.findOne({
                where: { id: rateCardId }
            });

            if (!rateCard) {
                throw new NotFoundException(`Rate card with ID ${rateCardId} not found`);
            }

            // Delete all fee items first
            await this.rateCardFeeItemRepository.deleteByRateCardId(rateCardId);

            // Delete the rate card
            await this.rateCardRepository.remove(rateCard);

            this.logger.log(`Deleted rate card: ${rateCard.displayName}`);
        } catch (error) {
            this.logger.error(
                `Error deleting rate card ${rateCardId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Get all active rate cards
     */
    async getActiveRateCards(): Promise<RateCard[]> {
        try {
            return await this.rateCardRepository.findActiveRateCards();
        } catch (error) {
            this.logger.error(`Error fetching active rate cards: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get rate cards by provider
     */
    async getRateCardsByProvider(providerName: RateCardProvider): Promise<RateCard[]> {
        try {
            const rateCard = await this.rateCardRepository.findByProvider(providerName);
            return rateCard ? [rateCard] : [];
        } catch (error) {
            this.logger.error(
                `Error fetching rate cards for provider ${providerName}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Get the default rate card
     */
    async getDefaultRateCard(): Promise<RateCard | null> {
        try {
            return await this.rateCardRepository.findDefault();
        } catch (error) {
            this.logger.error(`Error fetching default rate card: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Set a rate card as default
     */
    async setDefaultRateCard(rateCardId: string): Promise<RateCard> {
        try {
            const rateCard = await this.rateCardRepository.findOne({
                where: { id: rateCardId }
            });

            if (!rateCard) {
                throw new NotFoundException(`Rate card with ID ${rateCardId} not found`);
            }

            // Clear other defaults first
            await this.rateCardRepository.clearDefault();

            // Set this one as default
            rateCard.isDefault = true;
            rateCard.updatedAt = new Date();

            const updatedRateCard = await this.rateCardRepository.save(rateCard);
            this.logger.log(`Set rate card as default: ${updatedRateCard.displayName}`);

            return updatedRateCard;
        } catch (error) {
            this.logger.error(
                `Error setting default rate card ${rateCardId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Add a fee item to a rate card
     */
    async addFeeItem(rateCardId: string, dto: CreateFeeItemDto): Promise<RateCardFeeItem> {
        try {
            const rateCard = await this.rateCardRepository.findOne({
                where: { id: rateCardId }
            });

            if (!rateCard) {
                throw new NotFoundException(`Rate card with ID ${rateCardId} not found`);
            }

            const feeItem = this.rateCardFeeItemRepository.create({
                label: dto.label,
                categoryName: dto.categoryName,
                feeType: dto.feeType,
                netFee: dto.netFee,
                vatFee: dto.vatFee,
                totalFee: dto.totalFee,
                displayOrder: dto.displayOrder,
                active: dto.active,
                perParty: dto.perParty || false,
                rangeStart: dto.rangeStart,
                rangeEnd: dto.rangeEnd,
                applicableFor: dto.applicableFor || '',
                conditionSlug: dto.conditionSlug,
                dynamic: dto.dynamic || false,
                rateCardId,
                createdAt: new Date(),
                updatedAt: new Date()
            });

            const savedFeeItem = await this.rateCardFeeItemRepository.save(feeItem as any);
            this.logger.log(
                `Added fee item: ${savedFeeItem.label} to rate card: ${rateCard.displayName}`
            );

            return savedFeeItem;
        } catch (error) {
            this.logger.error(
                `Error adding fee item to rate card ${rateCardId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Update a fee item
     */
    async updateFeeItem(
        feeItemId: string,
        dto: Partial<CreateFeeItemDto>
    ): Promise<RateCardFeeItem> {
        try {
            const feeItem = await this.rateCardFeeItemRepository.findOne({
                where: { id: feeItemId }
            });

            if (!feeItem) {
                throw new NotFoundException(`Fee item with ID ${feeItemId} not found`);
            }

            Object.assign(feeItem, {
                ...dto,
                updatedAt: new Date()
            });

            const updatedFeeItem = await this.rateCardFeeItemRepository.save(feeItem);
            this.logger.log(`Updated fee item: ${updatedFeeItem.label}`);

            return updatedFeeItem;
        } catch (error) {
            this.logger.error(
                `Error updating fee item ${feeItemId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Delete a fee item
     */
    async deleteFeeItem(feeItemId: string): Promise<void> {
        try {
            const feeItem = await this.rateCardFeeItemRepository.findOne({
                where: { id: feeItemId }
            });

            if (!feeItem) {
                throw new NotFoundException(`Fee item with ID ${feeItemId} not found`);
            }

            await this.rateCardFeeItemRepository.remove(feeItem);
            this.logger.log(`Deleted fee item: ${feeItem.label}`);
        } catch (error) {
            this.logger.error(
                `Error deleting fee item ${feeItemId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Get all fee items for a rate card
     */
    async getRateCardFeeItems(rateCardId: string): Promise<RateCardFeeItem[]> {
        try {
            return await this.rateCardFeeItemRepository.findByRateCardId(rateCardId);
        } catch (error) {
            this.logger.error(
                `Error fetching fee items for rate card ${rateCardId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Compare rate cards for a specific transaction scenario
     */
    async compareRateCards(
        propertyValue: number,
        transactionType: string,
        propertyConditions: Record<string, any>
    ): Promise<RateCardComparisonResult[]> {
        try {
            const activeRateCards = await this.rateCardRepository.findActiveRateCards();
            const comparisonResults: RateCardComparisonResult[] = [];

            for (const rateCard of activeRateCards) {
                const feeItems = await this.rateCardFeeItemRepository.findByRateCardId(rateCard.id);

                // Filter applicable fee items based on transaction scenario
                const applicableItems = feeItems.filter((item) => {
                    // Basic filtering logic - can be enhanced
                    if (!item.active) return false;
                    if (item.applicableFor && !item.applicableFor.includes(transactionType))
                        return false;
                    if (item.rangeStart !== undefined && propertyValue < item.rangeStart)
                        return false;
                    if (item.rangeEnd !== undefined && propertyValue > item.rangeEnd) return false;
                    if (item.conditionSlug && !propertyConditions[item.conditionSlug]) return false;
                    return true;
                });

                // Calculate totals
                let totalFees = 0;
                const feeBreakdown = new Map<string, any[]>();

                for (const item of applicableItems) {
                    totalFees += item.totalFee;

                    if (!feeBreakdown.has(item.categoryName)) {
                        feeBreakdown.set(item.categoryName, []);
                    }

                    feeBreakdown.get(item.categoryName)!.push({
                        label: item.label,
                        netFee: item.netFee,
                        vatFee: item.vatFee,
                        totalFee: item.totalFee
                    });
                }

                comparisonResults.push({
                    rateCardId: rateCard.id,
                    providerName: rateCard.providerName,
                    displayName: rateCard.displayName,
                    totalFees,
                    feeBreakdown: Array.from(feeBreakdown.entries()).map(([category, items]) => ({
                        category,
                        items
                    })),
                    applicableFeeItems: applicableItems.length,
                    totalFeeItems: feeItems.length,
                    priority: rateCard.priority,
                    isDefault: rateCard.isDefault
                });
            }

            // Sort by total fees (ascending) and then by priority
            comparisonResults.sort((a, b) => {
                if (a.totalFees !== b.totalFees) {
                    return a.totalFees - b.totalFees;
                }
                return a.priority - b.priority;
            });

            return comparisonResults;
        } catch (error) {
            this.logger.error(`Error comparing rate cards: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get available providers
     */
    async getAvailableProviders(): Promise<RateCardProvider[]> {
        try {
            const activeRateCards = await this.rateCardRepository.findActiveRateCards();
            const providers = new Set<RateCardProvider>();

            activeRateCards.forEach((rateCard) => {
                providers.add(rateCard.providerCode);
            });

            return Array.from(providers);
        } catch (error) {
            this.logger.error(`Error fetching available providers: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get rate card statistics
     */
    async getRateCardStats(): Promise<{
        totalRateCards: number;
        activeRateCards: number;
        totalFeeItems: number;
        providers: { name: string; count: number }[];
    }> {
        try {
            const activeRateCards = await this.rateCardRepository.findActiveRateCards();
            const allRateCards = await this.rateCardRepository.find();

            let totalFeeItems = 0;
            const providerStats = new Map<string, number>();

            for (const rateCard of activeRateCards) {
                const feeItems = await this.rateCardFeeItemRepository.findByRateCardId(rateCard.id);
                totalFeeItems += feeItems.length;

                const currentCount = providerStats.get(rateCard.providerName) || 0;
                providerStats.set(rateCard.providerName, currentCount + 1);
            }

            return {
                totalRateCards: allRateCards.length,
                activeRateCards: activeRateCards.length,
                totalFeeItems,
                providers: Array.from(providerStats.entries()).map(([name, count]) => ({
                    name,
                    count
                }))
            };
        } catch (error) {
            this.logger.error(`Error fetching rate card stats: ${error.message}`, error.stack);
            throw error;
        }
    }
}
