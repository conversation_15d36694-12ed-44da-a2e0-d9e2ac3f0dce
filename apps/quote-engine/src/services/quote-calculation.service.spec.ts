import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { QuoteCalculationService } from './quote-calculation.service';
import {
    FeeCategory,
    FeeItem,
    PromoCode,
    PromoCodeStatus
} from '@app/common/typeorm/entities/tenant';
import { TenantConnectionService } from '@app/common/multi-tenancy';

describe('QuoteCalculationService', () => {
    let service: QuoteCalculationService;

    const mockFeeCategoryRepository = {
        find: jest.fn()
    };

    const mockFeeItemRepository = {
        find: jest.fn()
    };

    const mockPromoCodeRepository = {
        findOne: jest.fn()
    };

    const mockTenantConnectionService = {
        getTenantDataSource: jest.fn()
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                QuoteCalculationService,
                {
                    provide: getRepositoryToken(FeeCategory),
                    useValue: mockFeeCategoryRepository
                },
                {
                    provide: getRepositoryToken(FeeItem),
                    useValue: mockFeeItemRepository
                },
                {
                    provide: getRepositoryToken(PromoCode),
                    useValue: mockPromoCodeRepository
                },
                {
                    provide: TenantConnectionService,
                    useValue: mockTenantConnectionService
                }
            ]
        }).compile();

        service = module.get<QuoteCalculationService>(QuoteCalculationService);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('calculateQuote', () => {
        it('should calculate a basic quote', async () => {
            // Mock fee categories
            const mockCategories = [
                {
                    id: '1',
                    name: 'Legal Fees',
                    active: true,
                    displayOrder: 1
                }
            ];

            // Mock fee items
            const mockFeeItems = [
                {
                    id: '1',
                    label: 'Conveyancing Fee',
                    category: { id: '1', name: 'Legal Fees' },
                    netFee: 500,
                    vatFee: 100,
                    totalFee: 600,
                    active: true,
                    dynamic: false,
                    applicableFor: ['buy'],
                    conditionSlug: null,
                    rangeStart: null,
                    rangeEnd: null,
                    perParty: false
                }
            ];

            mockFeeCategoryRepository.find.mockResolvedValue(mockCategories);
            mockFeeItemRepository.find.mockResolvedValue(mockFeeItems);
            mockPromoCodeRepository.findOne.mockResolvedValue(null);

            const request = {
                propertyValue: 250000,
                propertyType: 'residential' as const,
                transactionType: 'buy' as const,
                location: {
                    postcode: 'SW1A 1AA',
                    region: 'London',
                    country: 'England'
                },
                propertyConditions: {
                    isNewBuild: false,
                    isLeasehold: false,
                    isWales: false,
                    isScotland: false,
                    isNorthernIreland: false,
                    isFirstTimeBuyer: true,
                    isBuyingWithMortgage: true,
                    isSharedOwnership: false,
                    isHelpToBuy: false,
                    isGiftedDeposit: false,
                    isCompanyOrTrust: false,
                    isResidingOutsideUK: false,
                    ownsMultipleProperties: false,
                    hasPreviousOwnership: false,
                    isMainResidence: true,
                    buildingOverLimit: false,
                    transferOfEquity: false,
                    mortgageRedemption: false
                },
                clientDetails: {
                    numberOfBuyers: 1
                }
            };

            const result = await service.calculateQuote(request);

            expect(result).toBeDefined();
            expect(result.categories).toHaveLength(1);
            expect(result.categories[0].name).toBe('Legal Fees');
            expect(result.categories[0].items).toHaveLength(1);
            expect(result.categories[0].items[0].label).toBe('Conveyancing Fee');
            expect(result.categories[0].items[0].net).toBe(500);
            expect(result.categories[0].items[0].vat).toBe(100);
            expect(result.categories[0].items[0].total).toBe(600);
            expect(result.grandTotal).toBe(600);
            expect(result.discount).toBe(0);
            expect(result.reference).toMatch(/^Q\d{11}$/);
        });

        it('should apply dynamic tax calculations for Welsh properties', async () => {
            const mockCategories = [
                {
                    id: '1',
                    name: 'Tax',
                    active: true,
                    displayOrder: 1
                }
            ];

            const mockFeeItems = [
                {
                    id: '1',
                    label: 'Land Transaction Tax',
                    category: { id: '1', name: 'Tax' },
                    netFee: 0,
                    vatFee: 0,
                    totalFee: 0,
                    active: true,
                    dynamic: true,
                    applicableFor: ['buy'],
                    conditionSlug: 'ltt_calculation',
                    rangeStart: null,
                    rangeEnd: null,
                    perParty: false
                }
            ];

            mockFeeCategoryRepository.find.mockResolvedValue(mockCategories);
            mockFeeItemRepository.find.mockResolvedValue(mockFeeItems);
            mockPromoCodeRepository.findOne.mockResolvedValue(null);

            const request = {
                propertyValue: 300000,
                propertyType: 'residential' as const,
                transactionType: 'buy' as const,
                location: {
                    postcode: 'CF10 1AA',
                    region: 'Wales',
                    country: 'Wales'
                },
                propertyConditions: {
                    isNewBuild: false,
                    isLeasehold: false,
                    isWales: true,
                    isScotland: false,
                    isNorthernIreland: false,
                    isFirstTimeBuyer: false,
                    isBuyingWithMortgage: true,
                    isSharedOwnership: false,
                    isHelpToBuy: false,
                    isGiftedDeposit: false,
                    isCompanyOrTrust: false,
                    isResidingOutsideUK: false,
                    ownsMultipleProperties: false,
                    hasPreviousOwnership: false,
                    isMainResidence: true,
                    buildingOverLimit: false,
                    transferOfEquity: false,
                    mortgageRedemption: false
                },
                clientDetails: {
                    numberOfBuyers: 1
                }
            };

            const result = await service.calculateQuote(request);

            expect(result).toBeDefined();
            expect(result.categories).toHaveLength(1);
            expect(result.categories[0].name).toBe('Tax');
            expect(result.categories[0].items).toHaveLength(1);

            const taxItem = result.categories[0].items[0];
            expect(taxItem.label).toBe('Land Transaction Tax');
            expect(taxItem.dynamic).toBe(true);
            expect(taxItem.calculationMethod).toBe('Welsh LTT');
            expect(taxItem.net).toBeGreaterThan(3000); // LTT for £300k property
        });

        it('should apply promo code discounts', async () => {
            const mockCategories = [
                {
                    id: '1',
                    name: 'Legal Fees',
                    active: true,
                    displayOrder: 1
                }
            ];

            const mockFeeItems = [
                {
                    id: '1',
                    label: 'Conveyancing Fee',
                    category: { id: '1', name: 'Legal Fees' },
                    netFee: 1000,
                    vatFee: 200,
                    totalFee: 1200,
                    active: true,
                    dynamic: false,
                    applicableFor: ['buy'],
                    conditionSlug: null,
                    rangeStart: null,
                    rangeEnd: null,
                    perParty: false
                }
            ];

            const mockPromoCode = {
                id: '1',
                code: 'SAVE10',
                discount: 10,
                status: PromoCodeStatus.ACTIVE
            };

            mockFeeCategoryRepository.find.mockResolvedValue(mockCategories);
            mockFeeItemRepository.find.mockResolvedValue(mockFeeItems);
            mockPromoCodeRepository.findOne.mockResolvedValue(mockPromoCode);

            const request = {
                propertyValue: 250000,
                propertyType: 'residential' as const,
                transactionType: 'buy' as const,
                location: {
                    postcode: 'SW1A 1AA',
                    region: 'London',
                    country: 'England'
                },
                propertyConditions: {
                    isNewBuild: false,
                    isLeasehold: false,
                    isWales: false,
                    isScotland: false,
                    isNorthernIreland: false,
                    isFirstTimeBuyer: true,
                    isBuyingWithMortgage: true,
                    isSharedOwnership: false,
                    isHelpToBuy: false,
                    isGiftedDeposit: false,
                    isCompanyOrTrust: false,
                    isResidingOutsideUK: false,
                    ownsMultipleProperties: false,
                    hasPreviousOwnership: false,
                    isMainResidence: true,
                    buildingOverLimit: false,
                    transferOfEquity: false,
                    mortgageRedemption: false
                },
                clientDetails: {
                    numberOfBuyers: 1
                },
                promoCode: 'SAVE10'
            };

            const result = await service.calculateQuote(request);

            expect(result).toBeDefined();
            expect(result.discount).toBe(10);
            expect(result.grandTotal).toBeGreaterThan(0);
            expect(result.grandTotal).toBeLessThan(1200); // Should be less than original due to discount
        });

        it('should handle per-party fees correctly', async () => {
            const mockCategories = [
                {
                    id: '1',
                    name: 'Legal Fees',
                    active: true,
                    displayOrder: 1
                }
            ];

            const mockFeeItems = [
                {
                    id: '1',
                    label: 'Client Registration Fee',
                    category: { id: '1', name: 'Legal Fees' },
                    netFee: 50,
                    vatFee: 10,
                    totalFee: 60,
                    active: true,
                    dynamic: false,
                    applicableFor: ['buy'],
                    conditionSlug: null,
                    rangeStart: null,
                    rangeEnd: null,
                    perParty: true
                }
            ];

            mockFeeCategoryRepository.find.mockResolvedValue(mockCategories);
            mockFeeItemRepository.find.mockResolvedValue(mockFeeItems);
            mockPromoCodeRepository.findOne.mockResolvedValue(null);

            const request = {
                propertyValue: 250000,
                propertyType: 'residential' as const,
                transactionType: 'buy' as const,
                location: {
                    postcode: 'SW1A 1AA',
                    region: 'London',
                    country: 'England'
                },
                propertyConditions: {
                    isNewBuild: false,
                    isLeasehold: false,
                    isWales: false,
                    isScotland: false,
                    isNorthernIreland: false,
                    isFirstTimeBuyer: true,
                    isBuyingWithMortgage: true,
                    isSharedOwnership: false,
                    isHelpToBuy: false,
                    isGiftedDeposit: false,
                    isCompanyOrTrust: false,
                    isResidingOutsideUK: false,
                    ownsMultipleProperties: false,
                    hasPreviousOwnership: false,
                    isMainResidence: true,
                    buildingOverLimit: false,
                    transferOfEquity: false,
                    mortgageRedemption: false
                },
                clientDetails: {
                    numberOfBuyers: 2
                }
            };

            const result = await service.calculateQuote(request);

            expect(result).toBeDefined();
            expect(result.categories[0].items[0].net).toBe(100); // 50 * 2 buyers
            expect(result.categories[0].items[0].vat).toBe(20); // 10 * 2 buyers
            expect(result.categories[0].items[0].total).toBe(120); // 60 * 2 buyers
        });
    });
});
