import { Injectable, Logger } from '@nestjs/common';
import { PromoCodeRepository } from '../repositories/promo-code.repository';
import { PromoCode, PromoCodeStatus } from '@app/common/typeorm/entities/tenant';

export interface PromoCodeValidationResult {
    isValid: boolean;
    discount: number;
    promoCodeId: string;
    message?: string;
}

export interface CreatePromoCodeDto {
    name: string;
    code: string;
    discount: number;
    expirationDate?: Date;
    usageLimit?: number;
}

export interface UpdatePromoCodeDto {
    name?: string;
    code?: string;
    discount?: number;
    expirationDate?: Date;
    usageLimit?: number;
    status?: PromoCodeStatus;
}

export interface PromoCodeUsageStats {
    totalUsage: number;
    remainingUsage: number;
    isExpired: boolean;
    isUsageLimitReached: boolean;
}

@Injectable()
export class PromoCodeManagementService {
    private readonly logger = new Logger(PromoCodeManagementService.name);

    constructor(private readonly promoCodeRepository: PromoCodeRepository) {}

    /**
     * Validate a promo code and return validation result
     */
    async validatePromoCode(code: string): Promise<PromoCodeValidationResult> {
        try {
            this.logger.log(`Validating promo code: ${code}`);

            const promoCode = await this.promoCodeRepository.findByCode(code);

            if (!promoCode) {
                this.logger.warn(`Promo code not found: ${code}`);
                return {
                    isValid: false,
                    discount: 0,
                    promoCodeId: '',
                    message: 'Promo code not found'
                };
            }

            // Check if promo code is active
            if (promoCode.status !== PromoCodeStatus.ACTIVE) {
                this.logger.warn(`Promo code is not active: ${code}, status: ${promoCode.status}`);
                return {
                    isValid: false,
                    discount: 0,
                    promoCodeId: promoCode.id,
                    message: 'Promo code is not active'
                };
            }

            // Check expiration date
            if (promoCode.expirationDate && promoCode.expirationDate < new Date()) {
                this.logger.warn(
                    `Promo code has expired: ${code}, expiration: ${promoCode.expirationDate}`
                );
                return {
                    isValid: false,
                    discount: 0,
                    promoCodeId: promoCode.id,
                    message: 'Promo code has expired'
                };
            }

            // Check usage limit
            if (promoCode.usageLimit && promoCode.usageCount >= promoCode.usageLimit) {
                this.logger.warn(
                    `Promo code usage limit reached: ${code}, usage: ${promoCode.usageCount}/${promoCode.usageLimit}`
                );
                return {
                    isValid: false,
                    discount: 0,
                    promoCodeId: promoCode.id,
                    message: 'Promo code usage limit reached'
                };
            }

            this.logger.log(
                `Promo code validation successful: ${code}, discount: ${promoCode.discount}%`
            );
            return {
                isValid: true,
                discount: promoCode.discount,
                promoCodeId: promoCode.id,
                message: 'Promo code is valid'
            };
        } catch (error) {
            this.logger.error(`Error validating promo code: ${error.message}`, error.stack);
            return {
                isValid: false,
                discount: 0,
                promoCodeId: '',
                message: 'Error validating promo code'
            };
        }
    }

    /**
     * Apply a promo code and increment usage count
     */
    async applyPromoCode(code: string): Promise<PromoCodeValidationResult> {
        try {
            this.logger.log(`Applying promo code: ${code}`);

            // First validate the promo code
            const validation = await this.validatePromoCode(code);

            if (!validation.isValid) {
                return validation;
            }

            // Get the promo code and increment usage count
            const promoCode = await this.promoCodeRepository.findOne({
                where: { id: validation.promoCodeId }
            });

            if (!promoCode) {
                return {
                    isValid: false,
                    discount: 0,
                    promoCodeId: '',
                    message: 'Promo code not found during application'
                };
            }

            // Increment usage count
            promoCode.usageCount += 1;
            await this.promoCodeRepository.save(promoCode);

            this.logger.log(
                `Promo code applied successfully: ${code}, new usage count: ${promoCode.usageCount}`
            );
            return validation;
        } catch (error) {
            this.logger.error(`Error applying promo code: ${error.message}`, error.stack);
            return {
                isValid: false,
                discount: 0,
                promoCodeId: '',
                message: 'Error applying promo code'
            };
        }
    }

    /**
     * Create a new promo code
     */
    async createPromoCode(createDto: CreatePromoCodeDto): Promise<PromoCode> {
        try {
            this.logger.log(`Creating promo code: ${createDto.code}`);

            // Check if code already exists
            const existingCode = await this.promoCodeRepository.findByCode(createDto.code);
            if (existingCode) {
                throw new Error(`Promo code '${createDto.code}' already exists`);
            }

            const promoCodeData = {
                name: createDto.name,
                code: createDto.code,
                discount: createDto.discount,
                expirationDate: createDto.expirationDate,
                usageLimit: createDto.usageLimit,
                status: PromoCodeStatus.ACTIVE,
                usageCount: 0
            };

            const savedPromoCode = await this.promoCodeRepository.save(promoCodeData);
            this.logger.log(`Promo code created successfully: ${savedPromoCode.id}`);
            return savedPromoCode;
        } catch (error) {
            this.logger.error(`Error creating promo code: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Update an existing promo code
     */
    async updatePromoCode(id: string, updateDto: UpdatePromoCodeDto): Promise<PromoCode> {
        try {
            this.logger.log(`Updating promo code: ${id}`);

            const promoCode = await this.promoCodeRepository.findOne({
                where: { id }
            });

            if (!promoCode) {
                throw new Error(`Promo code with ID '${id}' not found`);
            }

            // Check if new code already exists (if code is being changed)
            if (updateDto.code && updateDto.code !== promoCode.code) {
                const existingCode = await this.promoCodeRepository.findByCode(updateDto.code);
                if (existingCode) {
                    throw new Error(`Promo code '${updateDto.code}' already exists`);
                }
            }

            // Update fields
            Object.assign(promoCode, updateDto);
            const updatedPromoCode = await this.promoCodeRepository.save(promoCode);

            this.logger.log(`Promo code updated successfully: ${id}`);
            return updatedPromoCode;
        } catch (error) {
            this.logger.error(`Error updating promo code: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get all promo codes with optional filtering
     */
    async getPromoCodes(status?: PromoCodeStatus): Promise<PromoCode[]> {
        try {
            this.logger.log(`Getting promo codes${status ? ` with status: ${status}` : ''}`);

            const whereCondition = status ? { status } : {};
            const promoCodes = await this.promoCodeRepository.find({
                where: whereCondition,
                order: { createdAt: 'DESC' }
            });

            this.logger.log(`Retrieved ${promoCodes.length} promo codes`);
            return promoCodes;
        } catch (error) {
            this.logger.error(`Error getting promo codes: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get paginated promo codes with optional filtering
     */
    async getPromoCodesPaginated(
        page: number = 1,
        limit: number = 20,
        status?: PromoCodeStatus,
        search?: string
    ): Promise<{
        promoCodes: PromoCode[];
        total: number;
        page: number;
        totalPages: number;
        hasNext: boolean;
        hasPrevious: boolean;
    }> {
        try {
            this.logger.log(
                `Getting paginated promo codes - page: ${page}, limit: ${limit}, status: ${status}, search: ${search}`
            );

            const offset = (page - 1) * limit;
            const whereCondition: any = {};

            if (status) {
                whereCondition.status = status;
            }

            if (search) {
                whereCondition.name = { $ilike: `%${search}%` };
            }

            const [promoCodes, total] = await Promise.all([
                this.promoCodeRepository.find({
                    where: whereCondition,
                    order: { createdAt: 'DESC' },
                    skip: offset,
                    take: limit
                }),
                this.promoCodeRepository.count({ where: whereCondition })
            ]);

            const totalPages = Math.ceil(total / limit);
            const hasNext = page < totalPages;
            const hasPrevious = page > 1;

            this.logger.log(`Retrieved ${promoCodes.length} promo codes out of ${total} total`);

            return {
                promoCodes,
                total,
                page,
                totalPages,
                hasNext,
                hasPrevious
            };
        } catch (error) {
            this.logger.error(`Error getting paginated promo codes: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get promo code by ID
     */
    async getPromoCodeById(id: string): Promise<PromoCode | null> {
        try {
            this.logger.log(`Getting promo code by ID: ${id}`);

            const promoCode = await this.promoCodeRepository.findOne({
                where: { id }
            });

            if (!promoCode) {
                this.logger.warn(`Promo code not found: ${id}`);
                return null;
            }

            return promoCode;
        } catch (error) {
            this.logger.error(`Error getting promo code by ID: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get promo code usage statistics
     */
    async getPromoCodeUsageStats(id: string): Promise<PromoCodeUsageStats> {
        try {
            this.logger.log(`Getting usage stats for promo code: ${id}`);

            const promoCode = await this.promoCodeRepository.findOne({
                where: { id }
            });

            if (!promoCode) {
                throw new Error(`Promo code with ID '${id}' not found`);
            }

            const isExpired = promoCode.expirationDate
                ? promoCode.expirationDate < new Date()
                : false;
            const isUsageLimitReached = promoCode.usageLimit
                ? promoCode.usageCount >= promoCode.usageLimit
                : false;
            const remainingUsage = promoCode.usageLimit
                ? Math.max(0, promoCode.usageLimit - promoCode.usageCount)
                : -1;

            return {
                totalUsage: promoCode.usageCount,
                remainingUsage,
                isExpired,
                isUsageLimitReached
            };
        } catch (error) {
            this.logger.error(
                `Error getting promo code usage stats: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Deactivate a promo code
     */
    async deactivatePromoCode(id: string): Promise<PromoCode> {
        try {
            this.logger.log(`Deactivating promo code: ${id}`);

            const promoCode = await this.promoCodeRepository.findOne({
                where: { id }
            });

            if (!promoCode) {
                throw new Error(`Promo code with ID '${id}' not found`);
            }

            promoCode.status = PromoCodeStatus.INACTIVE;
            const updatedPromoCode = await this.promoCodeRepository.save(promoCode);

            this.logger.log(`Promo code deactivated successfully: ${id}`);
            return updatedPromoCode;
        } catch (error) {
            this.logger.error(`Error deactivating promo code: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Delete a promo code
     */
    async deletePromoCode(id: string): Promise<void> {
        try {
            this.logger.log(`Deleting promo code: ${id}`);

            const promoCode = await this.promoCodeRepository.findOne({
                where: { id }
            });

            if (!promoCode) {
                throw new Error(`Promo code with ID '${id}' not found`);
            }

            await this.promoCodeRepository.remove(promoCode);
            this.logger.log(`Promo code deleted successfully: ${id}`);
        } catch (error) {
            this.logger.error(`Error deleting promo code: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Generate a random promo code
     */
    generatePromoCode(length: number = 6): string {
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const numbers = '0123456789';
        const randomLetters = Array.from(
            { length: Math.ceil(length / 2) },
            () => letters[Math.floor(Math.random() * letters.length)]
        ).join('');
        const randomNumbers = Array.from(
            { length: Math.floor(length / 2) },
            () => numbers[Math.floor(Math.random() * numbers.length)]
        ).join('');
        return randomLetters + randomNumbers;
    }
}
