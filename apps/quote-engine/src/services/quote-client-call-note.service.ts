import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Request } from 'express';
import { QuoteClientCallNoteRepository } from '../repositories/quote-client-call-note.repository';
import { QuoteAuditService } from './quote-audit.service';
import {
    QuoteClientCallNote,
    CallType,
    CallOutcome,
    CallPriority,
    QuoteAuditAction
} from '@app/common/typeorm/entities/tenant';

export interface CreateClientCallNoteDto {
    quoteId: string;
    callType: CallType;
    callOutcome: CallOutcome;
    callPriority?: CallPriority;
    callDate: Date;
    callDuration?: number;
    clientName: string;
    clientPhone?: string;
    clientEmail?: string;
    staffMember: string;
    staffMemberName: string;
    callSummary: string;
    discussionPoints: string;
    clientConcerns?: string;
    objectionsRaised?: string;
    objectionsHandled?: string;
    nextSteps?: string;
    followUpRequired?: boolean;
    followUpDate?: Date;
    followUpAssignedTo?: string;
    followUpAssignedToName?: string;
    followUpNotes?: string;
    quoteRelated?: boolean;
    quoteDiscussed?: boolean;
    quoteFeedback?: string;
    clientSatisfaction?: number;
    callMetadata?: Record<string, any>;
}

export interface UpdateClientCallNoteDto {
    callType?: CallType;
    callOutcome?: CallOutcome;
    callPriority?: CallPriority;
    callDate?: Date;
    callDuration?: number;
    clientName?: string;
    clientPhone?: string;
    clientEmail?: string;
    callSummary?: string;
    discussionPoints?: string;
    clientConcerns?: string;
    objectionsRaised?: string;
    objectionsHandled?: string;
    nextSteps?: string;
    followUpRequired?: boolean;
    followUpDate?: Date;
    followUpAssignedTo?: string;
    followUpAssignedToName?: string;
    followUpNotes?: string;
    quoteRelated?: boolean;
    quoteDiscussed?: boolean;
    quoteFeedback?: string;
    clientSatisfaction?: number;
    callMetadata?: Record<string, any>;
}

export interface CallNoteFilters {
    callType?: CallType;
    callOutcome?: CallOutcome;
    callPriority?: CallPriority;
    followUpRequired?: boolean;
    quoteDiscussed?: boolean;
    startDate?: Date;
    endDate?: Date;
    staffMember?: string;
    clientEmail?: string;
}

@Injectable()
export class QuoteClientCallNoteService {
    private readonly logger = new Logger(QuoteClientCallNoteService.name);

    constructor(
        private readonly quoteClientCallNoteRepository: QuoteClientCallNoteRepository,
        private readonly quoteAuditService: QuoteAuditService
    ) {}

    /**
     * Create a new client call note
     */
    async createClientCallNote(
        createDto: CreateClientCallNoteDto,
        createdBy: string,
        request: Request
    ): Promise<QuoteClientCallNote> {
        try {
            this.logger.log(
                `Creating client call note for quote ${createDto.quoteId}: ${createDto.callType}`
            );

            const callNote = await this.quoteClientCallNoteRepository.create({
                quoteId: createDto.quoteId,
                callType: createDto.callType,
                callOutcome: createDto.callOutcome,
                callPriority: createDto.callPriority || CallPriority.MEDIUM,
                callDate: createDto.callDate,
                callDuration: createDto.callDuration,
                clientName: createDto.clientName,
                clientPhone: createDto.clientPhone,
                clientEmail: createDto.clientEmail,
                staffMember: createDto.staffMember,
                staffMemberName: createDto.staffMemberName,
                callSummary: createDto.callSummary,
                discussionPoints: createDto.discussionPoints,
                clientConcerns: createDto.clientConcerns,
                objectionsRaised: createDto.objectionsRaised,
                objectionsHandled: createDto.objectionsHandled,
                nextSteps: createDto.nextSteps,
                followUpRequired: createDto.followUpRequired || false,
                followUpDate: createDto.followUpDate,
                followUpAssignedTo: createDto.followUpAssignedTo,
                followUpAssignedToName: createDto.followUpAssignedToName,
                followUpNotes: createDto.followUpNotes,
                quoteRelated: createDto.quoteRelated !== undefined ? createDto.quoteRelated : true,
                quoteDiscussed: createDto.quoteDiscussed || false,
                quoteFeedback: createDto.quoteFeedback,
                clientSatisfaction: createDto.clientSatisfaction,
                callMetadata: createDto.callMetadata || {},
                createdBy,
                createdAt: new Date(),
                updatedAt: new Date()
            });

            const savedCallNote = await this.quoteClientCallNoteRepository.save(callNote);

            // Log audit trail
            await this.quoteAuditService.logAction(
                createDto.quoteId,
                QuoteAuditAction.NOTE_ADDED,
                createdBy,
                createDto.staffMemberName,
                request,
                {
                    callNoteId: savedCallNote.id,
                    callType: createDto.callType,
                    callOutcome: createDto.callOutcome,
                    clientName: createDto.clientName,
                    callDate: createDto.callDate,
                    callDuration: createDto.callDuration,
                    followUpRequired: createDto.followUpRequired
                }
            );

            this.logger.log(`Client call note created successfully: ${savedCallNote.id}`);
            return savedCallNote;
        } catch (error) {
            this.logger.error(`Failed to create client call note: ${error.message}`, error.stack);
            throw new HttpException(
                `Failed to create client call note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get call notes for a quote
     */
    async getCallNotesForQuote(
        quoteId: string,
        limit: number = 50
    ): Promise<QuoteClientCallNote[]> {
        try {
            this.logger.log(`Getting call notes for quote ${quoteId}`);
            return await this.quoteClientCallNoteRepository.findByQuoteId(quoteId, limit);
        } catch (error) {
            this.logger.error(
                `Failed to get call notes for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get call notes: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get call notes with pagination and filters
     */
    async getCallNotesPaginated(
        quoteId: string,
        page: number = 1,
        limit: number = 20,
        filters?: CallNoteFilters
    ): Promise<{
        callNotes: QuoteClientCallNote[];
        total: number;
        page: number;
        totalPages: number;
    }> {
        try {
            this.logger.log(`Getting paginated call notes for quote ${quoteId}`);

            const filterOptions = filters
                ? {
                      callType: filters.callType,
                      callOutcome: filters.callOutcome,
                      callPriority: filters.callPriority,
                      followUpRequired: filters.followUpRequired,
                      quoteDiscussed: filters.quoteDiscussed,
                      startDate: filters.startDate,
                      endDate: filters.endDate
                  }
                : undefined;

            return await this.quoteClientCallNoteRepository.getCallNotesPaginated(
                quoteId,
                page,
                limit,
                filterOptions
            );
        } catch (error) {
            this.logger.error(
                `Failed to get paginated call notes for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get paginated call notes: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get call note by ID
     */
    async getCallNoteById(callNoteId: string): Promise<QuoteClientCallNote> {
        try {
            const callNote = await this.quoteClientCallNoteRepository.findOne({
                where: { id: callNoteId }
            });

            if (!callNote) {
                throw new HttpException('Call note not found', HttpStatus.NOT_FOUND);
            }

            return callNote;
        } catch (error) {
            this.logger.error(
                `Failed to get call note ${callNoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get call note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Update call note
     */
    async updateCallNote(
        callNoteId: string,
        updateDto: UpdateClientCallNoteDto,
        updatedBy: string,
        request: Request
    ): Promise<QuoteClientCallNote> {
        try {
            this.logger.log(`Updating call note ${callNoteId}`);

            const callNote = await this.getCallNoteById(callNoteId);

            Object.assign(callNote, updateDto);
            callNote.updatedAt = new Date();

            const updatedCallNote = await this.quoteClientCallNoteRepository.save(callNote);

            // Log audit trail
            await this.quoteAuditService.logAction(
                callNote.quoteId,
                QuoteAuditAction.NOTE_UPDATED,
                updatedBy,
                updatedBy,
                request,
                {
                    callNoteId,
                    changes: updateDto
                }
            );

            this.logger.log(`Call note updated successfully: ${callNoteId}`);
            return updatedCallNote;
        } catch (error) {
            this.logger.error(
                `Failed to update call note ${callNoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to update call note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Archive call note
     */
    async archiveCallNote(
        callNoteId: string,
        archivedBy: string,
        request: Request
    ): Promise<QuoteClientCallNote> {
        try {
            this.logger.log(`Archiving call note ${callNoteId}`);

            const callNote = await this.quoteClientCallNoteRepository.archiveCallNote(
                callNoteId,
                archivedBy
            );

            // Log audit trail
            await this.quoteAuditService.logAction(
                callNote.quoteId,
                QuoteAuditAction.NOTE_DELETED,
                archivedBy,
                archivedBy,
                request,
                {
                    callNoteId,
                    callType: callNote.callType,
                    clientName: callNote.clientName,
                    callDate: callNote.callDate
                }
            );

            this.logger.log(`Call note archived: ${callNoteId}`);
            return callNote;
        } catch (error) {
            this.logger.error(
                `Failed to archive call note ${callNoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to archive call note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Set follow-up for call note
     */
    async setFollowUp(
        callNoteId: string,
        followUpDate: Date,
        assignedTo: string,
        assignedToName: string,
        followUpNotes: string,
        request: Request
    ): Promise<QuoteClientCallNote> {
        try {
            this.logger.log(`Setting follow-up for call note ${callNoteId}`);

            const callNote = await this.quoteClientCallNoteRepository.setFollowUp(
                callNoteId,
                followUpDate,
                assignedTo,
                assignedToName,
                followUpNotes
            );

            // Log audit trail
            await this.quoteAuditService.logAction(
                callNote.quoteId,
                QuoteAuditAction.REMINDER_SET,
                assignedTo,
                assignedToName,
                request,
                {
                    callNoteId,
                    followUpDate,
                    assignedTo,
                    followUpNotes
                }
            );

            this.logger.log(`Follow-up set for call note: ${callNoteId}`);
            return callNote;
        } catch (error) {
            this.logger.error(
                `Failed to set follow-up for call note ${callNoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to set follow-up: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Complete follow-up
     */
    async completeFollowUp(
        callNoteId: string,
        completedBy: string,
        request: Request
    ): Promise<QuoteClientCallNote> {
        try {
            this.logger.log(`Completing follow-up for call note ${callNoteId}`);

            const callNote = await this.quoteClientCallNoteRepository.completeFollowUp(callNoteId);

            // Log audit trail
            await this.quoteAuditService.logAction(
                callNote.quoteId,
                QuoteAuditAction.FOLLOW_UP_SENT,
                completedBy,
                completedBy,
                request,
                {
                    callNoteId,
                    completedAt: new Date()
                }
            );

            this.logger.log(`Follow-up completed for call note: ${callNoteId}`);
            return callNote;
        } catch (error) {
            this.logger.error(
                `Failed to complete follow-up for call note ${callNoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to complete follow-up: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get call notes statistics for a quote
     */
    async getCallNotesStats(quoteId: string): Promise<{
        totalCalls: number;
        followUpRequired: number;
        overdueFollowUps: number;
        averageSatisfaction: number;
        callTypeCounts: Record<string, number>;
        callOutcomeCounts: Record<string, number>;
        priorityCounts: Record<string, number>;
        lastCallDate: Date | null;
        totalCallDuration: number;
        averageCallDuration: number;
    }> {
        try {
            this.logger.log(`Getting call notes stats for quote ${quoteId}`);
            return await this.quoteClientCallNoteRepository.getCallNotesStats(quoteId);
        } catch (error) {
            this.logger.error(
                `Failed to get call notes stats for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get call notes stats: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get call notes requiring follow-up
     */
    async getFollowUpRequired(): Promise<QuoteClientCallNote[]> {
        try {
            this.logger.log('Getting call notes requiring follow-up');
            return await this.quoteClientCallNoteRepository.findFollowUpRequired();
        } catch (error) {
            this.logger.error(
                `Failed to get follow-up required call notes: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get follow-up required call notes: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get overdue follow-ups
     */
    async getOverdueFollowUps(): Promise<QuoteClientCallNote[]> {
        try {
            this.logger.log('Getting overdue follow-ups');
            return await this.quoteClientCallNoteRepository.findOverdueFollowUps();
        } catch (error) {
            this.logger.error(`Failed to get overdue follow-ups: ${error.message}`, error.stack);
            throw new HttpException(
                `Failed to get overdue follow-ups: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Search call notes by content
     */
    async searchCallNotes(
        quoteId: string,
        searchTerm: string,
        limit: number = 20
    ): Promise<QuoteClientCallNote[]> {
        try {
            this.logger.log(`Searching call notes for quote ${quoteId} with term: ${searchTerm}`);
            return await this.quoteClientCallNoteRepository.searchCallNotes(
                quoteId,
                searchTerm,
                limit
            );
        } catch (error) {
            this.logger.error(
                `Failed to search call notes for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to search call notes: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get recent calls for a staff member
     */
    async getRecentCallsForStaff(
        staffMember: string,
        limit: number = 20
    ): Promise<QuoteClientCallNote[]> {
        try {
            this.logger.log(`Getting recent calls for staff member ${staffMember}`);
            return await this.quoteClientCallNoteRepository.getRecentCallsForStaff(
                staffMember,
                limit
            );
        } catch (error) {
            this.logger.error(
                `Failed to get recent calls for staff member ${staffMember}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get recent calls: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get calls by client across all quotes
     */
    async getCallsByClient(
        clientEmail: string,
        limit: number = 50
    ): Promise<QuoteClientCallNote[]> {
        try {
            this.logger.log(`Getting calls by client ${clientEmail}`);
            return await this.quoteClientCallNoteRepository.getCallsByClient(clientEmail, limit);
        } catch (error) {
            this.logger.error(
                `Failed to get calls by client ${clientEmail}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get calls by client: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Delete call note
     */
    async deleteCallNote(callNoteId: string, deletedBy: string, request: Request): Promise<void> {
        try {
            this.logger.log(`Deleting call note ${callNoteId}`);

            const callNote = await this.getCallNoteById(callNoteId);
            const quoteId = callNote.quoteId;
            const clientName = callNote.clientName;
            const callDate = callNote.callDate;

            await this.quoteClientCallNoteRepository.deleteCallNote(callNoteId);

            // Log audit trail
            await this.quoteAuditService.logAction(
                quoteId,
                QuoteAuditAction.NOTE_DELETED,
                deletedBy,
                deletedBy,
                request,
                {
                    callNoteId,
                    clientName,
                    callDate
                }
            );

            this.logger.log(`Call note deleted successfully: ${callNoteId}`);
        } catch (error) {
            this.logger.error(
                `Failed to delete call note ${callNoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to delete call note: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get call type display name
     */
    getCallTypeDisplayName(type: CallType): string {
        const displayNames: Record<CallType, string> = {
            [CallType.INBOUND]: 'Inbound Call',
            [CallType.OUTBOUND]: 'Outbound Call',
            [CallType.FOLLOW_UP]: 'Follow-up Call',
            [CallType.CONSULTATION]: 'Consultation Call',
            [CallType.QUOTE_DISCUSSION]: 'Quote Discussion',
            [CallType.OBJECTION_HANDLING]: 'Objection Handling',
            [CallType.CLOSING]: 'Closing Call',
            [CallType.SUPPORT]: 'Support Call',
            [CallType.COMPLAINT]: 'Complaint Call',
            [CallType.OTHER]: 'Other Call'
        };

        return displayNames[type] || 'Unknown';
    }

    /**
     * Get call outcome display name
     */
    getCallOutcomeDisplayName(outcome: CallOutcome): string {
        const displayNames: Record<CallOutcome, string> = {
            [CallOutcome.SUCCESSFUL]: 'Successful',
            [CallOutcome.NO_ANSWER]: 'No Answer',
            [CallOutcome.BUSY]: 'Busy',
            [CallOutcome.VOICEMAIL]: 'Voicemail',
            [CallOutcome.CALLBACK_REQUESTED]: 'Callback Requested',
            [CallOutcome.APPOINTMENT_SCHEDULED]: 'Appointment Scheduled',
            [CallOutcome.QUOTE_REQUESTED]: 'Quote Requested',
            [CallOutcome.QUOTE_ACCEPTED]: 'Quote Accepted',
            [CallOutcome.QUOTE_REJECTED]: 'Quote Rejected',
            [CallOutcome.OBJECTION_RAISED]: 'Objection Raised',
            [CallOutcome.OBJECTION_RESOLVED]: 'Objection Resolved',
            [CallOutcome.COMPLAINT_RESOLVED]: 'Complaint Resolved',
            [CallOutcome.FOLLOW_UP_REQUIRED]: 'Follow-up Required',
            [CallOutcome.UNSUCCESSFUL]: 'Unsuccessful'
        };

        return displayNames[outcome] || 'Unknown';
    }

    /**
     * Get call priority display name
     */
    getCallPriorityDisplayName(priority: CallPriority): string {
        const displayNames: Record<CallPriority, string> = {
            [CallPriority.LOW]: 'Low',
            [CallPriority.MEDIUM]: 'Medium',
            [CallPriority.HIGH]: 'High',
            [CallPriority.URGENT]: 'Urgent'
        };

        return displayNames[priority] || 'Unknown';
    }

    /**
     * Validate client satisfaction rating
     */
    validateSatisfactionRating(rating: number): boolean {
        return rating >= 1 && rating <= 5;
    }

    /**
     * Get satisfaction rating description
     */
    getSatisfactionDescription(rating: number): string {
        const descriptions: Record<number, string> = {
            1: 'Very Dissatisfied',
            2: 'Dissatisfied',
            3: 'Neutral',
            4: 'Satisfied',
            5: 'Very Satisfied'
        };

        return descriptions[rating] || 'Unknown';
    }
}
