import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Request } from 'express';
import { QuoteAttachmentRepository } from '../repositories/quote-attachment.repository';
import { QuoteAuditService } from './quote-audit.service';
import { QuoteAttachment, QuoteAttachmentType } from '@app/common/typeorm/entities/tenant';

export interface CreateAttachmentDto {
    quoteId: string;
    filename: string;
    url: string;
    fileSize?: number;
    mimeType?: string;
    description?: string;
    attachmentType: QuoteAttachmentType;
    isPublic?: boolean;
    isRequired?: boolean;
    expiresAt?: Date;
    metadata?: Record<string, any>;
}

export interface UpdateAttachmentDto {
    filename?: string;
    description?: string;
    attachmentType?: QuoteAttachmentType;
    isPublic?: boolean;
    isRequired?: boolean;
    expiresAt?: Date;
    metadata?: Record<string, any>;
}

@Injectable()
export class QuoteAttachmentService {
    private readonly logger = new Logger(QuoteAttachmentService.name);

    constructor(
        private readonly quoteAttachmentRepository: QuoteAttachmentRepository,
        private readonly quoteAuditService: QuoteAuditService
    ) {}

    /**
     * Create a new attachment for a quote
     */
    async createAttachment(
        createDto: CreateAttachmentDto,
        uploadedBy: string,
        uploadedByName: string,
        request: Request
    ): Promise<QuoteAttachment> {
        try {
            this.logger.log(
                `Creating attachment for quote ${createDto.quoteId}: ${createDto.filename}`
            );

            const attachment = await this.quoteAttachmentRepository.create({
                quoteId: createDto.quoteId,
                filename: createDto.filename,
                url: createDto.url,
                fileSize: createDto.fileSize,
                mimeType: createDto.mimeType,
                description: createDto.description,
                attachmentType: createDto.attachmentType,
                isPublic: createDto.isPublic || false,
                isRequired: createDto.isRequired || false,
                expiresAt: createDto.expiresAt,
                metadata: createDto.metadata,
                uploadedBy,
                uploadedByName,
                uploadedAt: new Date(),
                createdAt: new Date(),
                updatedAt: new Date()
            });

            const savedAttachment = await this.quoteAttachmentRepository.save(attachment);

            // Log audit trail
            await this.quoteAuditService.logAttachmentAdded(
                createDto.quoteId,
                savedAttachment.id,
                createDto.filename,
                uploadedBy,
                uploadedByName,
                request
            );

            this.logger.log(`Attachment created successfully: ${savedAttachment.id}`);
            return savedAttachment;
        } catch (error) {
            this.logger.error(`Failed to create attachment: ${error.message}`, error.stack);
            throw new HttpException(
                `Failed to create attachment: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get all attachments for a quote
     */
    async getAttachmentsForQuote(quoteId: string): Promise<QuoteAttachment[]> {
        try {
            this.logger.log(`Getting attachments for quote ${quoteId}`);
            return await this.quoteAttachmentRepository.findByQuoteId(quoteId);
        } catch (error) {
            this.logger.error(
                `Failed to get attachments for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get attachments: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get public attachments for a quote (visible to clients)
     */
    async getPublicAttachmentsForQuote(quoteId: string): Promise<QuoteAttachment[]> {
        try {
            this.logger.log(`Getting public attachments for quote ${quoteId}`);
            return await this.quoteAttachmentRepository.findPublicByQuoteId(quoteId);
        } catch (error) {
            this.logger.error(
                `Failed to get public attachments for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get public attachments: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get required attachments for a quote
     */
    async getRequiredAttachmentsForQuote(quoteId: string): Promise<QuoteAttachment[]> {
        try {
            this.logger.log(`Getting required attachments for quote ${quoteId}`);
            return await this.quoteAttachmentRepository.findRequiredByQuoteId(quoteId);
        } catch (error) {
            this.logger.error(
                `Failed to get required attachments for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get required attachments: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get attachment by ID
     */
    async getAttachmentById(attachmentId: string): Promise<QuoteAttachment> {
        try {
            const attachment = await this.quoteAttachmentRepository.findOne({
                where: { id: attachmentId }
            });

            if (!attachment) {
                throw new HttpException('Attachment not found', HttpStatus.NOT_FOUND);
            }

            return attachment;
        } catch (error) {
            this.logger.error(
                `Failed to get attachment ${attachmentId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get attachment: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Update attachment metadata
     */
    async updateAttachment(
        attachmentId: string,
        updateDto: UpdateAttachmentDto,
        updatedBy: string,
        updatedByName: string,
        request: Request
    ): Promise<QuoteAttachment> {
        try {
            this.logger.log(`Updating attachment ${attachmentId}`);

            const attachment = await this.quoteAttachmentRepository.updateAttachmentMetadata(
                attachmentId,
                {
                    ...updateDto,
                    updatedAt: new Date()
                }
            );

            // Log audit trail
            await this.quoteAuditService.logAction(
                attachment.quoteId,
                'ATTACHMENT_UPDATED' as any,
                updatedBy,
                updatedByName,
                request,
                {
                    attachmentId,
                    filename: attachment.filename,
                    changes: updateDto
                }
            );

            this.logger.log(`Attachment updated successfully: ${attachmentId}`);
            return attachment;
        } catch (error) {
            this.logger.error(
                `Failed to update attachment ${attachmentId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to update attachment: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Delete attachment
     */
    async deleteAttachment(
        attachmentId: string,
        deletedBy: string,
        deletedByName: string,
        request: Request
    ): Promise<void> {
        try {
            this.logger.log(`Deleting attachment ${attachmentId}`);

            const attachment = await this.getAttachmentById(attachmentId);
            const quoteId = attachment.quoteId;
            const filename = attachment.filename;

            await this.quoteAttachmentRepository.deleteAttachment(attachmentId);

            // Log audit trail
            await this.quoteAuditService.logAction(
                quoteId,
                'ATTACHMENT_REMOVED' as any,
                deletedBy,
                deletedByName,
                request,
                {
                    attachmentId,
                    filename
                }
            );

            this.logger.log(`Attachment deleted successfully: ${attachmentId}`);
        } catch (error) {
            this.logger.error(
                `Failed to delete attachment ${attachmentId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to delete attachment: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get attachment statistics for a quote
     */
    async getAttachmentStats(quoteId: string): Promise<{
        totalAttachments: number;
        publicAttachments: number;
        requiredAttachments: number;
        expiredAttachments: number;
        totalFileSize: number;
        attachmentTypeCounts: Record<string, number>;
    }> {
        try {
            this.logger.log(`Getting attachment stats for quote ${quoteId}`);
            return await this.quoteAttachmentRepository.getAttachmentStats(quoteId);
        } catch (error) {
            this.logger.error(
                `Failed to get attachment stats for quote ${quoteId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get attachment stats: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get expired attachments
     */
    async getExpiredAttachments(): Promise<QuoteAttachment[]> {
        try {
            this.logger.log('Getting expired attachments');
            return await this.quoteAttachmentRepository.findExpired();
        } catch (error) {
            this.logger.error(`Failed to get expired attachments: ${error.message}`, error.stack);
            throw new HttpException(
                `Failed to get expired attachments: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get attachments expiring soon
     */
    async getAttachmentsExpiringSoon(days: number = 7): Promise<QuoteAttachment[]> {
        try {
            this.logger.log(`Getting attachments expiring within ${days} days`);
            return await this.quoteAttachmentRepository.findExpiringSoon(days);
        } catch (error) {
            this.logger.error(
                `Failed to get attachments expiring soon: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to get attachments expiring soon: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Generate presigned URL for attachment download
     * This would integrate with the document microservice
     */
    async generatePresignedUrl(attachmentId: string): Promise<string> {
        try {
            const attachment = await this.getAttachmentById(attachmentId);

            // TODO: Integrate with document microservice to generate presigned URL
            // For now, return the stored URL
            return attachment.url;
        } catch (error) {
            this.logger.error(
                `Failed to generate presigned URL for attachment ${attachmentId}: ${error.message}`,
                error.stack
            );
            throw new HttpException(
                `Failed to generate presigned URL: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Validate attachment type
     */
    validateAttachmentType(type: string): boolean {
        return Object.values(QuoteAttachmentType).includes(type as QuoteAttachmentType);
    }

    /**
     * Get attachment type display name
     */
    getAttachmentTypeDisplayName(type: QuoteAttachmentType): string {
        const displayNames: Record<QuoteAttachmentType, string> = {
            [QuoteAttachmentType.QUOTE_PDF]: 'Quote PDF',
            [QuoteAttachmentType.CLIENT_ID]: 'Client ID Document',
            [QuoteAttachmentType.PROPERTY_REPORT]: 'Property Report',
            [QuoteAttachmentType.LEGAL_SEARCH_RESULT]: 'Legal Search Result',
            [QuoteAttachmentType.SURVEY_REPORT]: 'Survey Report',
            [QuoteAttachmentType.MORTGAGE_OFFER]: 'Mortgage Offer',
            [QuoteAttachmentType.INSURANCE_DOCUMENT]: 'Insurance Document',
            [QuoteAttachmentType.CONTRACT_DRAFT]: 'Contract Draft',
            [QuoteAttachmentType.BANK_STATEMENT]: 'Bank Statement',
            [QuoteAttachmentType.PAYSLIP]: 'Payslip',
            [QuoteAttachmentType.UTILITY_BILL]: 'Utility Bill',
            [QuoteAttachmentType.COUNCIL_TAX_BILL]: 'Council Tax Bill',
            [QuoteAttachmentType.LAND_REGISTRY_DOCUMENT]: 'Land Registry Document',
            [QuoteAttachmentType.SEARCH_REPORT]: 'Search Report',
            [QuoteAttachmentType.VALUATION_REPORT]: 'Valuation Report',
            [QuoteAttachmentType.SURVEY_REPORT_BUILDING]: 'Building Survey Report',
            [QuoteAttachmentType.SURVEY_REPORT_STRUCTURAL]: 'Structural Survey Report',
            [QuoteAttachmentType.ENERGY_PERFORMANCE_CERTIFICATE]: 'Energy Performance Certificate',
            [QuoteAttachmentType.FLOOD_RISK_REPORT]: 'Flood Risk Report',
            [QuoteAttachmentType.OTHER]: 'Other Document'
        };

        return displayNames[type] || 'Unknown';
    }
}
