import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RateCard } from '@app/common/typeorm/entities/tenant/rate-card.entity';
import { RateCardFeeItem } from '@app/common/typeorm/entities/tenant/rate-card-fee-item.entity';
import { ParsedRateCard } from './rate-card-parser.service';
import { RateCardManagementService } from './rate-card-management.service';
import { CreateRateCardFeeItemDto } from '../dto/create-rate-card-fee-item.dto';

export interface ImportResult {
    success: boolean;
    rateCardId?: string;
    message: string;
    errors?: string[];
    feeItemsCount?: number;
}

@Injectable()
export class RateCardImportService {
    private readonly logger = new Logger(RateCardImportService.name);

    constructor(
        @InjectRepository(RateCard)
        private readonly rateCardRepository: Repository<RateCard>,
        @InjectRepository(RateCardFeeItem)
        private readonly rateCardFeeItemRepository: Repository<RateCardFeeItem>,
        private readonly rateCardManagementService: RateCardManagementService
    ) {}

    /**
     * Update existing rate card
     */
    private async updateExistingRateCard(
        rateCardId: string,
        parsedData: ParsedRateCard,
        tenantId: string
    ): Promise<ImportResult> {
        try {
            // Update the rate card
            const updateDto = {
                displayName: parsedData.displayName,
                description: parsedData.description,
                version: parsedData.version,
                effectiveDate: parsedData.effectiveDate,
                expiryDate: parsedData.expiryDate,
                priority: parsedData.priority,
                isDefault: parsedData.isDefault,
                metadata: parsedData.metadata
            };

            await this.rateCardManagementService.updateRateCard(rateCardId, updateDto);

            // Delete existing fee items
            await this.rateCardFeeItemRepository.delete({
                rateCardId
            });

            // Create new fee items if any
            let feeItemsCreated = 0;
            if (parsedData.feeItems && parsedData.feeItems.length > 0) {
                feeItemsCreated = await this.createFeeItems(
                    rateCardId,
                    parsedData.feeItems,
                    tenantId
                );
            }

            this.logger.log(
                `Successfully updated rate card: ${rateCardId} with ${feeItemsCreated} fee items`
            );

            return {
                success: true,
                rateCardId,
                message: `Successfully updated rate card for ${parsedData.providerName}`,
                feeItemsCount: feeItemsCreated
            };
        } catch (error) {
            this.logger.error(`Failed to update existing rate card: ${error.message}`);
            return {
                success: false,
                message: `Failed to update rate card: ${error.message}`,
                errors: [error.message]
            };
        }
    }

    /**
     * Create fee items for a rate card
     */
    private async createFeeItems(
        rateCardId: string,
        feeItems: any[],
        tenantId: string
    ): Promise<number> {
        const createdFeeItems: CreateRateCardFeeItemDto[] = feeItems.map((item) => ({
            rateCardId,
            label: item.label,
            feeType: item.feeType,
            categoryName: item.categoryName,
            rangeStart: item.rangeStart,
            rangeEnd: item.rangeEnd,
            conditionSlug: item.conditionSlug,
            netFee: item.netFee,
            vatFee: item.vatFee,
            totalFee: item.totalFee,
            vatType: item.vatType,
            applicableFor: item.applicableFor,
            perParty: item.perParty,
            dynamic: item.dynamic,
            displayOrder: item.displayOrder,
            notes: item.notes,
            tenantId
        }));

        // Save fee items in batches
        const batchSize = 100;
        for (let i = 0; i < createdFeeItems.length; i += batchSize) {
            const batch = createdFeeItems.slice(i, i + batchSize);
            await this.rateCardFeeItemRepository.save(batch);
        }

        return createdFeeItems.length;
    }
}
