import { Injectable, Logger } from '@nestjs/common';
import { RateCardProvider } from '@app/common/typeorm/entities/tenant/rate-card.entity';
import { FeeItemType } from '@app/common/typeorm/entities/tenant/rate-card-fee-item.entity';
import { VatType } from '@app/common/typeorm/entities/tenant/fee-item.entity';

export interface ParsedFeeItem {
    label: string;
    feeType: FeeItemType;
    categoryName: string;
    rangeStart?: number;
    rangeEnd?: number;
    conditionSlug?: string;
    netFee: number;
    vatFee: number;
    totalFee: number;
    vatType: VatType;
    applicableFor: string;
    perParty: boolean;
    dynamic: boolean;
    displayOrder: number;
    notes?: string;
}

export interface ParsedRateCard {
    providerName: string;
    providerCode: RateCardProvider;
    displayName: string;
    description?: string;
    version: string;
    effectiveDate: Date;
    expiryDate?: Date;
    priority: number;
    isDefault: boolean;
    metadata?: Record<string, any>;
    feeItems: ParsedFeeItem[];
}

@Injectable()
export class RateCardParserService {
    private readonly logger = new Logger(RateCardParserService.name);

    /**
     * Get provider code from provider name
     */
    private getProviderCode(providerName: string): RateCardProvider {
        const providerMap: Record<string, RateCardProvider> = {
            'Arrow Conveyancing': RateCardProvider.ARROW,
            'Charles Cameron': RateCardProvider.CHARLES_CAMERON,
            eKeeper: RateCardProvider.EKEEPER,
            Fluent: RateCardProvider.FLUENT,
            'Fort Advice Bureau': RateCardProvider.FORT_ADVICE,
            Gazeal: RateCardProvider.GAZEAL,
            Haysto: RateCardProvider.HAYSTO,
            Independent: RateCardProvider.INDEPENDENT,
            'John Charcol': RateCardProvider.JOHN_CHARCOL,
            'Key Club': RateCardProvider.KEYCLUB,
            'L&C': RateCardProvider.LANDC,
            LEAS: RateCardProvider.LEAS,
            Mojo: RateCardProvider.MOJO,
            Molo: RateCardProvider.MOLO,
            MSM: RateCardProvider.MSM,
            'Optimus Bid': RateCardProvider.OPTIMUS_BID,
            Pepper: RateCardProvider.PEPPER,
            Rayner: RateCardProvider.RAYNER,
            Remax: RateCardProvider.REMAX,
            Sesame: RateCardProvider.SESAME,
            TMG: RateCardProvider.TMG,
            Trussle: RateCardProvider.TRUSSLE,
            YOPA: RateCardProvider.YOPA
        };

        return providerMap[providerName] || RateCardProvider.CUSTOM;
    }

    /**
     * Get provider priority from provider name
     */
    private getProviderPriority(providerName: string): number {
        const priorityMap: Record<string, number> = {
            'Arrow Conveyancing': 1,
            'Charles Cameron': 2,
            eKeeper: 3,
            Fluent: 4,
            'Fort Advice Bureau': 5,
            Gazeal: 6,
            Haysto: 7,
            Independent: 8,
            'John Charcol': 9,
            'Key Club': 10,
            'L&C': 11,
            LEAS: 12,
            Mojo: 13,
            Molo: 14,
            MSM: 15,
            'Optimus Bid': 16,
            Pepper: 17,
            Rayner: 18,
            Remax: 19,
            Sesame: 20,
            TMG: 21,
            Trussle: 22,
            YOPA: 23
        };

        return priorityMap[providerName] || 100;
    }
}
