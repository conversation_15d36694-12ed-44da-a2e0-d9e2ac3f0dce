#!/usr/bin/env node

/**
 * Quote Engine Service Health Check
 * 
 * This script tests if the quote engine service is running and accessible.
 * Run this before testing with Postman to ensure the service is ready.
 */

const http = require('http');
const https = require('https');

// Configuration
const config = {
  baseUrl: process.env.QUOTE_ENGINE_URL || 'http://localhost:3000',
  timeout: 5000,
  endpoints: [
    '/health',
    '/quotes/calculate',
    '/quotes/analytics'
  ]
};

/**
 * Make HTTP request
 */
function makeRequest(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname,
      method: method,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Quote-Engine-Health-Check/1.0'
      }
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = client.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: responseData
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * Test health endpoint
 */
async function testHealthEndpoint() {
  Logger.log('🏥 Testing health endpoint...');
  
  try {
    const response = await makeRequest(`${config.baseUrl}/health`);
    
    if (response.statusCode === 200) {
      Logger.log('✅ Health endpoint is accessible');
      return true;
    } else {
      Logger.log(`❌ Health endpoint returned status ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    Logger.log(`❌ Health endpoint failed: ${error.message}`);
    return false;
  }
}

/**
 * Test quote calculation endpoint (without auth)
 */
async function testQuoteEndpoint() {
  Logger.log('🧮 Testing quote calculation endpoint...');
  
  const testData = {
    propertyValue: 250000,
    propertyType: "residential",
    transactionType: "buy",
    location: {
      postcode: "SW1A 1AA",
      region: "London",
      country: "England"
    },
    propertyConditions: {
      isNewBuild: false,
      isLeasehold: false,
      isWales: false,
      isScotland: false,
      isNorthernIreland: false,
      isFirstTimeBuyer: true,
      isBuyingWithMortgage: true,
      isSharedOwnership: false,
      isHelpToBuy: false,
      isGiftedDeposit: false,
      isCompanyOrTrust: false,
      isResidingOutsideUK: false,
      ownsMultipleProperties: false,
      hasPreviousOwnership: false,
      isMainResidence: true,
      buildingOverLimit: false,
      transferOfEquity: false,
      mortgageRedemption: false
    },
    clientDetails: {
      numberOfBuyers: 1
    }
  };
  
  try {
    const response = await makeRequest(`${config.baseUrl}/quotes/calculate`, 'POST', testData);
    
    if (response.statusCode === 401) {
      Logger.log('✅ Quote endpoint is accessible (authentication required)');
      return true;
    } else if (response.statusCode === 200) {
      Logger.log('✅ Quote endpoint is accessible and working');
      return true;
    } else {
      Logger.log(`❌ Quote endpoint returned status ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    Logger.log(`❌ Quote endpoint failed: ${error.message}`);
    return false;
  }
}

/**
 * Test analytics endpoint (without auth)
 */
async function testAnalyticsEndpoint() {
  Logger.log('📊 Testing analytics endpoint...');
  
  try {
    const response = await makeRequest(`${config.baseUrl}/quotes/analytics`);
    
    if (response.statusCode === 401) {
      Logger.log('✅ Analytics endpoint is accessible (authentication required)');
      return true;
    } else if (response.statusCode === 200) {
      Logger.log('✅ Analytics endpoint is accessible and working');
      return true;
    } else {
      Logger.log(`❌ Analytics endpoint returned status ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    Logger.log(`❌ Analytics endpoint failed: ${error.message}`);
    return false;
  }
}

/**
 * Main test function
 */
async function runHealthCheck() {
  Logger.log('🚀 Quote Engine Service Health Check');
  Logger.log('=====================================');
  Logger.log(`📍 Testing service at: ${config.baseUrl}`);
  Logger.log('');
  
  const results = {
    health: await testHealthEndpoint(),
    quote: await testQuoteEndpoint(),
    analytics: await testAnalyticsEndpoint()
  };
  
  Logger.log('');
  Logger.log('📋 Test Results:');
  Logger.log('================');
  Logger.log(`Health Endpoint: ${results.health ? '✅ PASS' : '❌ FAIL'}`);
  Logger.log(`Quote Endpoint: ${results.quote ? '✅ PASS' : '❌ FAIL'}`);
  Logger.log(`Analytics Endpoint: ${results.analytics ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = Object.values(results).every(result => result);
  
  Logger.log('');
  if (allPassed) {
    Logger.log('🎉 All tests passed! The service is ready for testing.');
    Logger.log('');
    Logger.log('Next steps:');
    Logger.log('1. Import the Postman collection');
    Logger.log('2. Set up authentication token');
    Logger.log('3. Run the automated flow');
    process.exit(0);
  } else {
    Logger.log('❌ Some tests failed. Please check the service status.');
    Logger.log('');
    Logger.log('Troubleshooting:');
    Logger.log('1. Ensure the service is running: npm run start:dev');
    Logger.log('2. Check database connection');
    Logger.log('3. Verify port 3000 is available');
    process.exit(1);
  }
}

// Run the health check
if (require.main === module) {
  runHealthCheck().catch(error => {
    Logger.error('💥 Health check failed:', error.message);
    process.exit(1);
  });
}

module.exports = {
  runHealthCheck,
  testHealthEndpoint,
  testQuoteEndpoint,
  testAnalyticsEndpoint
}; 