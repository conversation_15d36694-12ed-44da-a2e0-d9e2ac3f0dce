NODE_ENV=development

# Service Ports
CORE_PORT=3000
COMMUNICATION_PORT=3001
DOCUMENT_ENGINE_PORT=3002
AUTH_PORT=3003
CASE_MANAGEMENT_PORT=3004
TASK_MANAGEMENT_PORT=3005
QUOTE_ENGINE_PORT=3006

# Database Conf
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=tk_lpm_test
POSTGRES_SSL=false
POSTGRES_MAX_CONNECTIONS=100
POSTGRES_IDLE_TIMEOUT_MS=30000
POSTGRES_CONNECTION_TIMEOUT_MS=2000

# Used by NestJS API service
KEYCLOAK_SERVER_URL=http://localhost:8080
KEYCLOAK_REALM=REALM_NAME
KEYCLOAK_CLIENT_ID=admin-cli
KEYCLOAK_CLIENT_SECRET=ulpN9SOvItx4ncOOUvR0CmIpza4khgMo

# Used by Docker Keycloak container
KEYCLOAK_HOST=postgres
KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_HOSTNAME_PORT=8080

# API Configuration
API_GLOBAL_PREFIX=api
API_RATE_LIMIT_WINDOW_MS=60000
API_RATE_LIMIT_MAX=100

# Logging
LOG_LEVEL=info

# CORS Configuration
CORS_ENABLED=true
CORS_ORIGIN=*

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_USERNAME=default
REDIS_PASSWORD=P4ssw0rd
REDIS_DB=1

# Cache TTL Configuration
CACHE_TTL_CASE_DETAILS=3600
CACHE_TTL_TASK_DETAILS=1800
REDIS_MAXMEMORY=512mb
REDIS_MAXMEMORY_POLICY=allkeys-lru
REDIS_LOG_LEVEL=notice
REDIS_APPENDONLY=yes
REDIS_APPENDFSYNC=everysec

# Redis ACL user passwords (for testing - use strong passwords in production)
REDIS_MONITOR_PASSWORD=monitor_test_123
REDIS_HEALTH_PASSWORD=health_test_123
REDIS_ADMIN_PASSWORD=admin_test_456
REDIS_BULLMQ_PASSWORD=bullmq_test_789
REDIS_READONLY_PASSWORD=readonly_test_321

#Send grid
SENDGRID_API_KEY=*********************************************************************

#Mail Gun
MAILGUN_API_KEY=**************************************************
MAILGUN_DOMAIN=sandbox849dccbefb6f452b879eed2ae7a9e861.mailgun.org
MAILGUN_URL=https://api.mailgun.net

FROM_EMAIL=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# AWS Configuration for SES
AWS_SES_REGION=us-east-1

# MinIO Configuration (S3-compatible storage for local development)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin
AWS_DOCUMENT_BUCKET_NAME=tk-lpm-documents
AWS_S3_ENDPOINT=http://localhost:9000
AWS_S3_FORCE_PATH_STYLE=true
AWS_S3_USE_SSL=false
AWS_S3_PRESIGNED_URL_EXPIRATION=3600

# AWS SES (keeping original for email)
AWS_SES_ACCESS_KEY_ID=********************
AWS_SES_SECRET_ACCESS_KEY=cfauJRUHLHMApRmCKMMlxSIT7C/XY2OU57BvwQis