import { DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';

const logger = new Logger('CreateDefaultMilestoneTables');

/**
 * Migration to create default milestone tables in tenant schemas
 * These tables store seeded milestone and task configurations
 */
export async function createDefaultMilestoneTablesInTenant(dataSource: DataSource): Promise<void> {
    const queryRunner = dataSource.createQueryRunner();

    try {
        await queryRunner.connect();

        logger.log('Creating default milestone tables...');

        // Create default_milestones table
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS default_milestones (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(255) NOT NULL,
                description TEXT,
                order_index INTEGER NOT NULL,
                case_type VARCHAR(50) NOT NULL DEFAULT 'CONVEYANCING',
                target_days INTEGER,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );
        `);

        // Create default_milestone_tasks table
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS default_milestone_tasks (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                milestone_id UUID NOT NULL REFERENCES default_milestones(id) ON DELETE CASCADE,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                order_index INTEGER NOT NULL,
                priority VARCHAR(20) NOT NULL DEFAULT 'MEDIUM',
                estimated_days INTEGER DEFAULT 1,
                is_required BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );
        `);

        // Create indexes for better performance
        await queryRunner.query(`
            CREATE INDEX IF NOT EXISTS idx_default_milestones_case_type 
            ON default_milestones(case_type);
        `);

        await queryRunner.query(`
            CREATE INDEX IF NOT EXISTS idx_default_milestones_order 
            ON default_milestones(order_index);
        `);

        await queryRunner.query(`
            CREATE INDEX IF NOT EXISTS idx_default_milestone_tasks_milestone_id 
            ON default_milestone_tasks(milestone_id);
        `);

        await queryRunner.query(`
            CREATE INDEX IF NOT EXISTS idx_default_milestone_tasks_order 
            ON default_milestone_tasks(order_index);
        `);

        logger.log('Default milestone tables created successfully');
    } catch (error) {
        logger.error('Error creating default milestone tables:', error);
        throw error;
    } finally {
        await queryRunner.release();
    }
}
