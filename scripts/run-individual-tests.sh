#!/bin/bash

# Set colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Running Individual Integration Tests in Order ===${NC}"

# Function to handle errors and cleanup
cleanup() {
  echo -e "${YELLOW}Cleaning up test environment...${NC}"
  
  # Stop Docker containers if they're running
  if [ -n "$(docker ps -q -f name=tk-lpm-postgres-test)" ] || [ -n "$(docker ps -q -f name=tk-lpm-keycloak-test)" ]; then
    echo -e "${YELLOW}Stopping Docker containers...${NC}"
    docker compose -f docker-compose.test.yml down
  fi
  
  exit ${1:-0}
}

# Trap SIGINT, SIGTERM and ERR signals to ensure cleanup
trap 'cleanup $?' ERR
trap 'cleanup' SIGINT SIGTERM

# Function to run a specific test and check its result
run_test() {
  local test_file=$1
  local test_name=$(basename "$test_file" .integration.spec.ts)
  
  echo -e "${YELLOW}Running test: ${test_name}${NC}"
  INTEGRATION_TEST=true yarn jest "$test_file" --detectOpenHandles
  
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ $test_name test passed${NC}"
    return 0
  else
    echo -e "${RED}✗ $test_name test failed${NC}"
    return 1
  fi
}

# Start Docker containers
echo -e "${BLUE}Starting Docker containers...${NC}"
docker compose -f docker-compose.test.yml down -v
docker compose -f docker-compose.test.yml up -d

# Wait for services to be ready
echo -e "${BLUE}Waiting for services to be ready...${NC}"
TIMEOUT=180
ELAPSED=0
RETRY_INTERVAL=5

# Check if PostgreSQL is ready
echo -e "${YELLOW}Checking PostgreSQL readiness...${NC}"
while [ $ELAPSED -lt $TIMEOUT ]; do
  if docker exec tk-lpm-postgres-test pg_isready -U postgres > /dev/null 2>&1; then
    echo -e "${GREEN}✓ PostgreSQL is ready!${NC}"
    break
  fi
  echo -e "Waiting for PostgreSQL to be ready... ($ELAPSED/$TIMEOUT seconds)"
  sleep $RETRY_INTERVAL
  ELAPSED=$((ELAPSED+RETRY_INTERVAL))
done

if [ $ELAPSED -ge $TIMEOUT ]; then
  echo -e "${RED}❌ Timeout waiting for PostgreSQL to be ready${NC}"
  docker logs tk-lpm-postgres-test
  cleanup 1
fi

# Reset elapsed time for Keycloak check
ELAPSED=0
echo -e "${YELLOW}Checking Keycloak readiness...${NC}"

# Wait for Keycloak to be ready
while [ $ELAPSED -lt $TIMEOUT ]; do
  # Check if container is running
  CONTAINER_RUNNING=$(docker ps --filter "name=tk-lpm-keycloak-test" --filter "status=running" -q)
  if [ -z "$CONTAINER_RUNNING" ]; then
    echo -e "${RED}❌ Keycloak container is not running anymore!${NC}"
    docker logs tk-lpm-keycloak-test
    cleanup 1
  fi
  
  # Check if Keycloak is responding to HTTP requests
  if curl -s http://localhost:8090/ | grep -q "Keycloak"; then
    echo -e "${GREEN}✓ Keycloak is responding to HTTP requests!${NC}"
    break
  fi
  
  echo -e "Waiting for Keycloak to be ready... ($ELAPSED/$TIMEOUT seconds)"
  sleep $RETRY_INTERVAL
  ELAPSED=$((ELAPSED+RETRY_INTERVAL))
done

if [ $ELAPSED -ge $TIMEOUT ]; then
  echo -e "${RED}❌ Timeout waiting for Keycloak to be ready${NC}"
  docker logs tk-lpm-keycloak-test
  cleanup 1
fi

# Set environment variables for tests
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5434
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=postgres
export POSTGRES_DB=tk_lpm_test
export KEYCLOAK_SERVER_URL=http://localhost:8090
export KEYCLOAK_REALM=REALM_NAME
export KEYCLOAK_CLIENT_ID=admin-cli
export KEYCLOAK_CLIENT_SECRET=ulpN9SOvItx4ncOOUvR0CmIpza4khgMo

# Setup test database 
echo -e "${BLUE}Setting up test database...${NC}"
node test/helpers/setup-test-db.js

# Running tests in order of dependency
# 1. First run the simple database test to verify connectivity
echo -e "${BLUE}Step 1: Testing database connectivity${NC}"
run_test "test/integration/simple-db.integration.spec.ts"
if [ $? -ne 0 ]; then
  echo -e "${RED}Database connectivity test failed. Stopping tests.${NC}"
  cleanup 1
fi

# 2. Test mock services next
echo -e "${BLUE}Step 2: Testing axios interceptors${NC}"
run_test "test/integration/axios-interceptors.integration.spec.ts"

echo -e "${BLUE}Step 3: Testing Keycloak mock${NC}"
run_test "test/integration/keycloak-mock.integration.spec.ts"

echo -e "${BLUE}Step 4: Testing role hierarchy mock${NC}"
run_test "test/integration/role-hierarchy-mock.integration.spec.ts"

# 3. Test auth module which might depend on the previous services
echo -e "${BLUE}Step 5: Testing auth module${NC}"
run_test "test/integration/auth.integration.spec.ts"

# 4. Finally test case management which might depend on auth
echo -e "${BLUE}Step 6: Testing case management module${NC}"
run_test "test/integration/case-management.integration.spec.ts"

# Cleanup Docker containers
cleanup 0 