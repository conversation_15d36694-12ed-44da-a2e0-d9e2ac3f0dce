import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { Logger } from '@nestjs/common';

dotenv.config({
    path: path.resolve(process.cwd(), '.env'),
});

const dropDependencies = async () => {
    const dataSource = new DataSource({
        type: 'postgres',
        host: process.env.POSTGRES_HOST,
        port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
        username: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: process.env.POSTGRES_DB,
        schema: 'public'
    });

    try {
        await dataSource.initialize();
        Logger.log('Connected to database');

        // First, disable all triggers
        Logger.log('Disabling triggers...');
        await dataSource.query('SET session_replication_role = replica;');

        // Drop all foreign key constraints first
        const dropFKQuery = `
            DO $$ 
            DECLARE 
                r RECORD;
            BEGIN
                FOR r IN (
                    SELECT DISTINCT
                        tc.table_schema, 
                        tc.constraint_name, 
                        tc.table_name
                    FROM information_schema.table_constraints tc
                    WHERE tc.constraint_type = 'FOREIGN KEY'
                )
                LOOP
                    EXECUTE 'ALTER TABLE ' || quote_ident(r.table_schema) || '.' || quote_ident(r.table_name) || 
                           ' DROP CONSTRAINT IF EXISTS ' || quote_ident(r.constraint_name);
                END LOOP;
            END $$;
        `;
        
        Logger.log('Dropping foreign key constraints...');
        await dataSource.query(dropFKQuery);

        // Drop the problematic tables with CASCADE
        Logger.log('Dropping tables...');
        await dataSource.query(`
            DROP TABLE IF EXISTS user_roles CASCADE;
            DROP TABLE IF EXISTS user_profiles CASCADE;
            DROP TABLE IF EXISTS tenant_roles CASCADE;
        `);

        // Re-enable triggers
        Logger.log('Re-enabling triggers...');
        await dataSource.query('SET session_replication_role = DEFAULT;');

        Logger.log('Successfully dropped all dependent objects');
    } catch (error) {
        Logger.error('Error dropping dependencies:', error);
        throw error;
    } finally {
        if (dataSource.isInitialized) {
            await dataSource.destroy();
        }
    }
};

dropDependencies()
    .then(() => {
        Logger.log('Completed cleanup');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Failed to complete cleanup:', error);
        process.exit(1);
    }); 