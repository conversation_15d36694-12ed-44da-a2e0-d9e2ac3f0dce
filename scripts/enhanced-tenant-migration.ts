#!/usr/bin/env ts-node
/* eslint-disable no-console, no-case-declarations */

import { DataSource } from 'typeorm';
import { Command } from 'commander';
import * as path from 'path';
import * as dotenv from 'dotenv';
import { TenantMigrationService, MigrationProgress } from './services/tenant-migration.service';
import { glob } from 'glob';

// Load environment variables
dotenv.config();

const program = new Command();

// Database configuration
const createDataSource = () =>
    new DataSource({
        type: 'postgres',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.POSTGRES_PORT || '5432'),
        username: process.env.POSTGRES_USER || 'postgres',
        password: process.env.POSTGRES_PASSWORD || 'postgres',
        database: process.env.POSTGRES_DB || 'tk_lpm',
        schema: 'public',
        entities: [
            path.join(__dirname, '../libs/common/src/typeorm/entities/tenant/**/*.entity.{ts,js}')
        ],
        migrations: [
            path.join(__dirname, '../libs/common/src/typeorm/migrations/tenant/**/*.{ts,js}')
        ],
        synchronize: false,
        logging: process.env.NODE_ENV === 'development'
    });

// Progress reporting
const createProgressReporter = () => {
    const startTime = Date.now();
    let completed = 0;
    let total = 0;

    return {
        setTotal: (count: number) => {
            total = count;
            console.log(`\n🚀 Starting migration for ${total} tenant schemas...\n`);
        },

        onProgress: (progress: MigrationProgress) => {
            const elapsed = Date.now() - startTime;
            const elapsedSeconds = Math.floor(elapsed / 1000);

            switch (progress.status) {
                case 'running':
                    console.log(`⏳ [${progress.schema}] Migration started...`);
                    break;

                case 'completed':
                    completed++;
                    const duration =
                        progress.endTime && progress.startTime
                            ? progress.endTime.getTime() - progress.startTime.getTime()
                            : 0;
                    console.log(
                        `✅ [${progress.schema}] Completed in ${duration}ms (${completed}/${total})`
                    );
                    break;

                case 'failed':
                    console.log(`❌ [${progress.schema}] Failed: ${progress.error}`);
                    break;
            }

            // Show overall progress
            if (progress.status === 'completed' || progress.status === 'failed') {
                const percentage = Math.round((completed / total) * 100);
                console.log(`📊 Overall progress: ${percentage}% (${elapsedSeconds}s elapsed)\n`);
            }
        }
    };
};

// Commands
program
    .name('tenant-migration')
    .description('Enhanced tenant migration management tool')
    .version('1.0.0');

program
    .command('discover')
    .description('Discover tenant schemas and entities')
    .action(async () => {
        const dataSource = createDataSource();
        const migrationService = new TenantMigrationService(dataSource);

        try {
            await dataSource.initialize();

            console.log('🔍 Discovering tenant schemas...');
            const schemas = await migrationService.discoverTenantSchemas();
            console.log(`Found ${schemas.length} tenant schemas:`);
            schemas.forEach((schema) => console.log(`  - ${schema.name}`));

            console.log('\n🔍 Discovering tenant entities...');
            const entities = await migrationService.discoverTenantEntities();
            console.log(`Found ${entities.length} tenant entities:`);
            entities.forEach((entity) => console.log(`  - ${entity}`));

            console.log('\n📋 Entity metadata:');
            const metadata = await migrationService.getEntityMetadata();
            metadata.forEach((meta) => {
                console.log(`  - ${meta.tableName} (${meta.columns.length} columns)`);
            });
        } catch (error) {
            console.error('❌ Discovery failed:', error);
            process.exit(1);
        } finally {
            if (dataSource.isInitialized) {
                await dataSource.destroy();
            }
        }
    });

program
    .command('run')
    .description('Run migrations for all tenant schemas')
    .option('-c, --concurrency <number>', 'Number of parallel migrations', '5')
    .option('--dry-run', 'Simulate migration without executing')
    .action(async (options) => {
        const dataSource = createDataSource();
        const migrationService = new TenantMigrationService(
            dataSource,
            parseInt(options.concurrency)
        );
        const reporter = createProgressReporter();

        try {
            await dataSource.initialize();

            // Get migration files
            const migrationPattern = path.join(
                __dirname,
                '../libs/common/src/typeorm/migrations/tenant/**/*.{ts,js}'
            );
            const migrationFiles = await glob(migrationPattern);

            if (migrationFiles.length === 0) {
                console.log('⚠️  No migration files found');
                return;
            }

            console.log(`📁 Found ${migrationFiles.length} migration files`);

            if (options.dryRun) {
                console.log('🧪 Running in DRY RUN mode - no changes will be made');
            }

            // Discover schemas first to set total count
            const schemas = await migrationService.discoverTenantSchemas();
            reporter.setTotal(schemas.length);

            // Run migrations
            const result = await migrationService.runMigrationsForAllSchemas(migrationFiles, {
                concurrency: parseInt(options.concurrency),
                dryRun: options.dryRun,
                onProgress: reporter.onProgress
            });

            // Final report
            console.log('\n📊 Migration Summary:');
            console.log(`  Total schemas: ${result.totalSchemas}`);
            console.log(`  Successful: ${result.successful}`);
            console.log(`  Failed: ${result.failed}`);
            console.log(`  Duration: ${Math.round(result.duration / 1000)}s`);

            if (result.failed > 0) {
                console.log('\n❌ Failed schemas:');
                result.results
                    .filter((r) => r.status === 'failed')
                    .forEach((r) => console.log(`  - ${r.schema}: ${r.error}`));
                process.exit(1);
            } else {
                console.log('\n🎉 All migrations completed successfully!');
            }
        } catch (error) {
            console.error('❌ Migration failed:', error);
            process.exit(1);
        } finally {
            if (dataSource.isInitialized) {
                await dataSource.destroy();
            }
        }
    });

program
    .command('generate <name>')
    .description('Generate a new migration based on entity changes')
    .action(async (name: string) => {
        const dataSource = createDataSource();
        const migrationService = new TenantMigrationService(dataSource);

        try {
            await dataSource.initialize();

            console.log(`🔧 Generating migration: ${name}`);
            const migrationPath = await migrationService.generateMigration(name);
            console.log(`✅ Migration generated: ${migrationPath}`);
        } catch (error) {
            console.error('❌ Migration generation failed:', error);
            process.exit(1);
        } finally {
            if (dataSource.isInitialized) {
                await dataSource.destroy();
            }
        }
    });

// Parse command line arguments
program.parse(process.argv);
