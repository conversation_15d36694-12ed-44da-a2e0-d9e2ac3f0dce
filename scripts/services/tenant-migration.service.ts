/* eslint-disable no-console, @typescript-eslint/no-unused-vars */
import 'reflect-metadata';
import { DataSource, DataSourceOptions, EntityMetadata } from 'typeorm';
import { glob } from 'glob';
import * as path from 'path';
import * as fs from 'fs';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import { TENANT_ENTITIES } from '../../libs/common/src/typeorm/entities/index';
import { TENANT_ENTITY_KEY } from '../../libs/common/src/multi-tenancy/decorators/tenant-entity.decorator';

/**
 * Class to track migration changes
 */
class MigrationChanges {
    public newTables: EntityMetadata[] = [];
    public droppedTables: any[] = [];
    public newColumns: Map<string, any[]> = new Map();
    public droppedColumns: Map<string, any[]> = new Map();
    public modifiedColumns: Map<string, Array<{ prev: any; current: any }>> = new Map();
    public newIndices: Map<string, any[]> = new Map();
    public droppedIndices: Map<string, any[]> = new Map();
    public newUniqueConstraints: Map<string, any[]> = new Map();
    public droppedUniqueConstraints: Map<string, any[]> = new Map();

    addNewTable(entity: EntityMetadata): void {
        this.newTables.push(entity);
    }

    addDroppedTable(entity: any): void {
        this.droppedTables.push(entity);
    }

    addNewColumn(tableName: string, column: any): void {
        if (!this.newColumns.has(tableName)) {
            this.newColumns.set(tableName, []);
        }
        this.newColumns.get(tableName)!.push(column);
    }

    addDroppedColumn(tableName: string, column: any): void {
        if (!this.droppedColumns.has(tableName)) {
            this.droppedColumns.set(tableName, []);
        }
        this.droppedColumns.get(tableName)!.push(column);
    }

    addModifiedColumn(tableName: string, prevColumn: any, currentColumn: any): void {
        if (!this.modifiedColumns.has(tableName)) {
            this.modifiedColumns.set(tableName, []);
        }
        this.modifiedColumns.get(tableName)!.push({ prev: prevColumn, current: currentColumn });
    }

    addNewIndex(tableName: string, index: any): void {
        if (!this.newIndices.has(tableName)) {
            this.newIndices.set(tableName, []);
        }
        this.newIndices.get(tableName)!.push(index);
    }

    addDroppedIndex(tableName: string, index: any): void {
        if (!this.droppedIndices.has(tableName)) {
            this.droppedIndices.set(tableName, []);
        }
        this.droppedIndices.get(tableName)!.push(index);
    }

    addNewUniqueConstraint(tableName: string, column: any): void {
        if (!this.newUniqueConstraints.has(tableName)) {
            this.newUniqueConstraints.set(tableName, []);
        }
        this.newUniqueConstraints.get(tableName)!.push(column);
    }

    addDroppedUniqueConstraint(tableName: string, column: any): void {
        if (!this.droppedUniqueConstraints.has(tableName)) {
            this.droppedUniqueConstraints.set(tableName, []);
        }
        this.droppedUniqueConstraints.get(tableName)!.push(column);
    }

    isEmpty(): boolean {
        return (
            this.newTables.length === 0 &&
            this.droppedTables.length === 0 &&
            this.newColumns.size === 0 &&
            this.droppedColumns.size === 0 &&
            this.modifiedColumns.size === 0 &&
            this.newIndices.size === 0 &&
            this.droppedIndices.size === 0 &&
            this.newUniqueConstraints.size === 0 &&
            this.droppedUniqueConstraints.size === 0
        );
    }

    logSummary(): void {
        console.log(`  - New tables: ${this.newTables.length}`);
        console.log(`  - Dropped tables: ${this.droppedTables.length}`);
        console.log(`  - Tables with new columns: ${this.newColumns.size}`);
        console.log(`  - Tables with dropped columns: ${this.droppedColumns.size}`);
        console.log(`  - Tables with modified columns: ${this.modifiedColumns.size}`);
        console.log(`  - Tables with new indices: ${this.newIndices.size}`);
        console.log(`  - Tables with dropped indices: ${this.droppedIndices.size}`);
        console.log(`  - Tables with new unique constraints: ${this.newUniqueConstraints.size}`);
        console.log(
            `  - Tables with dropped unique constraints: ${this.droppedUniqueConstraints.size}`
        );
    }
}

export interface TenantSchema {
    name: string;
    exists: boolean;
}

export interface MigrationProgress {
    schema: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    error?: string;
    startTime?: Date;
    endTime?: Date;
}

export interface MigrationResult {
    totalSchemas: number;
    successful: number;
    failed: number;
    results: MigrationProgress[];
    duration: number;
}

export class TenantMigrationService {
    private dataSource: DataSource;
    private concurrency: number;

    constructor(dataSource: DataSource, concurrency: number = 5) {
        this.dataSource = dataSource;
        this.concurrency = concurrency;
    }

    /**
     * Discovers all tenant schemas in the database
     */
    async discoverTenantSchemas(): Promise<TenantSchema[]> {
        const query = `
            SELECT schema_name as name
            FROM information_schema.schemata
            WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'public')
            AND schema_name ~ '^tenant_'
            ORDER BY schema_name
        `;

        const result = await this.dataSource.query(query);
        return result.map((row: any) => ({
            name: row.name,
            exists: true
        }));
    }

    /**
     * Discovers all tenant entities from the file system
     */
    async discoverTenantEntities(): Promise<string[]> {
        const entitiesPath = path.join(
            process.cwd(),
            'libs/common/src/typeorm/entities/tenant/**/*.entity.{ts,js}'
        );
        const entityFiles = await glob(entitiesPath);

        const entities: string[] = [];

        for (const file of entityFiles) {
            try {
                const relativePath = path.relative(process.cwd(), file);
                entities.push(relativePath);
            } catch (error) {
                console.warn(`Failed to process entity file ${file}:`, error);
            }
        }

        return entities;
    }

    /**
     * Gets entity metadata for all tenant entities
     */
    async getEntityMetadata(): Promise<EntityMetadata[]> {
        if (!this.dataSource.isInitialized) {
            await this.dataSource.initialize();
        }

        // Filter entity metadata using multiple approaches to ensure we get all tenant entities
        const tenantEntities = this.dataSource.entityMetadatas.filter((metadata) => {
            try {
                // Approach 1: Check if the entity is in the TENANT_ENTITIES array
                const isInTenantEntitiesArray = TENANT_ENTITIES.some(
                    (entity) => metadata.target === entity
                );

                // Approach 2: Check if the entity has the @TenantEntity() decorator metadata
                let hasTenantEntityMetadata = false;
                if (metadata.target && typeof metadata.target === 'function') {
                    try {
                        hasTenantEntityMetadata =
                            Reflect.getMetadata(TENANT_ENTITY_KEY, metadata.target) === true;
                    } catch (error) {
                        // Ignore metadata errors and continue with other approaches
                        console.warn(
                            `Failed to get metadata for ${metadata.tableName}:`,
                            error.message
                        );
                    }
                }

                // Approach 3: Check if the entity is in the tenant folder path (fallback)
                const isInTenantFolder =
                    metadata.tablePath?.includes('tenant') ||
                    metadata.target?.toString().includes('tenant');

                // Return true if any of the approaches match
                return isInTenantEntitiesArray || hasTenantEntityMetadata || isInTenantFolder;
            } catch (error) {
                console.warn(
                    `Error processing entity metadata for ${metadata.tableName}:`,
                    error.message
                );
                return false;
            }
        });

        // Log detected entities for debugging
        console.log(`Detected ${tenantEntities.length} tenant entities:`);
        tenantEntities.forEach((metadata) => {
            const targetName =
                typeof metadata.target === 'function' ? metadata.target.name : 'unknown';
            console.log(`  - ${metadata.tableName} (${targetName})`);
        });

        return tenantEntities;
    }

    /**
     * Runs migrations for all tenant schemas in parallel
     */
    async runMigrationsForAllSchemas(
        migrationPaths: string[],
        options: {
            concurrency?: number;
            dryRun?: boolean;
            onProgress?: (progress: MigrationProgress) => void;
        } = {}
    ): Promise<MigrationResult> {
        const startTime = Date.now();
        const concurrency = options.concurrency || this.concurrency;

        // Discover all tenant schemas
        const schemas = await this.discoverTenantSchemas();
        console.log(`Found ${schemas.length} tenant schemas`);

        // Initialize progress tracking
        const results: MigrationProgress[] = schemas.map((schema) => ({
            schema: schema.name,
            status: 'pending' as const
        }));

        // Process schemas in batches with controlled concurrency
        const batches = this.createBatches(schemas, concurrency);

        for (const batch of batches) {
            const promises = batch.map((schema) =>
                this.runMigrationForSchema(schema.name, migrationPaths, options, results)
            );

            await Promise.allSettled(promises);
        }

        const endTime = Date.now();
        const successful = results.filter((r) => r.status === 'completed').length;
        const failed = results.filter((r) => r.status === 'failed').length;

        return {
            totalSchemas: schemas.length,
            successful,
            failed,
            results,
            duration: endTime - startTime
        };
    }

    /**
     * Runs migration for a specific schema
     */
    private async runMigrationForSchema(
        schemaName: string,
        migrationPaths: string[],
        options: any,
        results: MigrationProgress[]
    ): Promise<void> {
        const progress = results.find((r) => r.schema === schemaName);
        if (!progress) return;

        try {
            progress.status = 'running';
            progress.startTime = new Date();

            if (options.onProgress) {
                options.onProgress(progress);
            }

            console.log(`Running migrations for schema: ${schemaName}`);

            if (options.dryRun) {
                // Simulate migration for dry run
                await new Promise((resolve) => setTimeout(resolve, 100));
            } else {
                await this.executeMigrationForSchema(schemaName, migrationPaths);
            }

            progress.status = 'completed';
            progress.endTime = new Date();

            if (options.onProgress) {
                options.onProgress(progress);
            }
        } catch (error) {
            progress.status = 'failed';
            progress.error = error instanceof Error ? error.message : String(error);
            progress.endTime = new Date();

            console.error(`Migration failed for schema ${schemaName}:`, error);

            if (options.onProgress) {
                options.onProgress(progress);
            }
        }
    }

    /**
     * Executes migration for a specific schema
     */
    private async executeMigrationForSchema(
        schemaName: string,
        migrationPaths: string[]
    ): Promise<void> {
        const baseOptions = this.dataSource.options as PostgresConnectionOptions;

        try {
            // Create a new data source instance for this schema with proper search path
            const schemaDataSource = new DataSource({
                ...baseOptions,
                schema: schemaName,
                migrations: migrationPaths,
                migrationsRun: true,
                migrationsTableName: 'migrations',
                // Ensure the search path is set correctly for this connection
                extra: {
                    ...baseOptions.extra,
                    // Set search path in connection options
                    options: `-c search_path="${schemaName}"`
                }
            } as DataSourceOptions);

            if (!schemaDataSource.isInitialized) {
                await schemaDataSource.initialize();

                // Double-check: Set search path immediately after initialization
                await schemaDataSource.query(`SET search_path TO "${schemaName}"`);
            }

            // Run pending migrations
            await schemaDataSource.runMigrations();

            // Clean up
            if (schemaDataSource.isInitialized) {
                await schemaDataSource.destroy();
            }
        } catch (error) {
            console.error(`Migration failed for schema ${schemaName}:`, error);
            throw error;
        }
    }

    /**
     * Creates batches for parallel processing
     */
    private createBatches<T>(items: T[], batchSize: number): T[][] {
        const batches: T[][] = [];
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }
        return batches;
    }

    /**
     * Generates a new migration based on entity changes (differential)
     */
    async generateMigration(name: string): Promise<string> {
        const timestamp = Date.now();
        // Convert kebab-case to PascalCase for class name
        const className =
            name
                .split('-')
                .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
                .join('') + timestamp;
        const fileName = `${timestamp}-${name}.ts`;

        // Get current entity metadata
        const currentEntities = await this.getEntityMetadata();

        // Get previous migration state
        const previousState = this.getLastMigrationState();

        // Detect changes between current and previous state
        const changes = this.detectEntityChanges(previousState, currentEntities);

        if (changes.isEmpty()) {
            console.log('No changes detected. No migration generated.');
            return '';
        }

        // Generate migration content based on changes only
        const migrationContent = this.generateDifferentialMigrationContent(className, changes);

        // Write migration file
        const migrationPath = path.join(
            process.cwd(),
            'libs/common/src/typeorm/migrations/tenant',
            fileName
        );

        fs.writeFileSync(migrationPath, migrationContent);

        // Save current state for next comparison
        this.saveCurrentMigrationState(currentEntities, fileName);

        console.log(`Generated migration: ${fileName}`);
        console.log('Changes detected:');
        changes.logSummary();

        return migrationPath;
    }

    /**
     * Gets the last migration state from saved metadata
     */
    private getLastMigrationState(): EntityMetadata[] {
        const stateFilePath = path.join(
            process.cwd(),
            'libs/common/src/typeorm/migrations/tenant',
            '.migration-state.json'
        );

        if (!fs.existsSync(stateFilePath)) {
            console.log('No previous migration state found. This will be the initial migration.');
            return [];
        }

        try {
            const stateData = JSON.parse(fs.readFileSync(stateFilePath, 'utf8'));
            return stateData.entities || [];
        } catch (error) {
            console.warn('Failed to read previous migration state:', error);
            return [];
        }
    }

    /**
     * Saves current entity state for future comparisons
     */
    private saveCurrentMigrationState(entities: EntityMetadata[], migrationFileName: string): void {
        const stateFilePath = path.join(
            process.cwd(),
            'libs/common/src/typeorm/migrations/tenant',
            '.migration-state.json'
        );

        // Serialize entity metadata (only the parts we need for comparison)
        const serializedEntities = entities.map((entity) => ({
            tableName: entity.tableName,
            columns: entity.columns.map((col) => {
                // Handle special TypeORM decorators for default values
                let defaultValue = col.default;

                // Handle @CreateDateColumn and @UpdateDateColumn
                if (col.isCreateDate || col.isUpdateDate) {
                    defaultValue = 'now()';
                } else if (typeof col.default === 'function') {
                    // Handle function defaults
                    const funcStr = col.default.toString();
                    if (funcStr.includes('CURRENT_TIMESTAMP') || funcStr.includes('now()')) {
                        defaultValue = 'now()';
                    } else if (funcStr.includes('gen_random_uuid')) {
                        defaultValue = 'gen_random_uuid()';
                    } else if (funcStr.includes('uuid_generate_v4')) {
                        // Convert uuid_generate_v4 to gen_random_uuid for consistency
                        defaultValue = 'gen_random_uuid()';
                    } else if (funcStr.includes("'{}'")) {
                        defaultValue = "'{}'";
                    }
                }

                // Use canonical type normalization for consistency
                const columnType = this.normalizeColumnType(col, entity.tableName);

                // Properly detect unique constraints using the same logic as isColumnUnique
                const isUnique = this.isColumnUnique(col);

                return {
                    databaseName: col.databaseName,
                    type: columnType,
                    isNullable: col.isNullable,
                    isPrimary: col.isPrimary,
                    isUnique: isUnique,
                    unique: isUnique, // Also store as 'unique' for backward compatibility
                    isCreateDate: col.isCreateDate,
                    isUpdateDate: col.isUpdateDate,
                    default: defaultValue,
                    enum: col.enum,
                    enumName: (col as any).enumName,
                    length: col.length,
                    precision: col.precision,
                    scale: col.scale
                };
            }),
            relations: entity.relations.map((rel) => ({
                propertyName: rel.propertyName,
                type: rel.relationType,
                joinColumns: rel.joinColumns?.map((jc) => ({
                    databaseName: jc.databaseName,
                    referencedColumn: jc.referencedColumn?.databaseName
                })),
                inverseEntityMetadata: {
                    tableName: rel.inverseEntityMetadata.tableName
                }
            })),
            indices: entity.indices.map((idx) => ({
                name: idx.name,
                columnNames: idx.columns.map((col) => col.databaseName),
                isUnique: idx.isUnique
            }))
        }));

        const stateData = {
            lastMigration: migrationFileName,
            timestamp: Date.now(),
            entities: serializedEntities
        };

        fs.writeFileSync(stateFilePath, JSON.stringify(stateData, null, 2));
        console.log('Migration state saved for future comparisons.');
    }

    /**
     * Detects changes between previous and current entity states
     */
    private detectEntityChanges(
        previousEntities: any[],
        currentEntities: EntityMetadata[]
    ): MigrationChanges {
        const changes = new MigrationChanges();

        // Convert previous entities to a map for easier lookup
        const previousMap = new Map(previousEntities.map((e) => [e.tableName, e]));
        const currentMap = new Map(currentEntities.map((e) => [e.tableName, e]));

        // Detect new tables
        for (const entity of currentEntities) {
            if (!previousMap.has(entity.tableName)) {
                changes.addNewTable(entity);
            }
        }

        // Detect dropped tables
        for (const prevEntity of previousEntities) {
            if (!currentMap.has(prevEntity.tableName)) {
                changes.addDroppedTable(prevEntity);
            }
        }

        // Detect table modifications
        for (const entity of currentEntities) {
            const prevEntity = previousMap.get(entity.tableName);
            if (prevEntity) {
                this.detectTableChanges(prevEntity, entity, changes);
            }
        }

        return changes;
    }

    /**
     * Detects changes within a specific table
     */
    private detectTableChanges(
        prevEntity: any,
        currentEntity: EntityMetadata,
        changes: MigrationChanges
    ): void {
        const tableName = currentEntity.tableName;

        // Convert columns to maps for easier comparison
        const prevColumns = new Map(prevEntity.columns.map((c: any) => [c.databaseName, c]));
        const currentColumns = new Map(currentEntity.columns.map((c) => [c.databaseName, c]));

        // Detect new columns
        for (const column of currentEntity.columns) {
            if (!prevColumns.has(column.databaseName)) {
                changes.addNewColumn(tableName, column);
            }
        }

        // Detect dropped columns
        for (const prevColumn of prevEntity.columns) {
            if (!currentColumns.has(prevColumn.databaseName)) {
                changes.addDroppedColumn(tableName, prevColumn);
            }
        }

        // Detect column modifications
        for (const column of currentEntity.columns) {
            const prevColumn = prevColumns.get(column.databaseName);
            if (prevColumn && this.hasColumnChanged(prevColumn, column)) {
                changes.addModifiedColumn(tableName, prevColumn, column);
            }
        }

        // Detect index changes
        this.detectIndexChanges(prevEntity, currentEntity, changes);

        // Detect unique constraint changes
        this.detectUniqueConstraintChanges(prevEntity, currentEntity, changes);
    }

    /**
     * Checks if a column has changed with detailed debugging
     */
    private hasColumnChanged(prevColumn: any, currentColumn: any): boolean {
        // Normalize current column default value for comparison
        let currentDefault = currentColumn.default;
        if (currentColumn.isCreateDate || currentColumn.isUpdateDate) {
            currentDefault = 'now()';
        } else if (typeof currentColumn.default === 'function') {
            const funcStr = currentColumn.default.toString();
            if (funcStr.includes('CURRENT_TIMESTAMP') || funcStr.includes('now()')) {
                currentDefault = 'now()';
            } else if (funcStr.includes('gen_random_uuid')) {
                currentDefault = 'gen_random_uuid()';
            } else if (funcStr.includes('uuid_generate_v4')) {
                // Convert uuid_generate_v4 to gen_random_uuid for consistency
                currentDefault = 'gen_random_uuid()';
            } else if (funcStr.includes("'{}'")) {
                currentDefault = "'{}'";
            }
        }

        // Use canonical type normalization for both columns
        const tableName = currentColumn.entityMetadata?.tableName || 'table';
        const currentType = this.normalizeColumnType(currentColumn, tableName);
        const prevType = this.normalizeColumnType(prevColumn, tableName);

        // Normalize previous column default value for comparison
        let prevDefault = prevColumn.default;
        if (typeof prevColumn.default === 'string') {
            if (prevColumn.default.includes('uuid_generate_v4')) {
                prevDefault = 'gen_random_uuid()';
            }
        }

        // Check each property for changes
        const typeChanged = prevType !== currentType;
        const nullableChanged = prevColumn.isNullable !== currentColumn.isNullable;
        const defaultChanged = prevDefault !== currentDefault;
        const enumChanged = JSON.stringify(prevColumn.enum) !== JSON.stringify(currentColumn.enum);
        const lengthChanged = prevColumn.length !== currentColumn.length;
        const precisionChanged = prevColumn.precision !== currentColumn.precision;
        const scaleChanged = prevColumn.scale !== currentColumn.scale;

        // Additional check for unique constraint changes
        const prevUnique = prevColumn.isUnique || prevColumn.unique || false;
        const currentUnique = this.isColumnUnique(currentColumn);
        const uniqueChanged = prevUnique !== currentUnique;

        const hasChanged =
            typeChanged ||
            nullableChanged ||
            defaultChanged ||
            enumChanged ||
            lengthChanged ||
            precisionChanged ||
            scaleChanged ||
            uniqueChanged;

        // Debug logging for false positives
        if (hasChanged) {
            console.log(
                `🔍 Column change detected: ${currentColumn.databaseName || currentColumn.propertyName}`
            );
            if (typeChanged) {
                console.log(`  📝 Type: "${prevType}" → "${currentType}"`);
            }
            if (nullableChanged) {
                console.log(
                    `  📝 Nullable: ${prevColumn.isNullable} → ${currentColumn.isNullable}`
                );
            }
            if (defaultChanged) {
                console.log(`  📝 Default: "${prevDefault}" → "${currentDefault}"`);
            }
            if (enumChanged) {
                console.log(
                    `  📝 Enum: ${JSON.stringify(prevColumn.enum)} → ${JSON.stringify(currentColumn.enum)}`
                );
            }
            if (lengthChanged) {
                console.log(`  📝 Length: ${prevColumn.length} → ${currentColumn.length}`);
            }
            if (precisionChanged) {
                console.log(`  📝 Precision: ${prevColumn.precision} → ${currentColumn.precision}`);
            }
            if (scaleChanged) {
                console.log(`  📝 Scale: ${prevColumn.scale} → ${currentColumn.scale}`);
            }
            if (uniqueChanged) {
                console.log(`  📝 Unique: ${prevUnique} → ${currentUnique}`);
            }
        }

        return hasChanged;
    }

    /**
     * Detects index changes
     */
    private detectIndexChanges(
        prevEntity: any,
        currentEntity: EntityMetadata,
        changes: MigrationChanges
    ): void {
        const tableName = currentEntity.tableName;

        // Convert indices to maps for easier comparison
        const prevIndices = new Map(
            prevEntity.indices?.map((i: any) => [i.name || i.columnNames.join('_'), i]) || []
        );
        const currentIndices = new Map(
            currentEntity.indices.map((i) => [
                i.name || i.columns.map((c) => c.databaseName).join('_'),
                i
            ])
        );

        // Detect new indices
        for (const [key, index] of currentIndices) {
            if (!prevIndices.has(key)) {
                changes.addNewIndex(tableName, index);
            }
        }

        // Detect dropped indices
        for (const [key, index] of prevIndices) {
            if (!currentIndices.has(key as string)) {
                changes.addDroppedIndex(tableName, index);
            }
        }
    }

    /**
     * Detects unique constraint changes
     */
    private detectUniqueConstraintChanges(
        prevEntity: any,
        currentEntity: EntityMetadata,
        changes: MigrationChanges
    ): void {
        const tableName = currentEntity.tableName;

        // Get unique constraints from previous state
        const prevUniqueColumns = new Set<string>();
        if (prevEntity.columns) {
            prevEntity.columns.forEach((col: any) => {
                if (col.isUnique || col.unique) {
                    prevUniqueColumns.add(col.databaseName);
                }
            });
        }

        // Get unique constraints from current state
        const currentUniqueColumns = new Set<string>();
        currentEntity.columns.forEach((column) => {
            if (this.isColumnUnique(column)) {
                currentUniqueColumns.add(column.databaseName);
            }
        });

        // Detect new unique constraints
        for (const columnName of currentUniqueColumns) {
            if (!prevUniqueColumns.has(columnName)) {
                const column = currentEntity.columns.find((c) => c.databaseName === columnName);
                if (column) {
                    changes.addNewUniqueConstraint(tableName, column);
                }
            }
        }

        // Detect dropped unique constraints
        for (const columnName of prevUniqueColumns) {
            if (!currentUniqueColumns.has(columnName)) {
                const prevColumn = prevEntity.columns?.find(
                    (c: any) => c.databaseName === columnName
                );
                if (prevColumn) {
                    changes.addDroppedUniqueConstraint(tableName, prevColumn);
                }
            }
        }
    }

    /**
     * Generates differential migration content based on detected changes
     */
    private generateDifferentialMigrationContent(
        className: string,
        changes: MigrationChanges
    ): string {
        const upStatements: string[] = [];
        const downStatements: string[] = [];

        // Generate enum statements for new tables
        const enumStatements = this.generateEnumStatementsForChanges(changes);
        upStatements.push(...enumStatements);

        // Generate CREATE TABLE statements for new tables
        const createTableStatements = this.generateCreateTableStatements(changes.newTables);
        upStatements.push(...createTableStatements);

        // Generate ADD COLUMN statements
        const addColumnStatements = this.generateAddColumnStatements(changes.newColumns);
        upStatements.push(...addColumnStatements);

        // Generate ALTER COLUMN statements
        const alterColumnStatements = this.generateAlterColumnStatements(changes.modifiedColumns);
        upStatements.push(...alterColumnStatements);

        // Generate CREATE INDEX statements
        const createIndexStatements = this.generateCreateIndexStatements(changes.newIndices);
        upStatements.push(...createIndexStatements);

        // Generate ADD UNIQUE CONSTRAINT statements
        const addUniqueConstraintStatements = this.generateAddUniqueConstraintStatements(
            changes.newUniqueConstraints
        );
        upStatements.push(...addUniqueConstraintStatements);

        // Generate DROP statements for down migration
        this.generateDownStatements(changes, downStatements);

        return `import { MigrationInterface, QueryRunner } from 'typeorm';

export class ${className} implements MigrationInterface {
    name = '${className}';

    public async up(queryRunner: QueryRunner): Promise<void> {
${upStatements.length > 0 ? upStatements.join('\n\n') : '        // No changes to apply'}
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
${downStatements.length > 0 ? downStatements.join('\n\n') : '        // No changes to revert'}
    }
}
`;
    }

    /**
     * Generates enum statements for new tables in changes
     */
    private generateEnumStatementsForChanges(changes: MigrationChanges): string[] {
        const enumTypes = new Map<string, { statement: string; values: string[] }>();

        // Process new tables to extract enum types
        for (const entity of changes.newTables) {
            entity.columns.forEach((column) => {
                if (column.enum) {
                    const enumName =
                        (column as any).enumName ||
                        this.getEnumTypeName(entity.tableName, column.databaseName);
                    const enumValues = Object.values(column.enum).map((val) => `'${val}'`);

                    if (!enumTypes.has(enumName)) {
                        enumTypes.set(enumName, {
                            statement: `        await queryRunner.query(\`CREATE TYPE "${enumName}" AS ENUM(${enumValues.join(', ')})\`);`,
                            values: enumValues
                        });
                    }
                }
            });
        }

        return Array.from(enumTypes.values()).map((e) => e.statement);
    }

    /**
     * Generates CREATE TABLE statements for new tables
     */
    private generateCreateTableStatements(newTables: EntityMetadata[]): string[] {
        const statements: string[] = [];

        // Sort tables by dependencies first
        const sortedTables = this.sortEntitiesByDependencies(newTables);

        for (const entity of sortedTables) {
            const tableStatement = this.generateSingleTableStatement(entity);
            statements.push(tableStatement);

            // Generate non-unique indexes for this table
            const indexStatements = this.generateTableIndexStatements(entity);
            statements.push(...indexStatements);
        }

        // Generate junction tables for many-to-many relationships
        const junctionTableStatements = this.generateJunctionTableStatements(newTables);
        statements.push(...junctionTableStatements);

        return statements;
    }

    /**
     * Generates junction table statements for many-to-many relationships
     */
    private generateJunctionTableStatements(entities: EntityMetadata[]): string[] {
        const statements: string[] = [];
        const processedJunctionTables = new Set<string>();

        for (const entity of entities) {
            for (const relation of entity.relations) {
                if (
                    relation.isManyToMany &&
                    relation.joinTableName &&
                    !processedJunctionTables.has(relation.joinTableName)
                ) {
                    processedJunctionTables.add(relation.joinTableName);

                    const junctionTableStatement = this.generateJunctionTableStatement(relation);
                    if (junctionTableStatement) {
                        statements.push(junctionTableStatement);
                    }
                }
            }
        }

        return statements;
    }

    /**
     * Generates a single junction table statement
     */
    private generateJunctionTableStatement(relation: any): string | null {
        if (!relation.joinTableName || !relation.joinColumns || !relation.inverseJoinColumns) {
            return null;
        }

        const tableName = relation.joinTableName;
        const joinColumn = relation.joinColumns[0];
        const inverseJoinColumn = relation.inverseJoinColumns[0];

        const joinColumnName = joinColumn.databaseName;
        const inverseJoinColumnName = inverseJoinColumn.databaseName;

        const ownerTable = relation.entityMetadata.tableName;
        const inverseTable = relation.inverseEntityMetadata.tableName;

        const joinColumnRef = joinColumn.referencedColumn?.databaseName || 'id';
        const inverseJoinColumnRef = inverseJoinColumn.referencedColumn?.databaseName || 'id';

        return `        await queryRunner.query(\`CREATE TABLE "${tableName}" (
            "${joinColumnName}" uuid NOT NULL,
            "${inverseJoinColumnName}" uuid NOT NULL,
            CONSTRAINT "PK_${tableName}" PRIMARY KEY ("${joinColumnName}", "${inverseJoinColumnName}"),
            CONSTRAINT "FK_${tableName}_${joinColumnName}"
                FOREIGN KEY ("${joinColumnName}")
                REFERENCES "${ownerTable}"("${joinColumnRef}")
                ON DELETE CASCADE,
            CONSTRAINT "FK_${tableName}_${inverseJoinColumnName}"
                FOREIGN KEY ("${inverseJoinColumnName}")
                REFERENCES "${inverseTable}"("${inverseJoinColumnRef}")
                ON DELETE CASCADE
        )\`);`;
    }

    /**
     * Generates ADD COLUMN statements
     */
    private generateAddColumnStatements(newColumns: Map<string, any[]>): string[] {
        const statements: string[] = [];

        for (const [tableName, columns] of newColumns) {
            for (const column of columns) {
                const columnDef = this.generateColumnDefinition(column);
                statements.push(
                    `        await queryRunner.query(\`ALTER TABLE "${tableName}" ADD COLUMN ${columnDef}\`);`
                );
            }
        }

        return statements;
    }

    /**
     * Generates ALTER COLUMN statements
     */
    private generateAlterColumnStatements(
        modifiedColumns: Map<string, Array<{ prev: any; current: any }>>
    ): string[] {
        const statements: string[] = [];

        for (const [tableName, columns] of modifiedColumns) {
            for (const { prev, current } of columns) {
                // Generate appropriate ALTER statements based on what changed
                const alterStatements = this.generateColumnAlterStatements(
                    tableName,
                    prev,
                    current
                );
                statements.push(...alterStatements);
            }
        }

        return statements;
    }

    /**
     * Generates CREATE INDEX statements
     */
    private generateCreateIndexStatements(newIndices: Map<string, any[]>): string[] {
        const statements: string[] = [];

        for (const [tableName, indices] of newIndices) {
            for (const index of indices) {
                const indexStatement = this.generateIndexStatement(tableName, index);
                statements.push(indexStatement);
            }
        }

        return statements;
    }

    /**
     * Generates ADD UNIQUE CONSTRAINT statements
     */
    private generateAddUniqueConstraintStatements(
        newUniqueConstraints: Map<string, any[]>
    ): string[] {
        const statements: string[] = [];

        for (const [tableName, columns] of newUniqueConstraints) {
            for (const column of columns) {
                const constraintName = `UQ_${this.generateHash(tableName + '_' + column.databaseName)}`;
                statements.push(
                    `        await queryRunner.query(\`ALTER TABLE "${tableName}" ADD CONSTRAINT "${constraintName}" UNIQUE ("${column.databaseName}")\`);`
                );
            }
        }

        return statements;
    }

    /**
     * Generates DOWN migration statements
     */
    private generateDownStatements(changes: MigrationChanges, downStatements: string[]): void {
        // Drop new unique constraints
        for (const [tableName, columns] of changes.newUniqueConstraints) {
            for (const column of columns) {
                const constraintName = `UQ_${this.generateHash(tableName + '_' + column.databaseName)}`;
                downStatements.push(
                    `        await queryRunner.query(\`ALTER TABLE "${tableName}" DROP CONSTRAINT "${constraintName}"\`);`
                );
            }
        }

        // Drop new indices
        for (const [tableName, indices] of changes.newIndices) {
            for (const index of indices) {
                const indexName =
                    index.name ||
                    `IDX_${tableName}_${index.columns?.map((c: any) => c.databaseName).join('_')}`;
                downStatements.push(
                    `        await queryRunner.query(\`DROP INDEX "${indexName}"\`);`
                );
            }
        }

        // Drop new columns
        for (const [tableName, columns] of changes.newColumns) {
            for (const column of columns) {
                downStatements.push(
                    `        await queryRunner.query(\`ALTER TABLE "${tableName}" DROP COLUMN "${column.databaseName}"\`);`
                );
            }
        }

        // Drop junction tables first (they have foreign keys to main tables)
        const junctionTablesToDrop = new Set<string>();
        for (const entity of changes.newTables) {
            for (const relation of entity.relations) {
                if (relation.isManyToMany && relation.joinTableName) {
                    junctionTablesToDrop.add(relation.joinTableName);
                }
            }
        }

        for (const junctionTableName of junctionTablesToDrop) {
            downStatements.push(
                `        await queryRunner.query(\`DROP TABLE "${junctionTableName}"\`);`
            );
        }

        // Drop new tables (in reverse dependency order)
        const sortedTables = this.sortEntitiesByDependencies(changes.newTables).reverse();
        for (const entity of sortedTables) {
            downStatements.push(
                `        await queryRunner.query(\`DROP TABLE "${entity.tableName}"\`);`
            );
        }

        // Drop new enum types
        const enumTypes = new Set<string>();
        for (const entity of changes.newTables) {
            entity.columns.forEach((column) => {
                if (column.enum) {
                    const enumName =
                        (column as any).enumName ||
                        this.getEnumTypeName(entity.tableName, column.databaseName);
                    enumTypes.add(enumName);
                }
            });
        }

        for (const enumName of enumTypes) {
            downStatements.push(`        await queryRunner.query(\`DROP TYPE "${enumName}"\`);`);
        }
    }

    /**
     * Generates a single table creation statement
     */
    private generateSingleTableStatement(entity: EntityMetadata): string {
        const columns: string[] = [];
        const foreignKeys: string[] = [];

        // Generate column definitions
        entity.columns.forEach((column) => {
            const columnDef = this.generateColumnDefinition(column);
            columns.push(`            ${columnDef}`);
        });

        // Generate foreign key constraints
        entity.relations.forEach((relation) => {
            if (relation.isManyToOne || relation.isOneToOne) {
                const fkConstraint = this.generateForeignKeyConstraint(entity.tableName, relation);
                if (fkConstraint) {
                    foreignKeys.push(`            ${fkConstraint}`);
                }
            }
        });

        // Generate unique constraints
        const uniqueConstraints = this.generateUniqueConstraints(entity);

        const allConstraints = [...columns, ...foreignKeys, ...uniqueConstraints];

        return `        await queryRunner.query(\`CREATE TABLE "${entity.tableName}" (
${allConstraints.join(',\n')}
        )\`);`;
    }

    /**
     * Generates column definition for SQL
     */
    private generateColumnDefinition(column: any): string {
        let definition = `"${column.databaseName}" `;

        // Handle enum types
        if (column.enum) {
            const enumName =
                column.enumName ||
                this.getEnumTypeName(
                    column.entityMetadata?.tableName || 'unknown',
                    column.databaseName
                );
            definition += `"${enumName}"`;
        } else {
            // Map TypeORM types to PostgreSQL types
            definition += this.mapTypeOrmTypeToPostgres(column);
        }

        // Add constraints
        if (column.isPrimary) {
            definition += ' NOT NULL';
            // Handle UUID primary key generation
            if (column.isGenerated && column.generationStrategy === 'uuid') {
                definition += ' DEFAULT gen_random_uuid()';
            }
            definition += ' PRIMARY KEY';
        } else if (!column.isNullable) {
            definition += ' NOT NULL';
        }

        // Handle special TypeORM decorators for timestamps first
        if (column.isCreateDate || column.isUpdateDate) {
            definition += ' DEFAULT now()';
        } else if (column.default !== undefined && column.default !== null && !column.isPrimary) {
            // Handle other default values (but not for primary keys as they're handled above)
            const defaultValue = this.formatDefaultValue(column);
            if (defaultValue) {
                definition += ` DEFAULT ${defaultValue}`;
            }
        }

        return definition;
    }

    /**
     * Generates column alter statements
     */
    private generateColumnAlterStatements(
        tableName: string,
        prevColumn: any,
        currentColumn: any
    ): string[] {
        const statements: string[] = [];
        const columnName = currentColumn.databaseName;

        // Type change
        if (prevColumn.type !== currentColumn.type) {
            const newType = this.mapTypeOrmTypeToPostgres(currentColumn);
            statements.push(
                `        await queryRunner.query(\`ALTER TABLE "${tableName}" ALTER COLUMN "${columnName}" TYPE ${newType}\`);`
            );
        }

        // Nullable change
        if (prevColumn.isNullable !== currentColumn.isNullable) {
            const constraint = currentColumn.isNullable ? 'DROP NOT NULL' : 'SET NOT NULL';
            statements.push(
                `        await queryRunner.query(\`ALTER TABLE "${tableName}" ALTER COLUMN "${columnName}" ${constraint}\`);`
            );
        }

        // Default value change
        if (prevColumn.default !== currentColumn.default) {
            if (currentColumn.default !== undefined && currentColumn.default !== null) {
                const defaultValue = this.formatDefaultValue(currentColumn);
                if (defaultValue) {
                    statements.push(
                        `        await queryRunner.query(\`ALTER TABLE "${tableName}" ALTER COLUMN "${columnName}" SET DEFAULT ${defaultValue}\`);`
                    );
                }
            } else {
                statements.push(
                    `        await queryRunner.query(\`ALTER TABLE "${tableName}" ALTER COLUMN "${columnName}" DROP DEFAULT\`);`
                );
            }
        }

        return statements;
    }

    /**
     * Generates index creation statement
     */
    private generateIndexStatement(tableName: string, index: any): string {
        const indexName =
            index.name ||
            `IDX_${tableName}_${index.columns?.map((c: any) => c.databaseName).join('_')}`;
        const columnNames = index.columns?.map((c: any) => `"${c.databaseName}"`).join(', ') || '';
        const unique = index.isUnique ? 'UNIQUE ' : '';

        return `        await queryRunner.query(\`CREATE ${unique}INDEX "${indexName}" ON "${tableName}" (${columnNames})\`);`;
    }

    /**
     * Generates index statements for a table (non-unique indexes only, unique are handled as constraints)
     */
    private generateTableIndexStatements(entity: EntityMetadata): string[] {
        const statements: string[] = [];

        // Generate indexes for non-unique indices
        entity.indices.forEach((index) => {
            if (!index.isUnique) {
                // Non-unique indexes
                const columnNames = index.columns.map((col) => `"${col.databaseName}"`).join(', ');
                const indexName =
                    index.name ||
                    `IDX_${entity.tableName}_${index.columns.map((col) => col.databaseName).join('_')}`;
                statements.push(
                    `        await queryRunner.query(\`CREATE INDEX "${indexName}" ON "${entity.tableName}" (${columnNames})\`);`
                );
            }
        });

        return statements;
    }

    /**
     * Generates foreign key constraint
     */
    private generateForeignKeyConstraint(tableName: string, relation: any): string | null {
        if (!relation.joinColumns || relation.joinColumns.length === 0) {
            return null;
        }

        const joinColumn = relation.joinColumns[0];
        const constraintName = `FK_${this.generateHash(tableName + '_' + joinColumn.databaseName)}`;
        const referencedTable = relation.inverseEntityMetadata.tableName;
        const referencedColumn = joinColumn.referencedColumn?.databaseName || 'id';

        return `CONSTRAINT "${constraintName}" FOREIGN KEY ("${joinColumn.databaseName}") REFERENCES "${referencedTable}"("${referencedColumn}")`;
    }

    /**
     * Generates unique constraints (enhanced with legacy logic)
     */
    private generateUniqueConstraints(entity: EntityMetadata): string[] {
        const constraints: string[] = [];

        // Unique constraints from indices (multi-column unique constraints)
        const uniqueConstraints = entity.indices
            .filter((index) => index.isUnique && !index.columns.every((col) => col.isPrimary))
            .map((index) => {
                const columnNames = index.columns.map((col) => `"${col.databaseName}"`).join(', ');
                // Generate TypeORM-style constraint name (hash-based)
                const constraintName = `UQ_${this.generateHash(
                    entity.tableName + '_' + index.columns.map((col) => col.databaseName).join('_')
                )}`;
                return `            CONSTRAINT "${constraintName}" UNIQUE (${columnNames})`;
            });

        // Single-column unique constraints
        const uniqueColumns: string[] = [];
        entity.columns.forEach((column) => {
            if (this.isColumnUnique(column) && !column.isPrimary) {
                const constraintName = `UQ_${this.generateHash(entity.tableName + '_' + column.databaseName)}`;
                uniqueColumns.push(
                    `            CONSTRAINT "${constraintName}" UNIQUE ("${column.databaseName}")`
                );
            }
        });

        constraints.push(...uniqueConstraints, ...uniqueColumns);
        return constraints;
    }

    /**
     * Canonical type normalization function - used for both serialization and comparison
     * This ensures consistent type representation across all operations
     */
    private normalizeColumnType(column: any, tableName?: string): string {
        // Handle enum types - check both enum property and type === 'enum'
        if (column.enum || column.type === 'enum') {
            const enumName =
                column.enumName ||
                this.getEnumTypeName(
                    tableName || column.entityMetadata?.tableName || 'table',
                    column.databaseName
                );
            return enumName; // Return just the enum name without quotes for consistency
        }

        // Infer type if not explicitly set (same logic as serialization)
        let columnType = column.type;
        if (!columnType) {
            if (column.enum) {
                columnType = 'enum';
            } else if (
                typeof column.default === 'boolean' ||
                column.databaseName.includes('is_') ||
                column.databaseName.includes('enabled')
            ) {
                columnType = 'boolean';
            } else if (typeof column.default === 'number') {
                columnType = 'integer';
            } else {
                columnType = 'varchar'; // Default fallback
            }
        }

        // Normalize to consistent PostgreSQL types (lowercase for consistency)
        switch (columnType) {
            case 'uuid':
                return 'uuid';
            case 'varchar':
            case String:
                return 'character varying';
            case 'text':
                return 'text';
            case 'boolean':
            case Boolean:
                return 'boolean';
            case 'int':
            case 'integer':
            case Number:
                return 'integer';
            case 'bigint':
                return 'bigint';
            case 'decimal':
            case 'numeric':
                return 'numeric';
            case 'timestamp':
            case 'datetime':
            case Date:
                return 'timestamp';
            case 'date':
                return 'date';
            case 'time':
                return 'time';
            case 'jsonb':
                return 'jsonb';
            case 'json':
                return 'json';
            case 'float':
            case 'double':
                return 'double precision';
            case 'real':
                return 'real';
            default:
                // If it's already an enum name (like 'task_status'), return as-is
                if (typeof columnType === 'string' && columnType.includes('_')) {
                    return columnType;
                }
                // Fallback for unknown types
                return 'character varying';
        }
    }

    /**
     * Maps TypeORM types to PostgreSQL types for SQL generation (uppercase for SQL)
     */
    private mapTypeOrmTypeToPostgres(column: any): string {
        const normalizedType = this.normalizeColumnType(column);

        // Convert to uppercase for SQL generation
        switch (normalizedType) {
            case 'uuid':
                return 'UUID';
            case 'character varying':
                return column.length ? `CHARACTER VARYING(${column.length})` : 'CHARACTER VARYING';
            case 'text':
                return 'TEXT';
            case 'boolean':
                return 'BOOLEAN';
            case 'integer':
                return 'INTEGER';
            case 'bigint':
                return 'BIGINT';
            case 'numeric':
                return column.precision && column.scale
                    ? `NUMERIC(${column.precision},${column.scale})`
                    : 'NUMERIC';
            case 'timestamp':
                return 'TIMESTAMP';
            case 'date':
                return 'DATE';
            case 'time':
                return 'TIME';
            case 'jsonb':
                return 'JSONB';
            case 'json':
                return 'JSON';
            case 'double precision':
                return 'DOUBLE PRECISION';
            case 'real':
                return 'REAL';
            default:
                // Handle enum types
                if (column.enum) {
                    const enumName =
                        column.enumName ||
                        this.getEnumTypeName(
                            column.entityMetadata?.tableName || 'unknown',
                            column.databaseName
                        );
                    return `"${enumName}"`;
                }
                return 'CHARACTER VARYING';
        }
    }

    /**
     * Generates a hash for constraint names
     */
    private generateHash(input: string): string {
        let hash = 0;
        for (let i = 0; i < input.length; i++) {
            const char = input.charCodeAt(i);
            hash = (hash << 5) - hash + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16).substring(0, 8);
    }

    /**
     * Checks if a column is unique (comprehensive approach from legacy)
     */
    private isColumnUnique(column: any): boolean {
        // Approach 1: Check standard properties
        let isUnique =
            column.isUnique || column.unique || (column.options && column.options.unique);

        // Approach 2: Check if this column appears in a single-column unique index
        if (!isUnique && column.entityMetadata?.indices) {
            const singleColumnUniqueIndex = column.entityMetadata.indices.find(
                (index: any) =>
                    index.isUnique &&
                    index.columns.length === 1 &&
                    index.columns[0].databaseName === column.databaseName
            );
            isUnique = !!singleColumnUniqueIndex;
        }

        // Approach 3: Access TypeORM's internal column metadata
        if (
            !isUnique &&
            column.entityMetadata?.target &&
            typeof column.entityMetadata.target === 'function'
        ) {
            try {
                // Get the column metadata from TypeORM's metadata storage
                const columnMetadata =
                    Reflect.getMetadata('typeorm:columns', column.entityMetadata.target) || [];
                const matchingColumn = columnMetadata.find(
                    (meta: any) =>
                        (meta.options?.name || meta.propertyName) === column.databaseName ||
                        meta.propertyName === column.propertyName
                );

                if (matchingColumn && matchingColumn.options) {
                    isUnique = matchingColumn.options.unique === true;
                }
            } catch (error) {
                // Ignore reflection errors
            }
        }

        // Approach 4: Hardcoded known unique columns (fallback)
        if (!isUnique && column.entityMetadata?.tableName) {
            const knownUniqueColumns = [
                { table: 'cases', column: 'case_number' },
                { table: 'system_users', column: 'email' },
                { table: 'tenants', column: 'realm' },
                { table: 'tenants', column: 'admin_email' },
                { table: 'products', column: 'sku' },
                { table: 'system_roles', column: 'name' },
                { table: 'tenant_roles', column: 'name' }
            ];

            isUnique = knownUniqueColumns.some(
                (known) =>
                    known.table === column.entityMetadata.tableName &&
                    known.column === column.databaseName
            );
        }

        return isUnique;
    }

    /**
     * Sorts entities by foreign key dependencies (referenced tables first)
     */
    private sortEntitiesByDependencies(entities: EntityMetadata[]): EntityMetadata[] {
        const entityMap = new Map<string, EntityMetadata>();
        const dependencies = new Map<string, Set<string>>();
        const visited = new Set<string>();
        const visiting = new Set<string>();
        const sorted: EntityMetadata[] = [];

        // Build entity map and dependency graph
        entities.forEach((entity) => {
            entityMap.set(entity.tableName, entity);
            dependencies.set(entity.tableName, new Set());
        });

        // Analyze foreign key dependencies
        entities.forEach((entity) => {
            entity.relations.forEach((relation) => {
                if (relation.isManyToOne || relation.isOneToOne) {
                    const referencedTable = relation.inverseEntityMetadata.tableName;
                    if (referencedTable !== entity.tableName) {
                        // Avoid self-references
                        dependencies.get(entity.tableName)?.add(referencedTable);
                    }
                }
            });
        });

        // Topological sort using DFS
        const visit = (tableName: string): void => {
            if (visiting.has(tableName)) {
                // Circular dependency detected - log warning but continue
                console.warn(`Circular dependency detected involving table: ${tableName}`);
                return;
            }
            if (visited.has(tableName)) {
                return;
            }

            visiting.add(tableName);

            // Visit all dependencies first
            const deps = dependencies.get(tableName) || new Set();
            deps.forEach((depTable) => {
                if (entityMap.has(depTable)) {
                    visit(depTable);
                }
            });

            visiting.delete(tableName);
            visited.add(tableName);

            const entity = entityMap.get(tableName);
            if (entity) {
                sorted.push(entity);
            }
        };

        // Visit all entities
        entities.forEach((entity) => {
            if (!visited.has(entity.tableName)) {
                visit(entity.tableName);
            }
        });

        console.log('Table creation order:', sorted.map((e) => e.tableName).join(' -> '));
        return sorted;
    }

    /**
     * Generates migration content based on entity metadata (LEGACY - keeping for fallback)
     */
    private generateMigrationContentLegacy(className: string, entities: EntityMetadata[]): string {
        const enumStatements: string[] = [];
        const tableStatements: string[] = [];
        const indexStatements: string[] = [];

        // Sort entities by dependencies first
        const sortedEntities = this.sortEntitiesByDependencies(entities);

        // First pass: collect all enum types (avoid duplicates)
        const enumTypes = new Map<string, { statement: string; values: string[] }>();
        sortedEntities.forEach((entity) => {
            entity.columns.forEach((column) => {
                if (column.enum) {
                    // Check if column has a custom enumName, otherwise use table_column format
                    const enumName =
                        (column as any).enumName ||
                        this.getEnumTypeName(entity.tableName, column.databaseName);
                    const enumValues = Object.values(column.enum).map((val) => `'${val}'`);

                    // Only add if we haven't seen this enum name before, or if the values are the same
                    if (!enumTypes.has(enumName)) {
                        enumTypes.set(enumName, {
                            statement: `        await queryRunner.query(\`CREATE TYPE "${enumName}" AS ENUM(${enumValues.join(', ')})\`);`,
                            values: enumValues
                        });
                    } else {
                        // Verify that the enum values are the same if the name is reused
                        const existing = enumTypes.get(enumName)!;
                        const existingValues = existing.values.sort();
                        const currentValues = enumValues.sort();

                        if (JSON.stringify(existingValues) !== JSON.stringify(currentValues)) {
                            console.warn(
                                `Enum name conflict: "${enumName}" has different values in different entities`
                            );
                            console.warn(`Existing: [${existingValues.join(', ')}]`);
                            console.warn(`Current: [${currentValues.join(', ')}]`);
                        }
                    }
                }
            });
        });
        enumStatements.push(...Array.from(enumTypes.values()).map((e) => e.statement));

        // Second pass: create tables (using sorted entities)
        sortedEntities.forEach((entity) => {
            const tableName = entity.tableName;
            const columns = entity.columns
                .map((column) => {
                    const pgType = this.getPostgreSQLType(column, entity.tableName);
                    let columnDef = `"${column.databaseName}" ${pgType}`;

                    if (column.isPrimary) {
                        columnDef += ' NOT NULL';
                        if (column.isGenerated && column.generationStrategy === 'uuid') {
                            columnDef += ' DEFAULT gen_random_uuid()';
                        }
                    } else if (column.isNullable === false) {
                        columnDef += ' NOT NULL';
                    }

                    if (
                        column.default !== undefined &&
                        column.default !== null &&
                        !column.isPrimary
                    ) {
                        const defaultValue = this.formatDefaultValue(column);
                        if (defaultValue) {
                            columnDef += ` DEFAULT ${defaultValue}`;
                        }
                    }

                    return `                ${columnDef}`;
                })
                .join(',\n');

            // Primary key constraint
            const primaryKeys = entity.columns
                .filter((col) => col.isPrimary)
                .map((col) => `"${col.databaseName}"`)
                .join(', ');

            // Unique constraints from indices
            const uniqueConstraints = entity.indices
                .filter((index) => index.isUnique && !index.columns.every((col) => col.isPrimary))
                .map((index) => {
                    const columnNames = index.columns
                        .map((col) => `"${col.databaseName}"`)
                        .join(', ');
                    // Generate TypeORM-style constraint name (hash-based)
                    const constraintName = this.generateConstraintName(
                        'UQ',
                        tableName,
                        index.columns.map((col) => col.databaseName)
                    );
                    return `                CONSTRAINT "${constraintName}" UNIQUE (${columnNames})`;
                });

            // Check for unique columns by examining the column metadata more thoroughly
            const uniqueColumns: string[] = [];
            entity.columns.forEach((column) => {
                // Try multiple approaches to detect unique constraints
                let isUnique = false;

                // Approach 1: Check standard properties
                isUnique =
                    (column as any).isUnique ||
                    (column as any).unique ||
                    ((column as any).options && (column as any).options.unique);

                // Approach 2: Check if this column appears in a single-column unique index
                if (!isUnique) {
                    const singleColumnUniqueIndex = entity.indices.find(
                        (index) =>
                            index.isUnique &&
                            index.columns.length === 1 &&
                            index.columns[0].databaseName === column.databaseName
                    );
                    isUnique = !!singleColumnUniqueIndex;
                }

                // Approach 3: Access TypeORM's internal column metadata
                if (!isUnique && entity.target && typeof entity.target === 'function') {
                    try {
                        // Get the column metadata from TypeORM's metadata storage
                        const columnMetadata =
                            Reflect.getMetadata('typeorm:columns', entity.target) || [];
                        const matchingColumn = columnMetadata.find(
                            (meta: any) =>
                                (meta.options?.name || meta.propertyName) === column.databaseName ||
                                meta.propertyName === column.propertyName
                        );

                        if (matchingColumn && matchingColumn.options) {
                            isUnique = matchingColumn.options.unique === true;
                        }
                    } catch (error) {
                        // Ignore reflection errors
                    }
                }

                // Approach 4: Hardcoded known unique columns (fallback)
                if (!isUnique) {
                    const knownUniqueColumns = [
                        { table: 'cases', column: 'case_number' },
                        { table: 'system_users', column: 'email' },
                        { table: 'tenants', column: 'realm' },
                        { table: 'tenants', column: 'admin_email' },
                        { table: 'products', column: 'sku' },
                        { table: 'system_roles', column: 'name' },
                        { table: 'tenant_roles', column: 'name' }
                    ];

                    isUnique = knownUniqueColumns.some(
                        (known) => known.table === tableName && known.column === column.databaseName
                    );
                }

                if (isUnique && !column.isPrimary) {
                    const constraintName = this.generateConstraintName('UQ', tableName, [
                        column.databaseName
                    ]);
                    uniqueColumns.push(
                        `                CONSTRAINT "${constraintName}" UNIQUE ("${column.databaseName}")`
                    );
                }
            });

            const allUniqueConstraints = [...uniqueConstraints, ...uniqueColumns];

            // Include foreign keys inline with table creation (TypeORM style)
            const inlineForeignKeys = entity.relations
                .filter((relation) => relation.isManyToOne || relation.isOneToOne)
                .map((relation) => {
                    const joinColumn = relation.joinColumns[0];
                    if (joinColumn) {
                        const constraintName = this.generateConstraintName('FK', tableName, [
                            joinColumn.databaseName
                        ]);
                        const referencedTable = relation.inverseEntityMetadata.tableName;
                        const referencedColumn = joinColumn.referencedColumn?.databaseName || 'id';

                        return `                CONSTRAINT "${constraintName}" FOREIGN KEY ("${joinColumn.databaseName}") REFERENCES "${referencedTable}"("${referencedColumn}") ON DELETE ${relation.onDelete || 'NO ACTION'} ON UPDATE ${relation.onUpdate || 'NO ACTION'}`;
                    }
                    return null;
                })
                .filter(Boolean);

            const constraints = [
                primaryKeys
                    ? `                CONSTRAINT "PK_${tableName}" PRIMARY KEY (${primaryKeys})`
                    : '',
                ...allUniqueConstraints,
                ...inlineForeignKeys
            ]
                .filter(Boolean)
                .join(',\n');

            tableStatements.push(`        await queryRunner.query(\`
            CREATE TABLE "${tableName}" (
${columns}${constraints ? `,\n${constraints}` : ''}
            )
        \`);`);

            // Collect index statements
            entity.indices.forEach((index) => {
                if (!index.isUnique) {
                    // Non-unique indexes
                    const columnNames = index.columns
                        .map((col) => `"${col.databaseName}"`)
                        .join(', ');
                    const indexName =
                        index.name ||
                        `IDX_${tableName}_${index.columns.map((col) => col.databaseName).join('_')}`;
                    indexStatements.push(
                        `        await queryRunner.query(\`CREATE INDEX "${indexName}" ON "${tableName}" (${columnNames})\`);`
                    );
                }
            });
        });

        const allStatements = [...enumStatements, ...tableStatements, ...indexStatements].filter(
            Boolean
        );

        return `import { MigrationInterface, QueryRunner } from 'typeorm';

export class ${className} implements MigrationInterface {
    name = '${className}';

    public async up(queryRunner: QueryRunner): Promise<void> {
${allStatements.join('\n\n')}
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop tables in reverse order (foreign keys are dropped automatically)
${sortedEntities
    .reverse()
    .map((entity) => `        await queryRunner.query(\`DROP TABLE "${entity.tableName}"\`);`)
    .join('\n')}

        // Drop enum types (avoid duplicates)
${Array.from(
    new Set(
        sortedEntities.flatMap((entity) =>
            entity.columns
                .filter((column) => column.enum)
                .map((column) => {
                    const enumName =
                        (column as any).enumName ||
                        this.getEnumTypeName(entity.tableName, column.databaseName);
                    return `        await queryRunner.query(\`DROP TYPE "${enumName}"\`);`;
                })
        )
    )
).join('\n')}
    }
}
`;
    }

    /**
     * Generates enum type name for PostgreSQL
     */
    private getEnumTypeName(tableName: string, columnName: string): string {
        return `${tableName}_${columnName}_enum`;
    }

    /**
     * Generates TypeORM-style constraint names (simplified hash-based approach)
     */
    private generateConstraintName(type: string, tableName: string, columnNames: string[]): string {
        // Simple hash function to mimic TypeORM's constraint naming
        const input = `${tableName}_${columnNames.join('_')}`;
        let hash = 0;
        for (let i = 0; i < input.length; i++) {
            const char = input.charCodeAt(i);
            hash = (hash << 5) - hash + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        const hashStr = Math.abs(hash).toString(16).substring(0, 8);

        switch (type) {
            case 'UQ':
                return `UQ_${hashStr}`;
            case 'FK':
                return `FK_${hashStr}`;
            case 'PK':
                return `PK_${tableName}`;
            case 'IDX':
                return `IDX_${hashStr}`;
            default:
                return `${type}_${hashStr}`;
        }
    }

    /**
     * Converts TypeORM column metadata to PostgreSQL column type
     */
    private getPostgreSQLType(column: any, tableName?: string): string {
        // Handle enum types
        if (column.enum) {
            const enumName =
                column.enumName || this.getEnumTypeName(tableName || 'table', column.databaseName);
            return `"${enumName}"`;
        }

        // Handle enum type by checking the column type
        if (column.type === 'enum' && tableName) {
            const enumName =
                column.enumName || this.getEnumTypeName(tableName, column.databaseName);
            return `"${enumName}"`;
        }

        // Handle specific TypeORM types
        switch (column.type) {
            case 'uuid':
                return 'uuid';
            case 'varchar':
            case String:
                return column.length ? `character varying(${column.length})` : 'character varying';
            case 'text':
                return 'text';
            case 'boolean':
            case Boolean:
                return 'boolean';
            case 'int':
            case 'integer':
            case Number:
                return 'integer';
            case 'bigint':
                return 'bigint';
            case 'decimal':
            case 'numeric':
                return `numeric${column.precision ? `(${column.precision}${column.scale ? `,${column.scale}` : ''})` : ''}`;
            case 'timestamp':
            case 'datetime':
            case Date:
                return 'TIMESTAMP';
            case 'date':
                return 'date';
            case 'time':
                return 'time';
            case 'jsonb':
                return 'jsonb';
            case 'json':
                return 'json';
            case 'float':
            case 'double':
                return 'double precision';
            case 'real':
                return 'real';
            default:
                // Fallback: try to get string representation
                if (typeof column.type === 'string') {
                    return column.type.toLowerCase();
                }
                // Default to VARCHAR for unknown types
                console.warn(
                    `Unknown column type for ${column.databaseName}: ${column.type}, defaulting to character varying`
                );
                return 'character varying';
        }
    }

    /**
     * Formats default values for PostgreSQL
     */
    private formatDefaultValue(column: any): string | null {
        if (column.default === undefined || column.default === null) {
            return null;
        }

        const defaultValue = column.default;

        // Handle special TypeORM decorators first
        if (column.isCreateDate || column.isUpdateDate) {
            return 'now()';
        }

        // Handle function defaults (TypeORM uses () => 'function_name' format)
        if (typeof defaultValue === 'function') {
            const funcStr = defaultValue.toString();
            if (funcStr.includes('CURRENT_TIMESTAMP') || funcStr.includes('now()')) {
                return 'now()';
            }
            if (funcStr.includes('gen_random_uuid')) {
                return 'gen_random_uuid()';
            }
            // Convert uuid_generate_v4 to gen_random_uuid (modern PostgreSQL standard)
            if (funcStr.includes('uuid_generate_v4')) {
                console.log(
                    `Converting uuid_generate_v4() to gen_random_uuid() for column ${column.databaseName}`
                );
                return 'gen_random_uuid()';
            }
            if (funcStr.includes("'{}'")) {
                return "'{}'";
            }
            // Try to extract the return value from the function
            const match = funcStr.match(/return\s+['"`]([^'"`]+)['"`]/);
            if (match) {
                return `'${match[1]}'`;
            }
            // If we can't parse the function, return null to avoid invalid SQL
            console.warn(
                `Could not parse function default for column ${column.databaseName}: ${funcStr}`
            );
            return null;
        }

        // Handle string defaults
        if (typeof defaultValue === 'string') {
            // Check for SQL functions
            if (
                defaultValue === 'CURRENT_TIMESTAMP' ||
                defaultValue === 'NOW()' ||
                defaultValue === 'now()'
            ) {
                return 'now()';
            }
            if (defaultValue.includes('gen_random_uuid')) {
                return 'gen_random_uuid()';
            }
            // Convert uuid_generate_v4 to gen_random_uuid (modern PostgreSQL standard)
            if (defaultValue.includes('uuid_generate_v4')) {
                console.log(
                    `Converting uuid_generate_v4() to gen_random_uuid() for column ${column.databaseName}`
                );
                return 'gen_random_uuid()';
            }
            // Quote string literals
            return `'${defaultValue.replace(/'/g, "''")}'`; // Escape single quotes
        }

        // Handle boolean defaults
        if (typeof defaultValue === 'boolean') {
            return defaultValue.toString();
        }

        // Handle numeric defaults
        if (typeof defaultValue === 'number') {
            return defaultValue.toString();
        }

        // Handle object defaults (for JSONB)
        if (typeof defaultValue === 'object') {
            return `'${JSON.stringify(defaultValue).replace(/'/g, "''")}'`;
        }

        return defaultValue.toString();
    }

    /**
     * Checks if a schema exists
     */
    async schemaExists(schemaName: string): Promise<boolean> {
        const result = await this.dataSource.query(
            'SELECT schema_name FROM information_schema.schemata WHERE schema_name = $1',
            [schemaName]
        );
        return result.length > 0;
    }
}
