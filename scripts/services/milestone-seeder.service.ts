import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';

interface MilestoneTaskConfig {
    title: string;
    description: string;
    order: number;
    priority: string;
    estimatedDays: number;
    required: boolean;
}

interface MilestoneConfig {
    name: string;
    description: string;
    order: number;
    targetDays?: number;
    tasks: MilestoneTaskConfig[];
}

interface MilestoneSeederConfig {
    caseType: string;
    milestones: MilestoneConfig[];
}

@Injectable()
export class MilestoneSeederService {
    private readonly logger = new Logger(MilestoneSeederService.name);

    /**
     * Seed milestones for a specific tenant
     */
    async seedMilestonesForTenant(tenantDataSource: DataSource, tenantId: string): Promise<void> {
        const queryRunner = tenantDataSource.createQueryRunner();

        try {
            await queryRunner.connect();
            await queryRunner.startTransaction();

            this.logger.log(`Seeding milestones for tenant: ${tenantId}`);

            // Load configuration from JSON file
            this.logger.log('Loading milestone configuration...');
            const config = this.loadMilestoneConfig();
            this.logger.log(
                `Loaded ${config.milestones.length} milestones for case type: ${config.caseType}`
            );

            // Clear existing default milestones (for re-seeding) - only if tables exist
            this.logger.log('Checking if milestone tables exist...');
            const tablesExist = await this.checkTablesExist(queryRunner);

            if (tablesExist) {
                this.logger.log('Clearing existing milestone data...');
                await queryRunner.query('DELETE FROM default_milestone_tasks');
                await queryRunner.query('DELETE FROM default_milestones');
                this.logger.log('Cleared existing milestone data');
            } else {
                this.logger.log('Milestone tables do not exist yet, skipping cleanup');
            }

            // Insert milestones and tasks
            this.logger.log('Inserting milestones and tasks...');
            for (const milestoneConfig of config.milestones) {
                this.logger.log(`Inserting milestone: ${milestoneConfig.name}`);
                const milestoneId = await this.insertMilestone(
                    queryRunner,
                    milestoneConfig,
                    config.caseType
                );
                this.logger.log(
                    `Inserted milestone with ID: ${milestoneId}, inserting ${milestoneConfig.tasks.length} tasks`
                );
                await this.insertTasks(queryRunner, milestoneId, milestoneConfig.tasks);
                this.logger.log(`Completed milestone: ${milestoneConfig.name}`);
            }

            await queryRunner.commitTransaction();

            this.logger.log(
                `Successfully seeded ${config.milestones.length} milestones for tenant: ${tenantId}`
            );
        } catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`Error seeding milestones for tenant ${tenantId}:`, {
                message: error.message,
                stack: error.stack,
                code: error.code,
                detail: error.detail,
                hint: error.hint,
                position: error.position,
                internalPosition: error.internalPosition,
                internalQuery: error.internalQuery,
                where: error.where,
                schema: error.schema,
                table: error.table,
                column: error.column,
                dataType: error.dataType,
                constraint: error.constraint
            });
            throw error;
        } finally {
            await queryRunner.release();
        }
    }

    /**
     * Load milestone configuration from JSON file
     */
    private loadMilestoneConfig(): MilestoneSeederConfig {
        // Try multiple possible paths to handle both development and production environments
        const possiblePaths = [
            path.join(__dirname, '../data/conveyancing-milestones.json'), // From compiled scripts/services
            path.join(__dirname, '../../data/conveyancing-milestones.json'), // Alternative
            path.join(process.cwd(), 'scripts/data/conveyancing-milestones.json'), // From project root
            path.join(process.cwd(), 'dist/scripts/data/conveyancing-milestones.json') // Compiled version
        ];

        let configPath: string | null = null;
        for (const testPath of possiblePaths) {
            this.logger.log(`Checking path: ${testPath}`);
            if (fs.existsSync(testPath)) {
                configPath = testPath;
                this.logger.log(`Found config file at: ${configPath}`);
                break;
            }
        }

        if (!configPath) {
            const searchedPaths = possiblePaths.join('\n  - ');
            throw new Error(
                `Milestone configuration file not found. Searched paths:\n  - ${searchedPaths}`
            );
        }

        const configContent = fs.readFileSync(configPath, 'utf8');
        const config = JSON.parse(configContent);
        this.logger.log(
            `Successfully loaded config with ${config.milestones?.length || 0} milestones`
        );
        return config;
    }

    /**
     * Insert a milestone into the database
     */
    private async insertMilestone(
        queryRunner: any,
        config: MilestoneConfig,
        caseType: string
    ): Promise<string> {
        const result = await queryRunner.query(
            `
            INSERT INTO default_milestones (name, description, order_index, case_type, target_days)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id
        `,
            [config.name, config.description, config.order, caseType, config.targetDays || null]
        );

        return result[0].id;
    }

    /**
     * Insert tasks for a milestone
     */
    private async insertTasks(
        queryRunner: any,
        milestoneId: string,
        tasks: MilestoneTaskConfig[]
    ): Promise<void> {
        for (const task of tasks) {
            await queryRunner.query(
                `
                INSERT INTO default_milestone_tasks 
                (milestone_id, title, description, order_index, priority, estimated_days, is_required)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
            `,
                [
                    milestoneId,
                    task.title,
                    task.description,
                    task.order,
                    task.priority,
                    task.estimatedDays,
                    task.required
                ]
            );
        }
    }

    /**
     * Check if milestones are already seeded for a tenant
     */
    async areMilestonesSeeded(tenantDataSource: DataSource): Promise<boolean> {
        try {
            const result = await tenantDataSource.query(
                'SELECT COUNT(*) as count FROM default_milestones WHERE case_type = $1',
                ['CONVEYANCING']
            );
            return parseInt(result[0].count) > 0;
        } catch {
            // If table doesn't exist, milestones are not seeded
            return false;
        }
    }

    /**
     * Get seeded milestones for a case type
     */
    async getSeededMilestones(tenantDataSource: DataSource, caseType: string): Promise<any[]> {
        const milestones = await tenantDataSource.query(
            `
            SELECT 
                m.id,
                m.name,
                m.description,
                m.order_index as "sortOrder",
                m.target_days as "targetDays",
                json_agg(
                    json_build_object(
                        'title', t.title,
                        'description', t.description,
                        'priority', t.priority,
                        'estimatedDays', t.estimated_days,
                        'isDefault', t.is_required
                    ) ORDER BY t.order_index
                ) as tasks
            FROM default_milestones m
            LEFT JOIN default_milestone_tasks t ON m.id = t.milestone_id
            WHERE m.case_type = $1
            GROUP BY m.id, m.name, m.description, m.order_index, m.target_days
            ORDER BY m.order_index
        `,
            [caseType]
        );

        return milestones;
    }

    /**
     * Check if milestone tables exist in the current schema
     */
    private async checkTablesExist(queryRunner: any): Promise<boolean> {
        try {
            const result = await queryRunner.query(`
                SELECT COUNT(*) as count
                FROM information_schema.tables
                WHERE table_name IN ('default_milestones', 'default_milestone_tasks')
                AND table_schema = current_schema()
            `);

            const tableCount = parseInt(result[0].count);
            this.logger.log(`Found ${tableCount} milestone tables in current schema`);
            return tableCount === 2;
        } catch (error) {
            this.logger.warn('Error checking if tables exist:', error.message);
            return false;
        }
    }
}
