{"caseType": "CONVEYANCING", "milestones": [{"name": "Initial Review & Documentation", "description": "Review client requirements and initial property documentation", "order": 1, "targetDays": 3, "tasks": [{"title": "Review client questionnaire responses", "description": "Analyze client responses about property transaction details", "order": 1, "priority": "HIGH", "estimatedDays": 1, "required": true}, {"title": "Verify property details", "description": "Confirm property address, title, and basic details", "order": 2, "priority": "HIGH", "estimatedDays": 1, "required": true}, {"title": "Prepare client documentation package", "description": "Prepare initial documents and requirements for client", "order": 3, "priority": "HIGH", "estimatedDays": 1, "required": true}, {"title": "Open case file and create matter", "description": "Set up internal case management and file structure", "order": 4, "priority": "MEDIUM", "estimatedDays": 1, "required": true}]}, {"name": "Property Searches & Enquiries", "description": "Conduct necessary property searches and raise enquiries", "order": 2, "targetDays": 10, "tasks": [{"title": "Order local authority search", "description": "Request local authority search from relevant council", "order": 1, "priority": "HIGH", "estimatedDays": 1, "required": true}, {"title": "Order environmental search", "description": "Request environmental and contamination search", "order": 2, "priority": "MEDIUM", "estimatedDays": 1, "required": true}, {"title": "Order water and drainage search", "description": "Request water authority and drainage search", "order": 3, "priority": "MEDIUM", "estimatedDays": 1, "required": true}, {"title": "Review title documents", "description": "Examine title deeds and registered documents", "order": 4, "priority": "HIGH", "estimatedDays": 2, "required": true}, {"title": "Raise enquiries on title", "description": "Submit enquiries to seller's solicitor regarding title issues", "order": 5, "priority": "MEDIUM", "estimatedDays": 1, "required": true}]}, {"name": "Mortgage & Financial Arrangements", "description": "Handle mortgage application and financial requirements", "order": 3, "targetDays": 21, "tasks": [{"title": "Review mortgage offer", "description": "Examine mortgage offer terms and conditions", "order": 1, "priority": "HIGH", "estimatedDays": 1, "required": true}, {"title": "Report to lender", "description": "Submit certificate of title to mortgage lender", "order": 2, "priority": "HIGH", "estimatedDays": 2, "required": true}, {"title": "Arrange buildings insurance", "description": "Ensure adequate buildings insurance is in place", "order": 3, "priority": "MEDIUM", "estimatedDays": 1, "required": true}, {"title": "Calculate completion funds", "description": "Prepare statement of completion monies required", "order": 4, "priority": "HIGH", "estimatedDays": 1, "required": true}]}, {"name": "Contract & Exchange Preparation", "description": "Prepare contracts and arrange exchange of contracts", "order": 4, "targetDays": 28, "tasks": [{"title": "Review draft contract", "description": "Examine and approve draft contract terms", "order": 1, "priority": "HIGH", "estimatedDays": 2, "required": true}, {"title": "Negotiate contract terms", "description": "Negotiate any amendments to contract terms", "order": 2, "priority": "MEDIUM", "estimatedDays": 3, "required": true}, {"title": "Obtain client authority to exchange", "description": "Get client confirmation to proceed with exchange", "order": 3, "priority": "HIGH", "estimatedDays": 1, "required": true}, {"title": "Exchange contracts", "description": "Complete exchange of contracts with other party", "order": 4, "priority": "HIGHEST", "estimatedDays": 0, "required": true}]}, {"name": "Pre-Completion Preparations", "description": "Complete all pre-completion requirements", "order": 5, "targetDays": 35, "tasks": [{"title": "Prepare completion statement", "description": "Calculate final completion monies required", "order": 1, "priority": "HIGH", "estimatedDays": 1, "required": true}, {"title": "Arrange mortgage funds", "description": "Request mortgage advance from lender", "order": 2, "priority": "HIGH", "estimatedDays": 2, "required": true}, {"title": "Prepare transfer deed", "description": "Draft transfer deed for signature", "order": 3, "priority": "MEDIUM", "estimatedDays": 1, "required": true}, {"title": "Pre-completion searches", "description": "Conduct final searches before completion", "order": 4, "priority": "HIGH", "estimatedDays": 1, "required": true}]}, {"name": "Completion & Post-Completion", "description": "Complete transaction and handle post-completion matters", "order": 6, "targetDays": 42, "tasks": [{"title": "Complete transaction", "description": "Exchange completion monies and receive keys/deeds", "order": 1, "priority": "HIGHEST", "estimatedDays": 0, "required": true}, {"title": "Register title at Land Registry", "description": "Submit application to register new ownership", "order": 2, "priority": "HIGH", "estimatedDays": 3, "required": true}, {"title": "Pay Stamp Duty Land Tax", "description": "Submit SDLT return and payment to HMRC", "order": 3, "priority": "HIGH", "estimatedDays": 7, "required": true}, {"title": "Send completion report to client", "description": "Provide final report and return client documents", "order": 4, "priority": "MEDIUM", "estimatedDays": 5, "required": true}, {"title": "Archive case file", "description": "Complete file closure and archival procedures", "order": 5, "priority": "LOW", "estimatedDays": 2, "required": true}]}]}