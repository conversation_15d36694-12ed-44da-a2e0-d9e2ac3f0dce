import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { Logger } from '@nestjs/common';
dotenv.config({
    path: path.resolve(process.cwd(), '.env'),
});

const forceFixSchema = async () => {
    const dataSource = new DataSource({
        type: 'postgres',
        host: process.env.POSTGRES_HOST,
        port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
        username: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: process.env.POSTGRES_DB,
        schema: 'public'
    });

    try {
        await dataSource.initialize();
        Logger.log('Connected to database');

        // 1. Force drop all tenant schemas first
        Logger.log('Dropping all tenant schemas...');
        const dropTenantsQuery = `
            DO $$ 
            DECLARE 
                schema_name text;
            BEGIN 
                FOR schema_name IN (SELECT nspname FROM pg_namespace WHERE nspname LIKE 'tenant_%')
                LOOP
                    EXECUTE 'DROP SCHEMA IF EXISTS "' || schema_name || '" CASCADE';
                END LOOP;
            END $$;
        `;
        await dataSource.query(dropTenantsQuery);

        // 2. Force drop all related tables in public schema
        Logger.log('Dropping all related tables in public schema...');
        const dropTablesQuery = `
            DO $$ 
            DECLARE 
                tbl text;
            BEGIN 
                FOR tbl IN (
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name IN (
                        'user_roles',
                        'user_profiles',
                        'tenant_roles',
                        'typeorm_migrations',
                        'typeorm_migrations_lock'
                    )
                )
                LOOP
                    EXECUTE 'DROP TABLE IF EXISTS "' || tbl || '" CASCADE';
                END LOOP;
            END $$;
        `;
        await dataSource.query(dropTablesQuery);

        // 3. Clean up any remaining foreign key constraints
        Logger.log('Cleaning up remaining foreign key constraints...');
        const cleanConstraintsQuery = `
            DO $$ 
            DECLARE 
                r record;
            BEGIN 
                FOR r IN (
                    SELECT tc.table_schema, tc.constraint_name, tc.table_name
                    FROM information_schema.table_constraints tc
                    WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND (
                        tc.table_name LIKE '%user%' OR 
                        tc.table_name LIKE '%role%' OR 
                        tc.table_name LIKE '%tenant%' OR 
                        tc.table_name LIKE '%profile%'
                    )
                )
                LOOP
                    EXECUTE 'ALTER TABLE "' || r.table_schema || '"."' || r.table_name || '" DROP CONSTRAINT IF EXISTS "' || r.constraint_name || '" CASCADE';
                END LOOP;
            END $$;
        `;
        await dataSource.query(cleanConstraintsQuery);

        // 4. Reset the database state
        Logger.log('Resetting database state...');
        await dataSource.query(`
            DROP SCHEMA public CASCADE;
            CREATE SCHEMA public;
            GRANT ALL ON SCHEMA public TO postgres;
            GRANT ALL ON SCHEMA public TO public;
        `);

        Logger.log('Successfully reset database state');
    } catch (error) {
        Logger.error('Error during force fix:', error);
        throw error;
    } finally {
        if (dataSource.isInitialized) {
            await dataSource.destroy();
        }
    }
};

forceFixSchema()
    .then(() => {
        Logger.log('Force fix completed');
        process.exit(0);
    })
    .catch((error) => {
        Logger.error('Force fix failed:', error);
        process.exit(1);
    }); 