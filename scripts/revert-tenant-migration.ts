import * as fs from 'fs';
import * as path from 'path';
import { createTenantConfigFile, executeTenantCommand } from './utils/tenant-config';
import { Logger } from '@nestjs/common';

// Main execution
const main = async () => {
    try {
        Logger.log('Reverting the latest tenant schema migration...');

        // Create the tenant config file
        const configPath = createTenantConfigFile();

        // Check if any tenant migrations exist
        const tenantMigrationsDir = path.join(
            process.cwd(),
            'libs',
            'common',
            'src',
            'typeorm',
            'migrations',
            'tenant'
        );
        if (fs.existsSync(tenantMigrationsDir)) {
            const files = fs.readdirSync(tenantMigrationsDir);
            const migrationFiles = files.filter(
                (file) => file.endsWith('.ts') && !file.endsWith('.d.ts')
            );

            if (migrationFiles.length === 0) {
                Logger.log('No tenant migrations found. Nothing to revert.');
                return;
            }

            Logger.log(
                `Found ${migrationFiles.length} tenant migrations. Reverting the latest one.`
            );
        }

        // Revert the latest migration
        const command = `yarn typeorm migration:revert -d ${configPath}`;
        await executeTenantCommand(command, configPath, 'Tenant migration reverted successfully!');
    } catch (error) {
        Logger.error('Error reverting tenant migration:', error);
        process.exit(1);
    }
};

main();
