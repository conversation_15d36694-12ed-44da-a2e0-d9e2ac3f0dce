const { Client } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

/**
 * <PERSON>ript to create tables in all tenant schemas
 */
async function createTenantTables() {
    Logger.log('Creating tables in all tenant schemas...');

    // Connect to the database
    const client = new Client({
        host: process.env.POSTGRES_HOST,
        port: process.env.POSTGRES_PORT,
        user: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: process.env.POSTGRES_DB,
        ssl: process.env.POSTGRES_SSL === 'true'
    });

    try {
        await client.connect();
        Logger.log('Connected to database');

        // Get all tenant schemas
        const result = await client.query(`
            SELECT nspname
            FROM pg_namespace
            WHERE nspname LIKE 'tenant_%'
        `);

        const schemas = result.rows.map(row => row.nspname);
        Logger.log(`Found ${schemas.length} tenant schemas`);

        // Create the uuid-ossp extension in the public schema
        try {
            await client.query(`CREATE EXTENSION IF NOT EXISTS "uuid-ossp" SCHEMA public`);
            Logger.log('Created uuid-ossp extension in public schema');
        } catch (error) {
            Logger.error(`Failed to create uuid-ossp extension: ${error.message}`);
        }

        // Read the SQL script
        const sqlScript = fs.readFileSync(path.join(__dirname, 'create-tenant-tables.sql'), 'utf8');

        // Apply the SQL script to each schema
        for (const schema of schemas) {
            Logger.log(`Creating tables in schema: ${schema}`);

            try {
                // Set the search path to the tenant schema
                await client.query(`SET search_path TO "${schema}", public`);

                // Run the SQL script
                await client.query(sqlScript);

                Logger.log(`Successfully created tables in ${schema}`);
            } catch (error) {
                Logger.error(`Failed to create tables in ${schema}: ${error.message}`);
            }
        }
    } catch (error) {
        Logger.error(`Error: ${error.message}`);
    } finally {
        await client.end();
        Logger.log('Disconnected from database');
    }
}

createTenantTables().catch(Logger.error);
