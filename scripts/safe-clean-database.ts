import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { Logger } from '@nestjs/common';
// Load environment variables
dotenv.config({
    path: path.resolve(process.cwd(), '.env'),
});

interface Dependency {
    schema: string;
    table: string;
    constraint: string;
    referenced_table: string;
}

const safeDatabaseCleanup = async () => {
    const dataSource = new DataSource({
        type: 'postgres',
        host: process.env.POSTGRES_HOST,
        port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
        username: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: process.env.POSTGRES_DB,
        schema: 'public'
    });

    try {
        await dataSource.initialize();
        Logger.log('Connected to database');

        // 1. First, get all dependencies
        const dependencies: Dependency[] = await dataSource.query(`
            SELECT
                tc.table_schema as schema,
                tc.table_name as table,
                tc.constraint_name as constraint,
                ccu.table_name as referenced_table
            FROM 
                information_schema.table_constraints tc
                JOIN information_schema.constraint_column_usage ccu
                    ON tc.constraint_name = ccu.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND (tc.table_name LIKE '%user%' 
                OR tc.table_name LIKE '%role%'  
                OR tc.table_name LIKE '%tenant%'
                OR tc.table_name LIKE '%profile%')
        `);

        Logger.log('Found dependencies:', dependencies);

        // 2. Get all tenant schemas
        const schemas = await dataSource.query(
            `SELECT nspname FROM pg_namespace WHERE nspname LIKE 'tenant_%'`
        );

        // 3. For each schema, handle constraints safely
        for (const schema of schemas) {
            const schemaName = schema.nspname;
            Logger.log(`\nProcessing schema ${schemaName}`);

            // Get schema-specific constraints
            const schemaConstraints = dependencies.filter(d => d.schema === schemaName);
            
            // Drop foreign key constraints first
            for (const constraint of schemaConstraints) {
                Logger.log(`Dropping foreign key constraint ${constraint.constraint} from ${constraint.table}`);
                await dataSource.query(`
                    ALTER TABLE "${schemaName}"."${constraint.table}" 
                    DROP CONSTRAINT IF EXISTS "${constraint.constraint}"
                `);
            }
        }

        // 4. Now handle public schema constraints
        const publicConstraints = dependencies.filter(d => d.schema === 'public');
        for (const constraint of publicConstraints) {
            Logger.log(`Dropping public schema constraint ${constraint.constraint} from ${constraint.table}`);
            await dataSource.query(`
                ALTER TABLE "public"."${constraint.table}" 
                DROP CONSTRAINT IF EXISTS "${constraint.constraint}"
            `);
        }

        // 5. Drop tables in correct order
        const tablesToDrop = [
            'tenant_roles',
            'user_roles',
            'user_profiles'
        ];

        for (const table of tablesToDrop) {
            Logger.log(`\nChecking table ${table} for remaining dependencies...`);
            
            // Check for any remaining dependencies
            const remainingDeps = await dataSource.query(`
                SELECT tc.constraint_name, tc.table_name
                FROM information_schema.table_constraints tc
                WHERE tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_name = $1
            `, [table]);

            if (remainingDeps.length > 0) {
                Logger.log(`Warning: Table ${table} still has dependencies:`, remainingDeps);
                Logger.log('Attempting to drop these dependencies first...');
                
                for (const dep of remainingDeps) {
                    await dataSource.query(`
                        ALTER TABLE "${dep.table_name}" 
                        DROP CONSTRAINT IF EXISTS "${dep.constraint_name}"
                    `);
                }
            }

            Logger.log(`Dropping table ${table}`);
            await dataSource.query(`DROP TABLE IF EXISTS "${table}"`);
        }

        // 6. Finally, drop tenant schemas
        for (const schema of schemas) {
            const schemaName = schema.nspname;
            Logger.log(`\nDropping schema ${schemaName}`);
            await dataSource.query(`DROP SCHEMA IF EXISTS "${schemaName}" CASCADE`);
        }

        Logger.log('\nDatabase cleanup completed successfully');
    } catch (error) {
        Logger.error('Error during safe database cleanup:', error);
        throw error;
    } finally {
        if (dataSource.isInitialized) {
            await dataSource.destroy();
        }
    }
};

// Run the script
safeDatabaseCleanup()
    .then(() => {
        Logger.log('Safe cleanup completed');
        process.exit(0);
    })
    .catch((error) => {
        Logger.error('Safe cleanup failed:', error);
        process.exit(1);
    }); 