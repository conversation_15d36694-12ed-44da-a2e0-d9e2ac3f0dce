#!/bin/bash

# API Gateway Helper Script
# This script helps manage the microservices in different modes

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    echo "API Gateway Helper Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev           Start all services in development mode (separate ports)"
    echo "  gateway       Start services with API Gateway (Core as proxy)"
    echo "  docker        Start services with Docker (using docker-compose.yml)"
    echo "  docker-gateway Start services with Docker in Gateway mode (using docker-compose.gateway.yml)"
    echo "  stop          Stop all running services"
    echo "  logs          Show logs for all services"
    echo "  health        Check health of all services"
    echo "  test-gateway  Test the API Gateway routing"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev         # Start all services on separate ports for development"
    echo "  $0 gateway     # Start with Core as API Gateway"
    echo "  $0 docker      # Start with Docker (all ports exposed)"
    echo "  $0 docker-gateway # Start with Docker (only Core port exposed)"
}

# Start development mode (all services on separate ports)
start_dev() {
    print_info "Starting all services in development mode..."
    cd "$PROJECT_ROOT"
    yarn start:all:dev
}

# Start API Gateway mode (Core proxies to other services)
start_gateway() {
    print_info "Starting services with API Gateway mode..."
    print_info "Core service will act as API Gateway on port 3000"
    print_info "All requests should go to: http://localhost:3000/api/"
    cd "$PROJECT_ROOT"
    yarn start:gateway
}

# Start Docker mode (regular docker-compose)
start_docker() {
    print_info "Starting services with Docker (all ports exposed)..."
    cd "$PROJECT_ROOT"
    docker-compose up --build
}

# Start Docker Gateway mode
start_docker_gateway() {
    print_info "Starting services with Docker in Gateway mode..."
    print_info "Only Core service (port 3000) will be exposed externally"
    print_info "All requests should go to: http://localhost:3000/api/"
    cd "$PROJECT_ROOT"
    docker-compose -f docker-compose.gateway.yml up --build
}

# Stop all services
stop_services() {
    print_info "Stopping all services..."
    cd "$PROJECT_ROOT"
    
    # Stop Docker services
    docker-compose down 2>/dev/null || true
    docker-compose -f docker-compose.gateway.yml down 2>/dev/null || true
    
    # Kill any Node.js processes (for development mode)
    pkill -f "nest start" || true
    
    print_success "All services stopped"
}

# Show logs
show_logs() {
    print_info "Showing logs for Docker services..."
    cd "$PROJECT_ROOT"
    docker-compose logs -f
}

# Check health of services
check_health() {
    print_info "Checking health of services..."
    
    services=(
        "Core:http://localhost:3000/api/health"
        "Auth:http://localhost:3001/api/auth/health"
        "Communication:http://localhost:3002/api/communication/health"
        "Document Engine:http://localhost:3003/api/document-engine/health"
        "Case Management:http://localhost:3004/api/case-management/health"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name url <<< "$service"
        if curl -s "$url" > /dev/null 2>&1; then
            print_success "$name is healthy"
        else
            print_error "$name is not responding"
        fi
    done
}

# Test API Gateway routing
test_gateway() {
    print_info "Testing API Gateway routing..."
    
    gateway_url="http://localhost:3000/api"
    
    # Test endpoints through gateway
    endpoints=(
        "auth/health"
        "communication/health"
        "document-engine/health"
        "case-management/health"
    )
    
    print_info "Testing endpoints through Gateway ($gateway_url):"
    
    for endpoint in "${endpoints[@]}"; do
        url="$gateway_url/$endpoint"
        print_info "Testing: $url"
        
        if curl -s "$url" > /dev/null 2>&1; then
            print_success "✓ $endpoint - OK"
        else
            print_error "✗ $endpoint - Failed"
        fi
    done
    
    print_info ""
    print_info "Testing unknown service:"
    unknown_url="$gateway_url/unknown-service/test"
    response=$(curl -s "$unknown_url" 2>/dev/null || echo "Failed to connect")
    if [[ $response == *"not found"* ]] || [[ $response == *"404"* ]]; then
        print_success "✓ Unknown service properly returns 404"
    else
        print_warning "? Unknown service response: $response"
    fi
}

# Main script logic
case "${1:-help}" in
    "dev")
        start_dev
        ;;
    "gateway")
        start_gateway
        ;;
    "docker")
        start_docker
        ;;
    "docker-gateway")
        start_docker_gateway
        ;;
    "stop")
        stop_services
        ;;
    "logs")
        show_logs
        ;;
    "health")
        check_health
        ;;
    "test-gateway")
        test_gateway
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac 