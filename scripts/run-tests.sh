#!/bin/bash

# Function to handle errors and cleanup
cleanup() {
  echo "Cleaning up test environment..."
  docker compose -f docker-compose.test.yml down -v
  exit ${1:-0}
}

# Trap SIGINT, SIGTERM and ERR signals to ensure cleanup
trap 'cleanup $?' ERR
trap 'cleanup' SIGINT SIGTERM

# Start by bringing up the test containers
echo "Starting test environment..."
docker compose -f docker-compose.test.yml down -v
docker compose -f docker-compose.test.yml up -d

# Wait for services to be ready
echo "Waiting for services to be ready..."
TIMEOUT=180
ELAPSED=0
RETRY_INTERVAL=5

# Check if PostgreSQL is ready
echo "Checking PostgreSQL readiness..."
while [ $ELAPSED -lt $TIMEOUT ]; do
  if docker exec tk-lpm-postgres-test pg_isready -U postgres > /dev/null 2>&1; then
    echo "✅ PostgreSQL is ready!"
    break
  fi
  echo "Waiting for PostgreSQL to be ready... ($ELAPSED/$TIMEOUT seconds)"
  sleep $RETRY_INTERVAL
  ELAPSED=$((ELAPSED+RETRY_INTERVAL))
done

if [ $ELAPSED -ge $TIMEOUT ]; then
  echo "❌ Timeout waiting for PostgreSQL to be ready"
  docker logs tk-lpm-postgres-test
  cleanup 1
fi

# Reset elapsed time for Keycloak check
ELAPSED=0
echo "Checking Keycloak readiness..."

# Wait for Keycloak to be ready using simple health endpoint
while [ $ELAPSED -lt $TIMEOUT ]; do
  # Check if container is running
  CONTAINER_RUNNING=$(docker ps --filter "name=tk-lpm-keycloak-test" --filter "status=running" -q)
  if [ -z "$CONTAINER_RUNNING" ]; then
    echo "❌ Keycloak container is not running anymore!"
    docker logs tk-lpm-keycloak-test
    cleanup 1
  fi
  
  # Check a simpler endpoint first - just if Keycloak is responding
  if curl -s http://localhost:8090/ | grep -q "Keycloak"; then
    echo "✅ Keycloak is responding to HTTP requests!"
    break
  fi
  
  # Show progress and debugging info
  if [ $((ELAPSED % 30)) -eq 0 ] && [ $ELAPSED -gt 0 ]; then
    echo "⚠️ Checking Keycloak logs after $ELAPSED seconds:"
    docker logs --tail 10 tk-lpm-keycloak-test
  fi
  
  echo "Waiting for Keycloak to be ready... ($ELAPSED/$TIMEOUT seconds)"
  sleep $RETRY_INTERVAL
  ELAPSED=$((ELAPSED+RETRY_INTERVAL))
done

if [ $ELAPSED -ge $TIMEOUT ]; then
  echo "❌ Timeout waiting for Keycloak to be ready"
  docker logs tk-lpm-keycloak-test
  cleanup 1
fi

# Set environment variables for tests
export TEST_DB_HOST=localhost
export TEST_DB_PORT=5434
export TEST_DB_USERNAME=postgres
export TEST_DB_PASSWORD=postgres
export TEST_DB_DATABASE=tk_lpm_test
export TEST_KEYCLOAK_HOST=localhost
export TEST_KEYCLOAK_PORT=8090
export TEST_KEYCLOAK_USER=admin
export TEST_KEYCLOAK_PASSWORD=admin

# Set database connection environment variables (used by NestJS directly)
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5434
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=postgres
export POSTGRES_DB=tk_lpm_test

# Run the tests
echo "Running tests..."
if [ "$1" == "unit" ]; then
  yarn test:unit
elif [ "$1" == "integration" ]; then
  export INTEGRATION_TEST=true
  yarn test:integration:no-hang
elif [ "$1" == "simple-db" ]; then
  export INTEGRATION_TEST=true
  yarn test:integration:simple-db
else
  export INTEGRATION_TEST=true
  yarn test
fi

TEST_EXIT_CODE=$?

# Clean up
cleanup $TEST_EXIT_CODE 