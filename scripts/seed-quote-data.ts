import { DataSource } from 'typeorm';
import { FeeCategory } from '@app/common/typeorm/entities/tenant/fee-category.entity';
import { FeeItem } from '@app/common/typeorm/entities/tenant/fee-item.entity';
import { PromoCode } from '@app/common/typeorm/entities/tenant/promo-code.entity';
import { PromoCodeStatus } from '@app/common/typeorm/entities/tenant/promo-code.entity';
import { VatType } from '@app/common/typeorm/entities/tenant/fee-item.entity';
import { Logger } from '@nestjs/common';
import { TenantConnectionService } from '@app/common/multi-tenancy/tenant-connection.service';
import { ConfigService } from '@nestjs/config';
import { getTenantIdFromSchemaName } from '@app/common/multi-tenancy/sanitize-tenant-id.util';
import { getPublicDataSourceOptions } from '@app/common/config/database.config';

// Load environment variables from .env file
import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

export class QuoteDataSeeder {
    private tenantConnectionService: TenantConnectionService;
    private publicDataSource: DataSource;

    constructor() {
        const configService = new ConfigService();
        this.tenantConnectionService = new TenantConnectionService(configService);

        // Use the same database configuration as the application
        const publicDataSourceOptions = getPublicDataSourceOptions();
        this.publicDataSource = new DataSource({
            ...publicDataSourceOptions,
            name: 'seeder-public-connection',
            logging: false // Disable logging for seeding
        });
    }

    async initialize(): Promise<void> {
        await this.tenantConnectionService.onModuleInit();
        await this.publicDataSource.initialize();
    }

    async cleanup(): Promise<void> {
        await this.tenantConnectionService.onModuleDestroy();
        if (this.publicDataSource.isInitialized) {
            await this.publicDataSource.destroy();
        }
    }

    /**
     * Get all tenant schemas from the database
     */
    async getTenantSchemas(): Promise<string[]> {
        try {
            const result = await this.publicDataSource.query(`
                SELECT schema_name 
                FROM information_schema.schemata 
                WHERE schema_name LIKE 'tenant_%'
                AND schema_name != 'public'
                ORDER BY schema_name
            `);

            return result.map((row: any) => row.schema_name);
        } catch (error) {
            Logger.error('Error fetching tenant schemas:', error);
            throw error;
        }
    }

    /**
     * Extract tenant ID from schema name
     */
    private getTenantIdFromSchema(schemaName: string): string {
        const tenantId = getTenantIdFromSchemaName(schemaName);
        if (!tenantId) {
            throw new Error(`Invalid tenant schema name: ${schemaName}`);
        }
        return tenantId;
    }

    async seedFeeCategories(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const feeCategoryRepository = dataSource.getRepository(FeeCategory);

        const categories = [
            {
                id: '11111111-1111-1111-1111-111111111111',
                name: 'Legal Fee (Residential Transaction)',
                displayOrder: 1,
                active: true
            },
            {
                id: '22222222-2222-2222-2222-************',
                name: 'Disbursements',
                displayOrder: 2,
                active: true
            },
            {
                id: '********-3333-3333-3333-************',
                name: 'Other Fees',
                displayOrder: 3,
                active: true
            }
        ];

        for (const category of categories) {
            const existing = await feeCategoryRepository.findOne({ where: { id: category.id } });
            if (!existing) {
                await feeCategoryRepository.save(category);
                Logger.log(`✓ Created fee category: ${category.name} for tenant ${tenantId}`);
            } else {
                Logger.log(
                    `- Fee category already exists: ${category.name} for tenant ${tenantId}`
                );
            }
        }
    }

    async seedFeeItems(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const feeItemRepository = dataSource.getRepository(FeeItem);

        const feeItems = [
            // Buy Transaction Legal Fees
            {
                id: '101222bb-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 0.0,
                rangeEnd: 99999.0,
                conditionSlug: undefined,
                netFee: 549.0,
                vatFee: 109.8,
                totalFee: 658.8,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '101227dd-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 100000.0,
                rangeEnd: 250000.0,
                conditionSlug: undefined,
                netFee: 575.0,
                vatFee: 115.0,
                totalFee: 690.0,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '10122936-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 250001.0,
                rangeEnd: 500000.0,
                conditionSlug: undefined,
                netFee: 599.0,
                vatFee: 119.8,
                totalFee: 718.8,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '10122a26-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 500001.0,
                rangeEnd: 750000.0,
                conditionSlug: undefined,
                netFee: 649.0,
                vatFee: 129.8,
                totalFee: 778.8,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '10122b18-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 750001.0,
                rangeEnd: 1000000.0,
                conditionSlug: undefined,
                netFee: 749.0,
                vatFee: 149.8,
                totalFee: 898.8,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '10122c06-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 1000001.0,
                rangeEnd: 2000000.0,
                conditionSlug: undefined,
                netFee: 899.0,
                vatFee: 179.8,
                totalFee: 1078.8,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '10122cca-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 2000001.0,
                rangeEnd: 100000000.0,
                conditionSlug: undefined,
                netFee: 1149.0,
                vatFee: 229.8,
                totalFee: 1378.8,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: false,
                active: true
            },
            // Sell Transaction Legal Fees
            {
                id: '27ee94e7-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 0.0,
                rangeEnd: 250000.0,
                conditionSlug: undefined,
                netFee: 449.0,
                vatFee: 89.8,
                totalFee: 538.8,
                vatType: VatType.EXC,
                applicableFor: 'sell',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '27ee99b3-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 250001.0,
                rangeEnd: 500000.0,
                conditionSlug: undefined,
                netFee: 549.0,
                vatFee: 109.8,
                totalFee: 658.8,
                vatType: VatType.EXC,
                applicableFor: 'sell',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '27ee9a9b-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 500001.0,
                rangeEnd: 750000.0,
                conditionSlug: undefined,
                netFee: 649.0,
                vatFee: 129.8,
                totalFee: 778.8,
                vatType: VatType.EXC,
                applicableFor: 'sell',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '27eea187-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 750001.0,
                rangeEnd: 1000000.0,
                conditionSlug: undefined,
                netFee: 749.0,
                vatFee: 149.8,
                totalFee: 898.8,
                vatType: VatType.EXC,
                applicableFor: 'sell',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '27eea345-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 1000001.0,
                rangeEnd: 2000000.0,
                conditionSlug: undefined,
                netFee: 875.0,
                vatFee: 175.0,
                totalFee: 1050.0,
                vatType: VatType.EXC,
                applicableFor: 'sell',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '27eea523-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 2000001.0,
                rangeEnd: 100000000.0,
                conditionSlug: undefined,
                netFee: 950.0,
                vatFee: 190.0,
                totalFee: 1140.0,
                vatType: VatType.EXC,
                applicableFor: 'sell',
                perParty: false,
                dynamic: false,
                active: true
            },
            // Remortgage Transaction Legal Fees
            {
                id: '34bcd57a-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 0.0,
                rangeEnd: 1000000.0,
                conditionSlug: undefined,
                netFee: 350.0,
                vatFee: 70.0,
                totalFee: 420.0,
                vatType: VatType.EXC,
                applicableFor: 'remortgage',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '34bcd9fe-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Legal Fee (Residential Transaction)',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: 1000001.0,
                rangeEnd: 2000000.0,
                conditionSlug: undefined,
                netFee: 450.0,
                vatFee: 90.0,
                totalFee: 540.0,
                vatType: VatType.EXC,
                applicableFor: 'remortgage',
                perParty: false,
                dynamic: false,
                active: true
            },
            // Common Disbursements
            {
                id: '10122d90-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'SDLT Admin',
                categoryId: '22222222-2222-2222-2222-************',
                rangeStart: undefined,
                rangeEnd: undefined,
                conditionSlug: 'always',
                netFee: 100.0,
                vatFee: 20.0,
                totalFee: 120.0,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '10122e64-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Lawyer Checker',
                categoryId: '22222222-2222-2222-2222-************',
                rangeStart: undefined,
                rangeEnd: undefined,
                conditionSlug: 'always',
                netFee: 25.26,
                vatFee: 0.0,
                totalFee: 25.26,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '10122f29-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Bank Transfer Fee',
                categoryId: '********-3333-3333-3333-************',
                rangeStart: undefined,
                rangeEnd: undefined,
                conditionSlug: 'always',
                netFee: 45.0,
                vatFee: 9.0,
                totalFee: 54.0,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '10122ff4-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'AML check',
                categoryId: '********-3333-3333-3333-************',
                rangeStart: undefined,
                rangeEnd: undefined,
                conditionSlug: 'always',
                netFee: 25.0,
                vatFee: 5.0,
                totalFee: 30.0,
                vatType: VatType.EXC,
                applicableFor: 'buy,sell',
                perParty: true,
                dynamic: true,
                active: true
            },
            // Conditional Fees
            {
                id: '1946f7f9-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'New Build Fee',
                categoryId: '11111111-1111-1111-1111-111111111111',
                rangeStart: undefined,
                rangeEnd: undefined,
                conditionSlug: 'is_new_build',
                netFee: 150.0,
                vatFee: 30.0,
                totalFee: 180.0,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '10125fb9-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Leasehold Fee',
                categoryId: '22222222-2222-2222-2222-************',
                rangeStart: undefined,
                rangeEnd: undefined,
                conditionSlug: 'leasehold',
                netFee: 200.0,
                vatFee: 40.0,
                totalFee: 240.0,
                vatType: VatType.EXC,
                applicableFor: 'buy,sell,remortgage',
                perParty: false,
                dynamic: false,
                active: true
            },
            {
                id: '10125ed5-0e2b-11f0-b0f4-16243d6fb08b',
                label: 'Shared Ownership',
                categoryId: '22222222-2222-2222-2222-************',
                rangeStart: undefined,
                rangeEnd: undefined,
                conditionSlug: 'shared_ownership',
                netFee: 200.0,
                vatFee: 40.0,
                totalFee: 240.0,
                vatType: VatType.EXC,
                applicableFor: 'buy,sell',
                perParty: false,
                dynamic: false,
                active: true
            },
            // Tax Calculations (Dynamic)
            {
                id: 'e231f568-0e25-11f0-bb47-e0098a9a652c',
                label: 'Land Transaction Tax',
                categoryId: '********-3333-3333-3333-************',
                rangeStart: undefined,
                rangeEnd: undefined,
                conditionSlug: 'property_in_wales',
                netFee: 0.0,
                vatFee: 0.0,
                totalFee: 0.0,
                vatType: VatType.EXC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: true,
                active: true
            },
            {
                id: 'e231f568-0e25-11f0-bb47-e5568a9a652c',
                label: 'Stamp Duty Land Tax (SDLT)',
                categoryId: '********-3333-3333-3333-************',
                rangeStart: undefined,
                rangeEnd: undefined,
                conditionSlug: 'not_property_in_wales',
                netFee: 0.0,
                vatFee: 0.0,
                totalFee: 0.0,
                vatType: VatType.INC,
                applicableFor: 'buy',
                perParty: false,
                dynamic: true,
                active: true
            }
        ];

        for (const item of feeItems) {
            const existing = await feeItemRepository.findOne({ where: { id: item.id } });
            if (!existing) {
                await feeItemRepository.save(item);
                Logger.log(`✓ Created fee item: ${item.label} for tenant ${tenantId}`);
            } else {
                Logger.log(`- Fee item already exists: ${item.label} for tenant ${tenantId}`);
            }
        }
    }

    async seedPromoCodes(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const promoCodeRepository = dataSource.getRepository(PromoCode);

        const promoCodes = [
            {
                id: '75c20d4f-46c8-444a-ac79-034baa9baeda',
                name: 'WE GO!!!',
                code: 'LWP860',
                discount: 17,
                expirationDate: new Date('2025-06-29'),
                status: PromoCodeStatus.ACTIVE,
                usageLimit: undefined,
                usageCount: 0
            },
            {
                id: '88428526-33b4-4272-a862-a662e4cbf6e6',
                name: 'here we go',
                code: 'NUQ046',
                discount: 28,
                expirationDate: new Date('2025-12-22'),
                status: PromoCodeStatus.ACTIVE,
                usageLimit: undefined,
                usageCount: 0
            },
            {
                id: 'ace3cb9d-3a45-43da-a70b-4c5c532534f0',
                name: 'Kampala Boys',
                code: 'ITL575',
                discount: 50,
                expirationDate: new Date('2025-10-10'),
                status: PromoCodeStatus.ACTIVE,
                usageLimit: undefined,
                usageCount: 0
            }
        ];

        for (const promoCode of promoCodes) {
            const existing = await promoCodeRepository.findOne({ where: { id: promoCode.id } });
            if (!existing) {
                await promoCodeRepository.save(promoCode);
                Logger.log(
                    `✓ Created promo code: ${promoCode.name} (${promoCode.code}) for tenant ${tenantId}`
                );
            } else {
                Logger.log(
                    `- Promo code already exists: ${promoCode.name} (${promoCode.code}) for tenant ${tenantId}`
                );
            }
        }
    }

    async seedAllDataForTenant(tenantId: string): Promise<void> {
        Logger.log(`Starting quote data seeding for tenant: ${tenantId}`);

        try {
            await this.seedFeeCategories(tenantId);
            Logger.log(`✓ Fee categories seeded for tenant ${tenantId}`);

            await this.seedFeeItems(tenantId);
            Logger.log(`✓ Fee items seeded for tenant ${tenantId}`);

            await this.seedPromoCodes(tenantId);
            Logger.log(`✓ Promo codes seeded for tenant ${tenantId}`);

            Logger.log(`Quote data seeding completed successfully for tenant ${tenantId}!`);
        } catch (error) {
            Logger.error(`Error seeding data for tenant ${tenantId}:`, error);
            throw error;
        }
    }

    async seedAllTenants(): Promise<void> {
        Logger.log('Scanning for tenant schemas...');

        const tenantSchemas = await this.getTenantSchemas();
        Logger.log(`Found ${tenantSchemas.length} tenant schemas: ${tenantSchemas.join(', ')}`);

        if (tenantSchemas.length === 0) {
            Logger.log('No tenant schemas found. Skipping seeding.');
            return;
        }

        for (const schemaName of tenantSchemas) {
            try {
                const tenantId = this.getTenantIdFromSchema(schemaName);
                Logger.log(`Processing tenant schema: ${schemaName} (tenant ID: ${tenantId})`);

                await this.seedAllDataForTenant(tenantId);

                // Close the tenant connection after seeding
                await this.tenantConnectionService.closeTenantConnection(tenantId);
            } catch (error) {
                Logger.error(`Failed to seed data for schema ${schemaName}:`, error);
                // Continue with other schemas even if one fails
            }
        }

        Logger.log('Quote data seeding completed for all tenant schemas!');
    }
}

// Main execution function
async function main() {
    const seeder = new QuoteDataSeeder();

    try {
        await seeder.initialize();

        // Check command line arguments
        const args = process.argv.slice(2);
        const hasAllTenantsFlag = args.includes('--all-tenants');
        const specificTenantId = args.find((arg) => !arg.startsWith('--'));

        if (hasAllTenantsFlag || !specificTenantId) {
            Logger.log('Seeding data for all tenant schemas...');
            await seeder.seedAllTenants();
        } else {
            Logger.log(`Seeding data for specific tenant: ${specificTenantId}`);
            await seeder.seedAllDataForTenant(specificTenantId);
        }
    } catch (error) {
        Logger.error('Error during seeding:', error);
        process.exit(1);
    } finally {
        await seeder.cleanup();
    }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
    main();
}
