#!/bin/bash
set -e

echo "=== Running Case Management Tests ==="

# Export test environment variables
export NODE_ENV=test

# List the tests that will be run
echo "Case Management test files:"
find test/unit -name "case*.spec.ts" | sort

# Run case management tests with coverage
echo "Running Case Management tests with coverage..."
yarn jest "test/unit/case.*spec.ts" --coverage

echo "✅ Case Management tests completed"

# Display coverage summary
echo "Coverage summary for Case Management:"
cat coverage/lcov-report/index.html | grep -A 5 "app/case-management" || echo "No coverage data found for case-management"

exit 0 