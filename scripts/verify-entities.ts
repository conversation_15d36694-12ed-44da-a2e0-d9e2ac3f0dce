import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { SystemUser } from '../libs/common/src/typeorm/entities/public/system-user.entity';
import { SystemRole } from '../libs/common/src/typeorm/entities/public/system-role.entity';
import { Product } from '../libs/common/src/typeorm/entities/public/product.entity';
import { TenantRole } from '../libs/common/src/typeorm/entities/tenant/tenant-role.entity';
import { UserProfile } from '../libs/common/src/typeorm/entities/tenant/user-profile.entity';
import { Logger } from '@nestjs/common';

dotenv.config({
    path: path.resolve(process.cwd(), '.env')
});

const verifySetup = async () => {
    // Create DataSource with all entities
    const dataSource = new DataSource({
        type: 'postgres',
        host: process.env.POSTGRES_HOST || 'localhost',
        port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
        username: process.env.POSTGRES_USER || 'postgres',
        password: process.env.POSTGRES_PASSWORD,
        database: 'tk_lpm',
        entities: [SystemUser, SystemRole, Product, TenantRole, UserProfile],
        synchronize: false,
        logging: true
    });

    try {
        Logger.log('Attempting to connect to database...');
        await dataSource.initialize();
        Logger.log('Successfully connected to database!');

        // Get all entity metadata
        const entities = dataSource.entityMetadatas;

        Logger.log('\nEntity Information:');
        Logger.log('===================');

        for (const entity of entities) {
            Logger.log(`\nEntity: ${entity.name}`);
            Logger.log(`Table: ${entity.tableName}`);
            Logger.log(`Schema: ${entity.schema}`);

            if (entity.relations.length > 0) {
                Logger.log('Relations:');
                entity.relations.forEach((relation) => {
                    Logger.log(
                        `  - ${relation.propertyName} -> ${relation.inverseEntityMetadata.name} (${relation.relationType})`
                    );
                });
            }

            if (entity.indices.length > 0) {
                Logger.log('Indices:');
                entity.indices.forEach((index) => {
                    Logger.log(
                        `  - ${index.name}: ${index.columns.map((col) => col.databaseName).join(', ')}`
                    );
                });
            }
        }

        // Verify database schema existence
        const schemas = await dataSource.query(
            "SELECT schema_name FROM information_schema.schemata WHERE schema_name LIKE 'tenant_%' OR schema_name = 'public'"
        );

        Logger.log('\nDatabase Schemas:');
        Logger.log('=================');
        schemas.forEach((schema: any) => {
            Logger.log(`- ${schema.schema_name}`);
        });
    } catch (error) {
        Logger.error('Error during verification:', error);
        throw error;
    } finally {
        if (dataSource.isInitialized) {
            await dataSource.destroy();
        }
    }
};

verifySetup()
    .then(() => {
        Logger.log('\nVerification completed successfully');
        process.exit(0);
    })
    .catch((error) => {
        Logger.error('\nVerification failed:', error);
        process.exit(1);
    });
