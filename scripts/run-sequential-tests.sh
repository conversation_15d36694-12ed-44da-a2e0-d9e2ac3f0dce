#!/bin/bash

# A standardized script to run tests in sequential order
# This assumes the database is already set up and running

# Set environment variables
export INTEGRATION_TEST=true
export RUN_MIGRATIONS=true

echo "=== Running Tests in Sequential Order ==="

# 1. Set up the database schema first
echo "1. Setting up test database schema..."
node test/helpers/setup-test-db.js
if [ $? -ne 0 ]; then
  echo "❌ Failed to set up database schema. Tests may fail."
fi

# 2. Run auth tests first
echo "2. Running auth integration tests..."
yarn jest test/integration/auth.integration.spec.ts --detectOpenHandles
AUTH_EXIT_CODE=$?

# Wait a bit for any pending operations
sleep 2

# 3. Run case management tests
echo "3. Running case management integration tests..."
yarn jest test/integration/case-management.integration.spec.ts --detectOpenHandles
CASE_EXIT_CODE=$?

# 4. Run axios and keycloak mock tests
echo "4. Running axios and keycloak mock tests..."
yarn jest test/integration/axios-interceptors.integration.spec.ts test/integration/keycloak-mock.integration.spec.ts --detectOpenHandles
MOCK_EXIT_CODE=$?

# 5. Run remaining tests
echo "5. Running remaining integration tests..."
yarn jest test/integration/role-hierarchy-mock.integration.spec.ts test/integration/simple-db.integration.spec.ts --detectOpenHandles
OTHER_EXIT_CODE=$?

# 6. Summary
echo "=== Test Results ==="
echo "Auth tests: $([ $AUTH_EXIT_CODE -eq 0 ] && echo '✅ Passed' || echo '❌ Failed')"
echo "Case Management tests: $([ $CASE_EXIT_CODE -eq 0 ] && echo '✅ Passed' || echo '❌ Failed')"
echo "Mock tests: $([ $MOCK_EXIT_CODE -eq 0 ] && echo '✅ Passed' || echo '❌ Failed')"
echo "Other tests: $([ $OTHER_EXIT_CODE -eq 0 ] && echo '✅ Passed' || echo '❌ Failed')"

# Return worst exit code
WORST_EXIT_CODE=$AUTH_EXIT_CODE
[ $CASE_EXIT_CODE -gt $WORST_EXIT_CODE ] && WORST_EXIT_CODE=$CASE_EXIT_CODE
[ $MOCK_EXIT_CODE -gt $WORST_EXIT_CODE ] && WORST_EXIT_CODE=$MOCK_EXIT_CODE
[ $OTHER_EXIT_CODE -gt $WORST_EXIT_CODE ] && WORST_EXIT_CODE=$OTHER_EXIT_CODE

exit $WORST_EXIT_CODE 