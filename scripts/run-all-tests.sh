#!/bin/bash

# Set colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== TK-LPM Test Runner ===${NC}"
echo -e "${BLUE}This script runs all tests with Docker containers for integration tests${NC}"

# Function to handle errors and cleanup
cleanup() {
  echo -e "${YELLOW}Cleaning up test environment...${NC}"
  
  # Stop if Docker containers are still running
  if [ -n "$(docker ps -q -f name=tk-lpm-postgres-test)" ] || [ -n "$(docker ps -q -f name=tk-lpm-keycloak-test)" ]; then
    echo -e "${YELLOW}Stopping Docker containers...${NC}"
    docker compose -f docker-compose.test.yml down
  fi
  
  # Reset environment variables
  unset TEST_DB_HOST
  unset TEST_DB_PORT
  unset TEST_DB_USERNAME
  unset TEST_DB_PASSWORD
  unset TEST_DB_DATABASE
  unset TEST_KEYCLOAK_HOST
  unset TEST_KEYCLOAK_PORT
  unset TEST_KEYCLOAK_USER
  unset TEST_KEYCLOAK_PASSWORD
  unset POSTGRES_HOST
  unset POSTGRES_PORT
  unset POSTGRES_USER
  unset POSTGRES_PASSWORD
  unset POSTGRES_DB
  unset INTEGRATION_TEST
  
  echo -e "${YELLOW}Cleanup complete.${NC}"
  
  # Exit with the provided code or 0 if none provided
  exit ${1:-0}
}

# Trap signals to ensure cleanup
trap 'cleanup 1' INT TERM
trap 'cleanup $?' EXIT

# 1. Start by running unit tests first (they don't need Docker)
echo -e "${BLUE}Step 1: Running unit tests${NC}"
yarn test:unit
UNIT_TEST_EXIT=$?

if [ $UNIT_TEST_EXIT -ne 0 ]; then
  echo -e "${RED}Unit tests failed with exit code $UNIT_TEST_EXIT${NC}"
  echo -e "${YELLOW}Skipping integration tests and exiting.${NC}"
  exit $UNIT_TEST_EXIT
fi

echo -e "${GREEN}✓ Unit tests passed successfully${NC}"

# 2. Start Docker containers for integration tests
echo -e "${BLUE}Step 2: Starting Docker containers for integration tests${NC}"
docker compose -f docker-compose.test.yml down -v
docker compose -f docker-compose.test.yml up -d

# 3. Wait for services to be ready
echo -e "${BLUE}Step 3: Waiting for services to be ready${NC}"
TIMEOUT=180
ELAPSED=0
RETRY_INTERVAL=5

# Check if PostgreSQL is ready
echo -e "${YELLOW}Checking PostgreSQL readiness...${NC}"
while [ $ELAPSED -lt $TIMEOUT ]; do
  if docker exec tk-lpm-postgres-test pg_isready -U postgres > /dev/null 2>&1; then
    echo -e "${GREEN}✓ PostgreSQL is ready!${NC}"
    break
  fi
  echo -e "${YELLOW}Waiting for PostgreSQL to be ready... ($ELAPSED/$TIMEOUT seconds)${NC}"
  sleep $RETRY_INTERVAL
  ELAPSED=$((ELAPSED+RETRY_INTERVAL))
done

if [ $ELAPSED -ge $TIMEOUT ]; then
  echo -e "${RED}❌ Timeout waiting for PostgreSQL to be ready${NC}"
  docker logs tk-lpm-postgres-test
  exit 1
fi

# Reset elapsed time for Keycloak check
ELAPSED=0
echo -e "${YELLOW}Checking Keycloak readiness...${NC}"

# Wait for Keycloak to be ready
while [ $ELAPSED -lt $TIMEOUT ]; do
  # Check if container is running
  CONTAINER_RUNNING=$(docker ps --filter "name=tk-lpm-keycloak-test" --filter "status=running" -q)
  if [ -z "$CONTAINER_RUNNING" ]; then
    echo -e "${RED}❌ Keycloak container is not running anymore!${NC}"
    docker logs tk-lpm-keycloak-test
    exit 1
  fi
  
  # Check a simpler endpoint first
  if curl -s http://localhost:8090/ | grep -q "Keycloak"; then
    echo -e "${GREEN}✓ Keycloak is responding to HTTP requests!${NC}"
    break
  fi
  
  # Show progress and debugging info
  if [ $((ELAPSED % 30)) -eq 0 ] && [ $ELAPSED -gt 0 ]; then
    echo -e "${YELLOW}⚠️ Checking Keycloak logs after $ELAPSED seconds:${NC}"
    docker logs --tail 10 tk-lpm-keycloak-test
  fi
  
  echo -e "${YELLOW}Waiting for Keycloak to be ready... ($ELAPSED/$TIMEOUT seconds)${NC}"
  sleep $RETRY_INTERVAL
  ELAPSED=$((ELAPSED+RETRY_INTERVAL))
done

if [ $ELAPSED -ge $TIMEOUT ]; then
  echo -e "${RED}❌ Timeout waiting for Keycloak to be ready${NC}"
  docker logs tk-lpm-keycloak-test
  exit 1
fi

# 4. Set environment variables for tests
echo -e "${BLUE}Step 4: Setting environment variables for integration tests${NC}"
export TEST_DB_HOST=localhost
export TEST_DB_PORT=5434
export TEST_DB_USERNAME=postgres
export TEST_DB_PASSWORD=postgres
export TEST_DB_DATABASE=tk_lpm_test
export TEST_KEYCLOAK_HOST=localhost
export TEST_KEYCLOAK_PORT=8090
export TEST_KEYCLOAK_USER=admin
export TEST_KEYCLOAK_PASSWORD=admin

# Set database connection environment variables (used by NestJS directly)
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5434
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=postgres
export POSTGRES_DB=tk_lpm_test

# 5. Setup the test database schema
echo -e "${BLUE}Step 5: Setting up test database schema${NC}"
yarn test:db:setup
DB_SETUP_EXIT=$?

if [ $DB_SETUP_EXIT -ne 0 ]; then
  echo -e "${RED}❌ Failed to set up database schema. Tests may fail.${NC}"
  # Continue anyway as we might still want to run the tests
fi

# 6. Run integration tests in sequential mode (more reliable)
echo -e "${BLUE}Step 6: Running integration tests in sequential mode${NC}"
export INTEGRATION_TEST=true
yarn test:integration:sequential
INTEGRATION_TEST_EXIT=$?

# 7. Summarize results
echo -e "${BLUE}=== Test Results ===${NC}"
echo -e "Unit tests: $([ $UNIT_TEST_EXIT -eq 0 ] && echo -e "${GREEN}✓ Passed${NC}" || echo -e "${RED}❌ Failed${NC}")"
echo -e "Integration tests: $([ $INTEGRATION_TEST_EXIT -eq 0 ] && echo -e "${GREEN}✓ Passed${NC}" || echo -e "${RED}❌ Failed${NC}")"

# Return worst exit code
WORST_EXIT_CODE=$UNIT_TEST_EXIT
[ $INTEGRATION_TEST_EXIT -gt $WORST_EXIT_CODE ] && WORST_EXIT_CODE=$INTEGRATION_TEST_EXIT

# Exit with worst code - cleanup will be called automatically via trap
exit $WORST_EXIT_CODE 