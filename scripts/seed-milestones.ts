#!/usr/bin/env ts-node

import { Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
import { MilestoneSeederService } from './services/milestone-seeder.service';
import { createDefaultMilestoneTablesInTenant } from './migrations/create-default-milestone-tables';
import { getSchemaNameFromTenantId } from '../libs/common/src/multi-tenancy/sanitize-tenant-id.util';

// Load environment variables
dotenv.config();

const logger = new Logger('MilestoneSeeder');

interface TenantInfo {
    id: string;
    realm: string;
    display_name?: string;
}

/**
 * Standalone script to seed milestones across all tenant schemas
 * Usage:
 *   yarn seed:milestones:all - Seed all tenants
 *   yarn seed:milestones --tenant=tenant-id - Seed specific tenant
 *   yarn seed:milestones:update - Re-seed (update existing data)
 */
async function main() {
    try {
        const args = process.argv.slice(2);
        const specificTenant = args.find((arg) => arg.startsWith('--tenant='))?.split('=')[1];
        const isUpdate =
            args.includes('--update') || process.argv.includes('seed:milestones:update');

        logger.log('Starting milestone seeding process...');

        // Get list of tenants
        const tenants = await getTenantList();

        if (tenants.length === 0) {
            logger.warn('No tenants found in the system');
            return;
        }

        const tenantsToProcess = specificTenant
            ? tenants.filter((t) => t.id === specificTenant || t.realm === specificTenant)
            : tenants;

        if (tenantsToProcess.length === 0) {
            logger.error(`Tenant not found: ${specificTenant}`);
            return;
        }

        logger.log(`Processing ${tenantsToProcess.length} tenant(s)...`);
        const seederService = new MilestoneSeederService();

        for (const tenant of tenantsToProcess) {
            try {
                logger.log(`Processing tenant: ${tenant.realm} (${tenant.id})`);

                // Create tenant data source
                const tenantDataSource = await createTenantDataSource(tenant.id);

                // Create tables if they don't exist
                await createDefaultMilestoneTablesInTenant(tenantDataSource);

                // Check if already seeded (skip if not updating)
                const alreadySeeded = await seederService.areMilestonesSeeded(tenantDataSource);

                if (alreadySeeded && !isUpdate) {
                    logger.log(
                        `Tenant ${tenant.realm} already has milestones seeded. Use --update to re-seed.`
                    );
                    await tenantDataSource.destroy();
                    continue;
                }

                // Seed milestones
                await seederService.seedMilestonesForTenant(tenantDataSource, tenant.id);

                logger.log(`✅ Successfully processed tenant: ${tenant.realm}`);
                await tenantDataSource.destroy();
            } catch (error) {
                logger.error(`❌ Error processing tenant ${tenant.realm}:`, error);
                // Continue with other tenants
            }
        }

        logger.log('Milestone seeding process completed');
    } catch (error) {
        logger.error('Fatal error in milestone seeding:', error);
        process.exit(1);
    }
}

/**
 * Create a data source for a specific tenant using the same pattern as existing scripts
 */
async function createTenantDataSource(tenantId: string): Promise<DataSource> {
    const schemaName = getSchemaNameFromTenantId(tenantId);

    const tenantDataSource = new DataSource({
        type: 'postgres',
        host: process.env.POSTGRES_HOST || 'localhost',
        port: parseInt(process.env.POSTGRES_PORT || '5432'),
        username: process.env.POSTGRES_USER || 'postgres',
        password: process.env.POSTGRES_PASSWORD || 'password',
        database: process.env.POSTGRES_DB || 'lpm_db',
        schema: schemaName,
        synchronize: false,
        logging: false
    });

    await tenantDataSource.initialize();

    // Ensure schema exists and set search path
    await tenantDataSource.query(`CREATE SCHEMA IF NOT EXISTS "${schemaName}"`);
    await tenantDataSource.query(`SET search_path TO "${schemaName}"`);

    return tenantDataSource;
}

/**
 * Get list of all tenants from the public schema using the same env vars as existing scripts
 */
async function getTenantList(): Promise<TenantInfo[]> {
    const publicDataSource = new DataSource({
        type: 'postgres',
        host: process.env.POSTGRES_HOST || 'localhost',
        port: parseInt(process.env.POSTGRES_PORT || '5432'),
        username: process.env.POSTGRES_USER || 'postgres',
        password: process.env.POSTGRES_PASSWORD || 'password',
        database: process.env.POSTGRES_DB || 'lpm_db',
        schema: 'public',
        synchronize: false,
        logging: false
    });

    await publicDataSource.initialize();

    try {
        const tenants = await publicDataSource.query(
            'SELECT id, realm, display_name FROM tenants WHERE id IS NOT NULL AND enabled = true'
        );
        return tenants;
    } finally {
        await publicDataSource.destroy();
    }
}

// Run the script
if (require.main === module) {
    main().catch((error) => {
        logger.error('Script failed:', error);
        process.exit(1);
    });
}
