#!/bin/bash
set -e

echo "=== Running Integration Tests with Database Setup ==="

# Step 1: Set up necessary environment variables
export INTEGRATION_TEST=true
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5434
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=postgres
export POSTGRES_DB=tk_lpm_test
export NODE_ENV=test

# Step 2: Ensure Docker is running 
if ! docker info >/dev/null 2>&1; then
  echo "Docker is not running. Please start Docker and try again."
  exit 1
fi

# Step 3: Start test database if not already running
if ! docker ps | grep -q tk-lpm-postgres-test; then
  echo "Starting test database..."
  docker-compose -f docker-compose.test.yml up -d postgres-test
  
  echo "Waiting for database to be ready..."
  for i in {1..30}; do
    if docker exec tk-lpm-postgres-test pg_isready -U postgres -d tk_lpm_test >/dev/null 2>&1; then
      echo "Database is ready!"
      break
    fi
    echo -n "."
    sleep 1
  done
else
  echo "Test database is already running."
fi

# Step 4: Run migrations setup
echo "Setting up database schema..."
node test/helpers/ensure-migration.js

# Step 5: Run integration tests
echo "Running integration tests..."
yarn jest --testPathPattern="test/integration/.*\\.integration\\.spec\\.ts$" --detectOpenHandles "$@"

# Exit with test exit code
exit $? 