import { DataSource } from 'typeorm';
import {
    RateCard,
    RateCardProvider,
    RateCardStatus
} from '@app/common/typeorm/entities/tenant/rate-card.entity';
import {
    RateCardFeeItem,
    FeeItemType
} from '@app/common/typeorm/entities/tenant/rate-card-fee-item.entity';
import { VatType } from '@app/common/typeorm/entities/tenant/fee-item.entity';
import { Logger } from '@nestjs/common';
import { TenantConnectionService } from '@app/common/multi-tenancy/tenant-connection.service';
import { ConfigService } from '@nestjs/config';
import { getTenantIdFromSchemaName } from '@app/common/multi-tenancy/sanitize-tenant-id.util';
import { getPublicDataSourceOptions } from '@app/common/config/database.config';

// Load environment variables from .env file
import * as dotenv from 'dotenv';
dotenv.config({ path: '.env' });

export class RateCardSeeder {
    private tenantConnectionService: TenantConnectionService;
    private publicDataSource: DataSource;

    constructor() {
        const configService = new ConfigService();
        this.tenantConnectionService = new TenantConnectionService(configService);

        const publicDataSourceOptions = getPublicDataSourceOptions();
        this.publicDataSource = new DataSource({
            ...publicDataSourceOptions,
            name: 'rate-card-seeder-public-connection',
            logging: false
        });
    }

    async initialize(): Promise<void> {
        await this.tenantConnectionService.onModuleInit();
        await this.publicDataSource.initialize();
    }

    async cleanup(): Promise<void> {
        await this.tenantConnectionService.onModuleDestroy();
        if (this.publicDataSource.isInitialized) {
            await this.publicDataSource.destroy();
        }
    }

    async getTenantSchemas(): Promise<string[]> {
        try {
            const result = await this.publicDataSource.query(`
                SELECT schema_name 
                FROM information_schema.schemata 
                WHERE schema_name LIKE 'tenant_%'
                AND schema_name != 'public'
                ORDER BY schema_name
            `);
            return result.map((row: any) => row.schema_name);
        } catch (error) {
            Logger.error('Error fetching tenant schemas:', error);
            throw error;
        }
    }

    private getTenantIdFromSchema(schemaName: string): string {
        const tenantId = getTenantIdFromSchemaName(schemaName);
        if (!tenantId) {
            throw new Error(`Invalid tenant schema name: ${schemaName}`);
        }
        return tenantId;
    }

    async seedArrowRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        // Create Arrow Rate Card
        const arrowRateCard = rateCardRepository.create({
            providerName: 'Arrow Conveyancing',
            providerCode: RateCardProvider.ARROW,
            displayName: 'Arrow Conveyancing Rate Card',
            description:
                'Comprehensive Arrow Conveyancing fee structure with detailed disbursements',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: true,
            priority: 1,
            metadata: {
                format: 'multi-column',
                hasNewBuild: true,
                hasLtdCompany: true
            }
        });

        const savedRateCard = await rateCardRepository.save(arrowRateCard);

        // Arrow Purchase Legal Fees
        const arrowPurchaseFees = [
            { start: 0, end: 100000, fee: 549 },
            { start: 100001, end: 250000, fee: 575 },
            { start: 250001, end: 500000, fee: 599 },
            { start: 500001, end: 750000, fee: 649 },
            { start: 750001, end: 1000000, fee: 749 },
            { start: 1000001, end: 2000000, fee: 899 },
            { start: 2000001, end: *********, fee: 1149 }
        ];

        // Arrow Sale Legal Fees
        const arrowSaleFees = [
            { start: 0, end: 250000, fee: 449 },
            { start: 250001, end: 500000, fee: 549 },
            { start: 500001, end: 750000, fee: 649 },
            { start: 750001, end: 1000000, fee: 749 },
            { start: 1000001, end: 2000000, fee: 875 },
            { start: 2000001, end: *********, fee: 950 }
        ];

        // Arrow Remortgage Legal Fees
        const arrowRemortgageFees = [
            { start: 0, end: 1000000, fee: 350 },
            { start: 1000001, end: 2000000, fee: 450 }
        ];

        // Arrow Disbursements
        const arrowDisbursements = [
            { label: 'SDLT Admin', fee: 100, applicableFor: 'buy', conditionSlug: undefined },
            { label: 'Lawyer Checker', fee: 15, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Bank Transfer Fee',
                fee: 45,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'AML check',
                fee: 25,
                applicableFor: 'buy,sell',
                conditionSlug: undefined,
                perParty: true
            },
            {
                label: 'Buying with mort',
                fee: 100,
                applicableFor: 'buy',
                conditionSlug: 'buying_with_mortgage'
            },
            {
                label: 'Gifted Deposit Fee',
                fee: 100,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'Shared Ownership',
                fee: 200,
                applicableFor: 'buy,sell',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Leasehold Fee',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Help To Buy ISA',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'help_to_buy_isa'
            },
            {
                label: 'Online Tracking',
                fee: 50,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Excess Disbursement',
                fee: 50,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            { label: 'Dealing with BSA', fee: 500, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'New Build Fee',
                fee: 150,
                applicableFor: 'buy',
                conditionSlug: 'is_new_build'
            },
            { label: 'Search Pack', fee: 329, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Completion Searches',
                fee: 25,
                applicableFor: 'buy',
                conditionSlug: undefined
            },
            {
                label: 'LTD Company Fee',
                fee: 150,
                applicableFor: 'buy',
                conditionSlug: 'company_or_trust'
            },
            {
                label: 'Companies House Search Fee',
                fee: 25,
                applicableFor: 'buy',
                conditionSlug: 'company_or_trust'
            },
            { label: 'MRO1 Fee', fee: 25, applicableFor: 'buy', conditionSlug: 'company_or_trust' },
            {
                label: 'Companies House Winding Up Fee',
                fee: 25,
                applicableFor: 'buy',
                conditionSlug: 'company_or_trust'
            }
        ];

        // Create fee items
        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        arrowPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        arrowSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        arrowRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        arrowDisbursements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: item.perParty || false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Arrow rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedCharlesCameronRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        // Create Charles Cameron Rate Card
        const charlesCameronRateCard = rateCardRepository.create({
            providerName: 'Charles Cameron',
            providerCode: RateCardProvider.CHARLES_CAMERON,
            displayName: 'Charles Cameron Existing Solicitor Rate Card',
            description: 'Optimus format rate card with two-tier pricing structure',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 2,
            metadata: {
                format: 'optimus-three-column',
                pricingModel: 'two-tier',
                hasClientFees: true
            }
        });

        const savedRateCard = await rateCardRepository.save(charlesCameronRateCard);

        // Charles Cameron Purchase Legal Fees (Client Fee)
        const charlesCameronPurchaseFees = [
            { start: 0, end: 100000, fee: 600 },
            { start: 100001, end: 200000, fee: 630 },
            { start: 200001, end: 300000, fee: 645 },
            { start: 300001, end: 400000, fee: 660 },
            { start: 400001, end: 500000, fee: 675 },
            { start: 500001, end: 750000, fee: 775 },
            { start: 750001, end: 1000000, fee: 825 },
            { start: 1000001, end: 1250000, fee: 875 },
            { start: 1250001, end: 1500000, fee: 925 }
        ];

        // Charles Cameron Sale Legal Fees (Client Fee)
        const charlesCameronSaleFees = [
            { start: 0, end: 100000, fee: 575 },
            { start: 100001, end: 200000, fee: 590 },
            { start: 200001, end: 300000, fee: 605 },
            { start: 300001, end: 400000, fee: 620 },
            { start: 400001, end: 500000, fee: 635 },
            { start: 500001, end: 750000, fee: 735 },
            { start: 750001, end: 1000000, fee: 785 },
            { start: 1000001, end: 1250000, fee: 835 },
            { start: 1250001, end: 1500000, fee: 885 }
        ];

        // Charles Cameron Remortgage Legal Fees (Client Fee)
        const charlesCameronRemortgageFees = [
            { start: 0, end: 100000, fee: 201.67 },
            { start: 100001, end: 200000, fee: 193.33 },
            { start: 200001, end: 500000, fee: 185 },
            { start: 500001, end: 1000000, fee: 185 },
            { start: 1000001, end: *********, fee: 185 }
        ];

        // Charles Cameron Disbursements
        const charlesCameronDisbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 322.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // Charles Cameron Supplements
        const charlesCameronSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 125,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        // Create fee items
        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        charlesCameronPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        charlesCameronSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        charlesCameronRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        charlesCameronDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0, // Already inc VAT
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        charlesCameronSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Charles Cameron rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedFortAdviceBureauRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const fortAdviceRateCard = rateCardRepository.create({
            providerName: 'Fort Advice Bureau',
            providerCode: RateCardProvider.FORT_ADVICE,
            displayName: 'Fort Advice Bureau Rate Card',
            description: 'Complex multi-tier pricing with adjustable fees',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 5,
            metadata: {
                format: 'optimus-multi-tier',
                pricingModel: 'five-tier',
                hasAdjustableFees: true
            }
        });

        const savedRateCard = await rateCardRepository.save(fortAdviceRateCard);

        // Fort Advice Bureau Purchase Legal Fees (Client Fee)
        const fortAdvicePurchaseFees = [
            { start: 0, end: 125000, fee: 705 },
            { start: 125001, end: 250000, fee: 740 },
            { start: 250001, end: 325000, fee: 755 },
            { start: 325001, end: 400000, fee: 780 },
            { start: 400001, end: 500000, fee: 775 },
            { start: 500001, end: 750000, fee: 890 },
            { start: 750001, end: 1000000, fee: 980 },
            { start: 1000001, end: 1250000, fee: 1240 },
            { start: 1250001, end: 1500000, fee: 1355 }
        ];

        // Fort Advice Bureau Sale Legal Fees (Client Fee)
        const fortAdviceSaleFees = [
            { start: 0, end: 125000, fee: 730 },
            { start: 125001, end: 250000, fee: 780 },
            { start: 250001, end: 325000, fee: 780 },
            { start: 325001, end: 400000, fee: 830 },
            { start: 400001, end: 500000, fee: 830 },
            { start: 500001, end: 750000, fee: 910 },
            { start: 750001, end: 1000000, fee: 980 },
            { start: 1000001, end: 1250000, fee: 1285 },
            { start: 1250001, end: 1500000, fee: 1385 }
        ];

        // Fort Advice Bureau Remortgage Legal Fees (Client Fee)
        const fortAdviceRemortgageFees = [
            { start: 0, end: 100000, fee: 201.67 },
            { start: 100001, end: 200000, fee: 193.33 },
            { start: 200001, end: 500000, fee: 185 },
            { start: 500001, end: 1000000, fee: 185 },
            { start: 1000001, end: *********, fee: 185 }
        ];

        // Fort Advice Bureau Disbursements
        const fortAdviceDisbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 347.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // Fort Advice Bureau Supplements
        const fortAdviceSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 250,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        fortAdvicePurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        fortAdviceSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        fortAdviceRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        fortAdviceDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        fortAdviceSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Fort Advice Bureau rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedGazealRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const gazealRateCard = rateCardRepository.create({
            providerName: 'Gazeal',
            providerCode: RateCardProvider.GAZEAL,
            displayName: 'Gazeal Solicitor Rate Card',
            description: 'Simple two-tier pricing structure',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 6,
            metadata: {
                format: 'simple-two-tier',
                pricingModel: 'two-tier',
                hasRemortgage: false
            }
        });

        const savedRateCard = await rateCardRepository.save(gazealRateCard);

        // Gazeal Purchase Legal Fees (Client Fee)
        const gazealPurchaseFees = [
            { start: 0, end: 100000, fee: 645 },
            { start: 100001, end: 200000, fee: 675 },
            { start: 200001, end: 300000, fee: 690 },
            { start: 300001, end: 400000, fee: 705 },
            { start: 400001, end: 500000, fee: 720 },
            { start: 500001, end: 750000, fee: 820 },
            { start: 750001, end: 1000000, fee: 870 },
            { start: 1000001, end: 1250000, fee: 920 },
            { start: 1250001, end: 1500000, fee: 970 }
        ];

        // Gazeal Sale Legal Fees (Client Fee)
        const gazealSaleFees = [
            { start: 0, end: 100000, fee: 620 },
            { start: 100001, end: 200000, fee: 635 },
            { start: 200001, end: 300000, fee: 650 },
            { start: 300001, end: 400000, fee: 665 },
            { start: 400001, end: 500000, fee: 680 },
            { start: 500001, end: 750000, fee: 780 },
            { start: 750001, end: 1000000, fee: 830 },
            { start: 1000001, end: 1250000, fee: 880 },
            { start: 1250001, end: 1500000, fee: 930 }
        ];

        // Gazeal Disbursements
        const gazealDisbursements = [
            { label: 'CHAPS Fee', fee: 36, applicableFor: 'buy,sell', conditionSlug: undefined },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 347.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'sell', conditionSlug: undefined }
        ];

        // Gazeal Supplements
        const gazealSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            { label: 'Leasehold', fee: 250, applicableFor: 'buy,sell', conditionSlug: 'leasehold' },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        gazealPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        gazealSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        gazealDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        gazealSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Gazeal rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedHaystoRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const haystoRateCard = rateCardRepository.create({
            providerName: 'Haysto',
            providerCode: RateCardProvider.HAYSTO,
            displayName: 'Haysto Solicitor Rate Card',
            description: 'Mixed pricing model with different VAT handling for remortgage',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 7,
            metadata: {
                format: 'optimus-mixed-vat',
                pricingModel: 'mixed',
                remortgageVatInclusive: true
            }
        });

        const savedRateCard = await rateCardRepository.save(haystoRateCard);

        // Haysto Purchase Legal Fees (Client Fee)
        const haystoPurchaseFees = [
            { start: 0, end: 100000, fee: 795 },
            { start: 100001, end: 200000, fee: 825 },
            { start: 200001, end: 300000, fee: 840 },
            { start: 300001, end: 400000, fee: 855 },
            { start: 400001, end: 500000, fee: 870 },
            { start: 500001, end: 750000, fee: 970 },
            { start: 750001, end: 1000000, fee: 1020 },
            { start: 1000001, end: 1250000, fee: 1070 },
            { start: 1250001, end: 1500000, fee: 1120 }
        ];

        // Haysto Sale Legal Fees (Client Fee)
        const haystoSaleFees = [
            { start: 0, end: 100000, fee: 770 },
            { start: 100001, end: 200000, fee: 785 },
            { start: 200001, end: 300000, fee: 800 },
            { start: 300001, end: 400000, fee: 815 },
            { start: 400001, end: 500000, fee: 830 },
            { start: 500001, end: 750000, fee: 930 },
            { start: 750001, end: 1000000, fee: 980 },
            { start: 1000001, end: 1250000, fee: 1030 },
            { start: 1250001, end: 1500000, fee: 1080 }
        ];

        // Haysto Remortgage Legal Fees (Overall Fee to Client - inc. VAT)
        const haystoRemortgageFees = [
            { start: 0, end: 100000, fee: 330 },
            { start: 100001, end: 200000, fee: 330 },
            { start: 200001, end: 500000, fee: 335 },
            { start: 500001, end: 1000000, fee: 355 },
            { start: 1000001, end: *********, fee: 420 }
        ];

        // Haysto Disbursements
        const haystoDisbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 347.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'sell,remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // Haysto Supplements
        const haystoSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 250,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        haystoPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        haystoSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees (VAT inclusive)
        haystoRemortgageFees.forEach((range, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee: 0,
                    totalFee: range.fee,
                    vatType: VatType.INC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        haystoDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        haystoSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Haysto rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedIndependentRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const independentRateCard = rateCardRepository.create({
            providerName: 'Independent',
            providerCode: RateCardProvider.INDEPENDENT,
            displayName: 'Independent Solicitor Rate Card',
            description: 'Standard Optimus format with two-tier pricing',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 8,
            metadata: {
                format: 'optimus-three-column',
                pricingModel: 'two-tier',
                identicalTo: 'charles_cameron'
            }
        });

        const savedRateCard = await rateCardRepository.save(independentRateCard);

        // Independent Purchase Legal Fees (Client Fee) - Same as Charles Cameron
        const independentPurchaseFees = [
            { start: 0, end: 100000, fee: 600 },
            { start: 100001, end: 200000, fee: 630 },
            { start: 200001, end: 300000, fee: 645 },
            { start: 300001, end: 400000, fee: 660 },
            { start: 400001, end: 500000, fee: 675 },
            { start: 500001, end: 750000, fee: 775 },
            { start: 750001, end: 1000000, fee: 825 },
            { start: 1000001, end: 1250000, fee: 875 },
            { start: 1250001, end: 1500000, fee: 925 }
        ];

        // Independent Sale Legal Fees (Client Fee) - Same as Charles Cameron
        const independentSaleFees = [
            { start: 0, end: 100000, fee: 575 },
            { start: 100001, end: 200000, fee: 590 },
            { start: 200001, end: 300000, fee: 605 },
            { start: 300001, end: 400000, fee: 620 },
            { start: 400001, end: 500000, fee: 635 },
            { start: 500001, end: 750000, fee: 735 },
            { start: 750001, end: 1000000, fee: 785 },
            { start: 1000001, end: 1250000, fee: 835 },
            { start: 1250001, end: 1500000, fee: 885 }
        ];

        // Independent Remortgage Legal Fees (Client Fee) - Same as Charles Cameron
        const independentRemortgageFees = [
            { start: 0, end: 100000, fee: 201.67 },
            { start: 100001, end: 200000, fee: 193.33 },
            { start: 200001, end: 500000, fee: 185 },
            { start: 500001, end: 1000000, fee: 185 },
            { start: 1000001, end: *********, fee: 185 }
        ];

        // Independent Disbursements - Same as Charles Cameron
        const independentDisbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 347.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // Independent Supplements - Same as Charles Cameron
        const independentSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 125,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        independentPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        independentSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        independentRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        independentDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        independentSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Independent rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedJohnCharcolRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const johnCharcolRateCard = rateCardRepository.create({
            providerName: 'John Charcol',
            providerCode: RateCardProvider.JOHN_CHARCOL,
            displayName: 'John Charcol Existing Solicitor Rate Card',
            description: 'Standard Optimus format with variations in pricing',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 9,
            metadata: {
                format: 'optimus-three-column',
                pricingModel: 'two-tier',
                variations: ['gifted_deposit_75']
            }
        });

        const savedRateCard = await rateCardRepository.save(johnCharcolRateCard);

        // John Charcol Purchase Legal Fees (Client Fee)
        const johnCharcolPurchaseFees = [
            { start: 0, end: 100000, fee: 625 },
            { start: 100001, end: 200000, fee: 655 },
            { start: 200001, end: 300000, fee: 670 },
            { start: 300001, end: 400000, fee: 685 },
            { start: 400001, end: 500000, fee: 700 },
            { start: 500001, end: 750000, fee: 800 },
            { start: 750001, end: 1000000, fee: 850 },
            { start: 1000001, end: 1250000, fee: 900 },
            { start: 1250001, end: 1500000, fee: 950 }
        ];

        // John Charcol Sale Legal Fees (Client Fee)
        const johnCharcolSaleFees = [
            { start: 0, end: 100000, fee: 525 },
            { start: 100001, end: 200000, fee: 540 },
            { start: 200001, end: 300000, fee: 555 },
            { start: 300001, end: 400000, fee: 570 },
            { start: 400001, end: 500000, fee: 585 },
            { start: 500001, end: 750000, fee: 685 },
            { start: 750001, end: 1000000, fee: 735 },
            { start: 1000001, end: 1250000, fee: 785 },
            { start: 1250001, end: 1500000, fee: 835 }
        ];

        // John Charcol Remortgage Legal Fees (Client Fee)
        const johnCharcolRemortgageFees = [
            { start: 0, end: 100000, fee: 201.67 },
            { start: 100001, end: 200000, fee: 193.33 },
            { start: 200001, end: 500000, fee: 185 },
            { start: 500001, end: 1000000, fee: 185 },
            { start: 1000001, end: *********, fee: 185 }
        ];

        // John Charcol Disbursements
        const johnCharcolDisbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 322.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // John Charcol Supplements
        const johnCharcolSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 75,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            }, // Different from others
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 125,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        johnCharcolPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        johnCharcolSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        johnCharcolRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        johnCharcolDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        johnCharcolSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded John Charcol rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedKeyClub2025RateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const keyClub2025RateCard = rateCardRepository.create({
            providerName: 'Key Club',
            providerCode: RateCardProvider.KEYCLUB,
            displayName: 'Key Club Solicitor Rate Card - E&W 01032025',
            description: 'Platinum Rate Card with updated pricing for 2025',
            version: '2.0',
            effectiveDate: new Date('2025-03-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 10,
            metadata: {
                format: 'optimus-three-column',
                pricingModel: 'two-tier',
                version: '2025',
                searchPackFee: 369.0
            }
        });

        const savedRateCard = await rateCardRepository.save(keyClub2025RateCard);

        // Key Club 2025 Purchase Legal Fees (Client Fee)
        const keyClub2025PurchaseFees = [
            { start: 0, end: 100000, fee: 512.5 },
            { start: 100001, end: 200000, fee: 542.5 },
            { start: 200001, end: 300000, fee: 557.5 },
            { start: 300001, end: 400000, fee: 572.5 },
            { start: 400001, end: 500000, fee: 587.5 },
            { start: 500001, end: 750000, fee: 687.5 },
            { start: 750001, end: 1000000, fee: 737.5 },
            { start: 1000001, end: 1250000, fee: 787.5 },
            { start: 1250001, end: 1500000, fee: 837.5 }
        ];

        // Key Club 2025 Sale Legal Fees (Client Fee)
        const keyClub2025SaleFees = [
            { start: 0, end: 100000, fee: 487.5 },
            { start: 100001, end: 200000, fee: 502.5 },
            { start: 200001, end: 300000, fee: 517.5 },
            { start: 300001, end: 400000, fee: 532.5 },
            { start: 400001, end: 500000, fee: 547.5 },
            { start: 500001, end: 750000, fee: 647.5 },
            { start: 750001, end: 1000000, fee: 697.5 },
            { start: 1000001, end: 1250000, fee: 747.5 },
            { start: 1250001, end: 1500000, fee: 797.5 }
        ];

        // Key Club 2025 Remortgage Legal Fees (Client Fee)
        const keyClub2025RemortgageFees = [
            { start: 0, end: 100000, fee: 201.67 },
            { start: 100001, end: 200000, fee: 193.33 },
            { start: 200001, end: 500000, fee: 185 },
            { start: 500001, end: 1000000, fee: 185 },
            { start: 1000001, end: *********, fee: 185 }
        ];

        // Key Club 2025 Disbursements
        const keyClub2025Disbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 369.0, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // Key Club 2025 Supplements
        const keyClub2025Supplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 125,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        keyClub2025PurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        keyClub2025SaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        keyClub2025RemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        keyClub2025Disbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        keyClub2025Supplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Key Club 2025 rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedKeyClub2024RateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const keyClub2024RateCard = rateCardRepository.create({
            providerName: 'Key Club',
            providerCode: RateCardProvider.KEYCLUB,
            displayName: 'Key Club Solicitor Rate Card - EW 04032024',
            description: 'Platinum Rate Card with 2024 pricing',
            version: '1.0',
            effectiveDate: new Date('2024-03-04'),
            status: RateCardStatus.INACTIVE, // Older version
            isDefault: false,
            priority: 11,
            metadata: {
                format: 'optimus-three-column',
                pricingModel: 'two-tier',
                version: '2024',
                searchPackFee: 347.8
            }
        });

        const savedRateCard = await rateCardRepository.save(keyClub2024RateCard);

        // Key Club 2024 Purchase Legal Fees (Client Fee) - Lower than 2025
        const keyClub2024PurchaseFees = [
            { start: 0, end: 100000, fee: 475 },
            { start: 100001, end: 200000, fee: 505 },
            { start: 200001, end: 300000, fee: 520 },
            { start: 300001, end: 400000, fee: 535 },
            { start: 400001, end: 500000, fee: 550 },
            { start: 500001, end: 750000, fee: 650 },
            { start: 750001, end: 1000000, fee: 700 },
            { start: 1000001, end: 1250000, fee: 750 },
            { start: 1250001, end: 1500000, fee: 800 }
        ];

        // Key Club 2024 Sale Legal Fees (Client Fee) - Lower than 2025
        const keyClub2024SaleFees = [
            { start: 0, end: 100000, fee: 450 },
            { start: 100001, end: 200000, fee: 465 },
            { start: 200001, end: 300000, fee: 480 },
            { start: 300001, end: 400000, fee: 495 },
            { start: 400001, end: 500000, fee: 510 },
            { start: 500001, end: 750000, fee: 610 },
            { start: 750001, end: 1000000, fee: 660 },
            { start: 1000001, end: 1250000, fee: 710 },
            { start: 1250001, end: 1500000, fee: 760 }
        ];

        // Key Club 2024 Remortgage Legal Fees (Client Fee) - Same as 2025
        const keyClub2024RemortgageFees = [
            { start: 0, end: 100000, fee: 201.67 },
            { start: 100001, end: 200000, fee: 193.33 },
            { start: 200001, end: 500000, fee: 185 },
            { start: 500001, end: 1000000, fee: 185 },
            { start: 1000001, end: *********, fee: 185 }
        ];

        // Key Club 2024 Disbursements - Standard fees
        const keyClub2024Disbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 347.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // Key Club 2024 Supplements - Same as 2025
        const keyClub2024Supplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 125,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        keyClub2024PurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        keyClub2024SaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        keyClub2024RemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        keyClub2024Disbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        keyClub2024Supplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Key Club 2024 rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedLCJune2024RateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const lcJune2024RateCard = rateCardRepository.create({
            providerName: 'L&C',
            providerCode: RateCardProvider.LANDC,
            displayName: 'L&C Solicitor Rate Card - E&C 03062024',
            description: 'L&C Rate Card with complex conditional fee reductions',
            version: '1.0',
            effectiveDate: new Date('2024-06-03'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 12,
            metadata: {
                format: 'optimus-complex-conditions',
                pricingModel: 'two-tier',
                version: 'june-2024',
                hasConditionalReductions: true
            }
        });

        const savedRateCard = await rateCardRepository.save(lcJune2024RateCard);

        // L&C June 2024 Purchase Legal Fees (Client Fee)
        const lcJune2024PurchaseFees = [
            { start: 0, end: 125000, fee: 715 },
            { start: 125001, end: 250000, fee: 735 },
            { start: 250001, end: 325000, fee: 755 },
            { start: 325001, end: 400000, fee: 780 },
            { start: 400001, end: 500000, fee: 825 },
            { start: 500001, end: 750000, fee: 940 },
            { start: 750001, end: 1000000, fee: 1080 },
            { start: 1000001, end: 1250000, fee: 1340 },
            { start: 1250001, end: 1500000, fee: 1455 },
            { start: 1500001, end: 1750000, fee: 1505 },
            { start: 1750001, end: 2000000, fee: 1555 },
            { start: 2000001, end: *********, fee: 1780 }
        ];

        // L&C June 2024 Sale Legal Fees (Client Fee)
        const lcJune2024SaleFees = [
            { start: 0, end: 125000, fee: 730 },
            { start: 125001, end: 250000, fee: 750 },
            { start: 250001, end: 325000, fee: 780 },
            { start: 325001, end: 400000, fee: 830 },
            { start: 400001, end: 500000, fee: 880 },
            { start: 500001, end: 750000, fee: 960 },
            { start: 750001, end: 1000000, fee: 1080 },
            { start: 1000001, end: 1250000, fee: 1355 },
            { start: 1250001, end: 1500000, fee: 1455 },
            { start: 1500001, end: 1750000, fee: 1505 },
            { start: 1750001, end: 2000000, fee: 1580 },
            { start: 2000001, end: *********, fee: 1780 }
        ];

        // L&C June 2024 Remortgage Legal Fees (Client Fee)
        const lcJune2024RemortgageFees = [
            { start: 0, end: 100000, fee: 249.17 },
            { start: 100001, end: 200000, fee: 249.17 },
            { start: 200001, end: 500000, fee: 249.17 },
            { start: 500001, end: 1000000, fee: 249.17 },
            { start: 1000001, end: *********, fee: 249.17 }
        ];

        // L&C June 2024 Disbursements
        const lcJune2024Disbursements = [
            {
                label: 'CHAPS Fee',
                fee: 42,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 18,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 25,
                applicableFor: 'buy',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 329.0, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // L&C June 2024 Supplements
        const lcJune2024Supplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 100,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 125,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 275,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 75,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            {
                label: 'Right to Buy',
                fee: 150,
                applicableFor: 'buy',
                conditionSlug: 'right_to_buy'
            },
            { label: 'SDLT Form', fee: 75, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 450,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 195,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        lcJune2024PurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        lcJune2024SaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        lcJune2024RemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        lcJune2024Disbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        lcJune2024Supplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded L&C June 2024 rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedLCNovember2024RateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const lcNovember2024RateCard = rateCardRepository.create({
            providerName: 'L&C',
            providerCode: RateCardProvider.LANDC,
            displayName: 'L&C Solicitor Rate Card - E&W 29112024',
            description: 'L&C Rate Card with updated remortgage fees',
            version: '2.0',
            effectiveDate: new Date('2024-11-29'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 13,
            metadata: {
                format: 'optimus-complex-conditions',
                pricingModel: 'two-tier',
                version: 'november-2024',
                hasConditionalReductions: true,
                updatedRemortgageFees: true
            }
        });

        const savedRateCard = await rateCardRepository.save(lcNovember2024RateCard);

        // L&C November 2024 Purchase Legal Fees (Client Fee) - Same as June
        const lcNovember2024PurchaseFees = [
            { start: 0, end: 125000, fee: 715 },
            { start: 125001, end: 250000, fee: 735 },
            { start: 250001, end: 325000, fee: 755 },
            { start: 325001, end: 400000, fee: 780 },
            { start: 400001, end: 500000, fee: 825 },
            { start: 500001, end: 750000, fee: 940 },
            { start: 750001, end: 1000000, fee: 1080 },
            { start: 1000001, end: 1250000, fee: 1340 },
            { start: 1250001, end: 1500000, fee: 1455 },
            { start: 1500001, end: 1750000, fee: 1505 },
            { start: 1750001, end: 2000000, fee: 1555 },
            { start: 2000001, end: *********, fee: 1780 }
        ];

        // L&C November 2024 Sale Legal Fees (Client Fee) - Same as June
        const lcNovember2024SaleFees = [
            { start: 0, end: 125000, fee: 730 },
            { start: 125001, end: 250000, fee: 750 },
            { start: 250001, end: 325000, fee: 780 },
            { start: 325001, end: 400000, fee: 830 },
            { start: 400001, end: 500000, fee: 880 },
            { start: 500001, end: 750000, fee: 960 },
            { start: 750001, end: 1000000, fee: 1080 },
            { start: 1000001, end: 1250000, fee: 1355 },
            { start: 1250001, end: 1500000, fee: 1455 },
            { start: 1500001, end: 1750000, fee: 1505 },
            { start: 1750001, end: 2000000, fee: 1580 },
            { start: 2000001, end: *********, fee: 1780 }
        ];

        // L&C November 2024 Remortgage Legal Fees (Client Fee) - Updated
        const lcNovember2024RemortgageFees = [
            { start: 0, end: 100000, fee: 257.5 },
            { start: 100001, end: 200000, fee: 257.5 },
            { start: 200001, end: 500000, fee: 257.5 },
            { start: 500001, end: 1000000, fee: 257.5 },
            { start: 1000001, end: *********, fee: 257.5 }
        ];

        // L&C November 2024 Disbursements - Updated OCE's fee
        const lcNovember2024Disbursements = [
            {
                label: 'CHAPS Fee',
                fee: 42,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 18,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 25,
                applicableFor: 'buy',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 329.0, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 16, applicableFor: 'remortgage', conditionSlug: undefined }, // Updated from 6
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // L&C November 2024 Supplements - Same as June
        const lcNovember2024Supplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 100,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 125,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 275,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 75,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            {
                label: 'Right to Buy',
                fee: 150,
                applicableFor: 'buy',
                conditionSlug: 'right_to_buy'
            },
            { label: 'SDLT Form', fee: 75, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 450,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 195,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        lcNovember2024PurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        lcNovember2024SaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        lcNovember2024RemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        lcNovember2024Disbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        lcNovember2024Supplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded L&C November 2024 rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedLEASRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const leasRateCard = rateCardRepository.create({
            providerName: 'LEAS',
            providerCode: RateCardProvider.LEAS,
            displayName: 'LEAS Solicitor Rate Card - E&W 01102024',
            description: 'LEAS Rate Card with no remortgage fees',
            version: '1.0',
            effectiveDate: new Date('2024-10-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 14,
            metadata: {
                format: 'optimus-two-column',
                pricingModel: 'two-tier',
                version: '2024',
                hasRemortgage: false,
                leaseholdFee: 250
            }
        });

        const savedRateCard = await rateCardRepository.save(leasRateCard);

        // LEAS Purchase Legal Fees (Client Fee)
        const leasPurchaseFees = [
            { start: 0, end: 100000, fee: 650 },
            { start: 100001, end: 200000, fee: 680 },
            { start: 200001, end: 300000, fee: 695 },
            { start: 300001, end: 400000, fee: 710 },
            { start: 400001, end: 500000, fee: 725 },
            { start: 500001, end: 750000, fee: 825 },
            { start: 750001, end: 1000000, fee: 875 },
            { start: 1000001, end: 1250000, fee: 925 },
            { start: 1250001, end: 1500000, fee: 975 }
        ];

        // LEAS Sale Legal Fees (Client Fee)
        const leasSaleFees = [
            { start: 0, end: 100000, fee: 620 },
            { start: 100001, end: 200000, fee: 635 },
            { start: 200001, end: 300000, fee: 650 },
            { start: 300001, end: 400000, fee: 665 },
            { start: 400001, end: 500000, fee: 680 },
            { start: 500001, end: 750000, fee: 780 },
            { start: 750001, end: 1000000, fee: 830 },
            { start: 1000001, end: 1250000, fee: 880 },
            { start: 1250001, end: 1500000, fee: 930 }
        ];

        // LEAS Disbursements
        const leasDisbursements = [
            { label: 'CHAPS Fee', fee: 36, applicableFor: 'buy,sell', conditionSlug: undefined },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 347.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: 'OCEs', fee: 6, applicableFor: 'sell', conditionSlug: undefined }
        ];

        // LEAS Supplements
        const leasSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            { label: 'Leasehold', fee: 250, applicableFor: 'buy,sell', conditionSlug: 'leasehold' },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        leasPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        leasSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        leasDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        leasSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded LEAS rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedMojoRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const mojoRateCard = rateCardRepository.create({
            providerName: 'Mojo',
            providerCode: RateCardProvider.MOJO,
            displayName: 'Mojo Existing Solicitor Rate Card',
            description: 'Mojo Rate Card with lower fees',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 15,
            metadata: {
                format: 'optimus-three-column',
                pricingModel: 'two-tier',
                version: 'current',
                searchPackFee: 322.8
            }
        });

        const savedRateCard = await rateCardRepository.save(mojoRateCard);

        // Mojo Purchase Legal Fees (Client Fee)
        const mojoPurchaseFees = [
            { start: 0, end: 100000, fee: 600 },
            { start: 100001, end: 200000, fee: 630 },
            { start: 200001, end: 300000, fee: 645 },
            { start: 300001, end: 400000, fee: 660 },
            { start: 400001, end: 500000, fee: 675 },
            { start: 500001, end: 750000, fee: 775 },
            { start: 750001, end: 1000000, fee: 825 },
            { start: 1000001, end: 1250000, fee: 875 },
            { start: 1250001, end: 1500000, fee: 925 }
        ];

        // Mojo Sale Legal Fees (Client Fee)
        const mojoSaleFees = [
            { start: 0, end: 100000, fee: 475 },
            { start: 100001, end: 200000, fee: 490 },
            { start: 200001, end: 300000, fee: 505 },
            { start: 300001, end: 400000, fee: 520 },
            { start: 400001, end: 500000, fee: 535 },
            { start: 500001, end: 750000, fee: 635 },
            { start: 750001, end: 1000000, fee: 685 },
            { start: 1000001, end: 1250000, fee: 735 },
            { start: 1250001, end: 1500000, fee: 785 }
        ];

        // Mojo Remortgage Legal Fees (Client Fee)
        const mojoRemortgageFees = [
            { start: 0, end: 100000, fee: 201.67 },
            { start: 100001, end: 200000, fee: 193.33 },
            { start: 200001, end: 500000, fee: 185 },
            { start: 500001, end: 1000000, fee: 185 },
            { start: 1000001, end: *********, fee: 185 }
        ];

        // Mojo Disbursements
        const mojoDisbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 322.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // Mojo Supplements
        const mojoSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 125,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        mojoPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        mojoSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        mojoRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        mojoDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        mojoSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Mojo rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedMoloRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const moloRateCard = rateCardRepository.create({
            providerName: 'Molo',
            providerCode: RateCardProvider.MOLO,
            displayName: 'Molo Existing Solictor Rate Card - E&W',
            description: 'Complex multi-representation rate card with 8 fee structures',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 16,
            metadata: {
                format: 'molo-multi-representation',
                pricingModel: 'complex-eight-tier',
                version: 'current',
                hasMultipleRepresentations: true,
                representations: [
                    'sep_rep_individual',
                    'sep_rep_ltd',
                    'dual_rep_individual',
                    'dual_rep_ltd',
                    'independent_legal'
                ]
            }
        });

        const savedRateCard = await rateCardRepository.save(moloRateCard);

        // Molo Sep Rep Individual Purchase Legal Fees (Client Fee)
        const moloSepRepIndividualPurchaseFees = [
            { start: 0, end: 150000, fee: 450 },
            { start: 150001, end: 200000, fee: 450 },
            { start: 200001, end: 300000, fee: 450 },
            { start: 300001, end: 400000, fee: 450 },
            { start: 400001, end: 500000, fee: 450 },
            { start: 500001, end: 750000, fee: 500 },
            { start: 750001, end: 1000000, fee: 500 },
            { start: 1000001, end: 1500000, fee: 650 },
            { start: 1500001, end: 2000000, fee: 700 }
        ];

        // Molo Sep Rep Individual Remortgage Legal Fees (Client Fee)
        const moloSepRepIndividualRemortgageFees = [
            { start: 0, end: 150000, fee: 400 },
            { start: 150001, end: 200000, fee: 400 },
            { start: 200001, end: 300000, fee: 400 },
            { start: 300001, end: 400000, fee: 400 },
            { start: 400001, end: 500000, fee: 400 },
            { start: 500001, end: 750000, fee: 400 },
            { start: 750001, end: 1000000, fee: 400 },
            { start: 1000001, end: 1500000, fee: 550 },
            { start: 1500001, end: 2000000, fee: 600 }
        ];

        // Molo Sep Rep Ltd Company Purchase Legal Fees (Client Fee)
        const moloSepRepLtdPurchaseFees = [
            { start: 0, end: 150000, fee: 500 },
            { start: 150001, end: 200000, fee: 500 },
            { start: 200001, end: 300000, fee: 500 },
            { start: 300001, end: 400000, fee: 500 },
            { start: 400001, end: 500000, fee: 500 },
            { start: 500001, end: 750000, fee: 550 },
            { start: 750001, end: 1000000, fee: 550 },
            { start: 1000001, end: 1500000, fee: 700 },
            { start: 1500001, end: 4000000, fee: 750 }
        ];

        // Molo Sep Rep Ltd Company Remortgage Legal Fees (Client Fee)
        const moloSepRepLtdRemortgageFees = [
            { start: 0, end: 150000, fee: 450 },
            { start: 150001, end: 200000, fee: 450 },
            { start: 200001, end: 300000, fee: 450 },
            { start: 300001, end: 400000, fee: 450 },
            { start: 400001, end: 500000, fee: 450 },
            { start: 500001, end: 750000, fee: 450 },
            { start: 750001, end: 1000000, fee: 450 },
            { start: 1000001, end: 1500000, fee: 600 },
            { start: 1500001, end: 2000000, fee: 650 }
        ];

        // Molo Dual Rep Individual Purchase Legal Fees (Client Fee)
        const moloDualRepIndividualPurchaseFees = [
            { start: 0, end: 150000, fee: 340 },
            { start: 150001, end: 200000, fee: 420 },
            { start: 200001, end: 300000, fee: 420 },
            { start: 300001, end: 400000, fee: 420 },
            { start: 400001, end: 500000, fee: 420 },
            { start: 500001, end: 750000, fee: 570 },
            { start: 750001, end: 1000000, fee: 570 },
            { start: 1000001, end: 1500000, fee: 800 },
            { start: 1500001, end: 4000000, fee: 800 }
        ];

        // Molo Dual Rep Individual Remortgage Legal Fees (Client Fee)
        const moloDualRepIndividualRemortgageFees = [
            { start: 0, end: 150000, fee: 165 },
            { start: 150001, end: 200000, fee: 165 },
            { start: 200001, end: 300000, fee: 165 },
            { start: 300001, end: 400000, fee: 165 },
            { start: 400001, end: 500000, fee: 165 },
            { start: 500001, end: 750000, fee: 165 },
            { start: 750001, end: 1000000, fee: 165 },
            { start: 1000001, end: 1500000, fee: 199 },
            { start: 1500001, end: 2000000, fee: 199 }
        ];

        // Molo Dual Rep Ltd Company Purchase Legal Fees (Client Fee)
        const moloDualRepLtdPurchaseFees = [
            { start: 0, end: 150000, fee: 675 },
            { start: 150001, end: 200000, fee: 675 },
            { start: 200001, end: 300000, fee: 675 },
            { start: 300001, end: 400000, fee: 675 },
            { start: 400001, end: 500000, fee: 675 },
            { start: 500001, end: 750000, fee: 725 },
            { start: 750001, end: 1000000, fee: 725 },
            { start: 1000001, end: 1500000, fee: 775 },
            { start: 1500001, end: 4000000, fee: 825 }
        ];

        // Molo Dual Rep Ltd Company Remortgage Legal Fees (Client Fee)
        const moloDualRepLtdRemortgageFees = [
            { start: 0, end: 150000, fee: 525 },
            { start: 150001, end: 200000, fee: 525 },
            { start: 200001, end: 300000, fee: 525 },
            { start: 300001, end: 400000, fee: 525 },
            { start: 400001, end: 500000, fee: 525 },
            { start: 500001, end: 750000, fee: 525 },
            { start: 750001, end: 1000000, fee: 525 },
            { start: 1000001, end: 1500000, fee: 675 },
            { start: 1500001, end: 2000000, fee: 725 }
        ];

        // Molo Independent Legal Advice Fees
        const moloIndependentLegalFees = [
            {
                label: '1 Director',
                fee: 250,
                applicableFor: 'buy',
                conditionSlug: 'independent_legal_advice'
            },
            {
                label: 'Additional Director',
                fee: 150,
                applicableFor: 'buy',
                conditionSlug: 'independent_legal_advice'
            }
        ];

        // Molo Disbursements
        const moloDisbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 14.4,
                applicableFor: 'buy,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 322.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // Molo Supplements
        const moloSupplements = [
            {
                label: 'Buy to Let',
                fee: 0,
                applicableFor: 'buy,remortgage',
                conditionSlug: 'buy_to_let'
            },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            { label: 'HMO', fee: 100, applicableFor: 'buy', conditionSlug: 'hmo' },
            {
                label: 'Leasehold',
                fee: 295,
                applicableFor: 'buy,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Transfer of Equity',
                fee: 275,
                applicableFor: 'remortgage',
                conditionSlug: 'transfer_of_equity'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add Sep Rep Individual Purchase fees
        moloSepRepIndividualPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Sep Rep Individual - Purchase)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Sep Rep Individual - Purchase)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    conditionSlug: 'sep_rep_individual',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add Sep Rep Individual Remortgage fees
        moloSepRepIndividualRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Sep Rep Individual - Remortgage)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Sep Rep Individual - Remortgage)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    conditionSlug: 'sep_rep_individual',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add Sep Rep Ltd Company Purchase fees
        moloSepRepLtdPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Sep Rep Ltd Company - Purchase)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Sep Rep Ltd Company - Purchase)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    conditionSlug: 'sep_rep_ltd_company',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add Sep Rep Ltd Company Remortgage fees
        moloSepRepLtdRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Sep Rep Ltd Company - Remortgage)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Sep Rep Ltd Company - Remortgage)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    conditionSlug: 'sep_rep_ltd_company',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add Dual Rep Individual Purchase fees
        moloDualRepIndividualPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Dual Rep Individual - Purchase)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Dual Rep Individual - Purchase)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    conditionSlug: 'dual_rep_individual',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add Dual Rep Individual Remortgage fees
        moloDualRepIndividualRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Dual Rep Individual - Remortgage)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Dual Rep Individual - Remortgage)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    conditionSlug: 'dual_rep_individual',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add Dual Rep Ltd Company Purchase fees
        moloDualRepLtdPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Dual Rep Ltd Company - Purchase)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Dual Rep Ltd Company - Purchase)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    conditionSlug: 'dual_rep_ltd_company',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add Dual Rep Ltd Company Remortgage fees
        moloDualRepLtdRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Dual Rep Ltd Company - Remortgage)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Dual Rep Ltd Company - Remortgage)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    conditionSlug: 'dual_rep_ltd_company',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add Independent Legal Advice fees
        moloIndependentLegalFees.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Independent Legal Advice',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        moloDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        moloSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Molo rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedMSMCurrentRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const msmCurrentRateCard = rateCardRepository.create({
            providerName: 'MSM',
            providerCode: RateCardProvider.MSM,
            displayName: 'MSM Existing Solictor Rate Card - E&W',
            description: 'MSM Rate Card with current pricing',
            version: '2.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 17,
            metadata: {
                format: 'optimus-three-column',
                pricingModel: 'two-tier',
                version: 'current',
                searchPackFee: 322.8,
                leaseholdFee: 250
            }
        });

        const savedRateCard = await rateCardRepository.save(msmCurrentRateCard);

        // MSM Current Purchase Legal Fees (Client Fee)
        const msmCurrentPurchaseFees = [
            { start: 0, end: 100000, fee: 590 },
            { start: 100001, end: 200000, fee: 620 },
            { start: 200001, end: 300000, fee: 635 },
            { start: 300001, end: 400000, fee: 650 },
            { start: 400001, end: 500000, fee: 665 },
            { start: 500001, end: 750000, fee: 765 },
            { start: 750001, end: 1000000, fee: 815 },
            { start: 1000001, end: 1250000, fee: 865 },
            { start: 1250001, end: 1500000, fee: 915 }
        ];

        // MSM Current Sale Legal Fees (Client Fee)
        const msmCurrentSaleFees = [
            { start: 0, end: 100000, fee: 565 },
            { start: 100001, end: 200000, fee: 590 },
            { start: 200001, end: 300000, fee: 605 },
            { start: 300001, end: 400000, fee: 620 },
            { start: 400001, end: 500000, fee: 635 },
            { start: 500001, end: 750000, fee: 735 },
            { start: 750001, end: 1000000, fee: 785 },
            { start: 1000001, end: 1250000, fee: 835 },
            { start: 1250001, end: 1500000, fee: 885 }
        ];

        // MSM Current Remortgage Legal Fees (Client Fee)
        const msmCurrentRemortgageFees = [
            { start: 0, end: 100000, fee: 177.5 },
            { start: 100001, end: 200000, fee: 169.16 },
            { start: 200001, end: 500000, fee: 160.83 },
            { start: 500001, end: 1000000, fee: 160.83 },
            { start: 1000001, end: *********, fee: 160.83 }
        ];

        // MSM Current Disbursements
        const msmCurrentDisbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 322.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // MSM Current Supplements
        const msmCurrentSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 250,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        msmCurrentPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        msmCurrentSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        msmCurrentRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        msmCurrentDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        msmCurrentSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded MSM Current rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedMSM2023RateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const msm2023RateCard = rateCardRepository.create({
            providerName: 'MSM',
            providerCode: RateCardProvider.MSM,
            displayName: 'MSM Solictor Rate Card - E&W 26052023',
            description: 'MSM Rate Card with 2023 pricing',
            version: '1.0',
            effectiveDate: new Date('2023-05-26'),
            status: RateCardStatus.INACTIVE, // Older version
            isDefault: false,
            priority: 18,
            metadata: {
                format: 'optimus-three-column',
                pricingModel: 'two-tier',
                version: '2023',
                searchPackFee: 369.0,
                leaseholdFee: 250
            }
        });

        const savedRateCard = await rateCardRepository.save(msm2023RateCard);

        // MSM 2023 Purchase Legal Fees (Client Fee) - Higher than current
        const msm2023PurchaseFees = [
            { start: 0, end: 100000, fee: 665 },
            { start: 100001, end: 200000, fee: 695 },
            { start: 200001, end: 300000, fee: 710 },
            { start: 300001, end: 400000, fee: 725 },
            { start: 400001, end: 500000, fee: 740 },
            { start: 500001, end: 750000, fee: 840 },
            { start: 750001, end: 1000000, fee: 890 },
            { start: 1000001, end: 1250000, fee: 940 },
            { start: 1250001, end: 1500000, fee: 990 }
        ];

        // MSM 2023 Sale Legal Fees (Client Fee) - Higher than current
        const msm2023SaleFees = [
            { start: 0, end: 100000, fee: 640 },
            { start: 100001, end: 200000, fee: 665 },
            { start: 200001, end: 300000, fee: 680 },
            { start: 300001, end: 400000, fee: 695 },
            { start: 400001, end: 500000, fee: 710 },
            { start: 500001, end: 750000, fee: 810 },
            { start: 750001, end: 1000000, fee: 860 },
            { start: 1000001, end: 1250000, fee: 910 },
            { start: 1250001, end: 1500000, fee: 960 }
        ];

        // MSM 2023 Remortgage Legal Fees (Client Fee) - Same as current
        const msm2023RemortgageFees = [
            { start: 0, end: 100000, fee: 177.5 },
            { start: 100001, end: 200000, fee: 169.16 },
            { start: 200001, end: 500000, fee: 160.83 },
            { start: 500001, end: 1000000, fee: 160.83 },
            { start: 1000001, end: *********, fee: 160.83 }
        ];

        // MSM 2023 Disbursements - Higher Search Pack fee
        const msm2023Disbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 369.0, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // MSM 2023 Supplements - Same as current
        const msm2023Supplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 250,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        msm2023PurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        msm2023SaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        msm2023RemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        msm2023Disbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        msm2023Supplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded MSM 2023 rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedOptimusBidRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const optimusBidRateCard = rateCardRepository.create({
            providerName: 'Optimus Bid',
            providerCode: RateCardProvider.OPTIMUS_BID,
            displayName: 'Optimus Bid Rate Card - Sale',
            description: 'Auction sale rate card with 4-tier pricing structure',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 19,
            metadata: {
                format: 'optimus-auction-sale',
                pricingModel: 'four-tier-auction',
                version: 'current',
                hasRemortgage: false,
                hasPurchase: false,
                auctionSpecific: true,
                adjustableFees: true
            }
        });

        const savedRateCard = await rateCardRepository.save(optimusBidRateCard);

        // Optimus Bid Sale Legal Fees (Client Fee)
        const optimusBidSaleFees = [
            { start: 0, end: 100000, fee: 570 },
            { start: 100001, end: 200000, fee: 585 },
            { start: 200001, end: 300000, fee: 600 },
            { start: 300001, end: 400000, fee: 615 },
            { start: 400001, end: 500000, fee: 630 },
            { start: 500001, end: 750000, fee: 730 },
            { start: 750001, end: 1000000, fee: 780 },
            { start: 1000001, end: 1250000, fee: 830 },
            { start: 1250001, end: 1500000, fee: 880 }
        ];

        // Optimus Bid Disbursements
        const optimusBidDisbursements = [
            { label: 'CHAPS Fee', fee: 36, applicableFor: 'sell', conditionSlug: undefined },
            { label: 'ID Check (PP)', fee: 12, applicableFor: 'sell', conditionSlug: undefined },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'sell',
                conditionSlug: undefined
            }
        ];

        // Optimus Bid Supplements
        const optimusBidSupplements = [
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'sell',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'Leasehold', fee: 250, applicableFor: 'sell', conditionSlug: 'leasehold' },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'sell',
                conditionSlug: 'mortgage_redemption'
            },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'sell',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'sell',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add sale fees
        optimusBidSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Auction Sale)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Auction Sale)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    conditionSlug: 'auction_sale',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        optimusBidDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        optimusBidSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Optimus Bid rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedPepperRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const pepperRateCard = rateCardRepository.create({
            providerName: 'Pepper',
            providerCode: RateCardProvider.PEPPER,
            displayName: 'Pepper Solictor Rate Card - E&W',
            description: 'Remortgage-only rate card with all-inclusive pricing',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 20,
            metadata: {
                format: 'pepper-remortgage-only',
                pricingModel: 'all-inclusive',
                version: 'current',
                hasRemortgage: true,
                hasPurchase: false,
                hasSale: false,
                allFeesPaidByPepper: true
            }
        });

        const savedRateCard = await rateCardRepository.save(pepperRateCard);

        // Pepper Remortgage Legal Fees (Client Fee) - Flat fee for all amounts
        const pepperRemortgageFees = [{ start: 50000, end: 2000000, fee: 130 }];

        // Pepper Disbursements - All paid by Pepper
        const pepperDisbursements = [
            {
                label: 'CHAPS to clear existing lender',
                fee: 42,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Unsecured debts £36 capped at £180',
                fee: 180,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add remortgage fees
        pepperRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Remortgage - All Inclusive)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Remortgage - All Inclusive)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    conditionSlug: 'pepper_remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        pepperDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements (Paid by Pepper)',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Pepper rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedRaynerRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const raynerRateCard = rateCardRepository.create({
            providerName: 'Rayner',
            providerCode: RateCardProvider.RAYNER,
            displayName: 'Rayner Solicitor Rate Card - E&W',
            description: 'Standard Optimus rate card with typical structure',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 21,
            metadata: {
                format: 'optimus-three-column',
                pricingModel: 'two-tier',
                version: 'current',
                searchPackFee: 347.8
            }
        });

        const savedRateCard = await rateCardRepository.save(raynerRateCard);

        // Rayner Purchase Legal Fees (Client Fee)
        const raynerPurchaseFees = [
            { start: 0, end: 100000, fee: 525 },
            { start: 100001, end: 200000, fee: 555 },
            { start: 200001, end: 300000, fee: 570 },
            { start: 300001, end: 400000, fee: 585 },
            { start: 400001, end: 500000, fee: 600 },
            { start: 500001, end: 750000, fee: 700 },
            { start: 750001, end: 1000000, fee: 750 },
            { start: 1000001, end: 1250000, fee: 800 },
            { start: 1250001, end: 1500000, fee: 850 }
        ];

        // Rayner Sale Legal Fees (Client Fee)
        const raynerSaleFees = [
            { start: 0, end: 100000, fee: 500 },
            { start: 100001, end: 200000, fee: 515 },
            { start: 200001, end: 300000, fee: 530 },
            { start: 300001, end: 400000, fee: 545 },
            { start: 400001, end: 500000, fee: 560 },
            { start: 500001, end: 750000, fee: 660 },
            { start: 750001, end: 1000000, fee: 710 },
            { start: 1000001, end: 1250000, fee: 760 },
            { start: 1250001, end: 1500000, fee: 810 }
        ];

        // Rayner Remortgage Legal Fees (Client Fee)
        const raynerRemortgageFees = [
            { start: 0, end: 100000, fee: 211.67 },
            { start: 100001, end: 200000, fee: 203.33 },
            { start: 200001, end: 500000, fee: 195 },
            { start: 500001, end: 1000000, fee: 195 },
            { start: 1000001, end: *********, fee: 195 }
        ];

        // Rayner Disbursements
        const raynerDisbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 347.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // Rayner Supplements
        const raynerSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 125,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        raynerPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        raynerSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        raynerRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        raynerDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        raynerSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Rayner rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedRemaxRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const remaxRateCard = rateCardRepository.create({
            providerName: 'Remax',
            providerCode: RateCardProvider.REMAX,
            displayName: 'Remax Solictor Rate Card - E&W',
            description: 'Remax Rate Card with no remortgage fees',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 22,
            metadata: {
                format: 'optimus-two-column',
                pricingModel: 'two-tier',
                version: 'current',
                hasRemortgage: false,
                searchPackFee: 322.8,
                leaseholdFee: 300
            }
        });

        const savedRateCard = await rateCardRepository.save(remaxRateCard);

        // Remax Purchase Legal Fees (Client Fee)
        const remaxPurchaseFees = [
            { start: 0, end: 100000, fee: 600 },
            { start: 100001, end: 200000, fee: 630 },
            { start: 200001, end: 300000, fee: 645 },
            { start: 300001, end: 400000, fee: 660 },
            { start: 400001, end: 500000, fee: 675 },
            { start: 500001, end: 750000, fee: 775 },
            { start: 750001, end: 1000000, fee: 825 },
            { start: 1000001, end: 1250000, fee: 875 },
            { start: 1250001, end: 1500000, fee: 925 }
        ];

        // Remax Sale Legal Fees (Client Fee)
        const remaxSaleFees = [
            { start: 0, end: 100000, fee: 575 },
            { start: 100001, end: 200000, fee: 590 },
            { start: 200001, end: 300000, fee: 605 },
            { start: 300001, end: 400000, fee: 620 },
            { start: 400001, end: 500000, fee: 635 },
            { start: 500001, end: 750000, fee: 735 },
            { start: 750001, end: 1000000, fee: 785 },
            { start: 1000001, end: 1250000, fee: 835 },
            { start: 1250001, end: 1500000, fee: 885 }
        ];

        // Remax Disbursements
        const remaxDisbursements = [
            { label: 'CHAPS Fee', fee: 36, applicableFor: 'buy,sell', conditionSlug: undefined },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 322.8, applicableFor: 'buy', conditionSlug: undefined }
        ];

        // Remax Supplements
        const remaxSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            { label: 'Leasehold', fee: 300, applicableFor: 'buy,sell', conditionSlug: 'leasehold' },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        remaxPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        remaxSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        remaxDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        remaxSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Remax rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedAllRateCardsForTenant(tenantId: string): Promise<void> {
        Logger.log(`Starting to seed rate cards for tenant ${tenantId}`);

        await this.seedArrowRateCard(tenantId);
        await this.seedCharlesCameronRateCard(tenantId);
        await this.seedFortAdviceBureauRateCard(tenantId);
        await this.seedGazealRateCard(tenantId);
        await this.seedHaystoRateCard(tenantId);
        await this.seedIndependentRateCard(tenantId);
        await this.seedJohnCharcolRateCard(tenantId);
        await this.seedKeyClub2025RateCard(tenantId);
        await this.seedKeyClub2024RateCard(tenantId);
        await this.seedLCJune2024RateCard(tenantId);
        await this.seedLCNovember2024RateCard(tenantId);
        await this.seedLEASRateCard(tenantId);
        await this.seedMojoRateCard(tenantId);
        await this.seedMoloRateCard(tenantId);
        await this.seedMSMCurrentRateCard(tenantId);
        await this.seedMSM2023RateCard(tenantId);
        await this.seedOptimusBidRateCard(tenantId);
        await this.seedPepperRateCard(tenantId);
        await this.seedRaynerRateCard(tenantId);
        await this.seedRemaxRateCard(tenantId);
        await this.seedLCConveyancingRateCard(tenantId);
        await this.seedSesameRateCard(tenantId);
        await this.seedTMGRateCard(tenantId);
        await this.seedTrussleRateCard(tenantId);
        await this.seedYOPAAssociateRateCard(tenantId);
        await this.seedYOPASolicitorRateCard(tenantId);

        // Ensure Arrow remains the default rate card after all seeding
        await this.ensureArrowIsDefault(tenantId);

        Logger.log(`Completed seeding rate cards for tenant ${tenantId}`);
    }

    /**
     * Ensure Arrow rate card remains the default after all seeding
     */
    async ensureArrowIsDefault(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);

        // Clear any existing defaults by updating all rate cards
        await rateCardRepository.update({ isDefault: true }, { isDefault: false });

        // Set Arrow as default
        const arrowRateCard = await rateCardRepository.findOne({
            where: { providerCode: RateCardProvider.ARROW, status: RateCardStatus.ACTIVE }
        });

        if (arrowRateCard) {
            arrowRateCard.isDefault = true;
            await rateCardRepository.save(arrowRateCard);
            Logger.log(`Arrow rate card set as default for tenant ${tenantId}`);
        } else {
            Logger.warn(`Arrow rate card not found for tenant ${tenantId}`);
        }
    }

    async seedAllTenants(): Promise<void> {
        try {
            const schemas = await this.getTenantSchemas();
            Logger.log(`Found ${schemas.length} tenant schemas to seed`);

            for (const schema of schemas) {
                const tenantId = this.getTenantIdFromSchema(schema);
                Logger.log(`Seeding rate cards for tenant: ${tenantId}`);

                try {
                    await this.seedAllRateCardsForTenant(tenantId);
                    Logger.log(`Successfully seeded rate cards for tenant: ${tenantId}`);
                } catch (error) {
                    Logger.error(`Failed to seed rate cards for tenant ${tenantId}:`, error);
                }
            }
        } catch (error) {
            Logger.error('Error seeding rate cards for all tenants:', error);
            throw error;
        }
    }

    async seedLCConveyancingRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const lcConveyancingRateCard = rateCardRepository.create({
            providerName: 'L&C Conveyancing',
            providerCode: RateCardProvider.LANDC,
            displayName: 'L&C Conveyancing SUPPLIER Rate Cards (England Wales)',
            description: 'L&C remortgage-only rate card with flat pricing',
            version: '1.0',
            effectiveDate: new Date('2024-11-29'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 23,
            metadata: {
                format: 'lc-remortgage-only',
                pricingModel: 'flat-pricing',
                version: 'current',
                hasRemortgage: true,
                hasPurchase: false,
                hasSale: false,
                flatPricing: true
            }
        });

        const savedRateCard = await rateCardRepository.save(lcConveyancingRateCard);

        // L&C Remortgage Legal Fees - Flat fees for all amounts
        const lcRemortgageFees = [
            {
                label: 'Freehold Resi Remortgage',
                fee: 309,
                applicableFor: 'remortgage',
                conditionSlug: 'freehold'
            },
            {
                label: 'Leasehold Resi Remortgage',
                fee: 357,
                applicableFor: 'remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Freehold BTL Remortgage',
                fee: 357,
                applicableFor: 'remortgage',
                conditionSlug: 'buy_to_let'
            },
            {
                label: 'Leasehold BTL Remortgage',
                fee: 405,
                applicableFor: 'remortgage',
                conditionSlug: 'leasehold_buy_to_let'
            }
        ];

        // L&C Management Contribution
        const lcManagementContribution = [
            {
                label: 'Management and Marketing Contribution',
                fee: 110,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add remortgage fees
        lcRemortgageFees.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Remortgage - All Inclusive)',
                    rangeStart: 0,
                    rangeEnd: *********,
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add management contribution
        lcManagementContribution.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Management Contribution',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded L&C Conveyancing rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedSesameRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const sesameRateCard = rateCardRepository.create({
            providerName: 'Sesame',
            providerCode: RateCardProvider.SESAME,
            displayName: 'Sesame Existing Solicitor Rate Card - E&W',
            description: 'Standard Optimus rate card with typical structure',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 24,
            metadata: {
                format: 'optimus-three-column',
                pricingModel: 'two-tier',
                version: 'current',
                searchPackFee: 322.8
            }
        });

        const savedRateCard = await rateCardRepository.save(sesameRateCard);

        // Sesame Purchase Legal Fees (Client Fee)
        const sesamePurchaseFees = [
            { start: 0, end: 100000, fee: 625 },
            { start: 100001, end: 200000, fee: 655 },
            { start: 200001, end: 300000, fee: 670 },
            { start: 300001, end: 400000, fee: 685 },
            { start: 400001, end: 500000, fee: 700 },
            { start: 500001, end: 750000, fee: 800 },
            { start: 750001, end: 1000000, fee: 850 },
            { start: 1000001, end: 1250000, fee: 900 },
            { start: 1250001, end: 1500000, fee: 950 }
        ];

        // Sesame Sale Legal Fees (Client Fee)
        const sesameSaleFees = [
            { start: 0, end: 100000, fee: 525 },
            { start: 100001, end: 200000, fee: 540 },
            { start: 200001, end: 300000, fee: 555 },
            { start: 300001, end: 400000, fee: 570 },
            { start: 400001, end: 500000, fee: 585 },
            { start: 500001, end: 750000, fee: 685 },
            { start: 750001, end: 1000000, fee: 735 },
            { start: 1000001, end: 1250000, fee: 785 },
            { start: 1250001, end: 1500000, fee: 835 }
        ];

        // Sesame Remortgage Legal Fees (Client Fee)
        const sesameRemortgageFees = [
            { start: 0, end: 100000, fee: 201.67 },
            { start: 100001, end: 200000, fee: 193.33 },
            { start: 200001, end: 500000, fee: 185 },
            { start: 500001, end: 1000000, fee: 185 },
            { start: 1000001, end: *********, fee: 185 }
        ];

        // Sesame Disbursements
        const sesameDisbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 322.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // Sesame Supplements
        const sesameSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 125,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        sesamePurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        sesameSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        sesameRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        sesameDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        sesameSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Sesame rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedTMGRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const tmgRateCard = rateCardRepository.create({
            providerName: 'TMG',
            providerCode: RateCardProvider.TMG,
            displayName: 'TMG Existing Solictor Rate Card - E&W',
            description: 'TMG additional extras only - no base legal fees',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 25,
            metadata: {
                format: 'tmg-additional-extras',
                pricingModel: 'extras-only',
                version: 'current',
                hasBaseFees: false,
                extrasOnly: true
            }
        });

        const savedRateCard = await rateCardRepository.save(tmgRateCard);

        // TMG Transactional Extras
        const tmgTransactionalExtras = [
            {
                label: 'Additional title',
                fee: 95,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Auction', fee: 125, applicableFor: 'buy,sell', conditionSlug: 'auction' },
            {
                label: 'Bankruptcy or other Land Charge entry',
                fee: 75,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Change of name',
                fee: 75,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Discharge of 2nd or subsequent charges',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Discount – rearrangement of priority',
                fee: 150,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Drafting tenancy agreement',
                fee: 175,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Dual representation',
                fee: 100,
                applicableFor: 'buy,sell',
                conditionSlug: 'dual_representation'
            },
            {
                label: 'Flying freehold',
                fee: 150,
                applicableFor: 'buy,sell',
                conditionSlug: 'flying_freehold'
            },
            { label: 'HMO', fee: 100, applicableFor: 'buy,sell', conditionSlug: 'hmo' },
            {
                label: 'Indemnity insurance required',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Lawyerchecker',
                fee: 12,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Lease extension – agreed',
                fee: 650,
                applicableFor: 'buy,sell',
                conditionSlug: 'lease_extension'
            },
            {
                label: 'Lease extension – statutory process',
                fee: 950,
                applicableFor: 'buy,sell',
                conditionSlug: 'lease_extension_statutory'
            },
            {
                label: 'Leasehold title',
                fee: 250,
                applicableFor: 'buy,sell',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Merger of titles required',
                fee: 75,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'New Build',
                fee: 150,
                applicableFor: 'buy,sell',
                conditionSlug: 'is_new_build'
            },
            {
                label: 'Postponement/rearrangement of charges',
                fee: 150,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Restriction/entry on title',
                fee: 75,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Solar Panel lease',
                fee: 200,
                applicableFor: 'buy,sell',
                conditionSlug: 'solar_panel_lease'
            },
            {
                label: 'Staircasing',
                fee: 200,
                applicableFor: 'buy,sell',
                conditionSlug: 'staircasing'
            },
            {
                label: 'Title split or transfer of part required',
                fee: 200,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Trust (separate deed)',
                fee: 175,
                applicableFor: 'buy,sell',
                conditionSlug: 'trust_deed'
            }
        ];

        // TMG Remortgage Extras
        const tmgRemortgageExtras = [
            {
                label: 'Approving and registering third party documentation',
                fee: 100,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Cancelling a cheque',
                fee: 15,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Checking an existing Solar Panel Lease',
                fee: 150,
                applicableFor: 'remortgage',
                conditionSlug: 'solar_panel_lease'
            },
            {
                label: 'Declaration of Trust',
                fee: 275,
                applicableFor: 'remortgage',
                conditionSlug: 'trust_deed'
            },
            {
                label: 'Deed Of Postponement',
                fee: 275,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Electronic bank transfer',
                fee: 30,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Fees for forwarding Title Information Document',
                fee: 20,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Fees for receiving documentation with insufficient postage',
                fee: 5,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Fees for liaising with Lawyers',
                fee: 100,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Fees for updating name or address',
                fee: 10,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Obtaining a property search',
                fee: 50,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Obtaining an Indemnity Insurance Policy',
                fee: 50,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Ordering copy Lease from Land Registry',
                fee: 10,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Preparing and recording Undertakings',
                fee: 25,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Registered Restriction/Caution - removal',
                fee: 125,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Restriction/Caution registered - dealing with',
                fee: 75,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Reversing Legal Completion',
                fee: 500,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Satisfying special conditions',
                fee: 30,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'SDLT Legal Fee',
                fee: 75,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Validating Bankruptcy Entries',
                fee: 10,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Any other additional Legal work - Hourly rate',
                fee: 130,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add transactional extras
        tmgTransactionalExtras.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Transactional Extras',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage extras
        tmgRemortgageExtras.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Remortgage Extras',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(`Seeded TMG rate card with ${feeItems.length} fee items for tenant ${tenantId}`);
    }

    async seedTrussleRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const trussleRateCard = rateCardRepository.create({
            providerName: 'Trussle',
            providerCode: RateCardProvider.TRUSSLE,
            displayName: 'TrussleExisting Solictor Rate Card - E&W',
            description: 'Standard Optimus rate card with typical structure',
            version: '1.0',
            effectiveDate: new Date('2024-01-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 26,
            metadata: {
                format: 'optimus-three-column',
                pricingModel: 'two-tier',
                version: 'current',
                searchPackFee: 322.8
            }
        });

        const savedRateCard = await rateCardRepository.save(trussleRateCard);

        // Trussle Purchase Legal Fees (Client Fee)
        const trusslePurchaseFees = [
            { start: 0, end: 100000, fee: 700 },
            { start: 100001, end: 200000, fee: 730 },
            { start: 200001, end: 300000, fee: 745 },
            { start: 300001, end: 400000, fee: 760 },
            { start: 400001, end: 500000, fee: 775 },
            { start: 500001, end: 750000, fee: 875 },
            { start: 750001, end: 1000000, fee: 925 },
            { start: 1000001, end: 1250000, fee: 975 },
            { start: 1250001, end: 1500000, fee: 1025 }
        ];

        // Trussle Sale Legal Fees (Client Fee)
        const trussleSaleFees = [
            { start: 0, end: 100000, fee: 475 },
            { start: 100001, end: 200000, fee: 490 },
            { start: 200001, end: 300000, fee: 505 },
            { start: 300001, end: 400000, fee: 520 },
            { start: 400001, end: 500000, fee: 535 },
            { start: 500001, end: 750000, fee: 635 },
            { start: 750001, end: 1000000, fee: 685 },
            { start: 1000001, end: 1250000, fee: 735 },
            { start: 1250001, end: 1500000, fee: 785 }
        ];

        // Trussle Remortgage Legal Fees (Client Fee)
        const trussleRemortgageFees = [
            { start: 0, end: 100000, fee: 201.67 },
            { start: 100001, end: 200000, fee: 193.33 },
            { start: 200001, end: 500000, fee: 185 },
            { start: 500001, end: 1000000, fee: 185 },
            { start: 1000001, end: *********, fee: 185 }
        ];

        // Trussle Disbursements
        const trussleDisbursements = [
            {
                label: 'CHAPS Fee',
                fee: 36,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'ID Check (PP)',
                fee: 12,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 10,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 322.8, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 6, applicableFor: 'remortgage', conditionSlug: undefined },
            {
                label: 'Search Insurance',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            },
            {
                label: 'Bankruptcy & completion search',
                fee: 7,
                applicableFor: 'remortgage',
                conditionSlug: undefined
            }
        ];

        // Trussle Supplements
        const trussleSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'help_to_buy_equity'
            },
            { label: 'HTB (ISA)', fee: 50, applicableFor: 'buy', conditionSlug: 'help_to_buy_isa' },
            {
                label: 'Leasehold',
                fee: 125,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'leasehold'
            },
            {
                label: 'Mortgage Redemption',
                fee: 50,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 50, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell,remortgage',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        trusslePurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        trussleSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add remortgage fees
        trussleRemortgageFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'remortgage',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        trussleDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        trussleSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded Trussle rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedYOPAAssociateRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const yopaAssociateRateCard = rateCardRepository.create({
            providerName: 'YOPA Associate',
            providerCode: RateCardProvider.YOPA,
            displayName: 'YOPA Rate Card - Associate 19.02.2025',
            description: 'YOPA Associate rate card with two-column structure (no remortgage)',
            version: '1.0',
            effectiveDate: new Date('2025-02-19'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 27,
            metadata: {
                format: 'yopa-two-column',
                pricingModel: 'two-tier',
                version: 'associate',
                hasRemortgage: false,
                searchPackFee: 390.2,
                chapsFee: 42
            }
        });

        const savedRateCard = await rateCardRepository.save(yopaAssociateRateCard);

        // YOPA Associate Purchase Legal Fees (Client Fee)
        const yopaAssociatePurchaseFees = [
            { start: 0, end: 75000, fee: 630 },
            { start: 75001, end: 125000, fee: 745 },
            { start: 125001, end: 200000, fee: 805 },
            { start: 200001, end: 300000, fee: 865 },
            { start: 300001, end: 400000, fee: 915 },
            { start: 400001, end: 500000, fee: 970 },
            { start: 500001, end: 600000, fee: 1010 },
            { start: 600001, end: 750000, fee: 1080 },
            { start: 750001, end: 1000000, fee: 1180 },
            { start: 1000001, end: 1250000, fee: 1435 },
            { start: 1250001, end: 1500000, fee: 1690 }
        ];

        // YOPA Associate Sale Legal Fees (Client Fee) - Same as purchase
        const yopaAssociateSaleFees = yopaAssociatePurchaseFees;

        // YOPA Associate Disbursements
        const yopaAssociateDisbursements = [
            { label: 'CHAPS Fee', fee: 42, applicableFor: 'buy,sell', conditionSlug: undefined },
            {
                label: 'ID Check (PP)',
                fee: 16.8,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 30,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 390.2, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 60, applicableFor: 'sell', conditionSlug: undefined },
            {
                label: 'File Opening Fee',
                fee: 150,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            }
        ];

        // YOPA Associate Supplements
        const yopaAssociateSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 100,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell',
                conditionSlug: 'help_to_buy_equity'
            },
            {
                label: 'HTB (ISA)',
                fee: 100,
                applicableFor: 'buy',
                conditionSlug: 'help_to_buy_isa'
            },
            { label: 'Leasehold', fee: 250, applicableFor: 'buy,sell', conditionSlug: 'leasehold' },
            {
                label: 'Mortgage Redemption',
                fee: 125,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 75, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        yopaAssociatePurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        yopaAssociateSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        yopaAssociateDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        yopaAssociateSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded YOPA Associate rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }

    async seedYOPASolicitorRateCard(tenantId: string): Promise<void> {
        const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const rateCardRepository = dataSource.getRepository(RateCard);
        const feeItemRepository = dataSource.getRepository(RateCardFeeItem);

        const yopaSolicitorRateCard = rateCardRepository.create({
            providerName: 'YOPA Solicitor',
            providerCode: RateCardProvider.YOPA,
            displayName: 'YOPA Solicitor Rate Card - E&W 01102024',
            description: 'YOPA Solicitor rate card with two-column structure (no remortgage)',
            version: '1.0',
            effectiveDate: new Date('2024-10-01'),
            status: RateCardStatus.ACTIVE,
            isDefault: false,
            priority: 28,
            metadata: {
                format: 'yopa-two-column',
                pricingModel: 'two-tier',
                version: 'solicitor',
                hasRemortgage: false,
                searchPackFee: 390.2,
                chapsFee: 42
            }
        });

        const savedRateCard = await rateCardRepository.save(yopaSolicitorRateCard);

        // YOPA Solicitor Purchase Legal Fees (Client Fee)
        const yopaSolicitorPurchaseFees = [
            { start: 0, end: 75000, fee: 630 },
            { start: 75001, end: 125000, fee: 745 },
            { start: 125001, end: 200000, fee: 805 },
            { start: 200001, end: 300000, fee: 865 },
            { start: 300001, end: 400000, fee: 915 },
            { start: 400001, end: 500000, fee: 970 },
            { start: 500001, end: 600000, fee: 1010 },
            { start: 600001, end: 750000, fee: 1080 },
            { start: 750001, end: 1000000, fee: 1180 },
            { start: 1000001, end: 1250000, fee: 1435 },
            { start: 1250001, end: 1500000, fee: 1690 }
        ];

        // YOPA Solicitor Sale Legal Fees (Client Fee) - Same as purchase
        const yopaSolicitorSaleFees = yopaSolicitorPurchaseFees;

        // YOPA Solicitor Disbursements
        const yopaSolicitorDisbursements = [
            { label: 'CHAPS Fee', fee: 42, applicableFor: 'buy,sell', conditionSlug: undefined },
            {
                label: 'ID Check (PP)',
                fee: 16.8,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            {
                label: 'Completion Searches',
                fee: 24,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            },
            { label: 'Search Pack', fee: 390.2, applicableFor: 'buy', conditionSlug: undefined },
            { label: "OCE's", fee: 60, applicableFor: 'sell', conditionSlug: undefined },
            {
                label: 'File Opening Fee',
                fee: 150,
                applicableFor: 'buy,sell',
                conditionSlug: undefined
            }
        ];

        // YOPA Solicitor Supplements
        const yopaSolicitorSupplements = [
            { label: 'Buy to Let', fee: 0, applicableFor: 'buy', conditionSlug: 'buy_to_let' },
            {
                label: 'Concessionary',
                fee: 50,
                applicableFor: 'buy',
                conditionSlug: 'concessionary'
            },
            {
                label: 'Gifted Deposit',
                fee: 100,
                applicableFor: 'buy',
                conditionSlug: 'gifted_deposit'
            },
            {
                label: 'HTB (Equity Loan)',
                fee: 200,
                applicableFor: 'buy,sell',
                conditionSlug: 'help_to_buy_equity'
            },
            {
                label: 'HTB (ISA)',
                fee: 100,
                applicableFor: 'buy',
                conditionSlug: 'help_to_buy_isa'
            },
            { label: 'Leasehold', fee: 250, applicableFor: 'buy,sell', conditionSlug: 'leasehold' },
            {
                label: 'Mortgage Redemption',
                fee: 125,
                applicableFor: 'buy,sell',
                conditionSlug: 'mortgage_redemption'
            },
            { label: 'Right to Buy', fee: 75, applicableFor: 'buy', conditionSlug: 'right_to_buy' },
            { label: 'SDLT Form', fee: 75, applicableFor: 'buy', conditionSlug: undefined },
            {
                label: 'Shared Ownership',
                fee: 350,
                applicableFor: 'buy,sell',
                conditionSlug: 'shared_ownership'
            },
            {
                label: 'Unregistered',
                fee: 150,
                applicableFor: 'buy,sell',
                conditionSlug: 'unregistered'
            }
        ];

        const feeItems: RateCardFeeItem[] = [];

        // Add purchase fees
        yopaSolicitorPurchaseFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'buy',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add sale fees
        yopaSolicitorSaleFees.forEach((range, index) => {
            const vatFee = range.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: 'Legal Fee (Residential Transaction)',
                    feeType: FeeItemType.LEGAL_FEE,
                    categoryName: 'Legal Fee (Residential Transaction)',
                    rangeStart: range.start,
                    rangeEnd: range.end,
                    netFee: range.fee,
                    vatFee,
                    totalFee: range.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: 'sell',
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add disbursements
        yopaSolicitorDisbursements.forEach((item, index) => {
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.DISBURSEMENT,
                    categoryName: 'Disbursements',
                    netFee: item.fee,
                    vatFee: 0,
                    totalFee: item.fee,
                    vatType: VatType.INC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        // Add supplements
        yopaSolicitorSupplements.forEach((item, index) => {
            const vatFee = item.fee * 0.2;
            feeItems.push(
                feeItemRepository.create({
                    rateCardId: savedRateCard.id,
                    label: item.label,
                    feeType: FeeItemType.CONDITIONAL_FEE,
                    categoryName: 'Common Supplements',
                    netFee: item.fee,
                    vatFee,
                    totalFee: item.fee + vatFee,
                    vatType: VatType.EXC,
                    applicableFor: item.applicableFor,
                    conditionSlug: item.conditionSlug,
                    perParty: false,
                    dynamic: false,
                    active: true,
                    displayOrder: index + 1
                })
            );
        });

        await feeItemRepository.save(feeItems);
        Logger.log(
            `Seeded YOPA Solicitor rate card with ${feeItems.length} fee items for tenant ${tenantId}`
        );
    }
}

async function main() {
    const seeder = new RateCardSeeder();

    try {
        await seeder.initialize();
        await seeder.seedAllTenants();
        Logger.log('Rate card seeding completed successfully');
    } catch (error) {
        Logger.error('Rate card seeding failed:', error);
        process.exit(1);
    } finally {
        await seeder.cleanup();
    }
}

if (require.main === module) {
    main();
}
