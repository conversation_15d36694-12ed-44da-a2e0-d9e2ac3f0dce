import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { Logger } from '@nestjs/common';

dotenv.config({
    path: path.resolve(process.cwd(), '.env'),
});

const fixSchemaIssues = async () => {
    const dataSource = new DataSource({
        type: 'postgres',
        host: process.env.POSTGRES_HOST,
        port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
        username: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: process.env.POSTGRES_DB,
        schema: 'public'
    });

    try {
        await dataSource.initialize();
        Logger.log('Connected to database');

        // 1. First get all foreign key constraints referencing user_profiles
        const fkConstraints = await dataSource.query(`
            SELECT
                tc.table_schema, 
                tc.constraint_name,
                tc.table_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name
            FROM 
                information_schema.table_constraints AS tc 
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                    ON ccu.constraint_name = tc.constraint_name
                    AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY'
                AND ccu.table_name = 'user_profiles';
        `);

        Logger.log('Found foreign key constraints:', fkConstraints);

        // 2. Drop each foreign key constraint
        for (const constraint of fkConstraints) {
            Logger.log(`Dropping foreign key constraint ${constraint.constraint_name} from ${constraint.table_name}`);
            await dataSource.query(`
                ALTER TABLE "${constraint.table_schema}"."${constraint.table_name}"
                DROP CONSTRAINT IF EXISTS "${constraint.constraint_name}";
            `);
        }

        // 3. Now get all primary key constraints on user_profiles
        const pkConstraints = await dataSource.query(`
            SELECT 
                tc.table_schema,
                tc.constraint_name,
                tc.table_name
            FROM information_schema.table_constraints tc
            WHERE tc.constraint_type = 'PRIMARY KEY'
                AND tc.table_name = 'user_profiles';
        `);

        // 4. Drop primary key constraints
        for (const constraint of pkConstraints) {
            Logger.log(`Dropping primary key constraint ${constraint.constraint_name} from ${constraint.table_name}`);
            await dataSource.query(`
                ALTER TABLE "${constraint.table_schema}"."${constraint.table_name}"
                DROP CONSTRAINT IF EXISTS "${constraint.constraint_name}";
            `);
        }

        // 5. Drop the problematic tables in the correct order
        const tablesToDrop = [
            'user_roles',
            'user_profiles',
            'tenant_roles',
            'user_roles'
        ];

        for (const table of tablesToDrop) {
            Logger.log(`Dropping table ${table}`);
            await dataSource.query(`DROP TABLE IF EXISTS "${table}" CASCADE;`);
        }

        // 6. Drop tenant schemas if they exist
        const schemas = await dataSource.query(
            `SELECT nspname FROM pg_namespace WHERE nspname LIKE 'tenant_%'`
        );

        for (const schema of schemas) {
            Logger.log(`Dropping schema ${schema.nspname}`);
            await dataSource.query(`DROP SCHEMA IF EXISTS "${schema.nspname}" CASCADE;`);
        }

        Logger.log('Successfully fixed schema issues');
    } catch (error) {
        Logger.error('Error fixing schema issues:', error);
        throw error;
    } finally {
        if (dataSource.isInitialized) {
            await dataSource.destroy();
        }
    }
};

fixSchemaIssues()
    .then(() => {
        Logger.log('Schema cleanup completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Schema cleanup failed:', error);
        process.exit(1);
    }); 