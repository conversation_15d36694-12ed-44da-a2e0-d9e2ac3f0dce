import * as fs from 'fs';
import * as path from 'path';
import { exec } from 'child_process';
import { Logger } from '@nestjs/common';

/**
 * Creates a temporary TypeORM config file for tenant schema operations
 * @returns The path to the created config file
 */
export const createTenantConfigFile = (): string => {
    // Create a simpler tenant config that doesn't try to load entities directly
    const configContent = `
import { DataSource } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({
  path: path.resolve(process.cwd(), '.env'),
});

// Create a tenant-specific data source for migrations
// We don't directly include the entity files to avoid path resolution issues
const tenantConfig: PostgresConnectionOptions = {
  type: 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
  username: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  database: process.env.POSTGRES_DB || 'tk_lpm',
  synchronize: false,
  ssl: process.env.POSTGRES_SSL === 'true',
  // Use schema definition file instead of loading entity files directly
  migrations: [
    path.join(process.cwd(), 'libs/common/src/typeorm/migrations/tenant/**/*.ts')
  ],
  migrationsTableName: 'tenant_typeorm_migrations',
};

export default new DataSource(tenantConfig);
`;

    const configPath = path.join(process.cwd(), 'tenant-typeorm.config.ts');
    fs.writeFileSync(configPath, configContent);
    return configPath;
};

/**
 * Executes a TypeORM command using the tenant configuration
 * @param command The TypeORM command to execute
 * @param configPath The path to the tenant config file
 * @param successMessage Message to display on successful execution
 * @returns Promise that resolves when the command completes
 */
export const executeTenantCommand = (
    command: string,
    configPath: string,
    successMessage: string
): Promise<void> => {
    return new Promise((resolve, reject) => {
        Logger.log(`Executing: ${command}`);

        const env = {
            ...process.env,
            NODE_PATH: process.cwd(),
            TS_NODE_PROJECT: path.join(process.cwd(), 'tsconfig.json')
        };

        exec(command, { env }, (error, stdout, stderr) => {
            if (error) {
                Logger.error(`Error executing command: ${error.message}`);
                reject(error);
                return;
            }

            if (stderr) {
                Logger.error(`Command stderr: ${stderr}`);
            }

            Logger.log(stdout);
            Logger.log(successMessage);

            // Clean up temporary config file
            fs.unlinkSync(configPath);
            resolve();
        });
    });
};
