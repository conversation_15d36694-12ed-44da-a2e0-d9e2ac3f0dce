import { DataSource } from 'typeorm';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { Logger } from '@nestjs/common';

// Load environment variables
dotenv.config({
    path: path.resolve(process.cwd(), '.env'),
});

const dropTenantSchemas = async () => {
    // Create a connection to the database
    const dataSource = new DataSource({
        type: 'postgres',
        host: process.env.POSTGRES_HOST,
        port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
        username: process.env.POSTGRES_USER,
        password: process.env.POSTGRES_PASSWORD,
        database: process.env.POSTGRES_DB,
        schema: 'public'
    });

    try {
        await dataSource.initialize();
        Logger.log('Connected to database');

        // Get all tenant schemas
        const schemas = await dataSource.query(
            `SELECT nspname FROM pg_namespace WHERE nspname LIKE 'tenant_%'`
        );

        // Drop each schema
        for (const schema of schemas) {
            const schemaName = schema.nspname;
            Logger.log(`Dropping schema ${schemaName}`);
            await dataSource.query(`DROP SCHEMA IF EXISTS "${schemaName}" CASCADE`);
        }

        Logger.log('Successfully dropped all tenant schemas');
    } catch (error) {
        Logger.error('Error dropping tenant schemas:', error);
        throw error;
    } finally {
        if (dataSource.isInitialized) {
            await dataSource.destroy();
        }
    }
};

// Run the script
dropTenantSchemas()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error('Script failed:', error);
        process.exit(1);
    }); 