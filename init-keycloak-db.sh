#!/usr/bin/env bash
set -euo pipefail

: "${PGHOST:=postgres}"
: "${PGPORT:=5432}"
: "${PGUSER:=${POSTGRES_USER:-postgres}}"
: "${PGPASSWORD:=${POSTGRES_PASSWORD:-postgres}}"
: "${KEYCLOAK_DB:=${KEYCLOAK_DB:-keycloak}}"

export PGPASSWORD

echo "⏳ Waiting for Postgres at $PGHOST:$PGPORT…"
until psql -h "$PGHOST" -p "$PGPORT" -U "$PGUSER" -c '\q' 2>/dev/null; do
  sleep 1
done

echo "✅ Creating database '$KEYCLOAK_DB' if it doesn’t exist…"
psql -h "$PGHOST" -p "$PGPORT" -U "$PGUSER" <<‑EOSQL
DO
\$do\$
BEGIN
   IF NOT EXISTS (
      SELECT FROM pg_database WHERE datname = '$KEYCLOAK_DB'
   ) THEN
      CREATE DATABASE "$KEYCLOAK_DB";
   END IF;
END
\$do\$;
GRANT ALL PRIVILEGES ON DATABASE "$KEYCLOAK_DB" TO "$PGUSER";
EOSQL

echo "🎉 Database '$KEYCLOAK_DB' is ready."
