<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://coveralls.io/github/nestjs/nest?branch=master" target="_blank"><img src="https://coveralls.io/repos/github/nestjs/nest/badge.svg?branch=master#9" alt="Coverage" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg" alt="Donate us"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow" alt="Follow us on Twitter"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

# TK-LPM Backend

A multi-tenant platform built with NestJS, TypeORM, and Keycloak.

## Key Features

- Multi-tenant architecture with schema-based isolation
- Microservices design for scalability and maintainability
- **NEW: Single login system across all tenants**
- Role-based access control with granular permissions
- Document management and generation

## Documentation

For detailed documentation, see the docs folder:

- [Project Overview](./docs/README.md)
- [Authentication System](./docs/AUTHENTICATION.md)
- [Single Login Guide](./docs/SINGLE_LOGIN_GUIDE.md)
- [Database Management](./docs/DATABASE.md)

## Unified Configuration

All database and service configuration is now managed in a single config file/module. Update all references to use this unified config.

## Project Structure

This is a NestJS monorepo project with a modular monolith architecture.

```
/tk-lpm-backend
├── apps/                  # Application implementations
│   ├── core/              # Main monolithic application
│   ├── communication/     # Communication microservice
│   ├── document-engine/   # Document handling microservice
│   ├── auth/              # Authentication microservice
│   └── case-management/   # Case management microservice
│
├── libs/                  # Shared libraries
│   └── common/            # Common code shared across all apps
│       ├── dto/           # Data Transfer Objects
│       ├── events/        # Event definitions
│       ├── utils/         # Utility functions
│       ├── health/        # Health check system
│       ├── constants/     # Shared constants
│       ├── repositories/  # Shared repositories (now here)
│       ├── guards/        # Shared guards (now here)
│       └── ...
└── package.json           # Root package.json
```

## Database Management

This project uses a multi-tenant database architecture with PostgreSQL. The database system is structured with:
- A public schema for system-wide tables and tenant metadata
- Individual schemas for each tenant's data

For detailed information about database management, including:
- Schema management (public and tenant)
- Migration procedures
- Entity organization
- Multi-tenancy implementation
- Configuration
- Best practices
- Troubleshooting

Please refer to our comprehensive [Database Documentation](docs/DATABASE.md).

## Database Migrations

This project uses TypeORM for database management with a multi-tenant architecture. We have separate commands for managing migrations in public and tenant schemas.

### Generating Migrations

```bash
# Generate migrations for public schema
yarn migration:generate:public

# Generate migrations for tenant schema (requires a name)
# Note: Tenant migrations are created empty and require manual editing
yarn migration:generate:tenant MigrationName
```

### Running Migrations

```bash
# Run all migrations (both public and tenant)
yarn migration:run

# Run only public schema migrations
yarn migration:run:public

# Run only tenant schema migrations
yarn migration:run:tenant
```

### Reverting Migrations

```bash
# Revert last migration (both public and tenant)
yarn migration:revert

# Revert only public schema migration
yarn migration:revert:public

# Revert only tenant schema migration
yarn migration:revert:tenant
```

For more detailed information about migrations, please refer to [Database Migrations Guide](./docs/MIGRATIONS.md).

## Getting Started

```bash
# Install dependencies
yarn install

# Husky
yarn prepare

# Build all applications
yarn build:all

# Start the core application in development mode
yarn start:core:dev

# Start the communication microservice in development mode
yarn start:communication:dev

# Start all microservices (excluding core) in development mode
yarn start:microservices:dev

# Start both core and all microservices concurrently in development mode
yarn start:all:dev
```

## Health Checks

The project includes a comprehensive health check system for monitoring the status of all microservices. Health endpoints are available at `/api/health`, `/api/health/db`, and `/api/health/all` (aggregated view).

For detailed documentation on the health check system, see [Health Check Documentation](libs/common/src/health/README.md).

Key features:
- Basic service health status
- Database connectivity checks
- Aggregated system-wide health status
- Standardized response format for monitoring tools

## Scripts

- `yarn build:all` - Build all applications
- `yarn build:core` - Build only the core application
- `yarn build:communication` - Build only the communication microservice
- `yarn build:document-engine` - Build only the document engine microservice
- `yarn build:auth` - Build only the auth microservice
- `yarn build:case-management` - Build only the case management microservice
- `yarn start:core` - Start the core application
- `yarn start:core:dev` - Start the core application in watch mode
- `yarn start:communication` - Start the communication microservice
- `yarn start:communication:dev` - Start the communication microservice in watch mode
- `yarn start:document-engine` - Start the document engine microservice
- `yarn start:document-engine:dev` - Start the document engine microservice in watch mode
- `yarn start:auth` - Start the auth microservice
- `yarn start:auth:dev` - Start the auth microservice in watch mode
- `yarn start:case-management` - Start the case management microservice
- `yarn start:case-management:dev` - Start the case management microservice in watch mode
- `yarn start:microservices` - Start all microservices (excluding core)
- `yarn start:microservices:dev` - Start all microservices in watch mode (excluding core)
- `yarn start:all` - Start all applications concurrently
- `yarn start:all:dev` - Start all applications in watch mode concurrently

## Compile and run the project

```bash
# development
$ yarn run start

# watch mode
$ yarn run start:dev

# production mode
$ yarn run start:prod
```

## Run tests

```bash
# unit tests
$ yarn run test

# e2e tests
$ yarn run test:e2e

# test coverage
$ yarn run test:cov
```

## Testing Infrastructure

This project has two types of tests:

1. **Unit Tests** - Test individual components without external dependencies (no database or Keycloak required)
2. **Integration Tests** - Test the integration between components with actual database connections

### Running Tests

#### Unit Tests
```bash
# Run all unit tests
yarn test:unit

# Run unit tests with Docker environment
yarn test:unit:docker
```

#### Integration Tests
```bash
# Run all integration tests (requires Docker)
yarn test:docker integration

# Run specific integration tests for auth module
yarn test:integration:auth

# Run a simple database connectivity test
yarn test:docker simple-db
```

#### Quick Database Connection Test

To quickly verify that your test database is working:

```bash
# Run the database connection test script
./scripts/test-db-connection.sh
```

This script:
1. Ensures Docker containers are running
2. Verifies PostgreSQL is accessible
3. Runs a simple connectivity test 
4. Reports whether the connection was successful

#### Combined Test Suite
```bash
# Run both unit and integration tests
yarn test:docker
```

### Test Infrastructure

See [docs/TESTING.md](docs/TESTING.md) for detailed information about the testing infrastructure, including:

- Docker-based test environment
- Environment configuration
- How to debug test failures
- Test file organization

## Git flow And Contribution
Refer to our gitflow document on how to contribute to the project
https://docs.google.com/document/d/116wy8vFXxI7P4ORbMpfD4-qm-CZvUu9oDgo-B8ligNo/edit?tab=t.0#heading=h.3b5ewqow2gb9

## Deployment

The application uses containerization for deployment with a comprehensive CI/CD workflow. Each microservice is independently built, tested, containerized, and deployed.

### CI/CD Workflow

The CI/CD pipeline is implemented using GitHub Actions and includes:

- Automatic detection of changed services
- Independent building, testing, and containerization of each service
- Pushing container images to AWS ECR
- Deployment to the appropriate environment

For detailed information about the CI/CD workflow, see [CI/CD Documentation](CI-CD-README.md).

### Docker Compose

You can run all services locally using Docker Compose:

```bash
# Make scripts executable
chmod +x scripts/make-scripts-executable.sh
./scripts/make-scripts-executable.sh

# Start all services
./scripts/start-services.sh
```

To start a specific service:

```bash
./scripts/start-services.sh <service-name>
```

The CI/CD pipeline automatically builds and pushes Docker images to ECR when changes are pushed to the main or develop branches. No manual intervention is required for the CI/CD process.

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).

## Environment Configuration

The application uses environment variables for configuration. Copy the `.env.example` file to `.env` and adjust the values as needed.

```bash
# Copy the example environment file
cp .env.example .env
```

Default environment variables:

```
# Environment
NODE_ENV=development

# Service Ports
CORE_PORT=3000
COMMUNICATION_PORT=3001
DOCUMENT_ENGINE_PORT=3002
AUTH_PORT=3003
CASE_MANAGEMENT_PORT=3004
```

The health check system uses these service port environment variables to communicate with each microservice. If you change the default ports, make sure to update the corresponding environment variables.

You can override these values by editing the `.env` file or setting the environment variables directly.

## Testing Improvements

### Unit Testing Improvements

We've made several improvements to the testing infrastructure to make tests more reliable:

1. **Proper Axios Mocking**: Fixed the axios mocking implementation in KeycloakHttpService tests to correctly handle request/response interceptors and error mapping.

2. **Authentication Service Testing**:
   - Improved auth service unit tests with proper dependency mocking
   - Added explicit mocks for all required repositories and services
   - Fixed mock implementations to return proper data structures

3. **Simplified Test Approach**:
   - Created simpler test implementations that don't rely on NestJS testing module
   - Direct service instantiation with comprehensive mocks for better control
   - Added factory functions in `mock-factories.ts` for commonly used services

4. **Integration Test Fixes**:
   - Updated tenant creation requests to include the required `adminUsername` field
   - Fixed auth flow to use username instead of email for login

### Usage

To run unit tests:
```bash
yarn test:unit
```

To run integration tests:
```bash
yarn test:integration:no-hang
```

To run a specific test file:
```bash
yarn test path/to/test.spec.ts
```

## Testing with Docker

# Improved Testing with Docker

Our testing infrastructure now more closely mirrors the production environment while ensuring tests run in isolation. Key improvements include:

## Testing Infrastructure Updates

1. **Aligned with Production**: The testing environment now uses the same Docker image versions and initialization scripts as production.

2. **Isolated Environment**: Test services run on different ports to avoid conflicts with development services:
   - PostgreSQL: Port 5434 (instead of 5432)
   - Keycloak: Port 8090 (instead of 8080)

3. **Consistent Database Initialization**: The testing environment uses the same database initialization scripts as production to ensure consistency.

4. **Test-Specific Initialization**: Additional test-specific initialization ensures clean test runs with proper schema setup.

## Running Tests with Docker

To run tests using the Docker test environment:

```bash
# Run all tests (unit and integration)
yarn test:docker

# Run only unit tests
yarn test:unit:docker

# Run only integration tests
yarn test:integration:docker
```

The test script handles:
1. Starting the Docker containers with appropriate port configurations
2. Initializing the databases with the same scripts used in production
3. Waiting for services to be ready
4. Running the tests with the correct connection parameters
5. Cleaning up the containers when done

## Test Environment Configuration

The test environment uses a dedicated `docker-compose.test.yml` configuration that specifies:
- PostgreSQL database using the same version as production
- Keycloak instance with the same version as production
- Health checks to ensure services are ready before tests run
- Port configurations that don't conflict with development services

## Troubleshooting Test Environment

If you encounter issues with the test environment:

1. Check port conflicts:
   ```bash
   # Check if ports 5434 and 8090 are already in use
   netstat -tuln | grep -E '5434|8090'
   ```

2. Manually clean up test environment:
   ```bash
   docker compose -f docker-compose.test.yml down -v
   ```

3. View test container logs:
   ```bash
   docker compose -f docker-compose.test.yml logs
   ```

4. Debug database initialization:
   ```bash
   docker compose -f docker-compose.test.yml logs postgres-test
   ```

# Running Tests in Different Environments

The testing infrastructure is designed to work consistently across different environments:

## Local Development Environment

For daily development and quick iteration:

```bash
# Run unit tests
yarn test:unit

# Run integration tests (requires local postgres and keycloak running)
yarn test:integration
```

## Docker-Based Test Environment

For consistent tests that don't depend on your local configuration:

```bash
# Run all tests with dedicated Docker containers
yarn test:docker

# Run only unit tests with Docker
yarn test:unit:docker

# Run only integration tests with Docker
yarn test:integration:docker
```

## CI Environment

The same Docker-based testing approach is used in CI, ensuring that:
- Tests run consistently across all development machines and CI 
- No environment-specific configuration is needed
- Tests are isolated from any other services running on the machine

## Production-Like Testing

For testing against a production-like configuration, use the Docker-based approach:

```bash
# This will use the same Docker image versions as production
yarn test:docker
```

The Docker test environment:
- Uses the same Docker images as production
- Uses the same initialization scripts
- Runs on isolated ports (5434 for PostgreSQL, 8090 for Keycloak)
- Has automated test data cleanup
