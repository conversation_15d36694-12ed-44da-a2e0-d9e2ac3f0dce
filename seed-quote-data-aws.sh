#!/usr/bin/env bash
set -e

echo "🌱 Seeding Quote Data on AWS RDS"
echo "================================="
echo ""

# AWS RDS Connection Details
export POSTGRES_HOST="tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com"
export POSTGRES_PORT="5432"
export POSTGRES_USER="postgres"
export POSTGRES_PASSWORD="TKLPMPassword2025!"
export POSTGRES_DB="tklpm"
export POSTGRES_SSL="false"

echo "Database: $POSTGRES_HOST:$POSTGRES_PORT/$POSTGRES_DB"
echo ""

# Check if we should seed all tenants or a specific one
if [ "$1" = "--all-tenants" ]; then
    echo "📋 Seeding quote data for ALL tenants..."
    echo ""
    yarn seed:quote-data:all
else
    echo "📋 Seeding quote data for current tenant context..."
    echo ""
    echo "Note: Use --all-tenants flag to seed all tenants"
    echo ""
    yarn seed:quote-data
fi

echo ""
echo "=" | head -c 80
echo ""
echo "✅ Quote data seeding complete!"
echo ""
echo "Seeded data includes:"
echo "  • Fee categories"
echo "  • Fee items with pricing"
echo "  • Promo codes"
echo "  • Quote templates"
echo ""

