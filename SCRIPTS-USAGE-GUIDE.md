# Scripts Usage Guide

## 📋 Overview

This document explains when and how to use various scripts in the project.

---

## 🚀 Deployment Scripts

### `infrastructure/scripts/deploy-all-services-safe.sh` ⭐ **PRIMARY DEPLOYMENT SCRIPT**

**Purpose:** Safely deploy all microservices to AWS ECS with rate limit protection.

**When to use:**
- After GitHub Actions successfully builds and pushes new images to ECR
- When you want to update all services to the latest code
- Regular deployments after merging PRs

**Usage:**
```bash
./infrastructure/scripts/deploy-all-services-safe.sh staging
```

**What it does:**
- Deploys services sequentially: Core → Auth → Quote-engine → Communication → Document-engine → Task-management → Case-management
- 90-second delays between deployments to avoid AWS rate limits
- Monitors each service for stability (max 5 minutes)
- Provides detailed progress updates
- **Note:** Keycloak is excluded (it's infrastructure and rarely needs updates)

**Example workflow:**
1. Merge PR to `staging` branch
2. Wait for GitHub Actions build to complete (~10 minutes)
3. Run: `./infrastructure/scripts/deploy-all-services-safe.sh staging`
4. <PERSON><PERSON><PERSON> handles everything automatically

---

### `deploy-frontend-fixes.sh` ⚠️ **OBSOLETE - CAN BE DELETED**

**Purpose:** One-time script to deploy CORS and cookie parser fixes.

**Status:** No longer needed - changes are committed to repo.

**Recommendation:** Delete this file.

---

## 🏗️ Build Scripts

### `infrastructure/scripts/build-and-push-images.sh`

**Purpose:** Build Docker images locally and push to AWS ECR.

**When to use:**
- **Rarely** - GitHub Actions handles builds automatically
- Manual testing of build process
- Emergency deployments when CI/CD is down

**Why you probably don't need it:**
- GitHub Actions builds images on every push to staging/main
- Automated builds are faster and more reliable
- Your local machine may have different architecture (M1/M2 vs x86)

**Usage (if needed):**
```bash
./infrastructure/scripts/build-and-push-images.sh
```

---

### `infrastructure/scripts/build-and-push-simple.sh`

**Purpose:** Simplified version of build-and-push-images.sh

**When to use:**
- Testing build process
- Quick local builds for specific services

**Why you probably don't need it:**
- Same reasons as above - GitHub Actions handles this

---

## 🗄️ Database Scripts

### `infrastructure/scripts/create-keycloak-db.sh`

**Purpose:** Create Keycloak database in RDS.

**When to use:**
- **ONE-TIME ONLY** - Initial Keycloak setup
- Already done - Keycloak DB exists

**When NOT to use:**
- Regular deployments
- After every code change
- Unless setting up a new environment (dev/prod)

**Usage (if needed for new environment):**
```bash
./infrastructure/scripts/create-keycloak-db.sh
```

---

### `infrastructure/lambda/setup-keycloak-db.py`

**Purpose:** AWS Lambda function to initialize Keycloak database schema.

**When to use:**
- Deployed as Lambda function (not run directly)
- Triggered automatically during CloudFormation stack setup
- **ONE-TIME** - Initial environment setup

**When NOT to use:**
- Manual execution (it's a Lambda function)
- Regular deployments
- Unless setting up a new environment

---

## 📊 Script Decision Tree

```
Do you need to deploy services?
│
├─ YES → Use deploy-all-services-safe.sh ✅
│        After GitHub Actions builds complete
│
└─ NO  → What do you need?
         │
         ├─ Build images manually?
         │  → Usually NO - GitHub Actions handles this
         │  → If emergency: build-and-push-images.sh
         │
         ├─ Setup new environment?
         │  → create-keycloak-db.sh (one-time)
         │  → Lambda functions handle the rest
         │
         └─ Deploy frontend fixes?
              → deploy-frontend-fixes.sh is obsolete
              → Changes are already committed
```

---

## 🎯 Common Scenarios

### Scenario 1: New Code Merged to Staging
```bash
# 1. Wait for GitHub Actions (check Actions tab)
# 2. Once build completes:
./infrastructure/scripts/deploy-all-services-safe.sh staging
```

### Scenario 2: Emergency Hotfix
```bash
# 1. Merge hotfix to staging
# 2. Wait for GitHub Actions
# 3. Deploy:
./infrastructure/scripts/deploy-all-services-safe.sh staging
```

### Scenario 3: Setting Up New Environment (Dev/Prod)
```bash
# 1. Create infrastructure (CloudFormation)
# 2. Setup Keycloak DB (if not automated):
./infrastructure/scripts/create-keycloak-db.sh

# 3. Deploy services:
./infrastructure/scripts/deploy-all-services-safe.sh dev
```

### Scenario 4: Keycloak Needs Update
```bash
# Keycloak is separate - deploy manually if needed:
aws ecs update-service --cluster tk-lpm-staging-cluster \
  --service keycloak --force-new-deployment --region us-east-1
```

---

## 🗑️ Scripts That Can Be Deleted

1. **`deploy-frontend-fixes.sh`** - One-time fix, no longer needed

---

## ⚙️ GitHub Actions (Automated)

**What GitHub Actions does automatically:**
- Builds Docker images on push to staging/main/dev
- Tags images with commit hash
- Pushes to ECR
- Updates ECS task definitions

**What you still do manually:**
- Run `deploy-all-services-safe.sh` after builds complete
- This is intentional - gives you control over when services restart

---

## 📝 Summary

### ✅ Use Regularly
- `infrastructure/scripts/deploy-all-services-safe.sh` - Main deployment script

### ⚠️ Use Rarely
- `infrastructure/scripts/build-and-push-images.sh` - GitHub Actions handles this
- `infrastructure/scripts/create-keycloak-db.sh` - One-time setup only

### ❌ Don't Use
- `deploy-frontend-fixes.sh` - Obsolete (delete)
- `infrastructure/lambda/setup-keycloak-db.py` - Lambda only (automated)

---

**Last Updated:** October 20, 2025

