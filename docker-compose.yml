services:
  # 1) Postgres with init‑script to create both the app DB and the keycloak DB
  postgres:
    image: postgres:15-alpine
    container_name: tk-lpm-postgres
    restart: unless-stopped
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      # Additional PostgreSQL configurations
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      # any *.sh in this folder runs once at first init
      - ./initdb:/docker-entrypoint-initdb.d
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Uncomment to add pgAdmin for database management
  # pgadmin:
  #   image: dpage/pgadmin4:latest
  #   container_name: tk-lpm-pgadmin
  #   restart: unless-stopped
  #   env_file:
  #     - .env
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: <EMAIL>
  #     PGADMIN_DEFAULT_PASSWORD: admin
  #   ports:
  #     - "5050:80"
  #   volumes:
  #     - pgadmin_data:/var/lib/pgadmin
  #   networks:
  #     - tk-lpm-network
  #   depends_on:
  #     - postgres

  # Uncommented and configured Redis for caching
  redis:
    image: redis:7-alpine
    container_name: tk-lpm-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - tk-lpm-network
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis}
      REDIS_DB: ${REDIS_DB:-1}
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-redis}", "--databases", "${REDIS_DB:-1}"]
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-redis}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Uncomment to add RabbitMQ for message queuing
  # rabbitmq:
  #   image: rabbitmq:3-management-alpine
  #   container_name: tk-lpm-rabbitmq
  #   restart: unless-stopped
  #   ports:
  #     - "5672:5672"
  #     - "15672:15672"
  #   volumes:
  #     - rabbitmq_data:/var/lib/rabbitmq
  #   networks:
  #     - tk-lpm-network
  #   healthcheck:
  #     test: ["CMD", "rabbitmqctl", "status"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5

  # Uncomment to add Elasticsearch for full-text search
  # elasticsearch:
  #   image: elasticsearch:8.11.1
  #   container_name: tk-lpm-elasticsearch
  #   restart: unless-stopped
  #   environment:
  #     - discovery.type=single-node
  #     - xpack.security.enabled=false
  #     - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
  #   ports:
  #     - "9200:9200"
  #   volumes:
  #     - elasticsearch_data:/usr/share/elasticsearch/data
  #   networks:
  #     - tk-lpm-network
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:9200"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 5

  # Keycloak for authentication and authorization
  keycloak:
    image: quay.io/keycloak/keycloak:22.0.1
    container_name: tk-lpm-keycloak
    restart: unless-stopped
    env_file:
      - .env
    environment:
      KC_DB: postgres
      KC_DB_URL_HOST: postgres
      KC_DB_URL_PORT: 5432
      KC_DB_USERNAME: ${POSTGRES_USER}
      KC_DB_PASSWORD: ${POSTGRES_PASSWORD}
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
      KC_HOSTNAME_PORT: ${KEYCLOAK_HOSTNAME_PORT}
      KC_HTTP_ENABLED: "true"
      KC_PROXY: edge
      KC_HEALTH_ENABLED: "true"
    ports:
      - "${KEYCLOAK_HOSTNAME_PORT}:8080"
    volumes:
      - keycloak_data:/opt/keycloak/data
    depends_on:
      postgres:
        condition: service_healthy
    command:
      - start-dev
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s


  # MinIO for S3-compatible object storage (Document Engine)
  minio:
    image: minio/minio:latest
    container_name: tk-lpm-minio
    restart: unless-stopped
    env_file:
      - .env
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin}
      MINIO_BROWSER_REDIRECT_URL: http://localhost:9001
    ports:
      - "${MINIO_API_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s

  # 3) Application services (core, communication, document-engine, auth, case-management)
  core:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        SERVICE: core
        PORT: 3000
    container_name: tk-lpm-core
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV:            ${NODE_ENV}
      PORT:                ${CORE_PORT}
      POSTGRES_HOST:       postgres
      REDIS_HOST:          redis
      KEYCLOAK_SERVER_URL: ${KEYCLOAK_SERVER_URL}
    ports:
      - "${CORE_PORT}:${CORE_PORT}"
    depends_on:
      - postgres
      - redis
      - keycloak
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${CORE_PORT}/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 15s

  communication:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        SERVICE: communication
        PORT: 3003
    container_name: tk-lpm-communication
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV:            ${NODE_ENV}
      PORT:                ${COMMUNICATION_PORT}
      POSTGRES_HOST:       postgres
      REDIS_HOST:          redis
      KEYCLOAK_SERVER_URL: ${KEYCLOAK_SERVER_URL}
    ports:
      - "${COMMUNICATION_PORT}:${COMMUNICATION_PORT}"
    depends_on:
      - postgres
      - redis
      - keycloak
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${COMMUNICATION_PORT}/api/communication/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 15s

  document-engine:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        SERVICE: document-engine
        PORT: 3004
    container_name: tk-lpm-document-engine
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV:                        ${NODE_ENV}
      PORT:                           ${DOCUMENT_ENGINE_PORT}
      POSTGRES_HOST:                  postgres
      REDIS_HOST:                     redis
      KEYCLOAK_SERVER_URL:            ${KEYCLOAK_SERVER_URL}
      # S3/MinIO Configuration
      AWS_S3_ENDPOINT:                http://minio:9000
      AWS_ACCESS_KEY_ID:              ${MINIO_ROOT_USER:-minioadmin}
      AWS_SECRET_ACCESS_KEY:          ${MINIO_ROOT_PASSWORD:-minioadmin}
      AWS_DOCUMENT_BUCKET_NAME:       ${AWS_DOCUMENT_BUCKET_NAME:-tk-lpm-documents}
      AWS_REGION:                     ${AWS_REGION:-us-east-1}
      AWS_S3_FORCE_PATH_STYLE:        "true"
      AWS_S3_USE_SSL:                 "false"
      AWS_S3_PRESIGNED_URL_EXPIRATION: ${AWS_S3_PRESIGNED_URL_EXPIRATION:-3600}
    ports:
      - "${DOCUMENT_ENGINE_PORT}:${DOCUMENT_ENGINE_PORT}"
    depends_on:
      - postgres
      - redis
      - keycloak
      - minio
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${DOCUMENT_ENGINE_PORT}/api/document-engine/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 15s

  auth:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        SERVICE: auth
        PORT: 3001
    container_name: tk-lpm-auth
    restart: on-failure:5
    env_file:
      - .env
    environment:
      NODE_ENV:                   ${NODE_ENV}
      PORT:                       ${AUTH_PORT}
      POSTGRES_HOST:              postgres
      REDIS_HOST:                     redis
      POSTGRES_USER:              ${POSTGRES_USER}
      POSTGRES_PASSWORD:          ${POSTGRES_PASSWORD}
      POSTGRES_DB:                ${POSTGRES_DB}
      KEYCLOAK_SERVER_URL:        ${KEYCLOAK_SERVER_URL}
      KEYCLOAK_ADMIN:             ${KEYCLOAK_ADMIN}
      KEYCLOAK_ADMIN_PASSWORD:    ${KEYCLOAK_ADMIN_PASSWORD}
      KEYCLOAK_CONNECTION_TIMEOUT: 60000
      KEYCLOAK_CONNECTION_RETRIES: 10
      KEYCLOAK_RETRY_DELAY:       5000
      LOG_LEVEL:                  debug
    ports:
      - "${AUTH_PORT}:${AUTH_PORT}"
    depends_on:
      - postgres
      - redis
      - keycloak
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${AUTH_PORT}/api/auth/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  case-management:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        SERVICE: case-management
        PORT: 3002
    container_name: tk-lpm-case-management
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV:            ${NODE_ENV:-development}
      PORT:                ${CASE_MANAGEMENT_PORT}
      POSTGRES_HOST:       postgres
      REDIS_HOST:                     redis
      KEYCLOAK_SERVER_URL: ${KEYCLOAK_SERVER_URL}
    ports:
      - "${CASE_MANAGEMENT_PORT}:${CASE_MANAGEMENT_PORT}"
    depends_on:
      - postgres
      - redis
      - keycloak
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${CASE_MANAGEMENT_PORT}/api/case-management/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 15s

  quote-engine:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        SERVICE: quote-engine
        PORT: 3005
    container_name: tk-lpm-quote-engine
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV:            ${NODE_ENV:-development}
      PORT:                ${QUOTE_ENGINE_PORT}
      POSTGRES_HOST:       postgres
      REDIS_HOST:                     redis
      KEYCLOAK_SERVER_URL: ${KEYCLOAK_SERVER_URL}
    ports:
      - "${QUOTE_ENGINE_PORT}:${QUOTE_ENGINE_PORT}"
    depends_on:
      - postgres
      - redis
      - keycloak
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${QUOTE_ENGINE_PORT}/api/quote-engine/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 15s

  task-management:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        SERVICE: task-management
        PORT: 3006
    container_name: tk-lpm-task-management
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV:            ${NODE_ENV:-development}
      PORT:                ${TASK_MANAGEMENT_PORT}
      POSTGRES_HOST:       postgres
      REDIS_HOST:                     redis
      KEYCLOAK_SERVER_URL: ${KEYCLOAK_SERVER_URL}
    ports:
      - "${TASK_MANAGEMENT_PORT}:${TASK_MANAGEMENT_PORT}"
    depends_on:
      - postgres
      - redis
      - keycloak
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${TASK_MANAGEMENT_PORT}/api/task-management/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 15s

volumes:
  postgres_data:
    name: tk-lpm-postgres-data
  redis_data:
    name: tk-lpm-redis-data
  # pgadmin_data:
  #   name: tk-lpm-pgadmin-data
  # rabbitmq_data:
  #   name: tk-lpm-rabbitmq-data
  # elasticsearch_data:
  #   name: tk-lpm-elasticsearch-data
  keycloak_data:
    name: tk-lpm-keycloak-data
  minio_data:
    name: tk-lpm-minio-data

networks:
  tk-lpm-network:
    name: tk-lpm-network
    external: true
