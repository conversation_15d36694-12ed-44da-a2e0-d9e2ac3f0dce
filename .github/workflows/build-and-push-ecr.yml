name: Build and Push to ECR

on:
  push:
    branches:
      - dev          # Development environment
      - staging      # Staging environment
      - production   # Production environment
      - main         # Production environment (alias)
  workflow_dispatch: # Allow manual trigger
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - production

env:
  AWS_REGION: us-east-1
  ECR_REGISTRY: 039612857103.dkr.ecr.us-east-1.amazonaws.com

jobs:
  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        service:
          - name: core
            port: 3000
          - name: auth
            port: 3001
          - name: case-management
            port: 3002
          - name: communication
            port: 3003
          - name: document-engine
            port: 3004
          - name: quote-engine
            port: 3005
          - name: task-management
            port: 3006
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Determine environment
        id: env
        run: |
          # Determine environment based on branch or manual input
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            ENV="${{ github.event.inputs.environment }}"
          else
            BRANCH="${{ github.ref_name }}"
            case $BRANCH in
              main|production)
                ENV="production"
                ;;
              staging)
                ENV="staging"
                ;;
              dev)
                ENV="dev"
                ;;
              *)
                ENV="dev"
                ;;
            esac
          fi
          echo "environment=${ENV}" >> $GITHUB_OUTPUT
          echo "🌍 Deploying to environment: ${ENV}"
      
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      
      - name: Enable Corepack
        run: |
          corepack enable
          corepack prepare yarn@4.9.2 --activate
      
      - name: Install dependencies
        run: yarn install --immutable
      
      - name: Build ${{ matrix.service.name }}
        run: yarn build:${{ matrix.service.name }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Build Docker image
        env:
          ENVIRONMENT: ${{ steps.env.outputs.environment }}
        run: |
          docker build \
            --build-arg SERVICE=${{ matrix.service.name }} \
            --build-arg PORT=${{ matrix.service.port }} \
            -f Dockerfile.production \
            -t ${{ env.ECR_REGISTRY }}/tk-lpm/${{ matrix.service.name }}:${{ github.sha }} \
            -t ${{ env.ECR_REGISTRY }}/tk-lpm/${{ matrix.service.name }}:${ENVIRONMENT} \
            -t ${{ env.ECR_REGISTRY }}/tk-lpm/${{ matrix.service.name }}:${ENVIRONMENT}-${{ github.sha }} \
            -t ${{ env.ECR_REGISTRY }}/tk-lpm/${{ matrix.service.name }}:latest \
            .
      
      - name: Push Docker image to ECR
        env:
          ENVIRONMENT: ${{ steps.env.outputs.environment }}
        run: |
          echo "📤 Pushing images for ${{ matrix.service.name }} to ${ENVIRONMENT} environment..."
          docker push ${{ env.ECR_REGISTRY }}/tk-lpm/${{ matrix.service.name }}:${{ github.sha }}
          docker push ${{ env.ECR_REGISTRY }}/tk-lpm/${{ matrix.service.name }}:${ENVIRONMENT}
          docker push ${{ env.ECR_REGISTRY }}/tk-lpm/${{ matrix.service.name }}:${ENVIRONMENT}-${{ github.sha }}
          docker push ${{ env.ECR_REGISTRY }}/tk-lpm/${{ matrix.service.name }}:latest
      
      - name: Update deployment status
        env:
          ENVIRONMENT: ${{ steps.env.outputs.environment }}
        run: |
          echo "✅ Successfully deployed ${{ matrix.service.name }} to ${ENVIRONMENT}"
          echo "📦 Image tags:"
          echo "   - ${{ github.sha }}"
          echo "   - ${ENVIRONMENT}"
          echo "   - ${ENVIRONMENT}-${{ github.sha }}"
          echo "   - latest"

