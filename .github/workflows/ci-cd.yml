name: Microservice CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/**'
      - 'libs/**'
      - '.github/workflows/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'apps/**'
      - 'libs/**'
      - '.github/workflows/**'
  workflow_dispatch:
    inputs:
      service:
        description: 'Service to build (leave empty for auto-detection)'
        required: false
        type: string

jobs:
  detect-changes:
    name: Detect Changed Services
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      any_changes: ${{ steps.set-matrix.outputs.any_changes }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Detect changed services
        id: set-matrix
        run: |
          # If a specific service was provided via workflow_dispatch, use that
          if [[ "${{ github.event.inputs.service }}" != "" ]]; then
            SERVICES_JSON="[\"${{ github.event.inputs.service }}\"]"
            echo "matrix=${SERVICES_JSON}" >> $GITHUB_OUTPUT
            echo "any_changes=true" >> $GITHUB_OUTPUT
            echo "Building specific service: ${{ github.event.inputs.service }}"
            exit 0
          fi

          # For pull requests, compare with the base branch
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            BASE_SHA=${{ github.event.pull_request.base.sha }}
            HEAD_SHA=${{ github.event.pull_request.head.sha }}
          else
            # For pushes, get the before and after SHAs
            BASE_SHA=$(git rev-parse HEAD~1)
            HEAD_SHA=$(git rev-parse HEAD)
          fi

          echo "Comparing changes between ${BASE_SHA} and ${HEAD_SHA}"

          # Get all available services from the apps directory
          ALL_SERVICES=$(ls -d apps/*/ | cut -d'/' -f2)

          # Initialize an array to store changed services
          CHANGED_SERVICES=()

          # Check if common library has changed
          COMMON_CHANGED=$(git diff --name-only ${BASE_SHA} ${HEAD_SHA} | grep -q "libs/common/" && echo "true" || echo "false")

          # If common library changed, all services need to be rebuilt
          if [[ "${COMMON_CHANGED}" == "true" ]]; then
            echo "Common library changed, all services will be rebuilt"
            CHANGED_SERVICES=($ALL_SERVICES)
          else
            # Check each service directory for changes
            for SERVICE in $ALL_SERVICES; do
              CHANGED=$(git diff --name-only ${BASE_SHA} ${HEAD_SHA} | grep -q "apps/${SERVICE}/" && echo "true" || echo "false")
              if [[ "${CHANGED}" == "true" ]]; then
                echo "Service ${SERVICE} has changes"
                CHANGED_SERVICES+=("${SERVICE}")
              fi
            done
          fi

          # Convert the array to JSON for the matrix
          if [[ ${#CHANGED_SERVICES[@]} -gt 0 ]]; then
            SERVICES_JSON=$(printf '"%s",' "${CHANGED_SERVICES[@]}" | sed 's/,$//')
            SERVICES_JSON="[${SERVICES_JSON}]"
            echo "matrix=${SERVICES_JSON}" >> $GITHUB_OUTPUT
            echo "any_changes=true" >> $GITHUB_OUTPUT
            echo "Changed services: ${SERVICES_JSON}"
          else
            echo "No service changes detected"
            echo "matrix=[]" >> $GITHUB_OUTPUT
            echo "any_changes=false" >> $GITHUB_OUTPUT
          fi

  build-and-deploy:
    name: Build and Deploy
    needs: detect-changes
    if: needs.detect-changes.outputs.any_changes == 'true'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: ${{ fromJson(needs.detect-changes.outputs.matrix) }}
      fail-fast: false

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Lint
        run: yarn lint

      - name: Set build command
        id: detect-scripts
        run: |
          # Set the build command for this service
          echo "build_cmd=yarn build:${{ matrix.service }}" >> $GITHUB_OUTPUT

      - name: Run tests
        run: |
          echo "::group::Running Tests"
          echo "Running service-specific tests for ${{ matrix.service }}"
          yarn test:${{ matrix.service }}
          echo "::endgroup::"

      - name: Build service
        run: yarn build:${{ matrix.service }}

      - name: Verify Dockerfile exists
        run: |
          if [ ! -f "apps/${{ matrix.service }}/Dockerfile" ]; then
            echo "::error::Dockerfile not found for service ${{ matrix.service }}"
            exit 1
          fi

          echo "Using existing Dockerfile for ${{ matrix.service }}"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to AWS ECR
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./apps/${{ matrix.service }}/Dockerfile
          push: ${{ github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop' }}
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/${{ matrix.service }}:latest
            ${{ steps.login-ecr.outputs.registry }}/${{ matrix.service }}:v1.0.0-${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            SERVICE=${{ matrix.service }}

      - name: Validate Docker image
        run: |
          echo "::group::Docker Image Validation"
          echo "Validating Docker image for ${{ matrix.service }}"

          # If we're not pushing to ECR, build the image locally for validation
          if [[ "${{ github.ref }}" != "refs/heads/main" && "${{ github.ref }}" != "refs/heads/develop" ]]; then
            docker build -t ${{ matrix.service }}:test -f ./apps/${{ matrix.service }}/Dockerfile .

            # Determine the correct port for the service
            if [[ "${{ matrix.service }}" == "core" ]]; then
              PORT="${{ env.CORE_PORT || 3000 }}"
            elif [[ "${{ matrix.service }}" == "communication" ]]; then
              PORT="${{ env.COMMUNICATION_PORT || 3001 }}"
            elif [[ "${{ matrix.service }}" == "document-engine" ]]; then
              PORT="${{ env.DOCUMENT_ENGINE_PORT || 3002 }}"
            elif [[ "${{ matrix.service }}" == "auth" ]]; then
              PORT="${{ env.AUTH_PORT || 3003 }}"
            elif [[ "${{ matrix.service }}" == "case-management" ]]; then
              PORT="${{ env.CASE_MANAGEMENT_PORT || 3004 }}"
            else
              PORT="3000"
            fi

            # Run the container with health check
            docker run -d --name ${{ matrix.service }}_test \
              -e PORT=$PORT \
              -e NODE_ENV=test \
              ${{ matrix.service }}:test

            # Wait for container to start
            sleep 10

            # Check container status
            CONTAINER_STATUS=$(docker inspect --format='{{.State.Status}}' ${{ matrix.service }}_test)
            echo "Container status: $CONTAINER_STATUS"

            # Clean up
            docker stop ${{ matrix.service }}_test
            docker rm ${{ matrix.service }}_test
          else
            echo "Image pushed to ECR, skipping local validation"
          fi
          echo "::endgroup::"

      - name: Notify on failure
        if: failure()
        run: |
          echo "::error::Build and deployment failed for service ${{ matrix.service }}"
