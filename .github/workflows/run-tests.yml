name: Run Unit Tests Only

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'yarn'

    - name: Install dependencies
      run: yarn install --frozen-lockfile

    # Run only unit tests (no Docker needed)
    - name: Run unit tests
      run: yarn test:unit:only

    # Upload coverage and test results as artifacts
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: unit-test-results
        path: |
          coverage
          test-results
        retention-days: 7 