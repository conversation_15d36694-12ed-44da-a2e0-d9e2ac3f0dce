name: Service Deployment

on:
  workflow_call:
    inputs:
      service:
        required: true
        type: string
      environment:
        required: true
        type: string
        default: 'development'
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      AWS_REGION:
        required: true
      AWS_ACCOUNT_ID:
        required: true

jobs:
  deploy-service:
    name: Deploy ${{ inputs.service }} to ${{ inputs.environment }}
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'latest'

      - name: Update Kubernetes deployment
        run: |
          echo "::group::Updating Kubernetes deployment"
          
          # Set environment variables
          ECR_REGISTRY="${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com"
          IMAGE_TAG="v1.0.0-${{ github.sha }}"
          
          # Create deployment config
          cat > deployment.yaml << EOF
          apiVersion: apps/v1
          kind: Deployment
          metadata:
            name: ${{ inputs.service }}
            namespace: ${{ inputs.environment }}
          spec:
            replicas: 2
            selector:
              matchLabels:
                app: ${{ inputs.service }}
            template:
              metadata:
                labels:
                  app: ${{ inputs.service }}
              spec:
                containers:
                - name: ${{ inputs.service }}
                  image: ${ECR_REGISTRY}/${{ inputs.service }}:${IMAGE_TAG}
                  ports:
                  - containerPort: 3000
                  env:
                  - name: NODE_ENV
                    value: "${{ inputs.environment }}"
                  - name: PORT
                    value: "3000"
                  resources:
                    limits:
                      cpu: "500m"
                      memory: "512Mi"
                    requests:
                      cpu: "100m"
                      memory: "128Mi"
                  livenessProbe:
                    httpGet:
                      path: /health
                      port: 3000
                    initialDelaySeconds: 30
                    periodSeconds: 10
                  readinessProbe:
                    httpGet:
                      path: /health
                      port: 3000
                    initialDelaySeconds: 5
                    periodSeconds: 5
          EOF
          
          # Apply the deployment
          kubectl apply -f deployment.yaml
          
          # Wait for deployment to complete
          kubectl rollout status deployment/${{ inputs.service }} -n ${{ inputs.environment }} --timeout=300s
          
          echo "::endgroup::"

      - name: Verify deployment
        run: |
          echo "::group::Verifying deployment"
          
          # Check deployment status
          DEPLOYMENT_STATUS=$(kubectl get deployment ${{ inputs.service }} -n ${{ inputs.environment }} -o jsonpath='{.status.conditions[?(@.type=="Available")].status}')
          
          if [[ "$DEPLOYMENT_STATUS" == "True" ]]; then
            echo "Deployment successful!"
          else
            echo "Deployment failed!"
            kubectl get deployment ${{ inputs.service }} -n ${{ inputs.environment }} -o yaml
            exit 1
          fi
          
          echo "::endgroup::"
