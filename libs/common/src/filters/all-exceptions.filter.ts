import {
    ArgumentsHost,
    Catch,
    ExceptionFilter,
    HttpException,
    HttpStatus,
    Logger
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { HttpStatusCode, HttpStatusText } from '../enums/http-status.enum';
import { ApiResponse } from '../interfaces/api-response.interface';
import { Request } from 'express';

/**
 * Global exception filter that catches all exceptions
 * and formats them into a standardized response format
 */
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
    private readonly logger = new Logger(AllExceptionsFilter.name);

    constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

    catch(exception: unknown, host: ArgumentsHost): void {
        // Get HTTP adapter from host
        const { httpAdapter } = this.httpAdapterHost;

        // Get context from host
        const ctx = host.switchToHttp();
        const request = ctx.getRequest<Request>();
        const requestUrl = request.url;
        const requestMethod = request.method;

        // Determine if we're in production
        const isProduction = process.env.NODE_ENV === 'production';

        // Prepare response based on exception type
        let responseBody: ApiResponse<null>;
        let statusCode: number;

        if (exception instanceof HttpException) {
            // Handle HttpException (including NestJS built-in exceptions and our custom exceptions)
            statusCode = exception.getStatus();
            const exceptionResponse = exception.getResponse();

            // Check if the exception response is already in our format
            if (
                typeof exceptionResponse === 'object' &&
                exceptionResponse !== null &&
                'code' in exceptionResponse &&
                'status' in exceptionResponse &&
                'message' in exceptionResponse &&
                'data' in exceptionResponse
            ) {
                // Response is already in our format, use it directly
                responseBody = exceptionResponse as ApiResponse<null>;
            } else {
                // Format the exception response
                let message: string;
                let meta: Record<string, any> | undefined;

                if (typeof exceptionResponse === 'string') {
                    // String response
                    message = exceptionResponse;
                } else if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
                    // Object response
                    const exceptionObj = exceptionResponse as Record<string, any>;

                    // Extract message - handle both single string and array of messages
                    if (Array.isArray(exceptionObj.message)) {
                        // ValidationPipe returns an array of validation error messages
                        message = exceptionObj.message.join(', ');
                    } else {
                        message = exceptionObj.message || exceptionObj.error || 'An error occurred';
                    }

                    // If it's a validation error, include validation errors in metadata
                    if (exceptionObj.errors || exceptionObj.validationErrors) {
                        meta = {
                            validationErrors: exceptionObj.errors || exceptionObj.validationErrors
                        };
                    }
                } else {
                    // Fallback message
                    message = 'An error occurred';
                }

                // Create standardized response
                responseBody = {
                    code: statusCode,
                    status: HttpStatusText[statusCode as HttpStatusCode] || 'Error',
                    message,
                    data: null,
                    ...(meta ? { meta } : {})
                };
            }
        } else {
            // Handle unknown exceptions (internal server errors)
            statusCode = HttpStatus.INTERNAL_SERVER_ERROR;

            // Determine error message
            let errorMessage = 'Internal server error';

            // Include error details in non-production environments
            if (!isProduction) {
                if (exception instanceof Error) {
                    errorMessage = exception.message;

                    // Include stack trace in metadata for non-production environments
                    responseBody = {
                        code: statusCode,
                        status: HttpStatusText[HttpStatusCode.INTERNAL_SERVER_ERROR],
                        message: errorMessage,
                        data: null,
                        meta: {
                            stack: exception.stack,
                            name: exception.name
                        }
                    };
                } else {
                    // Handle non-Error exceptions (like thrown strings or objects)
                    try {
                        errorMessage = String(exception);
                        responseBody = {
                            code: statusCode,
                            status: HttpStatusText[HttpStatusCode.INTERNAL_SERVER_ERROR],
                            message: errorMessage,
                            data: null,
                            meta: {
                                rawException: exception
                            }
                        };
                    } catch {
                        // Fallback if String() conversion fails
                        responseBody = {
                            code: statusCode,
                            status: HttpStatusText[HttpStatusCode.INTERNAL_SERVER_ERROR],
                            message: 'Unknown error occurred',
                            data: null
                        };
                    }
                }
            } else {
                // In production, don't expose error details
                responseBody = {
                    code: statusCode,
                    status: HttpStatusText[HttpStatusCode.INTERNAL_SERVER_ERROR],
                    message: errorMessage,
                    data: null
                };
            }
        }

        // Log the error
        this.logException(exception, requestMethod, requestUrl, statusCode, responseBody);

        // Send the response
        httpAdapter.reply(ctx.getResponse(), responseBody, statusCode);
    }

    /**
     * Logs the exception details
     */
    private logException(
        exception: unknown,
        method: string,
        url: string,
        statusCode: number,
        responseBody: ApiResponse<null>
    ): void {
        const isProduction = process.env.NODE_ENV === 'production';

        if (statusCode >= 500) {
            // Log server errors with full details
            this.logger.error(
                `[${method}] ${url} - Status ${statusCode}: ${responseBody.message}`,
                isProduction
                    ? undefined
                    : exception instanceof Error
                      ? exception.stack
                      : String(exception)
            );
        } else if (statusCode >= 400 && statusCode < 500) {
            // Log client errors with less detail
            this.logger.warn(`[${method}] ${url} - Status ${statusCode}: ${responseBody.message}`);
        } else {
            // Log other exceptions as info
            this.logger.log(`[${method}] ${url} - Status ${statusCode}: ${responseBody.message}`);
        }

        // Here you could integrate with external logging services like Sentry
        // if (process.env.SENTRY_DSN && statusCode >= 500) {
        //   Sentry.captureException(exception);
        // }
    }
}
