import { createLogger, transports, format } from 'winston';
import * as Transport from 'winston-transport';
import * as path from 'path';
import { default as Sentry } from 'winston-transport-sentry-node';

let dir = process.env.LOG_DIRECTORY || '';
if (!dir) dir = path.resolve('logs');

/**

 * {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  verbose: 4,
  debug: 5,
  silly: 6
}
 * **/
const logLevel =
    process.env.NODE_ENV === 'development'
        ? 'debug'
        : 'info'; /*defines the max log level for the environment*/
const formatter = format.combine(
    format.errors({ stack: true }),
    format.colorize(),
    format.timestamp({ format: 'DD-MM-YYYY HH:mm:ss' }),
    format.splat(),
    format.printf((info) => {
        const { timestamp, level, message, ...meta } = info;

        return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
    })
);

const options = {
    file: {
        level: logLevel,
        filename: dir + '/exceptions-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        zippedArchive: true,
        timestamp: true,
        handleExceptions: true,
        humanReadableUnhandledException: true,
        prettyPrint: true,
        json: true,
        maxSize: '20m',
        colorize: true,
        maxFiles: '14d'
    },
    errorFile: {
        level: 'error',
        filename: `${dir}/app-error.log`
    }
};

const storeConfigs: Transport[] = [];

if (process.env.NODE_ENV === 'development') {
    /*if dev mode, add a console log*/
    storeConfigs.push(new transports.File(options.errorFile));
    storeConfigs.push(
        new transports.Console({
            level: logLevel,
            format: formatter,
            handleExceptions: true
        })
    );
} else if (process.env.NODE_ENV === 'sandbox') {
    storeConfigs.push(
        new transports.Console({
            level: logLevel,
            format: formatter,
            handleExceptions: true
        })
    );
} else if (process.env.NODE_ENV === 'production') {
    storeConfigs.push(
        new transports.Console({
            level: 'error',
            format: formatter,
            handleExceptions: true
        })
    );

    storeConfigs.push(
        new Sentry({
            level: 'error',
            format: formatter,
            handleExceptions: true,
            sentry: {
                dsn: process.env.SENTRY_DSN,
                environment: process.env.NODE_ENV
            }
        })
    );
}

// export log instance based on the current environment
export const instance = createLogger({
    transports: storeConfigs,
    //exceptionHandlers: [new DailyRotateFile(options.file)],
    exitOnError: false /*do not exit on unhandled exceptions*/
});
