import { HttpStatusCode, HttpStatusText } from '../enums/http-status.enum';
import { ApiResponse } from '../interfaces/api-response.interface';

/**
 * Utility class for creating standardized API responses
 */
export class ApiResponseUtil {
    /**
     * Creates a success response with status 200 OK
     * @param data Response data
     * @param message Response message
     * @param meta Optional metadata
     * @returns Standardized API response
     */
    static ok<T = any>(data: T, message = 'Success', meta?: Record<string, any>): ApiResponse<T> {
        return this.success(HttpStatusCode.OK, data, message, meta);
    }

    /**
     * Creates a success response with status 201 Created
     * @param data Response data
     * @param message Response message
     * @param meta Optional metadata
     * @returns Standardized API response
     */
    static created<T = any>(
        data: T,
        message = 'Resource created successfully',
        meta?: Record<string, any>
    ): ApiResponse<T> {
        return this.success(HttpStatusCode.CREATED, data, message, meta);
    }

    /**
     * Creates a success response with status 202 Accepted
     * @param data Response data
     * @param message Response message
     * @param meta Optional metadata
     * @returns Standardized API response
     */
    static accepted<T = any>(
        data: T,
        message = 'Request accepted',
        meta?: Record<string, any>
    ): ApiResponse<T> {
        return this.success(HttpStatusCode.ACCEPTED, data, message, meta);
    }

    /**
     * Creates a success response with status 204 No Content
     * @param message Response message
     * @param meta Optional metadata
     * @returns Standardized API response
     */
    static noContent(message = 'No content', meta?: Record<string, any>): ApiResponse<object> {
        return this.success(HttpStatusCode.NO_CONTENT, {}, message, meta);
    }

    /**
     * Creates a generic success response with the specified status code
     * @param statusCode HTTP status code
     * @param data Response data
     * @param message Response message
     * @param meta Optional metadata
     * @returns Standardized API response
     */
    static success<T = any>(
        statusCode: HttpStatusCode,
        data: T,
        message: string,
        meta?: Record<string, any>
    ): ApiResponse<T> {
        return {
            code: statusCode,
            status: HttpStatusText[statusCode],
            message,
            data,
            ...(meta ? { meta } : {})
        };
    }

    /**
     * Creates an error response with status 400 Bad Request
     * @param message Error message
     * @param meta Optional metadata
     * @returns Standardized API response
     */
    static badRequest(message = 'Bad request', meta?: Record<string, any>): ApiResponse<object> {
        return this.error(HttpStatusCode.BAD_REQUEST, message, meta);
    }

    /**
     * Creates an error response with status 401 Unauthorized
     * @param message Error message
     * @param meta Optional metadata
     * @returns Standardized API response
     */
    static unauthorized(message = 'Unauthorized', meta?: Record<string, any>): ApiResponse<object> {
        return this.error(HttpStatusCode.UNAUTHORIZED, message, meta);
    }

    /**
     * Creates an error response with status 403 Forbidden
     * @param message Error message
     * @param meta Optional metadata
     * @returns Standardized API response
     */
    static forbidden(message = 'Forbidden', meta?: Record<string, any>): ApiResponse<object> {
        return this.error(HttpStatusCode.FORBIDDEN, message, meta);
    }

    /**
     * Creates an error response with status 404 Not Found
     * @param message Error message
     * @param meta Optional metadata
     * @returns Standardized API response
     */
    static notFound(
        message = 'Resource not found',
        meta?: Record<string, any>
    ): ApiResponse<object> {
        return this.error(HttpStatusCode.NOT_FOUND, message, meta);
    }

    /**
     * Creates an error response with status 500 Internal Server Error
     * @param message Error message
     * @param meta Optional metadata
     * @returns Standardized API response
     */
    static internalServerError(
        message = 'Internal server error',
        meta?: Record<string, any>
    ): ApiResponse<object> {
        return this.error(HttpStatusCode.INTERNAL_SERVER_ERROR, message, meta);
    }

    /**
     * Creates a generic error response with the specified status code
     * @param statusCode HTTP status code
     * @param message Error message
     * @param meta Optional metadata
     * @returns Standardized API response
     */
    static error(
        statusCode: HttpStatusCode,
        message: string,
        meta?: Record<string, any>
    ): ApiResponse<object> {
        return {
            code: statusCode,
            status: HttpStatusText[statusCode],
            message,
            data: {},
            ...(meta ? { meta } : {})
        };
    }
}
