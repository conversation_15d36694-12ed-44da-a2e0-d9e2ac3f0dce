import { CaseTransitionDto } from 'apps/case-management/src/dto/case-transition.dto';
import { CaseStatus } from '../enums/case-status.enum';
import { CaseTransitionData } from '../interfaces/case-transition.interface';

export const getCaseStatusDisplayName = (status: CaseStatus): string => {
    const displayNames = {
        [CaseStatus.DRAFT]: 'DRAFT',
        [CaseStatus.SUBMITTED]: 'SUBMITTED',
        [CaseStatus.UNDER_REVIEW]: 'UNDER_REVIEW',
        [CaseStatus.ASSIGNED]: 'ASSIGNED',
        [CaseStatus.IN_PROGRESS]: 'IN_PROGRESS',
        [CaseStatus.ON_HOLD]: 'ON_HOLD',
        [CaseStatus.PENDING_APPROVAL]: 'PENDING_APPROVAL',
        [CaseStatus.APPROVED]: 'APPROVED',
        [CaseStatus.REJECTED]: 'REJECTED',
        [CaseStatus.RESOLVED]: 'RESOLVED',
        [CaseStatus.CLOSED]: 'CLOSED',
        [CaseStatus.REOPENED]: 'REOPENED'
    };

    return displayNames[status] || status;
};

/**
 * Helper function to convert transition DTO to a consistent data object
 */
export function buildTransitionData(transitionDto: CaseTransitionDto): CaseTransitionData {
    return {
        ...transitionDto.data,
        notes: transitionDto.notes,
        isPrivate: transitionDto.isPrivate,
        assignedTo: transitionDto.assignedTo,
        holdReason: transitionDto.holdReason,
        reopenReason: transitionDto.reopenReason,
        assignments: transitionDto.assignments
    };
}
