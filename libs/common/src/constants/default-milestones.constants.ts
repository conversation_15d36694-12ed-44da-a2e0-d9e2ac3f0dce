import { TaskPriority } from '../typeorm/entities/tenant/task.entity';

export interface DefaultMilestoneConfig {
    name: string;
    description: string;
    sortOrder: number;
    targetDays?: number; // Days from case creation
    tasks: DefaultTaskConfig[];
}

export interface DefaultTaskConfig {
    title: string;
    description: string;
    priority: TaskPriority;
    estimatedDays?: number; // Days from milestone start
    isDefault: boolean;
}

/**
 * Central configuration for default conveyancing milestones and tasks
 * This is the single source of truth for all default milestone configurations
 *
 *
 * make changes to this we dont have a target days ! remove this for the category
 *
 * should we leave the milestone / task ----
 */
export const DEFAULT_CONVEYANCING_MILESTONES: DefaultMilestoneConfig[] = [
    {
        name: 'Initial Review & Documentation',
        description: 'Review client requirements and initial property documentation',
        sortOrder: 1,
        targetDays: 3,
        tasks: [
            {
                title: 'Review client questionnaire responses',
                description: 'Analyze client responses about property transaction details',
                priority: TaskPriority.HIGH,
                estimatedDays: 1,
                isDefault: true
            },
            {
                title: 'Verify property details',
                description: 'Confirm property address, title, and basic details',
                priority: TaskPriority.HIGH,
                estimatedDays: 1,
                isDefault: true
            },
            {
                title: 'Prepare client documentation package',
                description: 'Prepare initial documents and requirements for client',
                priority: TaskPriority.HIGH,
                estimatedDays: 1,
                isDefault: true
            },
            {
                title: 'Open case file and create matter',
                description: 'Set up internal case management and file structure',
                priority: TaskPriority.MEDIUM,
                estimatedDays: 1,
                isDefault: true
            }
        ]
    },
    {
        name: 'Property Searches & Enquiries',
        description: 'Conduct necessary property searches and raise enquiries',
        sortOrder: 2,
        targetDays: 10,
        tasks: [
            {
                title: 'Order local authority search',
                description: 'Request local authority search from relevant council',
                priority: TaskPriority.HIGH,
                estimatedDays: 1,
                isDefault: true
            },
            {
                title: 'Order environmental search',
                description: 'Request environmental and contamination search',
                priority: TaskPriority.MEDIUM,
                estimatedDays: 1,
                isDefault: true
            },
            {
                title: 'Order water and drainage search',
                description: 'Request water authority and drainage search',
                priority: TaskPriority.MEDIUM,
                estimatedDays: 1,
                isDefault: true
            },
            {
                title: 'Review title documents',
                description: 'Examine title deeds and registered documents',
                priority: TaskPriority.HIGH,
                estimatedDays: 2,
                isDefault: true
            },
            {
                title: 'Raise enquiries on title',
                description: "Submit enquiries to seller's solicitor regarding title issues",
                priority: TaskPriority.MEDIUM,
                estimatedDays: 1,
                isDefault: true
            }
        ]
    },
    {
        name: 'Mortgage & Financial Arrangements',
        description: 'Handle mortgage application and financial requirements',
        sortOrder: 3,
        targetDays: 21,
        tasks: [
            {
                title: 'Review mortgage offer',
                description: 'Examine mortgage offer terms and conditions',
                priority: TaskPriority.HIGH,
                estimatedDays: 1,
                isDefault: true
            },
            {
                title: 'Report to lender',
                description: 'Submit certificate of title to mortgage lender',
                priority: TaskPriority.HIGH,
                estimatedDays: 2,
                isDefault: true
            },
            {
                title: 'Arrange buildings insurance',
                description: 'Ensure adequate buildings insurance is in place',
                priority: TaskPriority.MEDIUM,
                estimatedDays: 1,
                isDefault: true
            },
            {
                title: 'Calculate completion funds',
                description: 'Prepare statement of completion monies required',
                priority: TaskPriority.HIGH,
                estimatedDays: 1,
                isDefault: true
            }
        ]
    },
    {
        name: 'Contract & Exchange Preparation',
        description: 'Prepare contracts and arrange exchange of contracts',
        sortOrder: 4,
        targetDays: 28,
        tasks: [
            {
                title: 'Review draft contract',
                description: 'Examine and approve draft contract terms',
                priority: TaskPriority.HIGH,
                estimatedDays: 2,
                isDefault: true
            },
            {
                title: 'Negotiate contract terms',
                description: 'Negotiate any amendments to contract terms',
                priority: TaskPriority.MEDIUM,
                estimatedDays: 3,
                isDefault: true
            },
            {
                title: 'Obtain client authority to exchange',
                description: 'Get client confirmation to proceed with exchange',
                priority: TaskPriority.HIGH,
                estimatedDays: 1,
                isDefault: true
            },
            {
                title: 'Exchange contracts',
                description: 'Complete exchange of contracts with other party',
                priority: TaskPriority.HIGHEST,
                estimatedDays: 0,
                isDefault: true
            }
        ]
    },
    {
        name: 'Pre-Completion Preparations',
        description: 'Complete all pre-completion requirements',
        sortOrder: 5,
        targetDays: 35,
        tasks: [
            {
                title: 'Prepare completion statement',
                description: 'Calculate final completion monies required',
                priority: TaskPriority.HIGH,
                estimatedDays: 1,
                isDefault: true
            },
            {
                title: 'Arrange mortgage funds',
                description: 'Request mortgage advance from lender',
                priority: TaskPriority.HIGH,
                estimatedDays: 2,
                isDefault: true
            },
            {
                title: 'Prepare transfer deed',
                description: 'Draft transfer deed for signature',
                priority: TaskPriority.MEDIUM,
                estimatedDays: 1,
                isDefault: true
            },
            {
                title: 'Pre-completion searches',
                description: 'Conduct final searches before completion',
                priority: TaskPriority.HIGH,
                estimatedDays: 1,
                isDefault: true
            }
        ]
    },
    {
        name: 'Completion & Post-Completion',
        description: 'Complete transaction and handle post-completion matters',
        sortOrder: 6,
        targetDays: 42,
        tasks: [
            {
                title: 'Complete transaction',
                description: 'Exchange completion monies and receive keys/deeds',
                priority: TaskPriority.HIGHEST,
                estimatedDays: 0,
                isDefault: true
            },
            {
                title: 'Register title at Land Registry',
                description: 'Submit application to register new ownership',
                priority: TaskPriority.HIGH,
                estimatedDays: 3,
                isDefault: true
            },
            {
                title: 'Pay Stamp Duty Land Tax',
                description: 'Submit SDLT return and payment to HMRC',
                priority: TaskPriority.HIGH,
                estimatedDays: 7,
                isDefault: true
            },
            {
                title: 'Send completion report to client',
                description: 'Provide final report and return client documents',
                priority: TaskPriority.MEDIUM,
                estimatedDays: 5,
                isDefault: true
            },
            {
                title: 'Archive case file',
                description: 'Complete file closure and archival procedures',
                priority: TaskPriority.LOW,
                estimatedDays: 2,
                isDefault: true
            }
        ]
    }
];

/**
 * Get default milestone configuration by case type
 * This allows for future expansion to support different case types
 *
 * keep this logic updated for just convenyacing
 */
export function getDefaultMilestonesByType(caseType: string): DefaultMilestoneConfig[] {
    switch (caseType) {
        case 'REAL_ESTATE':
        case 'CONVEYANCING':
            return DEFAULT_CONVEYANCING_MILESTONES;
        default:
            return [];
    }
}
