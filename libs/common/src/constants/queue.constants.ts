export const QUEUE_NAMES = {
    EMAIL: 'EMAIL_QUEUE',
    NOTIFICATION: 'NOTIFICATION_QUEUE',
    MULTI_CHANNEL: 'MULTI_CHANNEL_QUEUE'
} as const;

export type QueueName = (typeof QUEUE_NAMES)[keyof typeof QUEUE_NAMES];

export const QUEUE_PRIORITIES = {
    LOW: 1,
    NORMAL: 5,
    HIGH: 10,
    URGENT: 20
} as const;

// Generate job names with tenant context for shared queues
export const getJobName = (tenantId: string, queueType: QueueName, channels?: string[]): string => {
    // Extract the channel type from queue name (e.g., 'shared-email-queue' -> 'email')
    const channelType = queueType.replace('shared-', '').replace('-queue', '');

    if (channels && channels.length > 1) {
        // Multi-channel job name
        const sortedChannels = channels.sort().join('-');
        return `${channelType}-multi-${sortedChannels}-${tenantId}`;
    }

    // Single channel job name
    return `${channelType}-${tenantId}`;
};
export const getJobNameByChannel = (
    tenantId: string,
    channel: string,
    isMultiChannel: boolean = false
): string => {
    if (isMultiChannel) {
        return `multi-channel-${tenantId}`;
    }
    return `${channel}-${tenantId}`;
};
