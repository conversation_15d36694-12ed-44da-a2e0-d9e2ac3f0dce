import { CasePermission } from '../enums/case-permissions.enum';
import { CaseStatus } from '../enums/case-status.enum';
import { AppRole } from '../enums/roles.enums';

/**
 * Default permissions for each application role
 */
export const ROLE_PERMISSIONS: Record<AppRole, CasePermission[]> = {
    [AppRole.USER]: [
        CasePermission.VIEW_CASE,
        CasePermission.VIEW_DETAILS,
        CasePermission.VIEW_HISTORY
    ],

    [AppRole.LAWYER]: [
        CasePermission.VIEW_ATTACHMENTS,
        CasePermission.ADD_NOTE,
        CasePermission.EDIT_DETAILS,
        CasePermission.EDIT_ATTACHMENTS,
        CasePermission.ADD_NOTE,
        CasePermission.SUBMIT_CASE,
        CasePermission.START_WORK,
        CasePermission.PUT_ON_HOLD,
        CasePermission.RESUME_WORK,
        CasePermission.REQUEST_APPROVAL,
        CasePermission.RESOLVE_CASE
    ],

    [AppRole.SUPERVISOR]: [
        CasePermission.ASSIGN_USERS,
        CasePermission.APPROVE_CASE,
        CasePermission.REJECT_CASE,
        CasePermission.REVIEW_CASE
    ],

    [AppRole.MANAGER]: [CasePermission.CLOSE_CASE, CasePermission.REOPEN_CASE],

    [AppRole.PARTNER]: [],

    [AppRole.FINANCE]: [],

    [AppRole.ADMIN]: [
        CasePermission.MANAGE_ALL, // Grants all permissions
        CasePermission.SELF_ASSIGN // Specific permission to self-assign
    ],

    [AppRole.SUPER_ADMIN]: [
        CasePermission.MANAGE_ALL, // Grants all permissions
        CasePermission.SELF_ASSIGN // Specific permission to self-assign
    ]
};

/**
 * Map from case status transitions to required permissions
 */
export const TRANSITION_PERMISSIONS: Record<string, CasePermission> = {
    [`${CaseStatus.DRAFT}:${CaseStatus.SUBMITTED}`]: CasePermission.SUBMIT_CASE,
    [`${CaseStatus.SUBMITTED}:${CaseStatus.DRAFT}`]: CasePermission.DRAFT_SUBMITED_CASE,
    [`${CaseStatus.SUBMITTED}:${CaseStatus.UNDER_REVIEW}`]: CasePermission.REVIEW_CASE,
    [`${CaseStatus.UNDER_REVIEW}:${CaseStatus.ASSIGNED}`]: CasePermission.ASSIGN_CASE,
    [`${CaseStatus.ASSIGNED}:${CaseStatus.IN_PROGRESS}`]: CasePermission.START_WORK,
    [`${CaseStatus.IN_PROGRESS}:${CaseStatus.ON_HOLD}`]: CasePermission.PUT_ON_HOLD,
    [`${CaseStatus.ON_HOLD}:${CaseStatus.IN_PROGRESS}`]: CasePermission.RESUME_WORK,
    [`${CaseStatus.IN_PROGRESS}:${CaseStatus.PENDING_APPROVAL}`]: CasePermission.REQUEST_APPROVAL,
    [`${CaseStatus.PENDING_APPROVAL}:${CaseStatus.APPROVED}`]: CasePermission.APPROVE_CASE,
    [`${CaseStatus.PENDING_APPROVAL}:${CaseStatus.REJECTED}`]: CasePermission.REJECT_CASE,
    [`${CaseStatus.APPROVED}:${CaseStatus.RESOLVED}`]: CasePermission.RESOLVE_CASE,
    [`${CaseStatus.RESOLVED}:${CaseStatus.CLOSED}`]: CasePermission.CLOSE_CASE,
    [`${CaseStatus.CLOSED}:${CaseStatus.REOPENED}`]: CasePermission.REOPEN_CASE,
    [`${CaseStatus.REOPENED}:${CaseStatus.IN_PROGRESS}`]: CasePermission.START_WORK,
    [`${CaseStatus.REJECTED}:${CaseStatus.IN_PROGRESS}`]: CasePermission.START_WORK
};
