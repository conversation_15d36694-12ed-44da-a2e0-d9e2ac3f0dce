import {
    Injectable,
    CanActivate,
    ExecutionContext,
    ForbiddenException,
    Logger,
    Inject,
    Optional
} from '@nestjs/common';
import { Request } from 'express';
import { AuditService } from '../audit/audit.service';
import { PermissionAuditAction } from '../permissions/permission.constants';
import {
    ICaseAssignmentChecker,
    CASE_ASSIGNMENT_CHECKER
} from '../interfaces/case-assignment-checker.interface';

/**
 * Interface for user object in request
 */
interface AuthenticatedUser {
    id: string;
    systemUserId: string;
    roleGroups?: { key: string; isAdmin: boolean; roleId: string }[];
    roleGroupKey?: string;
    roleGroupId?: string;
    isGroupAdmin?: boolean;
    isSuperAdmin: boolean;
    [key: string]: any;
}

/**
 * Extended Request interface with user
 */
interface AuthenticatedRequest extends Request {
    user: AuthenticatedUser;
}

/**
 * ConveyancerCaseGuard: Ensures conveyancers can only access their own cases.
 * - Super Admin: Allows unrestricted access (bypasses all checks)
 * - Non-conveyancers: Allows access (not subject to case assignment restrictions)
 * - Conveyancers: Checks if user is assigned to the case using CaseAssignmentService
 * - Logs every check via AuditService
 */
@Injectable()
export class ConveyancerCaseGuard implements CanActivate {
    private readonly logger = new Logger(ConveyancerCaseGuard.name);

    constructor(
        private readonly auditService: AuditService,
        @Inject(CASE_ASSIGNMENT_CHECKER)
        @Optional()
        private readonly caseAssignmentChecker?: ICaseAssignmentChecker
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const req = context.switchToHttp().getRequest<AuthenticatedRequest>();
        const user: AuthenticatedUser = req.user;
        const tenantId: string = req.headers['x-tenant-id'] as string;
        const caseId: string = req.params['id'] || req.params['caseId'];

        // Super Admin bypass: Super Admins have unrestricted access
        if (user.isSuperAdmin) {
            this.logger.debug(`User ${user.id} is Super Admin, allowing unrestricted access`);
            await this.log(req, tenantId, user, caseId, true);
            return true;
        }

        // Only apply to conveyancers role group
        // Check if user belongs to the 'conveyancers' role group
        const isConveyancer =
            user.roleGroups?.some((rg: any) => rg.key === 'conveyancers') ||
            user.roleGroupKey === 'conveyancers';

        if (!isConveyancer) {
            this.logger.debug(`User ${user.id} is not a conveyancer, allowing access`);
            return true;
        }

        // Implement your own logic to check if user is assigned to the case
        const isAssigned = await this.isUserAssignedToCase(user.id, caseId);
        await this.log(req, tenantId, user, caseId, isAssigned);
        if (!isAssigned)
            throw new ForbiddenException('Conveyancers can only access their own cases');
        return true;
    }

    private async isUserAssignedToCase(userId: string, caseId: string): Promise<boolean> {
        // If no case assignment checker is available, allow access (fail open for backward compatibility)
        if (!this.caseAssignmentChecker) {
            this.logger.warn('CaseAssignmentChecker not available, allowing access to case');
            return true;
        }

        try {
            // Use the injected service to check if user is assigned to the case
            const isAssigned = await this.caseAssignmentChecker.isUserAssignedToCase(
                caseId,
                userId
            );
            this.logger.debug(`User ${userId} assignment check for case ${caseId}: ${isAssigned}`);
            return isAssigned;
        } catch (error) {
            this.logger.error(
                `Error checking case assignment for user ${userId} and case ${caseId}:`,
                error
            );
            // Fail closed - deny access if we can't verify assignment
            return false;
        }
    }

    private async log(
        req: AuthenticatedRequest,
        tenantId: string,
        user: AuthenticatedUser,
        caseId: string,
        allowed: boolean
    ): Promise<void> {
        await this.auditService.logPermissionCheck({
            userId: user.id,
            tenantId,
            action: allowed
                ? PermissionAuditAction.PERMISSION_GRANTED
                : PermissionAuditAction.PERMISSION_DENIED,
            resourceType: 'CASE',
            resourceId: caseId || 'unknown',
            timestamp: new Date().toISOString(),
            result: allowed
                ? PermissionAuditAction.PERMISSION_GRANTED
                : PermissionAuditAction.PERMISSION_DENIED,
            permissions: [],
            ipAddress: req.ip || 'unknown',
            userAgent: req.headers['user-agent'] || 'unknown'
        });
    }
}
