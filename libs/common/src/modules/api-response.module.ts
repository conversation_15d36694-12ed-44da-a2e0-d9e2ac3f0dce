import { Module, Global } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { AllExceptionsFilter } from '../filters/all-exceptions.filter';
import { SuccessResponseInterceptor } from '../interceptors/success-response.interceptor';

/**
 * Configuration options for the ApiResponseModule
 */
export interface ApiResponseModuleOptions {
    /**
     * Routes to exclude from the success response interceptor
     */
    excludePaths?: string[];
}

/**
 * Module that provides the global exception filter and success response interceptor
 */
@Global()
@Module({
    providers: [
        {
            provide: APP_FILTER,
            useClass: AllExceptionsFilter
        },
        {
            provide: APP_INTERCEPTOR,
            useFactory: (options: ApiResponseModuleOptions = {}) => {
                return new SuccessResponseInterceptor({
                    excludePaths: options.excludePaths || ['/health', '/ping']
                });
            },
            inject: ['API_RESPONSE_OPTIONS']
        }
    ],
    exports: []
})
export class ApiResponseModule {
    /**
     * Registers the ApiResponseModule with custom options
     * @param options Configuration options
     * @returns Module with custom options
     */
    static forRoot(options: ApiResponseModuleOptions = {}) {
        return {
            module: ApiResponseModule,
            providers: [
                {
                    provide: 'API_RESPONSE_OPTIONS',
                    useValue: options
                }
            ]
        };
    }
}
