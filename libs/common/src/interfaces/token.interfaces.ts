/**
 * Interface definitions for CustomToken system
 */

/**
 * Token resolution context for generating token values
 */
export interface TokenResolutionContext {
    /** Case data for case-related tokens */
    case?: any;
    /** Client data for client-related tokens */
    client?: any;
    /** Property data for property-related tokens */
    property?: any;
    /** User data for user-related tokens */
    user?: any;
    /** Firm/tenant data for firm-related tokens */
    firm?: any;
    /** Additional custom entity data */
    customEntities?: Record<string, any>;
    /** Current timestamp for system tokens */
    timestamp?: Date;
}

/**
 * Token resolution result
 */
export interface TokenResolutionResult {
    /** The resolved token value */
    value: any;
    /** Whether the token was successfully resolved */
    success: boolean;
    /** Error message if resolution failed */
    error?: string;
    /** Data type of the resolved value */
    dataType: string;
    /** Whether the value was transformed */
    transformed: boolean;
}

/**
 * Token validation result for templates
 */
export interface TokenValidationResult {
    /** Whether all tokens are valid */
    isValid: boolean;
    /** List of valid tokens found */
    validTokens: string[];
    /** List of invalid/unknown tokens */
    invalidTokens: string[];
    /** List of system tokens found */
    systemTokens: string[];
    /** List of custom tokens found */
    customTokens: string[];
    /** Validation errors */
    errors: string[];
    /** Validation warnings */
    warnings: string[];
}

/**
 * Entity field mapping configuration
 */
export interface EntityFieldMapping {
    /** The entity name (e.g., 'case', 'client', 'property') */
    entityName: string;
    /** The field path using dot notation (e.g., 'property.address.street') */
    fieldPath: string;
    /** Whether this field is a relationship */
    isRelation?: boolean;
    /** Related entity name if this is a relationship */
    relatedEntity?: string;
    /** Join conditions for relationships */
    joinConditions?: Record<string, string>;
}

/**
 * Token creation request
 */
export interface CreateTokenRequest {
    /** Unique token name */
    tokenName: string;
    /** Token description */
    description?: string;
    /** Data type of the token */
    dataType: string;
    /** Entity name to resolve from */
    entityName: string;
    /** Field path within the entity */
    fieldPath: string;
    /** Transformation configuration */
    transformationConfig?: Record<string, any>;
    /** Validation configuration */
    validationConfig?: Record<string, any>;
    /** Token category */
    category?: string;
    /** Token tags */
    tags?: string[];
    /** Compatible template types */
    compatibleTemplateTypes?: string[];
    /** Compatible case types */
    compatibleCaseTypes?: string[];
}

/**
 * Token update request
 */
export interface UpdateTokenRequest {
    /** Token description */
    description?: string;
    /** Entity name to resolve from */
    entityName?: string;
    /** Field path within the entity */
    fieldPath?: string;
    /** Transformation configuration */
    transformationConfig?: Record<string, any>;
    /** Validation configuration */
    validationConfig?: Record<string, any>;
    /** Token category */
    category?: string;
    /** Token tags */
    tags?: string[];
    /** Compatible template types */
    compatibleTemplateTypes?: string[];
    /** Compatible case types */
    compatibleCaseTypes?: string[];
    /** Token status */
    status?: string;
    /** Active status */
    isActive?: boolean;
}

/**
 * Available entity information for token creation
 */
export interface AvailableEntity {
    /** Entity name */
    name: string;
    /** Display name for UI */
    displayName: string;
    /** Entity description */
    description: string;
    /** Available fields */
    fields: AvailableField[];
    /** Related entities */
    relations: EntityRelation[];
}

/**
 * Available field information
 */
export interface AvailableField {
    /** Field name */
    name: string;
    /** Full field path */
    path: string;
    /** Display name for UI */
    displayName: string;
    /** Field data type */
    dataType: string;
    /** Whether field is nullable */
    nullable: boolean;
    /** Field description */
    description?: string;
    /** Example value */
    example?: any;
}

/**
 * Entity relation information
 */
export interface EntityRelation {
    /** Relation property name */
    propertyName: string;
    /** Related entity name */
    relatedEntity: string;
    /** Display name for UI */
    displayName: string;
    /** Relation type (OneToOne, OneToMany, ManyToOne, ManyToMany) */
    relationType: string;
    /** Whether relation is nullable */
    nullable: boolean;
}

/**
 * Token usage statistics
 */
export interface TokenUsageStats {
    /** Token ID */
    tokenId: string;
    /** Token name */
    tokenName: string;
    /** Total usage count */
    usageCount: number;
    /** Last used timestamp */
    lastUsedAt?: Date;
    /** Templates using this token */
    templatesUsing: string[];
    /** Usage trend (increase/decrease) */
    usageTrend: 'increasing' | 'decreasing' | 'stable';
}

/**
 * Bulk token operation result
 */
export interface BulkTokenOperationResult {
    /** Number of successful operations */
    successful: number;
    /** Number of failed operations */
    failed: number;
    /** Detailed results for each token */
    results: Array<{
        tokenName: string;
        success: boolean;
        error?: string;
    }>;
}
