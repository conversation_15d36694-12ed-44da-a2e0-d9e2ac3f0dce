/**
 * Interface for checking case assignments
 * This allows the ConveyancerCaseGuard to check case assignments
 * without directly depending on the case-management module
 */
export interface ICaseAssignmentChecker {
    /**
     * Checks if a user is assigned to a specific case
     * @param caseId The case ID to check
     * @param userId The user ID to check
     * @returns Promise<boolean> true if user is assigned to the case, false otherwise
     */
    isUserAssignedToCase(caseId: string, userId: string): Promise<boolean>;
}

/**
 * Token for dependency injection of ICaseAssignmentChecker
 */
export const CASE_ASSIGNMENT_CHECKER = Symbol('CASE_ASSIGNMENT_CHECKER');
