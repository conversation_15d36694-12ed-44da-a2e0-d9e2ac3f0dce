import { CaseAssignmentDto } from 'apps/case-management/src/dto/case-transition.dto';

/**
 * Interface representing data that can be passed with a case transition
 */
export interface CaseTransitionData {
    // General transition data
    notes?: string;
    isPrivate?: boolean;

    // Assignment related
    assignedTo?: string;
    userName?: string;
    assignments?: CaseAssignmentDto[];

    // State specific reasons
    holdReason?: string;
    reopenReason?: string;
    resolutionNotes?: string;
    closureNotes?: string;
    rejectionReason?: string;

    // Any additional custom data
    [key: string]: any;
}
