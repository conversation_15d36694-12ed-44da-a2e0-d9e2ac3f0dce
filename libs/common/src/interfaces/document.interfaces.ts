/**
 * Interface definitions for Document Template system
 * These interfaces provide type safety and documentation for complex JSON fields
 */

/**
 * Configuration for automated document generation triggers
 */
export interface GenerationTriggerConfig {
    /** The trigger type that activates this template */
    trigger: string;
    /** Conditions that must be met for the trigger to activate */
    conditions?: Record<string, any>;
    /** Delay in minutes before generation starts (optional) */
    delayMinutes?: number;
    /** Priority level for the generation job */
    priority?: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
}

/**
 * Options for document generation process
 */
export interface DocumentGenerationOptions {
    /** Automatically attach generated document to the case */
    autoAttachToCase?: boolean;
    /** Automatically email the document to the client */
    autoEmailToClient?: boolean;
    /** Email template type to use for sending */
    emailTemplateType?: string;
    /** Custom output filename (can include tokens) */
    outputFileName?: string;
    /** Send notification when generation completes */
    notifyOnCompletion?: boolean;
    /** Also generate a PDF version */
    generatePdf?: boolean;
    /** Custom email recipients (in addition to client) */
    additionalEmailRecipients?: string[];
    /** Custom email subject (can include tokens) */
    emailSubject?: string;
}

/**
 * Template data structure for document generation
 */
export interface TemplateData {
    /** Client-related data */
    client?: Record<string, any>;
    /** Case-related data */
    case?: Record<string, any>;
    /** Firm/tenant-related data */
    firm?: Record<string, any>;
    /** System-generated data (dates, reference numbers, etc.) */
    system?: Record<string, any>;
    /** Custom fields specific to the template */
    custom?: Record<string, any>;
}

/**
 * Execution log entry for tracking generation progress
 */
export interface ExecutionLogEntry {
    /** Timestamp of the log entry */
    timestamp: string;
    /** Log level */
    level: 'info' | 'warn' | 'error' | 'debug';
    /** Log message */
    message: string;
    /** Additional details (optional) */
    details?: Record<string, any>;
    /** Processing step or stage */
    step?: string;
    /** Duration of the step in milliseconds (optional) */
    durationMs?: number;
}

/**
 * Token change tracking for version control
 */
export interface TokenChanges {
    /** Tokens that were added in this version */
    added?: string[];
    /** Tokens that were removed in this version */
    removed?: string[];
    /** Tokens that were modified (renamed or changed format) */
    modified?: Array<{
        from: string;
        to: string;
        reason?: string;
    }>;
}

/**
 * Metadata structure for templates and generations
 */
export interface DocumentTemplateMetadata {
    /** Creation context */
    createdFrom?: 'upload' | 'duplicate' | 'migration';
    /** Original template source (if duplicated) */
    originalTemplateId?: string;
    /** Template complexity indicators */
    complexity?: {
        tokenCount: number;
        conditionalLogicCount: number;
        imageCount: number;
        tableCount: number;
    };
    /** Usage analytics */
    analytics?: {
        averageGenerationTimeMs: number;
        successRate: number;
        lastOptimized: string;
    };
    /** Integration settings */
    integrations?: {
        crmFields?: string[];
        externalSystems?: string[];
        webhooks?: string[];
    };
}

/**
 * File information structure
 */
export interface FileInfo {
    /** Original filename */
    fileName: string;
    /** File path in storage */
    filePath: string;
    /** S3 object key */
    s3Key: string;
    /** S3 bucket name */
    s3Bucket: string;
    /** File size in bytes */
    sizeInBytes: number;
    /** File checksum (MD5 or SHA-256) */
    checksum: string;
    /** MIME type */
    mimeType: string;
}
