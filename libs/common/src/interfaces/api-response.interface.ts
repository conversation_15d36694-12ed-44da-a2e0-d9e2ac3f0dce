/**
 * Standard API response interface
 * All API responses will follow this structure
 */
export interface ApiResponse<T = unknown> {
    /**
     * HTTP status code
     */
    code: number;

    /**
     * HTTP status text
     */
    status: string;

    /**
     * Response message
     */
    message: string;

    /**
     * Response data
     */
    data: T | null;

    /**
     * Optional metadata
     */
    meta?: Record<string, unknown>;
}
