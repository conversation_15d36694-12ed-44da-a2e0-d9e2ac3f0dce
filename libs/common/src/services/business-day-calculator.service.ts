import { Injectable } from '@nestjs/common';
import { isBankHoliday } from '../config/bank-holidays.config';

/**
 * Service for calculating business days and working day logic for conveyancing
 */
@Injectable()
export class BusinessDayCalculatorService {
    /**
     * UK Bank Holidays are now managed via configuration file
     * @see libs/common/src/config/bank-holidays.config.ts
     */

    /**
     * Check if a date is a UK Bank Holiday
     */
    private isUKBankHoliday(date: Date): boolean {
        return isBankHoliday(date);
    }

    /**
     * Check if a date is a weekend (Saturday or Sunday)
     */
    private isWeekend(date: Date): boolean {
        const dayOfWeek = date.getDay();
        return dayOfWeek === 0 || dayOfWeek === 6; // Sunday = 0, Saturday = 6
    }

    /**
     * Check if a date is a working day (not weekend or bank holiday)
     */
    isWorkingDay(date: Date): boolean {
        return !this.isWeekend(date) && !this.isUKBankHoliday(date);
    }

    /**
     * Get the previous working day from a given date
     */
    getPreviousWorkingDay(date: Date): Date {
        const previousDay = new Date(date);
        previousDay.setDate(previousDay.getDate() - 1);

        while (!this.isWorkingDay(previousDay)) {
            previousDay.setDate(previousDay.getDate() - 1);
        }

        return previousDay;
    }

    /**
     * Get the next working day from a given date
     */
    getNextWorkingDay(date: Date): Date {
        const nextDay = new Date(date);
        nextDay.setDate(nextDay.getDate() + 1);

        while (!this.isWorkingDay(nextDay)) {
            nextDay.setDate(nextDay.getDate() + 1);
        }

        return nextDay;
    }

    /**
     * Add working days to a date
     */
    addWorkingDays(date: Date, workingDays: number): Date {
        const result = new Date(date);
        let daysToAdd = workingDays;

        while (daysToAdd > 0) {
            result.setDate(result.getDate() + 1);
            if (this.isWorkingDay(result)) {
                daysToAdd--;
            }
        }

        return result;
    }

    /**
     * Subtract working days from a date
     */
    subtractWorkingDays(date: Date, workingDays: number): Date {
        const result = new Date(date);
        let daysToSubtract = workingDays;

        while (daysToSubtract > 0) {
            result.setDate(result.getDate() - 1);
            if (this.isWorkingDay(result)) {
                daysToSubtract--;
            }
        }

        return result;
    }

    /**
     * Get working days between two dates
     */
    getWorkingDaysBetween(startDate: Date, endDate: Date): number {
        let count = 0;
        const current = new Date(startDate);

        while (current <= endDate) {
            if (this.isWorkingDay(current)) {
                count++;
            }
            current.setDate(current.getDate() + 1);
        }

        return count;
    }

    /**
     * Format date as DD/MM/YYYY for UK legal documents
     */
    formatUKDate(date: Date): string {
        return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    /**
     * Format date as long format for legal documents (e.g., "2nd July 2025")
     */
    formatUKDateLong(date: Date): string {
        return date.toLocaleDateString('en-GB', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        });
    }

    /**
     * Get day with ordinal suffix (1st, 2nd, 3rd, etc.)
     */
    private getOrdinalSuffix(day: number): string {
        if (day > 3 && day < 21) return 'th';
        switch (day % 10) {
            case 1:
                return 'st';
            case 2:
                return 'nd';
            case 3:
                return 'rd';
            default:
                return 'th';
        }
    }

    /**
     * Format date with ordinal day for legal documents (e.g., "2nd July 2025")
     */
    formatUKDateWithOrdinal(date: Date): string {
        const day = date.getDate();
        const month = date.toLocaleDateString('en-GB', { month: 'long' });
        const year = date.getFullYear();

        return `${day}${this.getOrdinalSuffix(day)} ${month} ${year}`;
    }
}
