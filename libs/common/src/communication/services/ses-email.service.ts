import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SESClient, SendTemplatedEmailCommand } from '@aws-sdk/client-ses';
import { UnifiedTemplateConfig } from './template.service';

export interface SESEmailData {
    to: string;
    templateType: string;
    templateData: Record<string, any>;
    tenantId: string;
    subject?: string;
}

export interface SESEmailResult {
    messageId: string;
    provider: 'ses';
    status: 'sent' | 'failed';
    templateType?: string;
    error?: string;
}

@Injectable()
export class SESEmailService {
    private readonly logger = new Logger(SESEmailService.name);
    private sesClient: SESClient | null = null;
    private readonly enabled: boolean;

    constructor(private readonly configService: ConfigService) {
        this.enabled = this.initializeSESClient();
    }

    private initializeSESClient(): boolean {
        const region =
            this.configService.get('AWS_SES_REGION') ||
            this.configService.get('AWS_REGION') ||
            'us-east-1';
        const accessKeyId = this.configService.get('AWS_ACCESS_KEY_ID');
        const secretAccessKey = this.configService.get('AWS_SECRET_ACCESS_KEY');
        // const sessionToken = this.configService.get('AWS_SESSION_TOKEN');

        if (!accessKeyId || !secretAccessKey) {
            this.logger.debug(
                'SES not configured - missing AWS credentials (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)'
            );
            return false;
        }

        try {
            const credentials: any = {
                accessKeyId,
                secretAccessKey
            };

            // if (sessionToken) {
            //     credentials.sessionToken = sessionToken;
            // }

            this.sesClient = new SESClient({
                region,
                credentials,
                maxAttempts: 3
            });

            this.logger.log(`SES client initialized for region: ${region}`);
            return true;
        } catch (error) {
            this.logger.error(`Failed to initialize SES client: ${error.message}`);
            return false;
        }
    }

    async sendTemplatedEmail(
        emailData: SESEmailData,
        template: UnifiedTemplateConfig
    ): Promise<SESEmailResult> {
        if (!this.enabled || !this.sesClient) {
            throw new Error('SES is not properly configured');
        }

        this.validateEmailData(emailData);
        const fromEmail = this.configService.get('FROM_EMAIL') || '<EMAIL>';

        try {
            const command = new SendTemplatedEmailCommand({
                Source: fromEmail,
                Destination: {
                    ToAddresses: [emailData.to.trim()]
                },
                Template: template.sesTemplateName,
                TemplateData: JSON.stringify(emailData.templateData),
                Tags: [
                    {
                        Name: 'tenant_id',
                        Value: emailData.tenantId.substring(0, 50) // AWS tag value limit
                    },
                    {
                        Name: 'template_type',
                        Value: emailData.templateType
                    },
                    ...template.categories.slice(0, 8).map((category, index) => ({
                        // AWS tag limit with unique names to avoid duplicates
                        Name: `category_${index + 1}`,
                        Value: category.substring(0, 50)
                    }))
                ]
            });

            this.logger.debug(
                `Sending SES templated email with template: ${template.sesTemplateName} to ${emailData.to}`
            );
            const response = await this.sesClient.send(command);

            return {
                messageId: response.MessageId || `ses-${Date.now()}`,
                provider: 'ses',
                status: 'sent',
                templateType: emailData.templateType
            };
        } catch (error) {
            this.logger.error(
                `SES templated email failed for template ${template.sesTemplateName}: ${error.message}`,
                {
                    error: error.message,
                    templateType: emailData.templateType,
                    recipient: emailData.to
                }
            );
            throw new Error(`SES templated email failed: ${error.message}`);
        }
    }

    private validateEmailData(emailData: SESEmailData): void {
        if (!emailData.to || !this.isValidEmail(emailData.to)) {
            throw new Error(`Invalid email address: ${emailData.to}`);
        }

        if (!emailData.tenantId || emailData.tenantId.trim().length === 0) {
            throw new Error('Tenant ID is required');
        }

        if (!emailData.templateType || emailData.templateType.trim().length === 0) {
            throw new Error('Template type is required');
        }

        if (!emailData.templateData || typeof emailData.templateData !== 'object') {
            throw new Error('Template data is required and must be an object');
        }
    }

    private isValidEmail(email: string): boolean {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email.trim());
    }

    isEnabled(): boolean {
        return this.enabled;
    }

    getStatus(): { configured: boolean; region?: string; status: string } {
        return {
            configured: this.enabled,
            region:
                this.configService.get('AWS_SES_REGION') || this.configService.get('AWS_REGION'),
            status: this.enabled ? 'active' : 'not configured'
        };
    }
}
