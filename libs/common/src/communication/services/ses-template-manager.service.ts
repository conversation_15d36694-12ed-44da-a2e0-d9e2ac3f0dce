import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
    SESClient,
    CreateTemplateCommand,
    UpdateTemplateCommand,
    DeleteTemplateCommand,
    GetTemplateCommand,
    ListTemplatesCommand,
    Template,
    TemplateDoesNotExistException,
    AlreadyExistsException
} from '@aws-sdk/client-ses';
import {
    SESTemplateException,
    ConfigurationException
} from '../exceptions/communication.exceptions';

export interface SESTemplateData {
    templateName: string;
    subject: string;
    htmlPart: string;
    textPart?: string;
}

export interface SESTemplateInfo {
    name: string;
    createdTimestamp?: Date;
}

export interface SESTemplateResult {
    templateName: string;
    status: 'created' | 'updated' | 'deleted' | 'exists';
    message?: string;
    error?: string;
}

@Injectable()
export class SESTemplateManagerService {
    private readonly logger = new Logger(SESTemplateManagerService.name);
    private sesClient: SESClient | null = null;
    private readonly enabled: boolean;

    constructor(private readonly configService: ConfigService) {
        this.enabled = this.initializeSESClient();
    }

    private initializeSESClient(): boolean {
        const region =
            this.configService.get('AWS_SES_REGION') ||
            this.configService.get('AWS_REGION') ||
            'us-east-1';
        const accessKeyId = this.configService.get('AWS_ACCESS_KEY_ID');
        const secretAccessKey = this.configService.get('AWS_SECRET_ACCESS_KEY');
        // const sessionToken = this.configService.get('AWS_SESSION_TOKEN');

        if (!accessKeyId || !secretAccessKey) {
            this.logger.debug('SES not configured - missing AWS credentials');
            return false;
        }

        try {
            const credentials: any = {
                accessKeyId,
                secretAccessKey
            };

            // if (sessionToken) {
            //     credentials.sessionToken = sessionToken;
            // }

            this.sesClient = new SESClient({
                region,
                credentials,
                maxAttempts: 3
            });

            this.logger.log(`SES Template Manager initialized for region: ${region}`);
            return true;
        } catch (error) {
            this.logger.error(`Failed to initialize SES client: ${error.message}`);
            return false;
        }
    }

    async createTemplate(templateData: SESTemplateData): Promise<SESTemplateResult> {
        if (!this.enabled || !this.sesClient) {
            throw new ConfigurationException('SES Template Manager', [
                'AWS_ACCESS_KEY_ID',
                'AWS_SECRET_ACCESS_KEY'
            ]);
        }

        this.validateTemplateData(templateData);

        try {
            const template: Template = {
                TemplateName: templateData.templateName,
                SubjectPart: templateData.subject,
                HtmlPart: templateData.htmlPart,
                TextPart: templateData.textPart || this.generateTextFromHtml(templateData.htmlPart)
            };

            const command = new CreateTemplateCommand({ Template: template });
            await this.sesClient.send(command);

            this.logger.log(`SES template created successfully: ${templateData.templateName}`);

            return {
                templateName: templateData.templateName,
                status: 'created',
                message: `Template '${templateData.templateName}' created successfully`
            };
        } catch (error) {
            if (error instanceof AlreadyExistsException) {
                this.logger.warn(`Template already exists: ${templateData.templateName}`);
                return {
                    templateName: templateData.templateName,
                    status: 'exists',
                    message: `Template '${templateData.templateName}' already exists`
                };
            }

            this.logger.error(
                `Failed to create SES template ${templateData.templateName}: ${error.message}`
            );
            throw new SESTemplateException(templateData.templateName, 'create', error.message, {
                templateData
            });
        }
    }

    async updateTemplate(templateData: SESTemplateData): Promise<SESTemplateResult> {
        if (!this.enabled || !this.sesClient) {
            throw new ConfigurationException('SES Template Manager', [
                'AWS_ACCESS_KEY_ID',
                'AWS_SECRET_ACCESS_KEY'
            ]);
        }

        this.validateTemplateData(templateData);

        try {
            const template: Template = {
                TemplateName: templateData.templateName,
                SubjectPart: templateData.subject,
                HtmlPart: templateData.htmlPart,
                TextPart: templateData.textPart || this.generateTextFromHtml(templateData.htmlPart)
            };

            const command = new UpdateTemplateCommand({ Template: template });
            await this.sesClient.send(command);

            this.logger.log(`SES template updated successfully: ${templateData.templateName}`);

            return {
                templateName: templateData.templateName,
                status: 'updated',
                message: `Template '${templateData.templateName}' updated successfully`
            };
        } catch (error) {
            if (error instanceof TemplateDoesNotExistException) {
                this.logger.warn(
                    `Template does not exist for update: ${templateData.templateName}`
                );
                // Try to create it instead
                return await this.createTemplate(templateData);
            }

            this.logger.error(
                `Failed to update SES template ${templateData.templateName}: ${error.message}`
            );
            throw new SESTemplateException(templateData.templateName, 'update', error.message, {
                templateData
            });
        }
    }

    async deleteTemplate(templateName: string): Promise<SESTemplateResult> {
        if (!this.enabled || !this.sesClient) {
            throw new ConfigurationException('SES Template Manager', [
                'AWS_ACCESS_KEY_ID',
                'AWS_SECRET_ACCESS_KEY'
            ]);
        }

        try {
            const command = new DeleteTemplateCommand({ TemplateName: templateName });
            await this.sesClient.send(command);

            this.logger.log(`SES template deleted successfully: ${templateName}`);

            return {
                templateName,
                status: 'deleted',
                message: `Template '${templateName}' deleted successfully`
            };
        } catch (error) {
            if (error instanceof TemplateDoesNotExistException) {
                this.logger.warn(`Template does not exist for deletion: ${templateName}`);
                return {
                    templateName,
                    status: 'deleted',
                    message: `Template '${templateName}' does not exist`
                };
            }

            this.logger.error(`Failed to delete SES template ${templateName}: ${error.message}`);
            throw new SESTemplateException(templateName, 'delete', error.message);
        }
    }

    async getTemplate(templateName: string): Promise<Template> {
        if (!this.enabled || !this.sesClient) {
            throw new ConfigurationException('SES Template Manager', [
                'AWS_ACCESS_KEY_ID',
                'AWS_SECRET_ACCESS_KEY'
            ]);
        }

        try {
            const command = new GetTemplateCommand({ TemplateName: templateName });
            const response = await this.sesClient.send(command);

            if (!response.Template) {
                throw new Error(`Template response is empty for: ${templateName}`);
            }

            return response.Template;
        } catch (error) {
            if (error instanceof TemplateDoesNotExistException) {
                throw new SESTemplateException(
                    templateName,
                    'get',
                    `Template '${templateName}' does not exist`
                );
            }

            this.logger.error(`Failed to get SES template ${templateName}: ${error.message}`);
            throw new SESTemplateException(templateName, 'get', error.message);
        }
    }

    async listTemplates(
        maxItems?: number,
        nextToken?: string
    ): Promise<{
        templates: SESTemplateInfo[];
        nextToken?: string;
    }> {
        if (!this.enabled || !this.sesClient) {
            throw new ConfigurationException('SES Template Manager', [
                'AWS_ACCESS_KEY_ID',
                'AWS_SECRET_ACCESS_KEY'
            ]);
        }

        try {
            const command = new ListTemplatesCommand({
                MaxItems: maxItems || 50,
                NextToken: nextToken
            });

            const response = await this.sesClient.send(command);

            const templates: SESTemplateInfo[] =
                response.TemplatesMetadata?.map((template) => ({
                    name: template.Name || '',
                    createdTimestamp: template.CreatedTimestamp
                })) || [];

            return {
                templates,
                nextToken: response.NextToken
            };
        } catch (error) {
            this.logger.error(`Failed to list SES templates: ${error.message}`);
            throw new SESTemplateException('*', 'list', error.message);
        }
    }

    async createOrUpdateTemplate(templateData: SESTemplateData): Promise<SESTemplateResult> {
        try {
            // Try to get the template first
            await this.getTemplate(templateData.templateName);
            // If it exists, update it
            return await this.updateTemplate(templateData);
        } catch (error) {
            if (error instanceof SESTemplateException && error.message.includes('does not exist')) {
                // If it doesn't exist, create it
                return await this.createTemplate(templateData);
            }
            throw error;
        }
    }

    async initializeDefaultTemplates(templates: SESTemplateData[]): Promise<SESTemplateResult[]> {
        this.logger.log(`Initializing ${templates.length} SES templates...`);

        const results: SESTemplateResult[] = [];

        for (const template of templates) {
            try {
                const result = await this.createOrUpdateTemplate(template);
                results.push(result);
                this.logger.log(
                    `Template ${template.templateName}: ${result.status} - ${result.message}`
                );
            } catch (error) {
                this.logger.error(
                    `Failed to initialize template ${template.templateName}: ${error.message}`
                );
                results.push({
                    templateName: template.templateName,
                    status: 'created',
                    error: error.message
                });
            }
        }

        const successful = results.filter((r) => !r.error).length;
        const failed = results.filter((r) => r.error).length;

        this.logger.log(
            `Template initialization completed: ${successful} successful, ${failed} failed`
        );

        return results;
    }

    private validateTemplateData(templateData: SESTemplateData): void {
        if (!templateData.templateName || templateData.templateName.trim().length === 0) {
            throw new Error('Template name is required');
        }

        if (!templateData.subject || templateData.subject.trim().length === 0) {
            throw new Error('Template subject is required');
        }

        if (!templateData.htmlPart || templateData.htmlPart.trim().length === 0) {
            throw new Error('Template HTML content is required');
        }

        // Validate template name format (SES requirements)
        const nameRegex = /^[a-zA-Z0-9._-]+$/;
        if (!nameRegex.test(templateData.templateName)) {
            throw new Error(
                'Template name can only contain alphanumeric characters, periods, underscores, and hyphens'
            );
        }

        if (templateData.templateName.length > 64) {
            throw new Error('Template name cannot exceed 64 characters');
        }
    }

    private generateTextFromHtml(htmlContent: string): string {
        // Basic HTML to text conversion
        // In production, consider using a library like 'html-to-text'
        return htmlContent
            .replace(/<style[^>]*>.*?<\/style>/gis, '')
            .replace(/<script[^>]*>.*?<\/script>/gis, '')
            .replace(/<[^>]+>/g, '')
            .replace(/\s+/g, ' ')
            .replace(/&nbsp;/g, ' ')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .trim();
    }

    isEnabled(): boolean {
        return this.enabled;
    }

    getStatus(): { configured: boolean; region?: string; status: string } {
        return {
            configured: this.enabled,
            region:
                this.configService.get('AWS_SES_REGION') || this.configService.get('AWS_REGION'),
            status: this.enabled ? 'active' : 'not configured'
        };
    }
}
