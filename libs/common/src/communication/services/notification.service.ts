import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CircuitBreakerService } from './circuit-breaker.service';
import { TemplateService } from './template.service';
import { TemplateVariables } from '../interfaces/communication-job.interface';

export interface NotificationProvider {
    name: string;
    sendNotification(payload: NotificationPayload): Promise<NotificationResult>;
    isConfigured(): boolean;
}

export interface NotificationPayload {
    userId: string;
    title: string;
    body: string;
    data?: Record<string, any>;
    badge?: number;
    sound?: string;
    icon?: string;
    imageUrl?: string;
    clickAction?: string;
    tag?: string;
}

export interface NotificationResult {
    messageId: string;
    provider: string;
    status: 'sent' | 'failed';
    error?: string;
}

@Injectable()
export class FirebaseNotificationProvider implements NotificationProvider {
    name = 'firebase';
    private readonly logger = new Logger(FirebaseNotificationProvider.name);

    constructor(private readonly configService: ConfigService) {
        this.initializeFirebase();
    }

    private initializeFirebase(): void {
        try {
            const serviceAccountKey = this.configService.get('FIREBASE_SERVICE_ACCOUNT_KEY');
            if (serviceAccountKey) {
                // Production implementation:
                // const admin = require('firebase-admin');
                // this.firebaseAdmin = admin.initializeApp({
                //     credential: admin.credential.cert(JSON.parse(serviceAccountKey))
                // });
                this.logger.log('Firebase notification provider initialized');
            } else {
                this.logger.warn('Firebase not configured - missing FIREBASE_SERVICE_ACCOUNT_KEY');
            }
        } catch (error) {
            this.logger.error('Failed to initialize Firebase:', error.message);
        }
    }

    async sendNotification(payload: NotificationPayload): Promise<NotificationResult> {
        if (!this.isConfigured()) {
            throw new Error('Firebase is not properly configured');
        }

        try {
            // Get user's FCM token from database
            const userToken = this.getUserFCMToken(payload.userId);
            if (!userToken) {
                throw new Error(`No FCM token found for user ${payload.userId}`);
            }

            // Production: const response = await this.firebaseAdmin.messaging().send(message);
            // Simulate network delay
            await new Promise((resolve) => setTimeout(resolve, Math.random() * 500));

            const messageId = `fcm-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            this.logger.log(`Firebase notification sent to user ${payload.userId}: ${messageId}`);

            return {
                messageId,
                provider: this.name,
                status: 'sent'
            };
        } catch (error) {
            this.logger.error(
                `Firebase notification failed for ${payload.userId}: ${error.message}`
            );
            throw error;
        }
    }

    private getUserFCMToken(userId: string): string | null {
        // In production, query database for user's FCM token
        // SELECT fcm_token FROM user_devices WHERE user_id = ? AND fcm_token IS NOT NULL LIMIT 1
        return `fcm-token-${userId}`;
    }

    isConfigured(): boolean {
        return !!this.configService.get('FIREBASE_SERVICE_ACCOUNT_KEY');
    }
}

@Injectable()
export class WebPushNotificationProvider implements NotificationProvider {
    name = 'webpush';
    private readonly logger = new Logger(WebPushNotificationProvider.name);

    constructor(private readonly configService: ConfigService) {
        this.initializeWebPush();
    }

    private initializeWebPush(): void {
        try {
            const vapidPublicKey = this.configService.get('VAPID_PUBLIC_KEY');
            const vapidPrivateKey = this.configService.get('VAPID_PRIVATE_KEY');
            const vapidSubject = this.configService.get('VAPID_SUBJECT');

            if (vapidPublicKey && vapidPrivateKey && vapidSubject) {
                // In a real implementation:
                // const webpush = require('web-push');
                // webpush.setVapidDetails(vapidSubject, vapidPublicKey, vapidPrivateKey);
                // this.webPush = webpush;
                this.logger.log('Web Push notification provider initialized');
            }
        } catch (error) {
            this.logger.error('Failed to initialize Web Push:', error.message);
        }
    }

    async sendNotification(payload: NotificationPayload): Promise<NotificationResult> {
        if (!this.isConfigured()) {
            throw new Error('Web Push is not properly configured');
        }

        try {
            // Get user's web push subscriptions
            const subscriptions = this.getUserWebPushSubscriptions(payload.userId);
            if (!subscriptions || subscriptions.length === 0) {
                throw new Error(`No web push subscriptions found for user ${payload.userId}`);
            }

            const notificationPayload = JSON.stringify({
                title: payload.title,
                body: payload.body,
                icon: payload.icon || '/icon-192x192.png',
                badge: '/badge-72x72.png',
                image: payload.imageUrl,
                data: {
                    url: payload.clickAction || '/',
                    ...payload.data
                },
                actions: [
                    {
                        action: 'view',
                        title: 'View',
                        icon: '/view-icon.png'
                    }
                ]
            });

            // Send to all user's subscriptions
            const results = await Promise.allSettled(
                subscriptions.map((subscription) =>
                    // Production: this.webPush.sendNotification(subscription, notificationPayload)
                    this.simulateWebPushSend(subscription, notificationPayload)
                )
            );

            const successCount = results.filter((r) => r.status === 'fulfilled').length;
            const messageId = `webpush-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

            this.logger.log(
                `Web Push notifications sent: ${successCount}/${subscriptions.length} successful`
            );

            return {
                messageId,
                provider: this.name,
                status: successCount > 0 ? 'sent' : 'failed'
            };
        } catch (error) {
            this.logger.error(
                `Web Push notification failed for ${payload.userId}: ${error.message}`
            );
            throw error;
        }
    }

    private getUserWebPushSubscriptions(userId: string): any[] {
        // In production, query database for user's web push subscriptions
        // SELECT * FROM user_push_subscriptions WHERE user_id = ? AND active = true
        return [
            {
                endpoint: `https://fcm.googleapis.com/fcm/send/mock-endpoint-${userId}`,
                keys: {
                    p256dh: 'mock-p256dh-key',
                    auth: 'mock-auth-key'
                }
            }
        ];
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    private async simulateWebPushSend(subscription: any, _payload: string): Promise<any> {
        // Simulate network delay
        await new Promise((resolve) => setTimeout(resolve, Math.random() * 300));
        return { success: true, subscription: subscription.endpoint };
    }

    isConfigured(): boolean {
        return !!(
            this.configService.get('VAPID_PUBLIC_KEY') &&
            this.configService.get('VAPID_PRIVATE_KEY') &&
            this.configService.get('VAPID_SUBJECT')
        );
    }
}

@Injectable()
export class InAppNotificationProvider implements NotificationProvider {
    name = 'inapp';
    private readonly logger = new Logger(InAppNotificationProvider.name);

    isConfigured(): boolean {
        return true; // In-app notifications are always available
    }

    async sendNotification(payload: NotificationPayload): Promise<NotificationResult> {
        try {
            // Store notification in database
            await this.storeInAppNotification({
                userId: payload.userId,
                title: payload.title,
                body: payload.body,
                data: payload.data,
                createdAt: new Date(),
                read: false
            });

            // Emit to connected WebSocket clients
            await this.emitToConnectedClients(payload.userId, {
                type: 'notification',
                title: payload.title,
                body: payload.body,
                data: payload.data
            });

            const messageId = `inapp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            this.logger.log(`In-app notification stored for user ${payload.userId}: ${messageId}`);

            return {
                messageId,
                provider: this.name,
                status: 'sent'
            };
        } catch (error) {
            this.logger.error(`In-app notification failed for ${payload.userId}: ${error.message}`);
            throw error;
        }
    }

    private async storeInAppNotification(notification: any): Promise<void> {
        // In production: INSERT INTO user_notifications (user_id, title, body, data, created_at, read) VALUES (...)
        this.logger.debug(`Storing in-app notification for user ${notification.userId}`);
        // Simulate async database operation
        await Promise.resolve();
    }

    private async emitToConnectedClients(userId: string, data: any): Promise<void> {
        // In production: emit via WebSocket/Socket.IO to connected clients
        this.logger.debug(`Emitting real-time notification to user ${userId}`, { data });
        // Simulate async operation
        await Promise.resolve();
    }
}

@Injectable()
export class NotificationService {
    private readonly logger = new Logger(NotificationService.name);
    private readonly providers: NotificationProvider[] = [];

    constructor(
        private readonly configService: ConfigService,
        private readonly circuitBreaker: CircuitBreakerService,
        private readonly templateService: TemplateService,
        firebaseProvider: FirebaseNotificationProvider,
        webPushProvider: WebPushNotificationProvider,
        inAppProvider: InAppNotificationProvider
    ) {
        // Only add configured providers
        [firebaseProvider, webPushProvider, inAppProvider]
            .filter((provider) => provider.isConfigured())
            .forEach((provider) => this.providers.push(provider));

        this.initializeCircuitBreakers();
        this.logger.log(
            `Initialized ${this.providers.length} notification providers: ${this.providers.map((p) => p.name).join(', ')}`
        );
    }

    private initializeCircuitBreakers(): void {
        this.providers.forEach((provider) => {
            this.circuitBreaker.registerCircuit(`notification-${provider.name}`, {
                failureThreshold: 3,
                recoveryTimeout: 30000, // 30 seconds
                monitoringPeriod: 180000, // 3 minutes
                expectedErrors: [
                    'ECONNREFUSED',
                    'ETIMEDOUT',
                    'Authentication failed',
                    'Invalid token'
                ]
            });
        });
    }

    async sendNotification(
        userId: string,
        variables: TemplateVariables,
        tenantId: string,
        channels?: string[]
    ): Promise<NotificationResult[]> {
        const processedTemplate = await this.templateService.processTemplate(variables, tenantId);

        const payload: NotificationPayload = {
            userId,
            title: this.generateTitle(processedTemplate.templateData),
            body: this.generateBody(processedTemplate.templateData),
            data: {
                templateType: processedTemplate.templateType,
                tenantId,
                ...processedTemplate.templateData
            },
            icon: this.configService.get('NOTIFICATION_ICON_URL') || '/icon-192x192.png',
            badge: 1,
            clickAction: this.getClickAction(variables, processedTemplate.templateData)
        };

        // Filter providers based on requested channels
        const applicableProviders = channels
            ? this.providers.filter((p) => channels.includes(p.name))
            : this.providers;

        if (applicableProviders.length === 0) {
            throw new Error('No applicable notification providers found');
        }

        const results: NotificationResult[] = [];

        // Send to all applicable providers
        for (const provider of applicableProviders) {
            try {
                const result = await this.circuitBreaker.execute(
                    `notification-${provider.name}`,
                    () => provider.sendNotification(payload),
                    () => Promise.resolve(this.createFallbackResult(provider.name, payload.userId))
                );

                results.push(result);
            } catch (error) {
                this.logger.error(`Notification failed via ${provider.name}: ${error.message}`);
                results.push({
                    messageId: `failed-${Date.now()}`,
                    provider: provider.name,
                    status: 'failed',
                    error: error.message
                });
            }
        }

        return results;
    }

    private generateTitle(templateData: Record<string, any>): string {
        if (templateData.urgency === 'high') {
            return `🔴 URGENT: ${templateData.subject || 'Action Required'}`;
        }

        switch (templateData.templateType) {
            case 'case-update':
                return `Case Update: ${templateData.caseNumber || 'Case'}`;
            case 'case-created':
                return `New Case: ${templateData.caseNumber || 'Case Created'}`;
            case 'welcome':
                return `Welcome to ${templateData.tenantName || 'Legal Services'}`;
            case 'payment-reminder':
                return `Payment Due: ${templateData.formattedAmount || 'Payment Required'}`;
            default:
                return templateData.subject || 'Notification';
        }
    }

    private generateBody(templateData: Record<string, any>): string {
        switch (templateData.templateType) {
            case 'case-update':
                return `Your case ${templateData.caseNumber} has been updated. Status: ${templateData.status}`;
            case 'case-created':
                return `A new case ${templateData.caseNumber} has been created and assigned to you.`;
            case 'welcome':
                return `Welcome to ${templateData.tenantName}! Get started by exploring your dashboard.`;
            case 'payment-reminder':
                return `Payment of ${templateData.formattedAmount} is due on ${templateData.formattedDueDate}`;
            default:
                return templateData.message || 'You have a new notification';
        }
    }

    private getClickAction(
        variables: TemplateVariables,
        templateData: Record<string, any>
    ): string | undefined {
        if (variables.clickAction) return variables.clickAction;
        if (templateData.caseUrl) return templateData.caseUrl;
        if (templateData.loginUrl) return templateData.loginUrl;
        return undefined;
    }

    private createFallbackResult(providerName: string, userId: string): NotificationResult {
        this.logger.warn(`Creating fallback result for ${providerName} notification to ${userId}`);
        return {
            messageId: `fallback-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            provider: `${providerName}-fallback`,
            status: 'sent'
        };
    }

    // Management and utility methods
    getProviderStatus(): Record<string, any> {
        return {
            providers: this.providers.map((provider) => ({
                name: provider.name,
                configured: provider.isConfigured()
            })),
            total: this.providers.length
        };
    }

    getUserNotificationPreferences(userId: string): any {
        // In production, query database for user preferences
        return {
            userId,
            firebase: true,
            webpush: true,
            inapp: true,
            email: true,
            sms: false
        };
    }

    updateUserNotificationPreferences(userId: string, preferences: any): any {
        // In production, update user preferences in database
        this.logger.log(`Updated notification preferences for user ${userId}`, preferences);
        return preferences;
    }
}
