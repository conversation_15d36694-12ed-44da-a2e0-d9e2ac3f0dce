import { Injectable, Logger } from '@nestjs/common';

export interface CircuitBreakerOptions {
    failureThreshold: number;
    recoveryTimeout: number;
    monitoringPeriod: number;
    expectedErrors?: string[];
}

export enum CircuitBreakerState {
    CLOSED = 'closed',
    OPEN = 'open',
    HALF_OPEN = 'half-open'
}

export interface CircuitBreakerStats {
    state: CircuitBreakerState;
    failureCount: number;
    successCount: number;
    lastFailureTime?: Date;
    nextAttemptTime?: Date;
}

@Injectable()
export class CircuitBreakerService {
    private readonly logger = new Logger(CircuitBreakerService.name);
    private readonly circuits = new Map<string, CircuitBreakerStats>();
    private readonly options = new Map<string, CircuitBreakerOptions>();

    private readonly defaultOptions: CircuitBreakerOptions = {
        failureThreshold: 5,
        recoveryTimeout: 60000, // 1 minute
        monitoringPeriod: 300000, // 5 minutes
        expectedErrors: ['ECONNREFUSED', 'ETIMEDOUT', 'ENOTFOUND']
    };

    registerCircuit(name: string, options?: Partial<CircuitBreakerOptions>): void {
        const circuitOptions = { ...this.defaultOptions, ...options };
        this.options.set(name, circuitOptions);
        this.circuits.set(name, {
            state: CircuitBreakerState.CLOSED,
            failureCount: 0,
            successCount: 0
        });

        this.logger.log(`Circuit breaker registered: ${name}`, {
            options: circuitOptions
        });
    }

    async execute<T>(
        circuitName: string,
        operation: () => Promise<T>,
        fallback?: () => Promise<T>
    ): Promise<T> {
        const circuit = this.circuits.get(circuitName);
        const options = this.options.get(circuitName);

        if (!circuit || !options) {
            throw new Error(`Circuit breaker not registered: ${circuitName}`);
        }

        // Check if circuit is open and still in timeout
        if (circuit.state === CircuitBreakerState.OPEN) {
            if (this.shouldAttemptReset(circuit)) {
                circuit.state = CircuitBreakerState.HALF_OPEN;
                this.logger.log(`Circuit breaker transitioning to HALF_OPEN: ${circuitName}`);
            } else {
                this.logger.warn(`Circuit breaker is OPEN, rejecting call: ${circuitName}`);
                if (fallback) {
                    return await fallback();
                }
                throw new Error(`Circuit breaker is open for ${circuitName}`);
            }
        }

        try {
            const result = await operation();
            this.recordSuccess(circuitName);
            return result;
        } catch (error) {
            this.recordFailure(circuitName, error);

            if (fallback) {
                this.logger.warn(
                    `Operation failed, executing fallback for ${circuitName}: ${error.message}`
                );
                return await fallback();
            }

            throw error;
        }
    }

    private shouldAttemptReset(circuit: CircuitBreakerStats): boolean {
        if (!circuit.nextAttemptTime) {
            return true;
        }
        return Date.now() >= circuit.nextAttemptTime.getTime();
    }

    private recordSuccess(circuitName: string): void {
        const circuit = this.circuits.get(circuitName);
        if (!circuit) return;

        circuit.successCount++;
        circuit.failureCount = 0;

        if (circuit.state === CircuitBreakerState.HALF_OPEN) {
            circuit.state = CircuitBreakerState.CLOSED;
            circuit.nextAttemptTime = undefined;
            this.logger.log(`Circuit breaker reset to CLOSED: ${circuitName}`);
        }

        this.circuits.set(circuitName, circuit);
    }

    private recordFailure(circuitName: string, error: any): void {
        const circuit = this.circuits.get(circuitName);
        const options = this.options.get(circuitName);

        if (!circuit || !options) return;

        // Check if this is an expected error that should trigger the circuit breaker
        const isExpectedError = this.isExpectedError(error, options.expectedErrors || []);

        if (!isExpectedError) {
            this.logger.debug(
                `Error not triggering circuit breaker for ${circuitName}: ${error.message}`
            );
            return;
        }

        circuit.failureCount++;
        circuit.lastFailureTime = new Date();

        if (circuit.failureCount >= options.failureThreshold) {
            circuit.state = CircuitBreakerState.OPEN;
            circuit.nextAttemptTime = new Date(Date.now() + options.recoveryTimeout);

            this.logger.error(
                `Circuit breaker opened due to ${circuit.failureCount} failures: ${circuitName}`,
                {
                    lastError: error.message,
                    nextAttemptTime: circuit.nextAttemptTime
                }
            );
        }

        this.circuits.set(circuitName, circuit);
    }

    private isExpectedError(error: any, expectedErrors: string[]): boolean {
        if (expectedErrors.length === 0) {
            return true; // If no specific errors defined, treat all as circuit-breaking
        }

        const errorMessage = error.message || error.code || String(error);
        return expectedErrors.some((expectedError) => errorMessage.includes(expectedError));
    }

    getCircuitStats(circuitName: string): CircuitBreakerStats | undefined {
        return this.circuits.get(circuitName);
    }

    getAllCircuitStats(): Map<string, CircuitBreakerStats> {
        return new Map(this.circuits);
    }

    resetCircuit(circuitName: string): void {
        const circuit = this.circuits.get(circuitName);
        if (!circuit) {
            throw new Error(`Circuit breaker not found: ${circuitName}`);
        }

        circuit.state = CircuitBreakerState.CLOSED;
        circuit.failureCount = 0;
        circuit.nextAttemptTime = undefined;
        circuit.lastFailureTime = undefined;

        this.circuits.set(circuitName, circuit);
        this.logger.log(`Circuit breaker manually reset: ${circuitName}`);
    }

    // Cleanup old monitoring data
    cleanup(): void {
        const now = Date.now();

        for (const [name, circuit] of this.circuits.entries()) {
            const options = this.options.get(name);
            if (!options || !circuit.lastFailureTime) continue;

            const timeSinceLastFailure = now - circuit.lastFailureTime.getTime();

            if (timeSinceLastFailure > options.monitoringPeriod) {
                circuit.failureCount = 0;
                circuit.successCount = 0;
                circuit.lastFailureTime = undefined;

                if (circuit.state === CircuitBreakerState.OPEN) {
                    circuit.state = CircuitBreakerState.CLOSED;
                    circuit.nextAttemptTime = undefined;
                }

                this.circuits.set(name, circuit);
                this.logger.debug(`Circuit breaker stats cleaned up: ${name}`);
            }
        }
    }
}
