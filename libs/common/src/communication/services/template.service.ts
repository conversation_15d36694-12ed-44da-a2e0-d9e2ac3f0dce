import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TemplateVariables } from '../interfaces/communication-job.interface';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Tenant } from '@app/common/typeorm/entities';

export interface UnifiedTemplateConfig {
    sendGridTemplateId: string;
    sesTemplateName: string;
    mailgunTemplateName: string;
    defaultSubject: string;
    categories: string[];
    requiredVariables: string[];
    optionalVariables: string[];
}

export interface ProcessedTemplateData {
    templateType: string;
    templateData: Record<string, any>;
    subject?: string;
}

@Injectable()
export class TemplateService {
    private readonly logger = new Logger(TemplateService.name);

    private readonly unifiedTemplates: Record<string, UnifiedTemplateConfig> = {
        'case-update': {
            sendGridTemplateId: 'd-case-update-template-id',
            sesTemplateName: 'case-update-template',
            mailgunTemplateName: 'case-update-template',
            defaultSubject: 'Case Update - {{caseNumber}}',
            categories: ['case', 'update', 'legal'],
            requiredVariables: ['tenantName', 'recipientName', 'caseNumber', 'status'],
            optionalVariables: [
                'caseSummary',
                'nextSteps',
                'handlerName',
                'handlerTitle',
                'urgency',
                'deadline'
            ]
        },
        'case-urgent': {
            sendGridTemplateId: 'd-case-urgent-template-id',
            sesTemplateName: 'case-urgent-template',
            mailgunTemplateName: 'case-urgent-template',
            defaultSubject: 'URGENT: {{caseNumber}} - Action Required',
            categories: ['case', 'urgent', 'legal'],
            requiredVariables: ['tenantName', 'recipientName', 'caseNumber', 'urgency'],
            optionalVariables: ['message', 'handlerName', 'courtDate', 'courtTime']
        },
        'case-created': {
            sendGridTemplateId: 'd-case-created-template-id',
            sesTemplateName: 'case-created-template',
            mailgunTemplateName: 'case-created-template',
            defaultSubject: 'New Case Created - {{caseNumber}}',
            categories: ['case', 'created', 'legal'],
            requiredVariables: ['tenantName', 'recipientName', 'caseNumber'],
            optionalVariables: ['caseType', 'assignedLawyer', 'loginUrl']
        },
        welcome: {
            sendGridTemplateId: 'd-welcome-template-id',
            sesTemplateName: 'welcome-template',
            mailgunTemplateName: 'welcome-template',
            defaultSubject: 'Welcome to {{tenantName}}',
            categories: ['auth', 'welcome', 'onboarding'],
            requiredVariables: ['tenantName', 'recipientName'],
            optionalVariables: ['role', 'inviterName', 'loginUrl', 'assignedLawyer']
        },
        'password-reset': {
            sendGridTemplateId: 'd-password-reset-template-id',
            sesTemplateName: 'password-reset-template',
            mailgunTemplateName: 'password-reset-template',
            defaultSubject: 'Reset Your Password - {{tenantName}}',
            categories: ['auth', 'security', 'password-reset'],
            requiredVariables: ['tenantName', 'recipientName', 'resetUrl'],
            optionalVariables: ['expirationTime', 'securityTip']
        },
        'user-invitation': {
            sendGridTemplateId: 'd-user-invitation-template-id',
            sesTemplateName: 'user-invitation-template',
            mailgunTemplateName: 'user-invitation-template',
            defaultSubject: "You've been invited to join {{tenantName}}",
            categories: ['auth', 'invitation', 'onboarding'],
            requiredVariables: ['tenantName', 'recipientName', 'inviteUrl'],
            optionalVariables: ['inviterName', 'role', 'loginUrl']
        },
        'billing-statement': {
            sendGridTemplateId: 'd-billing-statement-template-id',
            sesTemplateName: 'billing-statement-template',
            mailgunTemplateName: 'billing-statement-template',
            defaultSubject: 'Monthly Statement - {{billingMonth}}',
            categories: ['billing', 'statement', 'finance'],
            requiredVariables: ['tenantName', 'clientName', 'billingMonth', 'formattedAmount'],
            optionalVariables: [
                'invoiceNumber',
                'formattedDueDate',
                'paymentUrl',
                'itemizedCharges'
            ]
        },
        'payment-reminder': {
            sendGridTemplateId: 'd-payment-reminder-template-id',
            sesTemplateName: 'payment-reminder-template',
            mailgunTemplateName: 'payment-reminder-template',
            defaultSubject: 'Payment Reminder - Due {{formattedDueDate}}',
            categories: ['billing', 'reminder', 'finance'],
            requiredVariables: ['tenantName', 'clientName', 'formattedAmount', 'formattedDueDate'],
            optionalVariables: ['invoiceNumber', 'paymentUrl', 'gracePeriod']
        },
        'system-maintenance': {
            sendGridTemplateId: 'd-system-maintenance-template-id',
            sesTemplateName: 'system-maintenance-template',
            mailgunTemplateName: 'system-maintenance-template',
            defaultSubject: 'System Maintenance Notice - {{formattedMaintenanceDate}}',
            categories: ['system', 'maintenance', 'notification'],
            requiredVariables: ['tenantName', 'recipientName', 'formattedMaintenanceDate'],
            optionalVariables: ['maintenanceTime', 'expectedDowntime', 'affectedServices']
        },
        'weekly-report': {
            sendGridTemplateId: 'd-weekly-report-template-id',
            sesTemplateName: 'weekly-report-template',
            mailgunTemplateName: 'weekly-report-template',
            defaultSubject: 'Weekly Report - {{reportPeriod}}',
            categories: ['report', 'weekly', 'analytics'],
            requiredVariables: ['tenantName', 'recipientName', 'reportPeriod'],
            optionalVariables: [
                'casesSummary',
                'reportUrl',
                'newCases',
                'closedCases',
                'activeCases'
            ]
        },
        'generic-notification': {
            sendGridTemplateId: 'd-generic-notification-template-id',
            sesTemplateName: 'generic-notification-template',
            mailgunTemplateName: 'generic-notification-template',
            defaultSubject: 'Notification from {{tenantName}}',
            categories: ['generic', 'notification'],
            requiredVariables: ['tenantName', 'recipientName', 'message'],
            optionalVariables: ['loginUrl', 'supportEmail']
        }
    };

    constructor(
        private readonly configService: ConfigService,
        @InjectRepository(Tenant) private tenantRepository: Repository<Tenant>
    ) {}

    async processTemplate(
        variables: TemplateVariables,
        tenantId: string
    ): Promise<ProcessedTemplateData> {
        const templateType = this.determineTemplateType(variables);
        this.logger.debug(`Processing template type: ${templateType}`);

        const templateData = await this.prepareUnifiedTemplateData(
            variables,
            tenantId,
            templateType
        );

        this.validateTemplateData(templateType, templateData);

        return {
            templateType,
            templateData,
            subject: templateData.subject
        };
    }

    determineTemplateType(variables: Record<string, any>): string {
        if (variables.type && this.unifiedTemplates[variables.type]) {
            return variables.type;
        }

        if (variables.caseId && variables.urgency === 'high') return 'case-urgent';
        if (variables.caseId && variables.status) return 'case-update';
        if (variables.caseId && !variables.status) return 'case-created';
        if (variables.type === 'welcome' || variables.isWelcome) return 'welcome';
        if (variables.passwordResetToken || variables.resetUrl) return 'password-reset';
        if (variables.invitationToken || variables.inviteUrl) return 'user-invitation';
        if (variables.type === 'system_maintenance' || variables.maintenanceDate)
            return 'system-maintenance';
        if (variables.billingMonth || variables.invoiceNumber) return 'billing-statement';
        if (variables.dueDate && variables.amount) return 'payment-reminder';

        return 'generic-notification';
    }

    validateTemplateData(templateType: string, templateData: Record<string, any>): void {
        const template = this.unifiedTemplates[templateType];
        if (!template) {
            throw new Error(`Template configuration not found for type: ${templateType}`);
        }

        const missingRequired = template.requiredVariables.filter(
            (variable) => !templateData[variable] || String(templateData[variable]).trim() === ''
        );

        if (missingRequired.length > 0) {
            throw new Error(
                `Missing required template variables for ${templateType}: ${missingRequired.join(', ')}`
            );
        }
    }

    getTemplateConfig(templateType: string): UnifiedTemplateConfig {
        const template = this.unifiedTemplates[templateType];
        if (!template) {
            throw new Error(`Template configuration not found for type: ${templateType}`);
        }
        return template;
    }

    getUnifiedTemplates(): Record<string, UnifiedTemplateConfig> {
        return this.unifiedTemplates;
    }

    getTemplateVariables(templateType: string): {
        required: string[];
        optional: string[];
    } {
        const template = this.unifiedTemplates[templateType];
        if (!template) {
            throw new Error(`Template not found: ${templateType}`);
        }

        return {
            required: template.requiredVariables,
            optional: template.optionalVariables
        };
    }

    private async prepareUnifiedTemplateData(
        variables: Record<string, any>,
        tenantId: string,
        templateType: string
    ): Promise<Record<string, any>> {
        const tenant = await this.getTenantName(tenantId);
        const tenantName = tenant ?? 'Legal Services';

        const baseData: Record<string, any> = {
            tenantName,
            tenantId,
            tenantLogo: variables.tenantLogo || tenantName,
            tenantAddress: variables.tenantAddress || '123 Legal Street, Law City, LC 12345',
            recipientName: variables.recipientName || variables.name || 'Valued Client',
            userName: variables.userName || variables.name || 'User',
            supportEmail:
                this.configService.get('SUPPORT_EMAIL') ||
                `support@${tenantName.toLowerCase().replace(/\s+/g, '')}.com`,
            loginUrl:
                this.configService.get('CLIENT_PORTAL_URL') ||
                variables.loginUrl ||
                'https://portal.example.com',
            unsubscribeUrl:
                variables.unsubscribeUrl ||
                `${this.configService.get('APP_URL')}/unsubscribe?tenant=${tenantId}`,
            currentYear: new Date().getFullYear().toString(),
            currentDate: new Date().toLocaleDateString('en-US'),
            ...variables
        };

        switch (templateType) {
            case 'case-update':
            case 'case-created':
            case 'case-urgent':
                return this.prepareCaseTemplateData(baseData, variables);

            case 'welcome':
                return this.prepareWelcomeTemplateData(baseData, variables, tenantName);

            case 'password-reset':
                return this.preparePasswordResetTemplateData(baseData, variables);

            case 'billing-statement':
            case 'payment-reminder':
                return this.prepareBillingTemplateData(baseData, variables);

            case 'system-maintenance':
                return this.prepareMaintenanceTemplateData(baseData, variables);

            default:
                return {
                    ...baseData,
                    message: variables.message || 'You have received a new notification.'
                };
        }
    }

    private prepareCaseTemplateData(
        baseData: Record<string, any>,
        variables: Record<string, any>
    ): Record<string, any> {
        return {
            ...baseData,
            caseUrl: `${baseData.loginUrl}/cases/${variables.caseId}`,
            isUrgent: variables.urgency === 'high',
            urgencyLevel: variables.urgency || 'normal',
            statusIndicatorColor: this.getStatusColor(variables.status),
            formattedDeadline: variables.deadline ? this.formatDate(variables.deadline) : null,
            formattedOpeningDate: variables.openingDate
                ? this.formatDate(variables.openingDate)
                : this.formatDate(new Date()),
            caseNumber: variables.caseId || variables.caseNumber,
            clientName: variables.clientName || baseData.recipientName,
            caseType: variables.caseType || 'Legal Matter',
            handlerName:
                variables.assignedLawyer || variables.handlerName || variables.tenantName || '',
            handlerTitle: variables.handlerTitle || 'Legal Advisor',
            handlerEmail: variables.handlerEmail || baseData.supportEmail,
            handlerPhone: variables.handlerPhone || '',
            caseSummary:
                variables.description ||
                variables.message ||
                variables.caseSummary ||
                'Case has been updated.',
            additionalDetails: variables.additionalDetails || null,
            hasMoreDetails: !!variables.additionalDetails,
            nextSteps: this.formatNextStepsAsString(variables.nextSteps),
            nextStepsArray: this.formatNextStepsAsArray(variables.nextSteps)
        };
    }

    private prepareWelcomeTemplateData(
        baseData: Record<string, any>,
        variables: Record<string, any>,
        tenantName: string
    ): Record<string, any> {
        return {
            ...baseData,
            accountSetupUrl: `${baseData.loginUrl}/setup`,
            gettingStartedUrl: `${baseData.loginUrl}/getting-started`,
            role: variables.role || 'Client',
            inviterName: variables.inviterName || 'System Administrator',
            welcomeMessage:
                variables.welcomeMessage ||
                `Welcome to ${tenantName}! We're excited to work with you.`
        };
    }

    private preparePasswordResetTemplateData(
        baseData: Record<string, any>,
        variables: Record<string, any>
    ): Record<string, any> {
        return {
            ...baseData,
            resetUrl:
                variables.resetUrl ||
                `${baseData.loginUrl}/reset-password?token=${variables.passwordResetToken}`,
            expirationTime: variables.expirationTime || '24 hours',
            securityTip: 'For your security, this link will expire after one use.'
        };
    }

    private prepareBillingTemplateData(
        baseData: Record<string, any>,
        variables: Record<string, any>
    ): Record<string, any> {
        return {
            ...baseData,
            formattedAmount: this.formatCurrency(variables.totalAmount || variables.amount || 0),
            formattedDueDate: variables.dueDate ? this.formatDate(variables.dueDate) : null,
            paymentUrl: `${baseData.loginUrl}/billing/pay/${variables.invoiceNumber || 'latest'}`,
            clientName: variables.clientName || baseData.recipientName,
            billingMonth:
                variables.billingMonth ||
                new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
            gracePeriod: variables.gracePeriod || '7 days'
        };
    }

    private prepareMaintenanceTemplateData(
        baseData: Record<string, any>,
        variables: Record<string, any>
    ): Record<string, any> {
        return {
            ...baseData,
            formattedMaintenanceDate: variables.maintenanceDate
                ? this.formatDate(variables.maintenanceDate)
                : 'TBD',
            maintenanceTime: variables.maintenanceTime || 'TBD',
            expectedDowntime: variables.expectedDowntime || '2-4 hours',
            affectedServices: Array.isArray(variables.affectedServices)
                ? variables.affectedServices.join(', ')
                : 'All services',
            affectedServicesArray: Array.isArray(variables.affectedServices)
                ? variables.affectedServices
                : []
        };
    }

    private async getTenantName(id: string): Promise<string | null> {
        try {
            const tenant = await this.tenantRepository.findOne({ where: { id } });
            return tenant?.displayName ?? null;
        } catch (error) {
            this.logger.error(`Failed to fetch tenant ${id}: ${error.message}`, error.stack);
            throw new Error(`Unable to fetch tenant information for ${id}`);
        }
    }

    private getStatusColor(status: string): string {
        const colorMap: Record<string, string> = {
            draft: '#6c757d',
            submitted: '#007bff',
            under_review: '#ffc107',
            assigned: '#17a2b8',
            in_progress: '#28a745',
            on_hold: '#dc3545',
            pending_approval: '#fd7e14',
            approved: '#28a745',
            rejected: '#dc3545',
            resolved: '#6f42c1',
            closed: '#343a40',
            reopened: '#e83e8c'
        };
        return colorMap[status?.toLowerCase()] || '#6c757d';
    }

    private formatDate(dateString: string | Date): string {
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch {
            return String(dateString);
        }
    }

    private formatCurrency(amount: number | string): string {
        const num = typeof amount === 'string' ? parseFloat(amount) : amount;
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(num || 0);
    }

    private formatNextStepsAsString(nextSteps: any): string {
        if (!nextSteps) return '';

        if (Array.isArray(nextSteps)) {
            return nextSteps.map((step) => String(step)).join('; ');
        }

        if (typeof nextSteps === 'string') {
            return nextSteps;
        }

        return String(nextSteps);
    }

    private formatNextStepsAsArray(nextSteps: any): string[] {
        if (!nextSteps) return [];

        if (Array.isArray(nextSteps)) {
            return nextSteps.map((step) => String(step));
        }

        if (typeof nextSteps === 'string') {
            return nextSteps
                .split(/[;\n]/)
                .map((step) => step.trim())
                .filter((step) => step.length > 0);
        }

        return [String(nextSteps)];
    }
}
