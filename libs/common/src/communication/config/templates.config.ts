import { UnifiedTemplateConfig } from '../services/template.service';

export const DEFAULT_TEMPLATE_CONFIGURATIONS: Record<string, UnifiedTemplateConfig> = {
    'case-update': {
        sendGridTemplateId:
            process.env.SENDGRID_CASE_UPDATE_TEMPLATE_ID || 'd-case-update-template-id',
        sesTemplateName: process.env.SES_CASE_UPDATE_TEMPLATE || 'case-update-template',
        mailgunTemplateName: process.env.MAILGUN_CASE_UPDATE_TEMPLATE || 'case-update-template',
        defaultSubject: 'Case Update - {{caseNumber}}',
        categories: ['case', 'update', 'legal'],
        requiredVariables: ['tenantName', 'recipientName', 'caseNumber', 'status'],
        optionalVariables: [
            'caseSummary',
            'nextSteps',
            'handlerName',
            'handlerTitle',
            'urgency',
            'deadline'
        ]
    },
    'case-urgent': {
        sendGridTemplateId:
            process.env.SENDGRID_CASE_URGENT_TEMPLATE_ID || 'd-case-urgent-template-id',
        sesTemplateName: process.env.SES_CASE_URGENT_TEMPLATE || 'case-urgent-template',
        mailgunTemplateName: process.env.MAILGUN_CASE_URGENT_TEMPLATE || 'case-urgent-template',
        defaultSubject: 'URGENT: {{caseNumber}} - Action Required',
        categories: ['case', 'urgent', 'legal'],
        requiredVariables: ['tenantName', 'recipientName', 'caseNumber', 'urgency'],
        optionalVariables: ['message', 'handlerName', 'courtDate', 'courtTime']
    },
    'case-created': {
        sendGridTemplateId:
            process.env.SENDGRID_CASE_CREATED_TEMPLATE_ID || 'd-case-created-template-id',
        sesTemplateName: process.env.SES_CASE_CREATED_TEMPLATE || 'case-created-template',
        mailgunTemplateName: process.env.MAILGUN_CASE_CREATED_TEMPLATE || 'case-created-template',
        defaultSubject: 'New Case Created - {{caseNumber}}',
        categories: ['case', 'created', 'legal'],
        requiredVariables: ['tenantName', 'recipientName', 'caseNumber'],
        optionalVariables: ['caseType', 'assignedLawyer', 'loginUrl']
    },
    welcome: {
        sendGridTemplateId: process.env.SENDGRID_WELCOME_TEMPLATE_ID || 'd-welcome-template-id',
        sesTemplateName: process.env.SES_WELCOME_TEMPLATE || 'welcome-template',
        mailgunTemplateName: process.env.MAILGUN_WELCOME_TEMPLATE || 'welcome-template',
        defaultSubject: 'Welcome to {{tenantName}}',
        categories: ['auth', 'welcome', 'onboarding'],
        requiredVariables: ['tenantName', 'recipientName'],
        optionalVariables: ['role', 'inviterName', 'loginUrl', 'assignedLawyer']
    },
    'password-reset': {
        sendGridTemplateId:
            process.env.SENDGRID_PASSWORD_RESET_TEMPLATE_ID || 'd-password-reset-template-id',
        sesTemplateName: process.env.SES_PASSWORD_RESET_TEMPLATE || 'password-reset-template',
        mailgunTemplateName:
            process.env.MAILGUN_PASSWORD_RESET_TEMPLATE || 'password-reset-template',
        defaultSubject: 'Reset Your Password - {{tenantName}}',
        categories: ['auth', 'security', 'password-reset'],
        requiredVariables: ['tenantName', 'recipientName', 'resetUrl'],
        optionalVariables: ['expirationTime', 'securityTip']
    },
    'user-invitation': {
        sendGridTemplateId:
            process.env.SENDGRID_USER_INVITATION_TEMPLATE_ID || 'd-user-invitation-template-id',
        sesTemplateName: process.env.SES_USER_INVITATION_TEMPLATE || 'user-invitation-template',
        mailgunTemplateName:
            process.env.MAILGUN_USER_INVITATION_TEMPLATE || 'user-invitation-template',
        defaultSubject: "You've been invited to join {{tenantName}}",
        categories: ['auth', 'invitation', 'onboarding'],
        requiredVariables: ['tenantName', 'recipientName', 'inviteUrl'],
        optionalVariables: ['inviterName', 'role', 'loginUrl']
    },
    'billing-statement': {
        sendGridTemplateId:
            process.env.SENDGRID_BILLING_STATEMENT_TEMPLATE_ID || 'd-billing-statement-template-id',
        sesTemplateName: process.env.SES_BILLING_STATEMENT_TEMPLATE || 'billing-statement-template',
        mailgunTemplateName:
            process.env.MAILGUN_BILLING_STATEMENT_TEMPLATE || 'billing-statement-template',
        defaultSubject: 'Monthly Statement - {{billingMonth}}',
        categories: ['billing', 'statement', 'finance'],
        requiredVariables: ['tenantName', 'clientName', 'billingMonth', 'formattedAmount'],
        optionalVariables: ['invoiceNumber', 'formattedDueDate', 'paymentUrl', 'itemizedCharges']
    },
    'payment-reminder': {
        sendGridTemplateId:
            process.env.SENDGRID_PAYMENT_REMINDER_TEMPLATE_ID || 'd-payment-reminder-template-id',
        sesTemplateName: process.env.SES_PAYMENT_REMINDER_TEMPLATE || 'payment-reminder-template',
        mailgunTemplateName:
            process.env.MAILGUN_PAYMENT_REMINDER_TEMPLATE || 'payment-reminder-template',
        defaultSubject: 'Payment Reminder - Due {{formattedDueDate}}',
        categories: ['billing', 'reminder', 'finance'],
        requiredVariables: ['tenantName', 'clientName', 'formattedAmount', 'formattedDueDate'],
        optionalVariables: ['invoiceNumber', 'paymentUrl', 'gracePeriod']
    },
    'system-maintenance': {
        sendGridTemplateId:
            process.env.SENDGRID_SYSTEM_MAINTENANCE_TEMPLATE_ID ||
            'd-system-maintenance-template-id',
        sesTemplateName:
            process.env.SES_SYSTEM_MAINTENANCE_TEMPLATE || 'system-maintenance-template',
        mailgunTemplateName:
            process.env.MAILGUN_SYSTEM_MAINTENANCE_TEMPLATE || 'system-maintenance-template',
        defaultSubject: 'System Maintenance Notice - {{formattedMaintenanceDate}}',
        categories: ['system', 'maintenance', 'notification'],
        requiredVariables: ['tenantName', 'recipientName', 'formattedMaintenanceDate'],
        optionalVariables: ['maintenanceTime', 'expectedDowntime', 'affectedServices']
    },
    'weekly-report': {
        sendGridTemplateId:
            process.env.SENDGRID_WEEKLY_REPORT_TEMPLATE_ID || 'd-weekly-report-template-id',
        sesTemplateName: process.env.SES_WEEKLY_REPORT_TEMPLATE || 'weekly-report-template',
        mailgunTemplateName: process.env.MAILGUN_WEEKLY_REPORT_TEMPLATE || 'weekly-report-template',
        defaultSubject: 'Weekly Report - {{reportPeriod}}',
        categories: ['report', 'weekly', 'analytics'],
        requiredVariables: ['tenantName', 'recipientName', 'reportPeriod'],
        optionalVariables: ['casesSummary', 'reportUrl', 'newCases', 'closedCases', 'activeCases']
    },
    'generic-notification': {
        sendGridTemplateId:
            process.env.SENDGRID_GENERIC_NOTIFICATION_TEMPLATE_ID ||
            'd-generic-notification-template-id',
        sesTemplateName:
            process.env.SES_GENERIC_NOTIFICATION_TEMPLATE || 'generic-notification-template',
        mailgunTemplateName:
            process.env.MAILGUN_GENERIC_NOTIFICATION_TEMPLATE || 'generic-notification-template',
        defaultSubject: 'Notification from {{tenantName}}',
        categories: ['generic', 'notification'],
        requiredVariables: ['tenantName', 'recipientName', 'message'],
        optionalVariables: ['loginUrl', 'supportEmail']
    }
};

export interface CommunicationConfig {
    templates: Record<string, UnifiedTemplateConfig>;
    email: {
        defaultFromEmail: string;
        sendgrid: {
            apiKey?: string;
            enabled: boolean;
        };
        mailgun: {
            apiKey?: string;
            domain?: string;
            url?: string;
            enabled: boolean;
        };
        ses: {
            region?: string;
            accessKeyId?: string;
            secretAccessKey?: string;
            enabled: boolean;
        };
    };
    notifications: {
        firebase: {
            serviceAccountKey?: string;
            enabled: boolean;
        };
        webpush: {
            vapidPublicKey?: string;
            vapidPrivateKey?: string;
            vapidSubject?: string;
            enabled: boolean;
        };
    };
    circuitBreaker: {
        email: {
            failureThreshold: number;
            recoveryTimeout: number;
            monitoringPeriod: number;
        };
        notification: {
            failureThreshold: number;
            recoveryTimeout: number;
            monitoringPeriod: number;
        };
    };
}

export const getCommunicationConfig = (): CommunicationConfig => ({
    templates: DEFAULT_TEMPLATE_CONFIGURATIONS,
    email: {
        defaultFromEmail: process.env.FROM_EMAIL || '<EMAIL>',
        sendgrid: {
            apiKey: process.env.SENDGRID_API_KEY,
            enabled: !!process.env.SENDGRID_API_KEY
        },
        mailgun: {
            apiKey: process.env.MAILGUN_API_KEY,
            domain: process.env.MAILGUN_DOMAIN,
            url: process.env.MAILGUN_URL || 'https://api.mailgun.net',
            enabled: !!(process.env.MAILGUN_API_KEY && process.env.MAILGUN_DOMAIN)
        },
        ses: {
            region: process.env.AWS_SES_REGION || process.env.AWS_REGION || 'us-east-1',
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            enabled: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY)
        }
    },
    notifications: {
        firebase: {
            serviceAccountKey: process.env.FIREBASE_SERVICE_ACCOUNT_KEY,
            enabled: !!process.env.FIREBASE_SERVICE_ACCOUNT_KEY
        },
        webpush: {
            vapidPublicKey: process.env.VAPID_PUBLIC_KEY,
            vapidPrivateKey: process.env.VAPID_PRIVATE_KEY,
            vapidSubject: process.env.VAPID_SUBJECT,
            enabled: !!(
                process.env.VAPID_PUBLIC_KEY &&
                process.env.VAPID_PRIVATE_KEY &&
                process.env.VAPID_SUBJECT
            )
        }
    },
    circuitBreaker: {
        email: {
            failureThreshold: parseInt(process.env.EMAIL_CIRCUIT_BREAKER_FAILURE_THRESHOLD || '5'),
            recoveryTimeout: parseInt(
                process.env.EMAIL_CIRCUIT_BREAKER_RECOVERY_TIMEOUT || '60000'
            ),
            monitoringPeriod: parseInt(
                process.env.EMAIL_CIRCUIT_BREAKER_MONITORING_PERIOD || '300000'
            )
        },
        notification: {
            failureThreshold: parseInt(
                process.env.NOTIFICATION_CIRCUIT_BREAKER_FAILURE_THRESHOLD || '3'
            ),
            recoveryTimeout: parseInt(
                process.env.NOTIFICATION_CIRCUIT_BREAKER_RECOVERY_TIMEOUT || '30000'
            ),
            monitoringPeriod: parseInt(
                process.env.NOTIFICATION_CIRCUIT_BREAKER_MONITORING_PERIOD || '180000'
            )
        }
    }
});
