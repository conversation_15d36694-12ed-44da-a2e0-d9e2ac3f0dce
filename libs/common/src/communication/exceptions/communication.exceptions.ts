import { BaseException } from '@app/common/exceptions/base.exception';
import { HttpStatusCode } from '@app/common/enums/http-status.enum';

export class CommunicationException extends BaseException {
    constructor(
        message: string,
        statusCode: HttpStatusCode = HttpStatusCode.INTERNAL_SERVER_ERROR,
        errorCode?: string,
        details?: any
    ) {
        super(message, statusCode, errorCode, details);
    }
}

export class TemplateValidationException extends CommunicationException {
    constructor(templateType: string, missingVariables: string[], details?: any) {
        super(
            `Template validation failed for ${templateType}: Missing required variables: ${missingVariables.join(', ')}`,
            HttpStatusCode.BAD_REQUEST,
            'TEMPLATE_VALIDATION_FAILED',
            { templateType, missingVariables, ...details }
        );
    }
}

export class TemplateNotFoundException extends CommunicationException {
    constructor(templateType: string, details?: any) {
        super(
            `Template configuration not found for type: ${templateType}`,
            HttpStatusCode.NOT_FOUND,
            'TEMPLATE_NOT_FOUND',
            { templateType, ...details }
        );
    }
}

export class EmailProviderException extends CommunicationException {
    constructor(provider: string, message: string, details?: any) {
        super(
            `Email provider ${provider} failed: ${message}`,
            HttpStatusCode.SERVICE_UNAVAILABLE,
            'EMAIL_PROVIDER_FAILED',
            { provider, originalError: message, ...details }
        );
    }
}

export class NotificationProviderException extends CommunicationException {
    constructor(provider: string, message: string, details?: any) {
        super(
            `Notification provider ${provider} failed: ${message}`,
            HttpStatusCode.SERVICE_UNAVAILABLE,
            'NOTIFICATION_PROVIDER_FAILED',
            { provider, originalError: message, ...details }
        );
    }
}

export class RecipientValidationException extends CommunicationException {
    constructor(channel: string, recipient: string, reason: string, details?: any) {
        super(
            `Invalid recipient for ${channel}: ${recipient} - ${reason}`,
            HttpStatusCode.BAD_REQUEST,
            'RECIPIENT_VALIDATION_FAILED',
            { channel, recipient, reason, ...details }
        );
    }
}

export class QueueException extends CommunicationException {
    constructor(queueName: string, operation: string, message: string, details?: any) {
        super(
            `Queue operation failed for ${queueName}.${operation}: ${message}`,
            HttpStatusCode.INTERNAL_SERVER_ERROR,
            'QUEUE_OPERATION_FAILED',
            { queueName, operation, originalError: message, ...details }
        );
    }
}

export class CircuitBreakerOpenException extends CommunicationException {
    constructor(circuitName: string, nextAttemptTime?: Date, details?: any) {
        super(
            `Circuit breaker is open for ${circuitName}${nextAttemptTime ? `, next attempt at ${nextAttemptTime.toISOString()}` : ''}`,
            HttpStatusCode.SERVICE_UNAVAILABLE,
            'CIRCUIT_BREAKER_OPEN',
            { circuitName, nextAttemptTime, ...details }
        );
    }
}

export class TenantNotFoundException extends CommunicationException {
    constructor(tenantId: string, details?: any) {
        super(`Tenant not found: ${tenantId}`, HttpStatusCode.NOT_FOUND, 'TENANT_NOT_FOUND', {
            tenantId,
            ...details
        });
    }
}

export class BatchValidationException extends CommunicationException {
    constructor(validationErrors: string[], details?: any) {
        super(
            `Batch validation failed: ${validationErrors.length} errors`,
            HttpStatusCode.BAD_REQUEST,
            'BATCH_VALIDATION_FAILED',
            { validationErrors, errorCount: validationErrors.length, ...details }
        );
    }
}

export class ConfigurationException extends CommunicationException {
    constructor(service: string, missingConfig: string[], details?: any) {
        super(
            `${service} configuration incomplete: Missing ${missingConfig.join(', ')}`,
            HttpStatusCode.INTERNAL_SERVER_ERROR,
            'CONFIGURATION_INCOMPLETE',
            { service, missingConfig, ...details }
        );
    }
}

export class SESTemplateException extends CommunicationException {
    constructor(templateName: string, operation: string, message: string, details?: any) {
        super(
            `SES template operation failed for ${templateName}.${operation}: ${message}`,
            HttpStatusCode.BAD_REQUEST,
            'SES_TEMPLATE_FAILED',
            { templateName, operation, originalError: message, ...details }
        );
    }
}

export class SESQuotaExceededException extends CommunicationException {
    constructor(quotaType: string, details?: any) {
        super(
            `SES quota exceeded for ${quotaType}`,
            HttpStatusCode.TOO_MANY_REQUESTS,
            'SES_QUOTA_EXCEEDED',
            { quotaType, ...details }
        );
    }
}

export class SESAccountSendingDisabledException extends CommunicationException {
    constructor(details?: any) {
        super(
            'SES account sending is disabled',
            HttpStatusCode.SERVICE_UNAVAILABLE,
            'SES_SENDING_DISABLED',
            details
        );
    }
}
