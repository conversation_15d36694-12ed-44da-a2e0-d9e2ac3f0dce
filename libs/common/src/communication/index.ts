import { TemplateRequirements, TemplateVariables } from './interfaces/communication-job.interface';

export * from './services/template.service';
export * from './services/circuit-breaker.service';
export * from './services/ses-email.service';
export * from './services/ses-template-manager.service';
export * from './services/notification.service';
export * from './config/templates.config';
export * from './interfaces/communication-job.interface';
export * from './producers/communication.producer';
export * from './exceptions/communication.exceptions';

export function validateTemplateVariables<T extends keyof TemplateRequirements>(
    templateType: T,
    variables: Record<string, any>
): variables is TemplateVariables {
    const requirements = {
        'case-update': ['tenantName', 'recipientName', 'caseNumber', 'status'],
        'case-urgent': ['tenantName', 'recipientName', 'caseNumber', 'urgency'],
        'case-created': ['tenantName', 'recipientName', 'caseNumber'],
        welcome: ['tenantName', 'recipientName'],
        'password-reset': ['tenantName', 'recipientName', 'resetUrl'],
        'user-invitation': ['tenantName', 'recipientName', 'inviteUrl'],
        'billing-statement': ['tenantName', 'clientName', 'billingMonth', 'formattedAmount'],
        'payment-reminder': ['tenantName', 'clientName', 'formattedAmount', 'formattedDueDate'],
        'system-maintenance': ['tenantName', 'recipientName', 'formattedMaintenanceDate'],
        'weekly-report': ['tenantName', 'recipientName', 'reportPeriod'],
        'generic-notification': ['tenantName', 'recipientName', 'message'],
        'document-generated': [
            'tenantName',
            'recipientName',
            'documentName',
            'templateName',
            'generatedAt'
        ],
        'document-uploaded': ['tenantName', 'recipientName', 'documentName', 'uploadedAt'],
        'document-shared': ['tenantName', 'recipientName', 'documentName', 'sharedBy', 'sharedAt'],
        'document-deleted': ['tenantName', 'recipientName', 'documentName', 'deletedAt'],
        'document-generation-failed': [
            'tenantName',
            'recipientName',
            'templateName',
            'errorMessage',
            'failedAt'
        ],
        'batch-documents-uploaded': [
            'tenantName',
            'recipientName',
            'documentNames',
            'documentCount',
            'operatedAt'
        ],
        'batch-documents-deleted': [
            'tenantName',
            'recipientName',
            'documentNames',
            'documentCount',
            'operatedAt'
        ],
        'batch-documents-shared': [
            'tenantName',
            'recipientName',
            'documentNames',
            'documentCount',
            'operatedAt'
        ],
        'document-review-reminder': ['tenantName', 'recipientName', 'documentName', 'actionType'],
        'document-approval-reminder': ['tenantName', 'recipientName', 'documentName', 'actionType'],
        'document-signature-reminder': ['tenantName', 'recipientName', 'documentName', 'actionType']
    } as const;

    const required = requirements[templateType];
    const missing = required.filter(
        (field) => !variables[field] || String(variables[field]).trim() === ''
    );

    if (missing.length > 0) {
        throw new Error(`Missing required variables for ${templateType}: ${missing.join(', ')}`);
    }

    return true;
}
