import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import {
    DocumentActionType,
    DocumentOperationType
} from '@app/common/enums/document-action-types.enum';

export interface JobResponse {
    jobId: string;
    queueName: string;
    estimatedDelay?: number;
    status: 'queued' | 'delayed' | 'failed';
    channels?: string[];
}

export interface ChannelResult {
    channel: string;
    status: 'fulfilled' | 'rejected';
    result?: any;
    error?: string;
}

export interface MultiChannelJobResult {
    results: ChannelResult[];
    strategy: 'fail-fast' | 'continue-on-error' | 'sequential';
    status: 'all-completed' | 'partial-success';
    successes: number;
    failures: number;
}

export interface BaseTemplateVariables {
    tenantName?: string;
    tenantId?: string;
    tenantLogo?: string;
    tenantAddress?: string;
    recipientName?: string;
    userName?: string;
    name?: string;
    supportEmail?: string;
    loginUrl?: string;
    unsubscribeUrl?: string;
    message?: string;
    currentYear?: string;
    currentDate?: string;
    type?: string;
    clickAction?: string;
}

export interface CaseTemplateVariables extends BaseTemplateVariables {
    caseId: string;
    caseNumber?: string;
    status?: string;
    caseType?: string;
    caseSummary?: string;
    description?: string;
    clientName?: string;
    deadline?: string | Date;
    openingDate?: string | Date;
    courtDate?: string | Date;
    courtTime?: string;
    assignedLawyer?: string;
    handlerName?: string;
    handlerTitle?: string;
    handlerEmail?: string;
    handlerPhone?: string;
    urgency?: 'low' | 'normal' | 'high' | 'critical';
    isUrgent?: boolean;
    nextSteps?: string | string[];
    additionalDetails?: string;
    hasMoreDetails?: boolean;
    caseUrl?: string;
}

export interface AuthTemplateVariables extends BaseTemplateVariables {
    role?: string;
    inviterName?: string;
    inviteUrl?: string;
    resetUrl?: string;
    passwordResetToken?: string;
    expirationTime?: string;
    securityTip?: string;
    invitationToken?: string;
    isWelcome?: boolean;
    welcomeMessage?: string;
    accountSetupUrl?: string;
    gettingStartedUrl?: string;
}

export interface BillingTemplateVariables extends BaseTemplateVariables {
    clientName?: string;
    amount?: number;
    totalAmount?: number;
    formattedAmount?: string;
    dueDate?: string | Date;
    formattedDueDate?: string;
    billingMonth?: string;
    invoiceNumber?: string;
    paymentUrl?: string;
    itemizedCharges?: Array<{
        description: string;
        amount: number;
        quantity?: number;
    }>;
    gracePeriod?: string;
}

export interface SystemTemplateVariables extends BaseTemplateVariables {
    maintenanceDate?: string | Date;
    formattedMaintenanceDate?: string;
    maintenanceTime?: string;
    expectedDowntime?: string;
    affectedServices?: string | string[];
    affectedServicesArray?: string[];
}

export interface ReportTemplateVariables extends BaseTemplateVariables {
    reportPeriod?: string;
    reportUrl?: string;
    newCases?: number;
    closedCases?: number;
    activeCases?: number;
    casesSummary?: {
        total: number;
        byStatus: Record<string, number>;
        byType: Record<string, number>;
    };
    metrics?: Record<string, any>;
}

export interface CaseUpdateVariables extends CaseTemplateVariables {
    type?: 'case-update';
    status: string;
}

export interface CaseUrgentVariables extends CaseTemplateVariables {
    type?: 'case-urgent';
    urgency: 'high' | 'critical';
}

export interface CaseCreatedVariables extends CaseTemplateVariables {
    type?: 'case-created';
}

export interface WelcomeVariables extends AuthTemplateVariables {
    type?: 'welcome';
    tenantName: string;
    recipientName: string;
}

export interface PasswordResetVariables extends AuthTemplateVariables {
    type?: 'password-reset';
    resetUrl: string;
    tenantName: string;
    recipientName: string;
}

export interface UserInvitationVariables extends AuthTemplateVariables {
    type?: 'user-invitation';
    inviteUrl: string;
    tenantName: string;
    recipientName: string;
}

export interface BillingStatementVariables extends BillingTemplateVariables {
    type?: 'billing-statement';
    billingMonth: string;
    formattedAmount: string;
    tenantName: string;
    clientName: string;
}

export interface PaymentReminderVariables extends BillingTemplateVariables {
    type?: 'payment-reminder';
    formattedAmount: string;
    formattedDueDate: string;
    tenantName: string;
    clientName: string;
}

export interface SystemMaintenanceVariables extends SystemTemplateVariables {
    type?: 'system-maintenance';
    formattedMaintenanceDate: string;
    tenantName: string;
    recipientName: string;
}

export interface WeeklyReportVariables extends ReportTemplateVariables {
    type?: 'weekly-report';
    reportPeriod: string;
    tenantName: string;
    recipientName: string;
}

export interface GenericNotificationVariables extends BaseTemplateVariables {
    type?: 'generic-notification';
    message: string;
    tenantName: string;
    recipientName: string;
}

// Document-related template variables
export interface DocumentTemplateVariables extends BaseTemplateVariables {
    documentId?: string;
    documentName?: string;
    documentUrl?: string;
    caseId?: string;
    caseNumber?: string;
    templateId?: string;
    templateName?: string;
    folderId?: string;
    folderName?: string;
    fileSize?: string;
    fileType?: string;
    mimeType?: string;
    uploadedAt?: string;
    generatedAt?: string;
    sharedAt?: string;
    deletedAt?: string;
    sharedBy?: string;
    shareMessage?: string;
    actionType?: DocumentActionType;
    dueDate?: string;
    errorMessage?: string;
    documentNames?: string[];
    documentCount?: number;
    operation?: DocumentOperationType;
    operatedAt?: string;
    failedAt?: string;
}

export interface DocumentGeneratedVariables extends DocumentTemplateVariables {
    type?: 'document-generated';
    documentName: string;
    templateName: string;
    tenantName: string;
    recipientName: string;
    caseNumber?: string;
    generatedAt: string;
}

export interface DocumentUploadedVariables extends DocumentTemplateVariables {
    type?: 'document-uploaded';
    documentName: string;
    tenantName: string;
    recipientName: string;
    caseNumber?: string;
    uploadedAt: string;
}

export interface DocumentSharedVariables extends DocumentTemplateVariables {
    type?: 'document-shared';
    documentName: string;
    sharedBy: string;
    tenantName: string;
    recipientName: string;
    caseNumber?: string;
    sharedAt: string;
    shareMessage?: string;
}

export interface DocumentDeletedVariables extends DocumentTemplateVariables {
    type?: 'document-deleted';
    documentName: string;
    tenantName: string;
    recipientName: string;
    caseNumber?: string;
    deletedAt: string;
}

export interface DocumentGenerationFailedVariables extends DocumentTemplateVariables {
    type?: 'document-generation-failed';
    templateName: string;
    errorMessage: string;
    tenantName: string;
    recipientName: string;
    caseNumber?: string;
    failedAt: string;
}

export interface BatchDocumentsVariables extends DocumentTemplateVariables {
    type?: 'batch-documents-uploaded' | 'batch-documents-deleted' | 'batch-documents-shared';
    documentNames: string[];
    documentCount: number;
    operation: DocumentOperationType;
    tenantName: string;
    recipientName: string;
    caseNumber?: string;
    operatedAt: string;
}

export interface DocumentReminderVariables extends DocumentTemplateVariables {
    type?:
        | 'document-review-reminder'
        | 'document-approval-reminder'
        | 'document-signature-reminder';
    documentName: string;
    actionType: DocumentActionType;
    tenantName: string;
    recipientName: string;
    caseNumber?: string;
    dueDate?: string;
    documentUrl?: string;
}

export type TemplateVariables =
    | CaseUpdateVariables
    | CaseUrgentVariables
    | CaseCreatedVariables
    | WelcomeVariables
    | PasswordResetVariables
    | UserInvitationVariables
    | BillingStatementVariables
    | PaymentReminderVariables
    | SystemMaintenanceVariables
    | WeeklyReportVariables
    | GenericNotificationVariables
    | DocumentGeneratedVariables
    | DocumentUploadedVariables
    | DocumentSharedVariables
    | DocumentDeletedVariables
    | DocumentGenerationFailedVariables
    | BatchDocumentsVariables
    | DocumentReminderVariables;

export interface CommunicationJobData {
    tenantId: string;
    userId: string;
    channels: COMMUNICATION_CHANNELS[];
    recipients: {
        email?: string[];
        sms?: string[];
        notification?: string[];
    };
    variables: TemplateVariables;
    priority?: number;
    delay?: number;
    caseId?: string;
    scheduledAt?: Date;
    metadata?: Record<string, any>;
    processInParallel?: boolean;
    failureStrategy?: 'fail-fast' | 'continue-on-error';
}

export interface SingleChannelJobData {
    tenantId: string;
    userId: string;
    channel: COMMUNICATION_CHANNELS;
    recipient: string;
    variables: TemplateVariables;
    priority?: number;
    delay?: number;
    caseId?: string;
    scheduledAt?: Date;
    metadata?: Record<string, any>;
    originalJobId?: string;
}

export interface ScheduledJobData extends CommunicationJobData {
    cronExpression: string;
    timezone?: string;
    endDate?: Date;
}

// Template validation helper types
export interface TemplateRequirements {
    'case-update': {
        required: ['tenantName', 'recipientName', 'caseNumber', 'status'];
        optional: [
            'caseSummary',
            'nextSteps',
            'handlerName',
            'handlerTitle',
            'urgency',
            'deadline'
        ];
    };
    'case-urgent': {
        required: ['tenantName', 'recipientName', 'caseNumber', 'urgency'];
        optional: ['message', 'handlerName', 'courtDate', 'courtTime'];
    };
    'case-created': {
        required: ['tenantName', 'recipientName', 'caseNumber'];
        optional: ['caseType', 'assignedLawyer', 'loginUrl'];
    };
    welcome: {
        required: ['tenantName', 'recipientName'];
        optional: ['role', 'inviterName', 'loginUrl', 'assignedLawyer'];
    };
    'password-reset': {
        required: ['tenantName', 'recipientName', 'resetUrl'];
        optional: ['expirationTime', 'securityTip'];
    };
    'user-invitation': {
        required: ['tenantName', 'recipientName', 'inviteUrl'];
        optional: ['inviterName', 'role', 'loginUrl'];
    };
    'billing-statement': {
        required: ['tenantName', 'clientName', 'billingMonth', 'formattedAmount'];
        optional: ['invoiceNumber', 'formattedDueDate', 'paymentUrl', 'itemizedCharges'];
    };
    'payment-reminder': {
        required: ['tenantName', 'clientName', 'formattedAmount', 'formattedDueDate'];
        optional: ['invoiceNumber', 'paymentUrl', 'gracePeriod'];
    };
    'system-maintenance': {
        required: ['tenantName', 'recipientName', 'formattedMaintenanceDate'];
        optional: ['maintenanceTime', 'expectedDowntime', 'affectedServices'];
    };
    'weekly-report': {
        required: ['tenantName', 'recipientName', 'reportPeriod'];
        optional: ['casesSummary', 'reportUrl', 'newCases', 'closedCases', 'activeCases'];
    };
    'generic-notification': {
        required: ['tenantName', 'recipientName', 'message'];
        optional: ['loginUrl', 'supportEmail'];
    };
    'document-generated': {
        required: ['tenantName', 'recipientName', 'documentName', 'templateName', 'generatedAt'];
        optional: ['caseNumber', 'caseId', 'documentId', 'templateId'];
    };
    'document-uploaded': {
        required: ['tenantName', 'recipientName', 'documentName', 'uploadedAt'];
        optional: ['caseNumber', 'caseId', 'documentId', 'folderId'];
    };
    'document-shared': {
        required: ['tenantName', 'recipientName', 'documentName', 'sharedBy', 'sharedAt'];
        optional: ['caseNumber', 'caseId', 'shareMessage', 'documentId'];
    };
    'document-deleted': {
        required: ['tenantName', 'recipientName', 'documentName', 'deletedAt'];
        optional: ['caseNumber', 'caseId', 'documentId'];
    };
    'document-generation-failed': {
        required: ['tenantName', 'recipientName', 'templateName', 'errorMessage', 'failedAt'];
        optional: ['caseNumber', 'caseId', 'templateId'];
    };
    'batch-documents-uploaded': {
        required: ['tenantName', 'recipientName', 'documentNames', 'documentCount', 'operatedAt'];
        optional: ['caseNumber', 'caseId'];
    };
    'batch-documents-deleted': {
        required: ['tenantName', 'recipientName', 'documentNames', 'documentCount', 'operatedAt'];
        optional: ['caseNumber', 'caseId'];
    };
    'batch-documents-shared': {
        required: ['tenantName', 'recipientName', 'documentNames', 'documentCount', 'operatedAt'];
        optional: ['caseNumber', 'caseId', 'sharedBy'];
    };
    'document-review-reminder': {
        required: ['tenantName', 'recipientName', 'documentName', 'actionType'];
        optional: ['caseNumber', 'caseId', 'dueDate', 'documentUrl', 'documentId'];
    };
    'document-approval-reminder': {
        required: ['tenantName', 'recipientName', 'documentName', 'actionType'];
        optional: ['caseNumber', 'caseId', 'dueDate', 'documentUrl', 'documentId'];
    };
    'document-signature-reminder': {
        required: ['tenantName', 'recipientName', 'documentName', 'actionType'];
        optional: ['caseNumber', 'caseId', 'dueDate', 'documentUrl', 'documentId'];
    };
}

// Helper type for extracting required variables by template type
export type RequiredVariables<T extends keyof TemplateRequirements> =
    TemplateRequirements[T]['required'][number];

export type OptionalVariables<T extends keyof TemplateRequirements> =
    TemplateRequirements[T]['optional'][number];

// Type guard functions - return specific template types that are in the union
export function isCaseTemplate(
    variables: TemplateVariables
): variables is CaseUpdateVariables | CaseUrgentVariables | CaseCreatedVariables {
    return (
        variables.type === 'case-update' ||
        variables.type === 'case-urgent' ||
        variables.type === 'case-created' ||
        'caseId' in variables ||
        'caseNumber' in variables
    );
}

export function isAuthTemplate(
    variables: TemplateVariables
): variables is WelcomeVariables | PasswordResetVariables | UserInvitationVariables {
    return (
        variables.type === 'welcome' ||
        variables.type === 'password-reset' ||
        variables.type === 'user-invitation' ||
        'resetUrl' in variables ||
        'inviteUrl' in variables ||
        'isWelcome' in variables
    );
}

export function isBillingTemplate(
    variables: TemplateVariables
): variables is BillingStatementVariables | PaymentReminderVariables {
    return (
        variables.type === 'billing-statement' ||
        variables.type === 'payment-reminder' ||
        'billingMonth' in variables ||
        'invoiceNumber' in variables ||
        'amount' in variables
    );
}

export function isSystemTemplate(
    variables: TemplateVariables
): variables is SystemMaintenanceVariables {
    return (
        variables.type === 'system-maintenance' ||
        'maintenanceDate' in variables ||
        'formattedMaintenanceDate' in variables
    );
}

export function isReportTemplate(variables: TemplateVariables): variables is WeeklyReportVariables {
    return (
        variables.type === 'weekly-report' ||
        'reportPeriod' in variables ||
        'reportUrl' in variables
    );
}

export function isDocumentTemplate(
    variables: TemplateVariables
): variables is
    | DocumentGeneratedVariables
    | DocumentUploadedVariables
    | DocumentSharedVariables
    | DocumentDeletedVariables
    | DocumentGenerationFailedVariables
    | BatchDocumentsVariables
    | DocumentReminderVariables {
    if (!variables.type) {
        return (
            'documentName' in variables || 'documentId' in variables || 'templateName' in variables
        );
    }

    return (
        variables.type === 'document-generated' ||
        variables.type === 'document-uploaded' ||
        variables.type === 'document-shared' ||
        variables.type === 'document-deleted' ||
        variables.type === 'document-generation-failed' ||
        variables.type === 'batch-documents-uploaded' ||
        variables.type === 'batch-documents-deleted' ||
        variables.type === 'batch-documents-shared' ||
        variables.type === 'document-review-reminder' ||
        variables.type === 'document-approval-reminder' ||
        variables.type === 'document-signature-reminder'
    );
}

// Template variable validation function with proper typing
export function validateTemplateVariables(
    templateType: keyof TemplateRequirements,
    variables: Record<string, any>
): variables is TemplateVariables {
    const requirements = {
        'case-update': ['tenantName', 'recipientName', 'caseNumber', 'status'],
        'case-urgent': ['tenantName', 'recipientName', 'caseNumber', 'urgency'],
        'case-created': ['tenantName', 'recipientName', 'caseNumber'],
        welcome: ['tenantName', 'recipientName'],
        'password-reset': ['tenantName', 'recipientName', 'resetUrl'],
        'user-invitation': ['tenantName', 'recipientName', 'inviteUrl'],
        'billing-statement': ['tenantName', 'clientName', 'billingMonth', 'formattedAmount'],
        'payment-reminder': ['tenantName', 'clientName', 'formattedAmount', 'formattedDueDate'],
        'system-maintenance': ['tenantName', 'recipientName', 'formattedMaintenanceDate'],
        'weekly-report': ['tenantName', 'recipientName', 'reportPeriod'],
        'generic-notification': ['tenantName', 'recipientName', 'message'],
        'document-generated': [
            'tenantName',
            'recipientName',
            'documentName',
            'templateName',
            'generatedAt'
        ],
        'document-uploaded': ['tenantName', 'recipientName', 'documentName', 'uploadedAt'],
        'document-shared': ['tenantName', 'recipientName', 'documentName', 'sharedBy', 'sharedAt'],
        'document-deleted': ['tenantName', 'recipientName', 'documentName', 'deletedAt'],
        'document-generation-failed': [
            'tenantName',
            'recipientName',
            'templateName',
            'errorMessage',
            'failedAt'
        ],
        'batch-documents-uploaded': [
            'tenantName',
            'recipientName',
            'documentNames',
            'documentCount',
            'operatedAt'
        ],
        'batch-documents-deleted': [
            'tenantName',
            'recipientName',
            'documentNames',
            'documentCount',
            'operatedAt'
        ],
        'batch-documents-shared': [
            'tenantName',
            'recipientName',
            'documentNames',
            'documentCount',
            'operatedAt'
        ],
        'document-review-reminder': ['tenantName', 'recipientName', 'documentName', 'actionType'],
        'document-approval-reminder': ['tenantName', 'recipientName', 'documentName', 'actionType'],
        'document-signature-reminder': ['tenantName', 'recipientName', 'documentName', 'actionType']
    } as const;

    const required = requirements[templateType];
    const missing = required.filter(
        (field) => !variables[field] || String(variables[field]).trim() === ''
    );

    if (missing.length > 0) {
        throw new Error(`Missing required variables for ${templateType}: ${missing.join(', ')}`);
    }

    return true;
}

// Additional helper function for specific template type validation
export function validateSpecificTemplateVariables<T extends keyof TemplateRequirements>(
    templateType: T,
    variables: Record<string, any>
): asserts variables is TemplateVariables {
    validateTemplateVariables(templateType, variables);
}
