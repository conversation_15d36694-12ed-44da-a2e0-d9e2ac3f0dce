import { Injectable, Logger } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantContextService,
    TenantConnectionService
} from '@app/common/multi-tenancy';
import { TenantRole } from '@app/common/typeorm/entities/tenant/tenant-role.entity';
import { CreateRoleDto } from '../../../../apps/auth/src/dto/create-role.dto';

/**
 * Repository for tenant-specific roles
 */
@Injectable()
export class RoleRepository extends BaseTenantRepository<TenantRole> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(TenantRole, tenantContextService, tenantConnectionService);
    }

    /**
     * Finds a role by name
     * @param name The role name
     * @returns The role entity or null if not found
     */
    async findByName(name: string): Promise<TenantRole | null> {
        return this.findOneBy({ name });
    }

    /**
     * Creates a new role
     * @param createRoleDto The role creation DTO
     * @returns The created role entity
     */
    async createRole(createRoleDto: CreateRoleDto): Promise<TenantRole> {
        try {
            const role = await this.create({
                name: createRoleDto.name,
                description: createRoleDto.description
            });
            return this.save(role);
        } catch (error) {
            Logger.error('Error creating role:', error);
            throw error;
        }
    }

    /**
     * Updates a role
     * @param id The role ID
     * @param properties The properties to update
     * @returns The updated role entity
     */
    async updateRole(id: string, properties: Partial<TenantRole>): Promise<TenantRole | null> {
        return this.update(id, properties);
    }

    /**
     * Deletes a role
     * @param id The role ID
     * @returns True if the role was deleted, false otherwise
     */
    async deleteRole(id: string): Promise<boolean> {
        return this.removeById(id);
    }

    /**
     * Gets all roles
     * @returns Array of role entities
     */
    async getAllRoles(): Promise<TenantRole[]> {
        return this.find();
    }
}
