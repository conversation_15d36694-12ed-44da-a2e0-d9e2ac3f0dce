import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '../multi-tenancy';
import { Case } from '../typeorm/entities/tenant/case.entity';

@Injectable()
export class CaseRepository extends BaseTenantRepository<Case> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(Case, tenantContextService, tenantConnectionService);
    }

    /**
     * Find case by ID with related entities
     */
    async findCaseById(id: string, includeRelations: boolean = true): Promise<Case | null> {
        const relations = includeRelations ? ['client', 'property', 'rateCard'] : [];

        return this.findOne({
            where: { id },
            relations
        });
    }

    /**
     * Find case by case number
     */
    async findByCaseNumber(caseNumber: string): Promise<Case | null> {
        return this.findOne({
            where: { caseNumber },
            relations: ['client', 'property', 'rateCard']
        });
    }
}
