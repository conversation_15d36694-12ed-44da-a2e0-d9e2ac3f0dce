import { Injectable } from '@nestjs/common';
import { FindOptionsWhere, ILike, In, Not } from 'typeorm';
import {
    CustomToken,
    TokenType,
    TokenStatus
} from '../typeorm/entities/tenant/custom-token.entity';
import {
    BaseTenantRepository,
    TenantContextService,
    TenantConnectionService
} from '../multi-tenancy';

@Injectable()
export class CustomTokenRepository extends BaseTenantRepository<CustomToken> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(CustomToken, tenantContextService, tenantConnectionService);
    }

    /**
     * Create a new custom token
     */
    async create(tokenData: Partial<CustomToken>): Promise<CustomToken> {
        const repository = await this.getTenantRepository();
        const token = repository.create(tokenData);
        return repository.save(token);
    }

    /**
     * Find token by ID
     */
    async findById(id: string): Promise<CustomToken | null> {
        const repository = await this.getTenantRepository();

        return repository.findOne({ where: { id } });
    }

    /**
     * Find token by name
     */
    async findByName(tokenName: string): Promise<CustomToken | null> {
        const repository = await this.getTenantRepository();

        return repository.findOne({ where: { tokenName } });
    }

    /**
     * Find all active tokens
     */
    async findAllActive(): Promise<CustomToken[]> {
        const repository = await this.getTenantRepository();

        return repository.find({
            where: {
                status: TokenStatus.ACTIVE,
                isActive: true
            },
            order: { tokenName: 'ASC' }
        });
    }

    /**
     * Find tokens by type
     */
    async findByType(tokenType: TokenType): Promise<CustomToken[]> {
        const repository = await this.getTenantRepository();

        return repository.find({
            where: {
                tokenType,
                status: TokenStatus.ACTIVE,
                isActive: true
            },
            order: { tokenName: 'ASC' }
        });
    }

    /**
     * Find system tokens
     */
    async findSystemTokens(): Promise<CustomToken[]> {
        return this.findByType(TokenType.SYSTEM);
    }

    /**
     * Find custom tokens
     */
    async findCustomTokens(): Promise<CustomToken[]> {
        return this.findByType(TokenType.CUSTOM);
    }

    /**
     * Find tokens by entity
     */
    async findByEntity(entityName: string): Promise<CustomToken[]> {
        const repository = await this.getTenantRepository();

        return repository.find({
            where: {
                entityName,
                status: TokenStatus.ACTIVE,
                isActive: true
            },
            order: { tokenName: 'ASC' }
        });
    }

    /**
     * Find token by entity and field path (for uniqueness validation)
     */
    async findByEntityAndFieldPath(
        entityName: string,
        fieldPath: string
    ): Promise<CustomToken | null> {
        const repository = await this.getTenantRepository();

        return repository.findOne({
            where: {
                entityName,
                fieldPath,
                isActive: true
            }
        });
    }

    /**
     * Find tokens by category
     */
    async findByCategory(category: string): Promise<CustomToken[]> {
        const repository = await this.getTenantRepository();

        return repository.find({
            where: {
                category,
                status: TokenStatus.ACTIVE,
                isActive: true
            },
            order: { tokenName: 'ASC' }
        });
    }

    /**
     * Search tokens with filters
     */
    async findWithFilters(filters: {
        page?: number;
        limit?: number;
        search?: string;
        tokenType?: TokenType;
        entityName?: string;
        category?: string;
        status?: TokenStatus;
        isActive?: boolean;
        sortBy?: string;
        order?: 'ASC' | 'DESC';
    }): Promise<[CustomToken[], number]> {
        const {
            page = 1,
            limit = 10,
            search,
            tokenType,
            entityName,
            category,
            status,
            isActive,
            sortBy = 'tokenName',
            order = 'ASC'
        } = filters;

        const where: FindOptionsWhere<CustomToken> = {};

        if (search) {
            where.tokenName = ILike(`%${search}%`);
        }

        if (tokenType !== undefined) {
            where.tokenType = tokenType;
        }

        if (entityName) {
            where.entityName = entityName;
        }

        if (category) {
            where.category = category;
        }

        if (status !== undefined) {
            where.status = status;
        }

        if (isActive !== undefined) {
            where.isActive = isActive;
        }
        const repository = await this.getTenantRepository();

        return repository.findAndCount({
            where,
            order: { [sortBy]: order },
            skip: (page - 1) * limit,
            take: limit
        });
    }

    /**
     * Update token
     */
    async update(id: string, updateData: Partial<CustomToken>): Promise<CustomToken | null> {
        const repository = await this.getTenantRepository();

        await repository.update(id, {
            ...updateData,
            updatedAt: new Date()
        });
        return this.findById(id);
    }

    /**
     * Delete token
     */
    async delete(id: string): Promise<boolean> {
        const repository = await this.getTenantRepository();

        const result = await repository.delete(id);
        return (result.affected ?? 0) > 0;
    }

    /**
     * Soft delete (deactivate) token
     */
    async softDelete(id: string, userId: string): Promise<boolean> {
        const repository = await this.getTenantRepository();

        const result = await repository.update(id, {
            isActive: false,
            status: TokenStatus.INACTIVE,
            lastModifiedBy: userId,
            updatedAt: new Date()
        });
        return (result.affected ?? 0) > 0;
    }

    /**
     * Increment usage count
     */
    async incrementUsage(id: string): Promise<void> {
        const repository = await this.getTenantRepository();

        await repository.increment({ id }, 'usageCount', 1);
        await repository.update(id, {
            lastUsedAt: new Date(),
            updatedAt: new Date()
        });
    }

    /**
     * Get token usage statistics
     */
    async getUsageStats(): Promise<any[]> {
        const repository = await this.getTenantRepository();

        return repository
            .createQueryBuilder('token')
            .select([
                'token.id',
                'token.tokenName',
                'token.tokenType',
                'token.usageCount',
                'token.lastUsedAt',
                'token.category'
            ])
            .where('token.isActive = :isActive', { isActive: true })
            .orderBy('token.usageCount', 'DESC')
            .limit(50)
            .getMany();
    }

    /**
     * Get tokens by usage pattern
     */
    async findByUsagePattern(
        minUsage: number = 0,
        maxUsage: number = Number.MAX_SAFE_INTEGER
    ): Promise<CustomToken[]> {
        const repository = await this.getTenantRepository();

        return repository
            .createQueryBuilder('token')
            .where('token.usageCount >= :minUsage', { minUsage })
            .andWhere('token.usageCount <= :maxUsage', { maxUsage })
            .andWhere('token.isActive = :isActive', { isActive: true })
            .orderBy('token.usageCount', 'DESC')
            .getMany();
    }

    /**
     * Find unused tokens
     */
    async findUnusedTokens(): Promise<CustomToken[]> {
        return this.findByUsagePattern(0, 0);
    }

    /**
     * Find most used tokens
     */
    async findMostUsedTokens(limit: number = 10): Promise<CustomToken[]> {
        const repository = await this.getTenantRepository();

        return repository
            .createQueryBuilder('token')
            .where('token.isActive = :isActive', { isActive: true })
            .orderBy('token.usageCount', 'DESC')
            .limit(limit)
            .getMany();
    }

    /**
     * Get distinct categories
     */
    async getDistinctCategories(): Promise<string[]> {
        const repository = await this.getTenantRepository();

        const result = await repository
            .createQueryBuilder('token')
            .select('DISTINCT token.category', 'category')
            .where('token.category IS NOT NULL')
            .andWhere('token.isActive = :isActive', { isActive: true })
            .getRawMany();

        return result.map((r) => r.category).filter(Boolean);
    }

    /**
     * Get distinct entity names
     */
    async getDistinctEntityNames(): Promise<string[]> {
        const repository = await this.getTenantRepository();

        const result = await repository
            .createQueryBuilder('token')
            .select('DISTINCT token.entityName', 'entityName')
            .where('token.isActive = :isActive', { isActive: true })
            .getRawMany();

        return result.map((r) => r.entityName);
    }

    /**
     * Bulk create tokens
     */
    async bulkCreate(tokens: Partial<CustomToken>[]): Promise<CustomToken[]> {
        const repository = await this.getTenantRepository();

        const entities = tokens.map((tokenData) => repository.create(tokenData));
        return repository.save(entities);
    }

    /**
     * Bulk update token status
     */
    async bulkUpdateStatus(tokenIds: string[], status: TokenStatus, userId: string): Promise<void> {
        const repository = await this.getTenantRepository();

        await repository.update(
            { id: In(tokenIds) },
            {
                status,
                lastModifiedBy: userId,
                updatedAt: new Date()
            }
        );
    }

    /**
     * Check if token name exists
     */
    async existsByName(tokenName: string, excludeId?: string): Promise<boolean> {
        const repository = await this.getTenantRepository();

        const where: FindOptionsWhere<CustomToken> = { tokenName };

        if (excludeId) {
            where.id = Not(excludeId);
        }

        const count = await repository.count({ where });
        return count > 0;
    }

    /**
     * Find tokens compatible with template types
     */
    async findCompatibleWithTemplate(templateTypes: string[]): Promise<CustomToken[]> {
        const repository = await this.getTenantRepository();

        return repository
            .createQueryBuilder('token')
            .where('token.isActive = :isActive', { isActive: true })
            .andWhere('token.status = :status', { status: TokenStatus.ACTIVE })
            .andWhere(
                '(array_length(token.compatibleTemplateTypes, 1) IS NULL OR token.compatibleTemplateTypes && :templateTypes)',
                { templateTypes }
            )
            .orderBy('token.tokenName', 'ASC')
            .getMany();
    }

    /**
     * Find tokens compatible with case types
     */
    async findCompatibleWithCaseType(caseTypes: string[]): Promise<CustomToken[]> {
        const repository = await this.getTenantRepository();

        return repository
            .createQueryBuilder('token')
            .where('token.isActive = :isActive', { isActive: true })
            .andWhere('token.status = :status', { status: TokenStatus.ACTIVE })
            .andWhere(
                '(array_length(token.compatibleCaseTypes, 1) IS NULL OR token.compatibleCaseTypes && :caseTypes)',
                { caseTypes }
            )
            .orderBy('token.tokenName', 'ASC')
            .getMany();
    }
}
