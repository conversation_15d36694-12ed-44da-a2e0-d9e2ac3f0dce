import { forwardRef, Module } from '@nestjs/common';
import { UserRepository } from './user.repository';
import { PublicUserRepository } from './public-user.repository';
import { SystemRoleRepository } from './system-role.repository';
import { TenantRoleRepository } from './tenant-role.repository';
import { RoleRepository } from './role.repository';
import { TenantRepository } from './tenant.repository';
import { DocumentTemplateRepository } from './document-template.repository';
import { CommonModule } from '../common.module';
import { CustomTokenRepository } from './custom-token.repository';
import { CaseRepository } from './case.repository';

@Module({
    imports: [forwardRef(() => CommonModule)],
    providers: [
        UserRepository,
        PublicUserRepository,
        SystemRoleRepository,
        TenantRoleRepository,
        RoleRepository,
        TenantRepository,
        DocumentTemplateRepository,
        CustomTokenRepository,
        CaseRepository
    ],
    exports: [
        UserRepository,
        PublicUserRepository,
        SystemRoleRepository,
        TenantRoleRepository,
        RoleRepository,
        TenantRepository,
        DocumentTemplateRepository,
        CustomTokenRepository,
        CaseRepository
    ]
})
export class RepositoriesModule {}
