import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { Repository, SelectQueryBuilder } from 'typeorm';
import {
    DocumentTemplate,
    DocumentTemplateType,
    DocumentTemplateCategory,
    DocumentTemplateStatus
} from '@app/common/typeorm/entities';

export interface DocumentTemplateFilter {
    page?: number;
    limit?: number;
    sortBy?: string;
    order?: 'ASC' | 'DESC';
    search?: string;
    templateType?: DocumentTemplateType;
    category?: DocumentTemplateCategory;
    status?: DocumentTemplateStatus;
    isActive?: boolean;
    createdBy?: string;
    hasGenerationTriggers?: boolean;
    usedAfter?: Date;
    usedBefore?: Date;
    createdAfter?: Date;
    createdBefore?: Date;
}

export interface DocumentTemplateSearchOptions {
    includeInactive?: boolean;
    includeArchived?: boolean;
    limit?: number;
}

@Injectable()
export class DocumentTemplateRepository extends BaseTenantRepository<DocumentTemplate> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(DocumentTemplate, tenantContextService, tenantConnectionService);
    }

    /**
     * Find template by ID with full details
     */
    async findByIdWithDetails(id: string): Promise<DocumentTemplate | null> {
        const repository = await this.getTenantRepository();
        return repository
            .createQueryBuilder('template')
            .where('template.id = :id', { id })
            .getOne();
    }

    /**
     * Find template by name (case-insensitive)
     */
    async findByName(name: string): Promise<DocumentTemplate | null> {
        return this.findOne({
            where: {
                name: name,
                isActive: true
            }
        });
    }

    /**
     * Advanced search with filters
     */
    async findWithFilters(filter: DocumentTemplateFilter): Promise<[DocumentTemplate[], number]> {
        const {
            page = 1,
            limit = 10,
            sortBy = 'createdAt',
            order = 'DESC',
            search,
            ...filters
        } = filter;

        const repository = await this.getTenantRepository();
        const queryBuilder = repository.createQueryBuilder('template');

        this.applyFilters(queryBuilder, filters);

        // Apply search
        if (search) {
            queryBuilder.andWhere(
                '(' +
                    'template.name ILIKE :search OR ' +
                    'template.description ILIKE :search OR ' +
                    'template.fileName ILIKE :search' +
                    ')',
                { search: `%${search}%` }
            );
        }

        // Apply sorting
        this.applySorting(queryBuilder, sortBy, order);

        // Apply pagination
        queryBuilder.skip((page - 1) * limit).take(limit);

        return queryBuilder.getManyAndCount();
    }

    /**
     * Quick search for templates (optimized for autocomplete)
     */
    async quickSearch(
        searchTerm: string,
        options: DocumentTemplateSearchOptions = {}
    ): Promise<DocumentTemplate[]> {
        if (!searchTerm || searchTerm.length < 2) {
            return [];
        }

        const { includeInactive = false, includeArchived = false, limit = 10 } = options;

        const repository = await this.getTenantRepository();
        const queryBuilder = repository
            .createQueryBuilder('template')
            .select([
                'template.id',
                'template.name',
                'template.description',
                'template.templateType',
                'template.category',
                'template.status',
                'template.usageCount',
                'template.lastUsedAt'
            ])
            .where(
                '(' +
                    'template.name ILIKE :search OR ' +
                    'template.description ILIKE :search' +
                    ')',
                { search: `%${searchTerm}%` }
            );

        if (!includeInactive) {
            queryBuilder.andWhere('template.isActive = :isActive', { isActive: true });
        }

        if (!includeArchived) {
            queryBuilder.andWhere('template.status != :archivedStatus', {
                archivedStatus: DocumentTemplateStatus.ARCHIVED
            });
        }

        queryBuilder
            .orderBy('template.usageCount', 'DESC')
            .addOrderBy('template.lastUsedAt', 'DESC')
            .addOrderBy('template.name', 'ASC')
            .take(limit);

        return queryBuilder.getMany();
    }

    /**
     * Update template with version information
     */
    async updateVersion(
        templateId: string,
        newVersion: number,
        updatedBy: string
    ): Promise<DocumentTemplate | null> {
        const template = await this.findByIdWithDetails(templateId);
        if (!template) {
            return null;
        }

        template.lastModifiedBy = updatedBy;
        template.updatedAt = new Date();

        return this.save(template);
    }

    /**
     * Archive template (soft delete)
     */
    async archiveTemplate(templateId: string, archivedBy: string): Promise<boolean> {
        const repository = await this.getTenantRepository();

        const result = await repository
            .createQueryBuilder()
            .update(DocumentTemplate)
            .set({
                status: DocumentTemplateStatus.ARCHIVED,
                isActive: false,
                lastModifiedBy: archivedBy,
                updatedAt: new Date()
            })
            .where('id = :templateId', { templateId })
            .execute();

        return result.affected !== undefined && result.affected > 0;
    }

    /**
     * Restore archived template
     */
    async restoreTemplate(templateId: string, restoredBy: string): Promise<boolean> {
        const repository = await this.getTenantRepository();

        const result = await repository
            .createQueryBuilder()
            .update(DocumentTemplate)
            .set({
                status: DocumentTemplateStatus.ACTIVE,
                isActive: true,
                lastModifiedBy: restoredBy,
                updatedAt: new Date()
            })
            .where('id = :templateId', { templateId })
            .execute();

        return result.affected !== undefined && result.affected > 0;
    }

    /**
     * Find templates requiring token updates
     */
    async findTemplatesWithDetectedTokens(): Promise<DocumentTemplate[]> {
        const repository = await this.getTenantRepository();

        return repository
            .createQueryBuilder('template')
            .where('template.isActive = :isActive', { isActive: true })
            .andWhere('jsonb_array_length(template.detectedTokens) > 0')
            .getMany();
    }

    /**
     * Helper method to apply filters to query builder
     */
    private applyFilters(
        queryBuilder: SelectQueryBuilder<DocumentTemplate>,
        filters: Omit<DocumentTemplateFilter, 'page' | 'limit' | 'sortBy' | 'order' | 'search'>
    ): void {
        if (filters.templateType) {
            queryBuilder.andWhere('template.templateType = :templateType', {
                templateType: filters.templateType
            });
        }

        if (filters.category) {
            queryBuilder.andWhere('template.category = :category', {
                category: filters.category
            });
        }

        if (filters.status) {
            queryBuilder.andWhere('template.status = :status', {
                status: filters.status
            });
        }

        if (filters.isActive !== undefined) {
            queryBuilder.andWhere('template.isActive = :isActive', {
                isActive: filters.isActive
            });
        }

        if (filters.createdBy) {
            queryBuilder.andWhere('template.createdBy = :createdBy', {
                createdBy: filters.createdBy
            });
        }

        if (filters.hasGenerationTriggers) {
            queryBuilder.andWhere('jsonb_array_length(template.generationTriggers) > 0');
        }

        if (filters.usedAfter) {
            queryBuilder.andWhere('template.lastUsedAt >= :usedAfter', {
                usedAfter: filters.usedAfter
            });
        }

        if (filters.usedBefore) {
            queryBuilder.andWhere('template.lastUsedAt <= :usedBefore', {
                usedBefore: filters.usedBefore
            });
        }

        if (filters.createdAfter) {
            queryBuilder.andWhere('template.createdAt >= :createdAfter', {
                createdAfter: filters.createdAfter
            });
        }

        if (filters.createdBefore) {
            queryBuilder.andWhere('template.createdAt <= :createdBefore', {
                createdBefore: filters.createdBefore
            });
        }
    }

    /**
     * Helper method to apply sorting to query builder
     */
    private applySorting(
        queryBuilder: SelectQueryBuilder<DocumentTemplate>,
        sortBy: string,
        order: 'ASC' | 'DESC'
    ): void {
        const validSortFields = [
            'name',
            'createdAt',
            'updatedAt',
            'lastUsedAt',
            'usageCount',
            'templateType',
            'category',
            'status',
            'version'
        ];

        if (validSortFields.includes(sortBy)) {
            queryBuilder.orderBy(`template.${sortBy}`, order);
        } else {
            queryBuilder.orderBy('template.createdAt', 'DESC');
        }

        // Add secondary sorting for consistency
        queryBuilder.addOrderBy('template.name', 'ASC');
    }

    /**
     * Get the tenant repository for advanced operations
     */
    async getRepository(): Promise<Repository<DocumentTemplate>> {
        return this.getTenantRepository();
    }
}
