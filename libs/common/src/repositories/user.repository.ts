import { Injectable } from '@nestjs/common';
import { CreateUserDto } from '../../../../apps/auth/src/dto/create-user.dto';
import { BaseTenantRepository } from '@app/common/multi-tenancy/base-tenant.repository';
import { TenantContextService, TenantConnectionService } from '@app/common/multi-tenancy';
import { UserProfile } from '@app/common/typeorm/entities/tenant/user-profile.entity';

/**
 * Repository for tenant-specific users
 */
@Injectable()
export class UserRepository extends BaseTenantRepository<UserProfile> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(UserProfile, tenantContextService, tenantConnectionService);
    }

    /**
     * Finds a user by username
     * @param username The username
     * @returns The user entity or null if not found
     */
    async findByUsername(username: string): Promise<UserProfile | null> {
        return this.findOneBy({ username });
    }

    /**
     * Finds a user by email
     * @param email The email
     * @returns The user entity or null if not found
     */
    async findByEmail(email: string): Promise<UserProfile | null> {
        return this.findOneBy({ email });
    }

    /**
     * Finds a user by Keycloak ID
     * @param keycloakId The Keycloak ID
     * @returns The user entity or null if not found
     */
    async findByKeycloakId(keycloakId: string): Promise<UserProfile | null> {
        return this.findOneBy({ keycloakId });
    }

    /**
     * Creates a new user
     * @param createUserDto The user creation DTO
     * @param keycloakId The Keycloak ID
     * @returns The created user entity
     */
    async createUser(createUserDto: CreateUserDto, keycloakId: string): Promise<UserProfile> {
        const user = await this.create({
            username: createUserDto.username,
            email: createUserDto.email,
            firstName: createUserDto.firstName,
            lastName: createUserDto.lastName,
            keycloakId: keycloakId,
            enabled: true,
            emailVerified: false
        });

        return this.save(user);
    }

    /**
     * Updates a user
     * @param id The user ID
     * @param properties The properties to update
     * @returns The updated user entity
     */
    async updateUser(id: string, properties: Partial<UserProfile>): Promise<UserProfile | null> {
        return this.update(id, properties);
    }

    /**
     * Deletes a user
     * @param id The user ID
     * @returns True if the user was deleted, false otherwise
     */
    async deleteUser(id: string): Promise<boolean> {
        return this.removeById(id);
    }

    /**
     * Gets all users
     * @returns Array of user entities
     */
    async getAllUsers(): Promise<UserProfile[]> {
        return this.find();
    }

    /**
     * Gets users with their roles
     * @returns Array of user entities with roles
     */
    async getUsersWithRoles(): Promise<UserProfile[]> {
        const repository = await this.getTenantRepository();
        return repository.find({
            relations: ['roles']
        });
    }

    /**
     * Gets a user with their roles
     * @param id The user ID
     * @returns The user entity with roles
     */
    async getUserWithRoles(id: string): Promise<UserProfile | null> {
        const repository = await this.getTenantRepository();
        return repository.findOne({
            where: { id },
            relations: ['roles']
        });
    }
}
