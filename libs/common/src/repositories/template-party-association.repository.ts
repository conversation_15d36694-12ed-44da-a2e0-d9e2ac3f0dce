import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { TemplatePartyAssociation, PartyType } from '@app/common/typeorm/entities';

@Injectable()
export class TemplatePartyAssociationRepository extends BaseTenantRepository<TemplatePartyAssociation> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(TemplatePartyAssociation, tenantContextService, tenantConnectionService);
    }

    /**
     * Find all party associations for a template
     */
    async findByTemplateId(templateId: string): Promise<TemplatePartyAssociation[]> {
        return this.find({
            where: { templateId }
        });
    }

    /**
     * Find templates associated with specific party types
     */
    async findByPartyTypes(partyTypes: PartyType[]): Promise<TemplatePartyAssociation[]> {
        const repository = await this.getTenantRepository();
        return repository
            .createQueryBuilder('association')
            .where('association.partyType IN (:...partyTypes)', { partyTypes })
            .getMany();
    }

    /**
     * Remove all party associations for a template
     */
    async removeByTemplateId(templateId: string): Promise<void> {
        const repository = await this.getTenantRepository();
        await repository
            .createQueryBuilder()
            .delete()
            .from(TemplatePartyAssociation)
            .where('templateId = :templateId', { templateId })
            .execute();
    }

    /**
     * Create multiple party associations for a template
     */
    async createForTemplate(
        templateId: string,
        partyTypes: PartyType[]
    ): Promise<TemplatePartyAssociation[]> {
        const repository = await this.getTenantRepository();

        // Use raw insert to avoid entity creation issues
        const insertValues = partyTypes.map((partyType) => ({
            templateId,
            partyType
        }));

        const result = await repository
            .createQueryBuilder()
            .insert()
            .into(TemplatePartyAssociation)
            .values(insertValues)
            .returning(['id', 'templateId', 'partyType'])
            .execute();

        return result.generatedMaps as TemplatePartyAssociation[];
    }
}
