import { Injectable } from '@nestjs/common';
import { SystemUser } from '@app/common/typeorm/entities/public/system-user.entity';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { PasswordUtil } from '@app/common/utils/password.util';

@Injectable()
export class PublicUserRepository extends BaseTenantRepository<SystemUser> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(SystemUser, tenantContextService, tenantConnectionService, true);
    }

    async findByEmail(email: string): Promise<SystemUser | null> {
        return this.findOne({ where: { email } });
    }

    async createUser(userData: {
        email: string;
        password: string;
        firstName?: string;
        lastName?: string;
        isActive?: boolean;
    }): Promise<SystemUser> {
        const systemUser = new SystemUser();
        systemUser.email = userData.email;
        systemUser.password = await PasswordUtil.hashPassword(userData.password);
        systemUser.firstName = userData.firstName ?? '';
        systemUser.lastName = userData.lastName ?? '';
        systemUser.isActive = userData.isActive ?? true;

        return this.save(systemUser);
    }

    async findById(id: string): Promise<SystemUser | null> {
        return this.findOne({
            where: { id },
            relations: ['systemRoles']
        });
    }

    async getUserWithRoles(id: string): Promise<SystemUser | null> {
        return this.findOne({
            where: { id },
            relations: ['systemRoles']
        });
    }

    async getUserWithTenants(id: string): Promise<SystemUser | null> {
        return this.findOne({
            where: { id },
            relations: ['tenants']
        });
    }

    async getUserWithRolesAndTenants(id: string): Promise<SystemUser | null> {
        return this.findOne({
            where: { id },
            relations: ['systemRoles', 'tenants']
        });
    }

    /**
     * Verifies user credentials
     * @param email User email
     * @param password User password
     * @returns SystemUser object if credentials are valid, null otherwise
     */
    async verifyCredentials(email: string, password: string): Promise<SystemUser | null> {
        const user = await this.findByEmail(email);
        if (!user || !user.isActive) {
            return null;
        }

        const isValid = await PasswordUtil.verifyPassword(password, user.password);
        return isValid ? user : null;
    }
}
