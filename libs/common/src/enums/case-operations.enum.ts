export enum CaseOperationType {
    CREATE = 'create',
    UPDATE = 'update',
    DELETE = 'delete',
    ASSIGN = 'assign',
    UNASSIGN = 'unassign',
    STATUS_CHANGE = 'status_change',
    ADD_NOTE = 'add_note',
    ADD_ATTACHMENT = 'add_attachment',
    ADD_CONTACT = 'add_contact',
    ADD_EVENT = 'add_event',
    SET_REMINDER = 'set_reminder',
    ARCHIVE = 'archive',
    RESTORE = 'restore'
}

export enum CaseAuditOperation {
    CREATED = 'created',
    UPDATED = 'updated',
    DELETED = 'deleted',
    ASSIGNED = 'assigned',
    UNASSIGNED = 'unassigned',
    STATUS_CHANGED = 'status_changed',
    NOTE_ADDED = 'note_added',
    ATTACHMENT_ADDED = 'attachment_added',
    CONTACT_ADDED = 'contact_added',
    EVENT_ADDED = 'event_added',
    REMINDER_SET = 'reminder_set',
    CLIENT_CREATED = 'client_created'
}

export enum CaseSearchFilter {
    ALL = 'all',
    ASSIGNED_TO_ME = 'assigned_to_me',
    CREATED_BY_ME = 'created_by_me',
    OVERDUE = 'overdue',
    DUE_TODAY = 'due_today',
    DUE_THIS_WEEK = 'due_this_week',
    HIGH_PRIORITY = 'high_priority',
    URGENT = 'urgent'
}

export enum AuditSource {
    SYSTEM = 'system',
    USER = 'user',
    API = 'api',
    SCHEDULER = 'scheduler',
    WEBHOOK = 'webhook'
}
