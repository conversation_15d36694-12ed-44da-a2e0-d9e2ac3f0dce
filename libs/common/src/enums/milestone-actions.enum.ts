/**
 * Milestone-related audit actions
 * Used for tracking milestone operations in audit logs
 */
export enum MilestoneAction {
    DEFAULT_MILESTONES_CREATED = 'DEFAULT_MILESTONES_CREATED',
    MILESTONE_PROGRESS_UPDATED = 'MILESTONE_PROGRESS_UPDATED',
    MILESTONE_COMPLETED = 'MILESTONE_COMPLETED',
    CUSTOM_TASK_ADDED_TO_MILESTONE = 'CUSTOM_TASK_ADDED_TO_MILESTONE',
    TASK_STATUS_UPDATED_IN_MILESTONE = 'TASK_STATUS_UPDATED_IN_MILESTONE',
    MILESTONE_STATUS_CHANGED = 'MILESTONE_STATUS_CHANGED'
}
