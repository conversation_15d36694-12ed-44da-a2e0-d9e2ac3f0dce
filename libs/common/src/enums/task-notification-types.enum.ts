export enum TaskNotificationType {
    TASK_CREATED = 'task-created',
    TASK_REASSIGNED = 'task-reassigned',
    TASK_UNASSIGNED = 'task-unassigned',
    TASK_STATUS_UPDATED = 'task-status-updated',
    TASK_DUE_REMINDER = 'task-due-reminder',
    TASK_OVERDUE = 'task-overdue',
    TASK_COMMENT_ADDED = 'task-comment-added'
}

export enum TaskSystemUser {
    SYSTEM = 'system'
}

export enum TaskOperationType {
    TASK_CREATED = 'TASK_CREATED',
    STATUS_CHANGED = 'STATUS_CHANGED',
    DEPENDENCY_REMOVED = 'DEPENDENCY_REMOVED'
}

export const TASK_DEFAULT_VALUES = {
    NO_DESCRIPTION: 'No description provided',
    NO_DUE_DATE: 'Not set',
    NO_CASE_ASSOCIATION: 'Not associated with a case',
    DEFAULT_RECIPIENT_NAME: 'User',
    TEAM_MEMBER_NAME: 'Team Member'
} as const;

export const TASK_TEMPLATE_CONSTANTS = {
    GENERIC_NOTIFICATION: 'generic-notification'
} as const;
