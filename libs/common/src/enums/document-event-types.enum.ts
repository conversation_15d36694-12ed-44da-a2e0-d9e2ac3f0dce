export enum DocumentEventType {
    // Single document events
    DOCUMENT_GENERATED = 'document.generated',
    DOCUMENT_UPLOADED = 'document.uploaded',
    DOCUMENT_SHARED = 'document.shared',
    DOCUMENT_DELETED = 'document.deleted',
    DOCUMENT_GENERATION_FAILED = 'document.generation.failed',

    // Batch document events
    DOCUMENT_BATCH_UPLOADED = 'document.batch.uploaded',
    DOCUMENT_BATCH_DELETED = 'document.batch.deleted',
    DOCUMENT_BATCH_SHARED = 'document.batch.shared',

    // Document action reminders
    DOCUMENT_REVIEW_REMINDER = 'document.review.reminder',
    DOCUMENT_APPROVAL_REMINDER = 'document.approval.reminder',
    DOCUMENT_SIGNATURE_REMINDER = 'document.signature.reminder'
}
