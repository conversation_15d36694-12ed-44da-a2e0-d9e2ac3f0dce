export enum CasePermission {
    // View permissions
    VIEW_CASE = 'VIEW_CASE',
    VIEW_DETAILS = 'VIEW_DETAILS',
    VIEW_ATTACHMENTS = 'VIEW_ATTACHMENTS',
    VIEW_HISTORY = 'VIEW_HISTORY',

    // Edit permissions
    EDIT_DETAILS = 'EDIT_DETAILS',
    EDIT_ATTACHMENTS = 'EDIT_ATTACHMENTS',
    ADD_NOTE = 'ADD_NOTE',

    // Assignment permissions
    ASSIGN_USERS = 'ASSIGN_USERS',
    SELF_ASSIGN = 'SELF_ASSIGN',

    // Status transition permissions
    SUBMIT_CASE = 'SUBMIT_CASE', // DRAFT → SUBMITTED
    DRAFT_SUBMITED_CASE = 'DRAFT_SUBMITTED_CASE', // SUBMITTED -> DRAFT
    REVIEW_CASE = 'REVIEW_CASE', // SUBMITTED → UNDER_REVIEW
    ASSIGN_CASE = 'ASSIGN_CASE', // UNDER_REVIEW → ASSIGNED
    START_WORK = 'START_WORK', // ASSIGNED → IN_PROGRESS
    PUT_ON_HOLD = 'PUT_ON_HOLD', // IN_PROGRESS → ON_HOLD
    RESUME_WORK = 'RESUME_WORK', // ON_HOLD → IN_PROGRESS
    REQUEST_APPROVAL = 'REQUEST_APPROVAL', // IN_PROGRESS → PENDING_APPROVAL
    APPROVE_CASE = 'APPROVE_CASE', // PENDING_APPROVAL → APPROVED
    REJECT_CASE = 'REJECT_CASE', // PENDING_APPROVAL → REJECTED
    RESOLVE_CASE = 'RESOLVE_CASE', // APPROVED → RESOLVED
    CLOSE_CASE = 'CLOSE_CASE', // RESOLVED → CLOSED
    REOPEN_CASE = 'REOPEN_CASE', // CLOSED → REOPENED

    // Admin permission
    MANAGE_ALL = 'MANAGE_ALL' // Full access to case
}
