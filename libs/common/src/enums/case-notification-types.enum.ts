export enum CaseNotificationType {
    STATUS_CHANGE = 'status-change',
    CASE_CREATED = 'case-created',
    CASE_ASSIGNMENT = 'case-assignment',
    DEADLINE_REMINDER = 'deadline-reminder',
    MISSED_DEADLINE = 'missed-deadline',
    CASE_UPDATED = 'case-updated',
    CASE_COMPLETED = 'case-completed',
    CASE_REOPENED = 'case-reopened',
    CLIENT_CONTACTED = 'client-contacted',
    DOCUMENT_ADDED = 'document-added'
}

export enum CaseUrgencyLevel {
    LOW = 'low',
    NORMAL = 'normal',
    HIGH = 'high',
    CRITICAL = 'critical'
}

export enum CaseReminderType {
    EMAIL = 'email',
    IN_APP = 'in-app',
    SMS = 'sms',
    PUSH = 'push'
}

export enum ReminderStatus {
    SCHEDULED = 'scheduled',
    SENT = 'sent',
    FAILED = 'failed',
    CANCELLED = 'cancelled'
}

export const DEFAULT_NAMES = {
    CLIENT: 'Client',
    USER: 'User',
    SYSTEM: 'System'
} as const;

export const DEFAULT_MESSAGES = {
    NEW_CASE_CREATED: 'A new case has been created',
    CASE_UPDATED: 'Case has been updated',
    ASSIGNMENT_COMPLETED: 'Assignment has been completed'
} as const;
