export enum CaseEventType {
    CASE_CREATED = 'case.created',
    CASE_UPDATED = 'case.updated',
    CASE_DELETED = 'case.deleted',
    CASE_ASSIGNED = 'case.assigned',
    CASE_UNASSIGNED = 'case.unassigned',
    CASE_STATUS_CHANGED = 'case.status.changed',
    CASE_NOTE_ADDED = 'case.note.added',
    CASE_ATTACHMENT_ADDED = 'case.attachment.added',
    CASE_CONTACT_ADDED = 'case.contact.added',
    CASE_EVENT_ADDED = 'case.event.added',
    CASE_REMINDER_SET = 'case.reminder.set',
    CASE_DEADLINE_APPROACHING = 'case.deadline.approaching',
    CASE_DEADLINE_MISSED = 'case.deadline.missed',
    CLIENT_CREATED = 'client.created',
    CLIENT_UPDATED = 'client.updated',
    CLIENT_CONTACTED = 'client.contacted'
}

export enum CaseActionResult {
    SUCCESS = 'success',
    FAILED = 'failed',
    PARTIAL_SUCCESS = 'partial_success',
    VALIDATION_ERROR = 'validation_error',
    PERMISSION_DENIED = 'permission_denied',
    NOT_FOUND = 'not_found',
    CONFLICT = 'conflict'
}
