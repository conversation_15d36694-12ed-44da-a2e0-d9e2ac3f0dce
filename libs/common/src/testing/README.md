# Test Utilities

This directory contains utilities to simplify writing tests across the application.

## Contents

- **Mock Factories**: Functions for creating common mocks (ConfigService, Keycloak services, etc.)
- **Test Module Builders**: Utilities for creating NestJS test modules with common configurations
- **Shared Types**: Common types used in testing

## Usage Examples

### Creating a Mock ConfigService

```typescript
import { createMockConfigService } from '@app/common/testing';

const configService = createMockConfigService({
  auth: {
    serverUrl: 'http://keycloak:8080',
    admin: 'testadmin',
    adminPassword: 'testpassword'
  }
});
```

### Creating an Auth Test Module

```typescript
import { createAuthTestModule } from '@app/common/testing';
import { YourService } from './your.service';

describe('YourService', () => {
  let service: YourService;

  beforeEach(async () => {
    const module = await createAuthTestModule({
      providers: [YourService],
      configValues: {
        // Override specific config values
        auth: {
          serverUrl: 'http://test-keycloak:8080'
        }
      }
    });

    service = module.get<YourService>(YourService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
```

### Using Mock Responses

```typescript
import { mockResponses } from '@app/common/testing';

// In your test
const { keycloakToken } = mockResponses;
myMockService.getToken.mockResolvedValue(keycloakToken);
```

## Jest Configuration

The project uses a shared Jest configuration located at the root `jest.preset.js` and customized for each app in their respective jest.config.js files.

### Global Setup

Common test setup is handled in `/test/jest-setup.ts` which:

1. Automatically mocks axios 
2. Clears mocks between tests
3. Restores mocks after all tests

## Best Practices

1. **Avoid Repetition**: Use these utilities to avoid duplicating mock setup code
2. **Consistent Mocking**: Ensure consistent behavior across tests by using shared mock factories
3. **Isolated Tests**: Keep tests isolated and avoid dependencies on external services 

## Shared Entity Mocks

You can use the following factory functions to create mock entities for your tests:

```typescript
import { createMockUser, createMockCase, createMockRole } from '@app/common/testing';

const user = createMockUser();
const testCase = createMockCase({ status: 'SUBMITTED' });
const role = createMockRole({ name: 'Admin' });
```

## Integration Test Database

A dedicated PostgreSQL service for integration tests is available via Docker Compose:

```yaml
test_db:
  image: postgres:15
  environment:
    POSTGRES_DB: test_db
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: postgres
  ports:
    - "5433:5432"
```

Set your test environment variables to use this DB (e.g., `.env.test`):

```
DB_HOST=localhost
DB_PORT=5433
DB_NAME=test_db
DB_USER=postgres
DB_PASS=postgres
``` 