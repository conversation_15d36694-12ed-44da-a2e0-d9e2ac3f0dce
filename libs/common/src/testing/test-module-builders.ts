import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { createMockConfigService, createMockKeycloakHttpService } from './mock-factories';

/**
 * Options for the auth test module
 */
export interface AuthTestModuleOptions {
    /**
     * Additional providers to include in the module
     */
    providers?: any[];

    /**
     * Additional mock providers to include in the module
     * Should be in the format { provide: Class, useValue: mockValue }
     */
    mockProviders?: any[];

    /**
     * Custom config values to use for the mock ConfigService
     */
    configValues?: Record<string, any>;
}

/**
 * Creates a test module with common auth dependencies mocked
 */
export async function createAuthTestModule(
    options: AuthTestModuleOptions = {}
): Promise<TestingModule> {
    const { providers = [], mockProviders = [], configValues = {} } = options;

    const configService = createMockConfigService(configValues);
    const keycloakHttpService = createMockKeycloakHttpService();

    return Test.createTestingModule({
        providers: [
            ...providers,
            { provide: ConfigService, useValue: configService },
            { provide: 'KeycloakHttpService', useValue: keycloakHttpService },
            ...mockProviders
        ]
    }).compile();
}

/**
 * Options for creating a test module with custom overrides
 */
export interface CustomTestModuleOptions {
    /**
     * Modules to import
     */
    imports?: any[];

    /**
     * Providers to include in the module
     */
    providers?: any[];

    /**
     * Mock providers to include in the module
     * Should be in the format { provide: Class, useValue: mockValue }
     */
    mockProviders?: any[];

    /**
     * Controllers to include in the module
     */
    controllers?: any[];
}

/**
 * Creates a custom test module with the given options
 */
export async function createCustomTestModule(
    options: CustomTestModuleOptions
): Promise<TestingModule> {
    const { imports = [], providers = [], mockProviders = [], controllers = [] } = options;

    return Test.createTestingModule({
        imports,
        providers: [...providers, ...mockProviders],
        controllers
    }).compile();
}
