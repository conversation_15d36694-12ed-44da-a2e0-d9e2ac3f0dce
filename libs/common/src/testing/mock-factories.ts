import { HttpStatusCode } from '../enums/http-status.enum';
import axios from 'axios';
import { Subject } from 'rxjs';

/**
 * Enum for Keycloak error types
 * This is a duplicate of the one in apps/auth/src/services/keycloak-http.service.ts
 * to avoid circular dependencies
 */
export enum KeycloakErrorType {
    NETWORK = 'NETWORK',
    CLIENT = 'CLIENT',
    SERVER = 'SERVER',
    UNAUTHORIZED = 'UNAUTHORIZED',
    NOT_FOUND = 'NOT_FOUND',
    TIMEOUT = 'TIMEOUT',
    UNKNOWN = 'UNKNOWN'
}

/**
 * Creates a mock KeycloakHttpService with all required methods
 */
export function createMockKeycloakHttpService() {
    // Create a real Axios instance for the mock to use
    const axiosInstance = axios.create({
        baseURL: 'http://mock-keycloak:8080',
        timeout: 30000,
        headers: {
            'Content-Type': 'application/json'
        }
    });

    // Mock the methods that would be added by real service
    axiosInstance.interceptors.request.use = jest.fn().mockReturnValue(0);
    axiosInstance.interceptors.response.use = jest.fn().mockReturnValue(0);

    return {
        axiosInstance,
        get: jest.fn(),
        post: jest.fn(),
        put: jest.fn(),
        delete: jest.fn(),
        resetCircuitBreaker: jest.fn(),
        getCircuitState: jest.fn().mockReturnValue({ state: 'CLOSED', failureCount: 0 }),
        getMetrics: jest.fn().mockReturnValue({
            requestCount: 0,
            successRate: 100,
            avgLatency: 0,
            errorBreakdown: {},
            circuitState: 'CLOSED'
        }),
        resetMetrics: jest.fn(),
        checkHealth: jest.fn().mockResolvedValue({
            status: 'UP',
            details: { circuitState: 'CLOSED' }
        }),
        getInstance: jest.fn().mockReturnValue(axiosInstance),
        // Mock private methods that might be used in tests
        mapAxiosError: jest.fn().mockImplementation((error) => {
            if (error?.code === 'ECONNREFUSED') {
                return {
                    type: KeycloakErrorType.NETWORK,
                    message: 'Network error: Connection refused',
                    originalError: error
                };
            } else if (error?.code === 'ETIMEDOUT') {
                return {
                    type: KeycloakErrorType.TIMEOUT,
                    message: 'Request timeout',
                    originalError: error
                };
            } else if (error?.response?.status === HttpStatusCode.UNAUTHORIZED) {
                return {
                    type: KeycloakErrorType.UNAUTHORIZED,
                    status: HttpStatusCode.UNAUTHORIZED,
                    message: 'Unauthorized',
                    originalError: error
                };
            } else if (error?.response?.status === HttpStatusCode.NOT_FOUND) {
                return {
                    type: KeycloakErrorType.NOT_FOUND,
                    status: HttpStatusCode.NOT_FOUND,
                    message: 'Not found',
                    originalError: error
                };
            }
            return {
                type: KeycloakErrorType.UNKNOWN,
                message: 'Unknown error',
                originalError: error
            };
        }),
        canMakeRequest: jest.fn().mockReturnValue(true),
        trackLatency: jest.fn()
    };
}

/**
 * Creates a mock ConfigService that emulates the NestJS ConfigService
 */
export function createMockConfigService(mockConfig = {}) {
    // Create a changes subject for the Observable
    const changesSubject = new Subject();

    return {
        get: jest.fn((key) => {
            if (key && typeof key === 'string') {
                const parts = key.split('.');
                let value = mockConfig;
                for (const part of parts) {
                    if (value && typeof value === 'object' && part in value) {
                        value = value[part];
                    } else {
                        return undefined;
                    }
                }
                return value;
            }
            return undefined;
        }),
        getOrThrow: jest.fn((key) => {
            const value = this.get(key);
            if (value === undefined) {
                throw new Error(`Config key "${key}" not found`);
            }
            return value;
        }),
        set: jest.fn(),
        setEnvFilePaths: jest.fn(),
        changes$: changesSubject.asObservable()
    };
}

/**
 * Creates a mock KeycloakService with all required methods
 */
export function createMockKeycloakService() {
    return {
        // Tenant/realm management
        createRealm: jest.fn().mockResolvedValue({ id: 'realm-id-123', name: 'test-realm' }),
        createInitialAdminUser: jest.fn().mockResolvedValue({
            user: { id: 'user-id-123', username: 'admin' },
            client: { id: 'client-id-123', clientId: 'test-client' }
        }),

        // Authentication
        getToken: jest.fn().mockResolvedValue({
            access_token: 'mock-access-token',
            refresh_token: 'mock-refresh-token',
            expires_in: 300,
            token_type: 'bearer'
        }),
        refreshToken: jest.fn().mockResolvedValue({
            access_token: 'mock-refreshed-access-token',
            refresh_token: 'mock-refreshed-refresh-token',
            expires_in: 300,
            token_type: 'bearer'
        }),

        // User management
        createUser: jest.fn().mockResolvedValue('user-id-456'),
        updateUser: jest.fn().mockResolvedValue(true),
        deleteUser: jest.fn().mockResolvedValue(true),
        getUserById: jest.fn().mockResolvedValue({
            id: 'user-id-456',
            username: 'testuser',
            email: '<EMAIL>'
        }),
        getUserByUsername: jest.fn().mockResolvedValue({
            id: 'user-id-456',
            username: 'testuser',
            email: '<EMAIL>'
        }),

        // Role management
        createRole: jest.fn().mockResolvedValue({ id: 'role-id-123', name: 'test-role' }),
        assignRoleToUser: jest.fn().mockResolvedValue(true),

        // Client management
        getClientToken: jest.fn().mockResolvedValue({
            access_token: 'mock-client-token',
            expires_in: 300
        }),
        checkAndFixClientConfiguration: jest.fn().mockResolvedValue({
            clientId: 'test-client',
            directAccessGrantsEnabled: true,
            publicClient: false,
            standardFlowEnabled: true,
            wasUpdated: false
        }),

        // Meta
        getOpenIdConfiguration: jest.fn().mockResolvedValue({
            issuer: 'http://localhost:8080/auth/realms/test',
            authorization_endpoint:
                'http://localhost:8080/auth/realms/test/protocol/openid-connect/auth'
        }),
        getJwks: jest.fn().mockResolvedValue({
            keys: [{ kid: 'test-key-id', kty: 'RSA' }]
        }),
        findUsersByEmail: jest
            .fn()
            .mockResolvedValue([
                { id: 'user-id-456', username: 'testuser', email: '<EMAIL>' }
            ])
    };
}

/**
 * Creates a mock DataSource that doesn't connect to a real database
 */
export function createMockDataSource() {
    return {
        initialize: jest.fn().mockResolvedValue(undefined),
        destroy: jest.fn().mockResolvedValue(undefined),
        isInitialized: true,
        driver: {
            connect: jest.fn().mockResolvedValue(undefined),
            disconnect: jest.fn().mockResolvedValue(undefined)
        },
        createQueryRunner: jest.fn().mockReturnValue({
            connect: jest.fn().mockResolvedValue(undefined),
            release: jest.fn().mockResolvedValue(undefined),
            startTransaction: jest.fn().mockResolvedValue(undefined),
            commitTransaction: jest.fn().mockResolvedValue(undefined),
            rollbackTransaction: jest.fn().mockResolvedValue(undefined),
            query: jest.fn().mockResolvedValue([])
        }),
        manager: {
            query: jest.fn().mockResolvedValue([]),
            save: jest.fn().mockImplementation((entity) => Promise.resolve(entity)),
            findOne: jest.fn().mockResolvedValue(null),
            find: jest.fn().mockResolvedValue([]),
            create: jest.fn().mockImplementation((EntityClass, data) => data)
        },
        query: jest.fn().mockResolvedValue([{ number: 1 }])
    };
}

/**
 * Creates a mock TypeORM config
 */
export function createMockTypeormConfig() {
    return {
        type: 'postgres',
        host: 'localhost',
        port: 5433,
        username: 'postgres',
        password: 'postgres',
        database: 'test_db',
        synchronize: false,
        ssl: false,
        schema: 'public',
        entities: [],
        migrations: [],
        migrationsTableName: 'typeorm_migrations'
    };
}

/**
 * Mock responses for various services
 */
export const mockResponses = {
    keycloakToken: {
        access_token:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
        refresh_token:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
        expires_in: 300,
        refresh_expires_in: 1800,
        token_type: 'bearer',
        not_before_policy: 0,
        session_state: 'mock-session-state',
        scope: 'profile email'
    }
};

/**
 * Creates a mock Express response object
 */
export function createMockResponse() {
    const res = {
        cookie: jest.fn().mockReturnThis(),
        clearCookie: jest.fn().mockReturnThis(),
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockReturnThis(),
        send: jest.fn().mockReturnThis()
    };
    return res;
}

/**
 * Creates a mock User entity
 */
export function createMockUser(overrides = {}) {
    return {
        id: 'user-uuid',
        email: '<EMAIL>',
        password: 'hashedpassword',
        firstName: 'Test',
        lastName: 'User',
        isActive: true,
        emailVerified: false,
        rememberMe: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        ...overrides
    };
}

/**
 * Creates a mock Case entity
 */
export function createMockCase(overrides = {}) {
    return {
        id: 'case-uuid',
        caseNumber: 'CASE-001',
        title: 'Test Case',
        description: 'A test case',
        status: 'DRAFT',
        priority: 'MEDIUM',
        type: 'OTHER',
        clientId: 'client-uuid',
        createdBy: 'user-uuid',
        createdAt: new Date(),
        updatedAt: new Date(),
        deadline: null,
        ...overrides
    };
}

/**
 * Creates a mock Role entity
 */
export function createMockRole(overrides = {}) {
    return {
        id: 'role-uuid',
        name: 'Test Role',
        description: 'A test role',
        permissions: {},
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        ...overrides
    };
}

/**
 * Creates a mock PublicSchemaMigrationService
 */
export function createMockPublicSchemaMigrationService() {
    return {
        create: jest.fn().mockResolvedValue(undefined),
        upgrade: jest.fn().mockResolvedValue(undefined),
        isPublicSchemaReady: jest.fn().mockResolvedValue(true)
    };
}

/**
 * Creates a mock TenantSchemaMigrationService
 */
export function createMockTenantSchemaMigrationService() {
    return {
        createTenantSchema: jest.fn().mockResolvedValue(undefined),
        upgrade: jest.fn().mockResolvedValue(undefined)
    };
}

/**
 * Communication Service Mock Factories
 */

export function createMockSESEmailService() {
    return {
        sendTemplatedEmail: jest.fn().mockResolvedValue({
            messageId: 'ses-test-message-id',
            provider: 'ses',
            status: 'sent',
            templateType: 'case-update'
        }),
        isEnabled: jest.fn().mockReturnValue(true),
        getStatus: jest.fn().mockReturnValue({
            configured: true,
            region: 'us-east-1',
            status: 'active'
        }),
        validateEmailData: jest.fn()
    };
}

export function createMockTemplateService() {
    const mockTemplateConfig = {
        sendGridTemplateId: 'd-test-template-id',
        sesTemplateName: 'test-template',
        mailgunTemplateName: 'test-template',
        defaultSubject: 'Test Subject - {{caseNumber}}',
        categories: ['test', 'case', 'legal'],
        requiredVariables: ['tenantName', 'recipientName', 'caseNumber'],
        optionalVariables: ['caseSummary', 'handlerName']
    };

    return {
        processTemplate: jest.fn().mockResolvedValue({
            templateType: 'case-update',
            templateData: {
                tenantName: 'Test Legal Firm',
                recipientName: 'John Test',
                caseNumber: 'TEST-2024-001',
                subject: 'Test Subject - TEST-2024-001'
            },
            subject: 'Test Subject - TEST-2024-001'
        }),
        determineTemplateType: jest.fn().mockReturnValue('case-update'),
        validateTemplateData: jest.fn(),
        getTemplateConfig: jest.fn().mockReturnValue(mockTemplateConfig),
        getUnifiedTemplates: jest.fn().mockReturnValue({
            'case-update': mockTemplateConfig,
            welcome: mockTemplateConfig,
            'generic-notification': mockTemplateConfig
        }),
        getTemplateVariables: jest.fn().mockReturnValue({
            required: ['tenantName', 'recipientName', 'caseNumber'],
            optional: ['caseSummary', 'handlerName']
        })
    };
}

export function createMockCircuitBreakerService() {
    return {
        registerCircuit: jest.fn(),
        execute: jest
            .fn()
            .mockImplementation(async (circuitName: string, operation: () => Promise<any>) => {
                return await operation();
            }),
        getCircuitState: jest.fn().mockReturnValue({
            state: 'CLOSED',
            failureCount: 0,
            nextAttempt: null
        }),
        resetCircuit: jest.fn(),
        isCircuitOpen: jest.fn().mockReturnValue(false),
        recordSuccess: jest.fn(),
        recordFailure: jest.fn()
    };
}

export function createMockEmailProviders() {
    return {
        sendgrid: {
            send: jest.fn().mockResolvedValue([
                {
                    statusCode: 202,
                    headers: { 'x-message-id': 'sg-test-message-id' }
                }
            ])
        },
        mailgun: {
            messages: {
                create: jest.fn().mockResolvedValue({
                    id: 'mg-test-message-id',
                    message: 'Queued. Thank you.'
                })
            }
        },
        ses: {
            send: jest.fn().mockResolvedValue({
                MessageId: 'ses-test-message-id',
                $metadata: {
                    httpStatusCode: 200,
                    requestId: 'test-request-id'
                }
            })
        }
    };
}

export function createMockCommunicationProducer() {
    return {
        sendEmail: jest.fn().mockResolvedValue({
            jobId: 'test-job-id',
            channel: 'email',
            status: 'queued'
        }),
        sendNotification: jest.fn().mockResolvedValue({
            jobId: 'test-notification-job-id',
            channel: 'notification',
            status: 'queued'
        }),
        sendMultiChannel: jest.fn().mockResolvedValue({
            jobId: 'test-multi-channel-job-id',
            channels: ['email', 'notification'],
            status: 'queued'
        })
    };
}

export function createMockNotificationService() {
    return {
        sendPushNotification: jest.fn().mockResolvedValue({
            messageId: 'notification-test-id',
            provider: 'firebase',
            status: 'sent'
        }),
        sendWebPush: jest.fn().mockResolvedValue({
            messageId: 'webpush-test-id',
            provider: 'webpush',
            status: 'sent'
        }),
        getProviderStatus: jest.fn().mockReturnValue({
            firebase: { configured: true, status: 'active' },
            webpush: { configured: true, status: 'active' },
            overall: { healthy: true }
        })
    };
}

export function createMockTenant() {
    return {
        id: 'test-tenant-123',
        displayName: 'Test Legal Firm',
        subdomain: 'test-firm',
        email: '<EMAIL>',
        logo: 'https://testfirm.com/logo.png',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        isActive: true
    };
}

export function createMockEmailTemplateData() {
    return {
        caseUpdate: {
            type: 'case-update' as const,
            tenantName: 'Test Legal Firm',
            recipientName: 'John Test',
            caseNumber: 'TEST-2024-001',
            status: 'in_progress',
            caseSummary: 'Test case has been updated with new information',
            handlerName: 'Jane Test Attorney',
            handlerTitle: 'Senior Partner',
            caseUrl: 'https://portal.testfirm.com/cases/123',
            urgency: 'normal' as const
        },
        caseCreated: {
            type: 'case-created' as const,
            caseId: 'case-123',
            tenantName: 'Test Legal Firm',
            recipientName: 'John Test',
            caseNumber: 'TEST-2024-001',
            status: 'created',
            caseSummary: 'New case has been created',
            handlerName: 'Jane Test Attorney',
            handlerTitle: 'Senior Partner',
            caseUrl: 'https://portal.testfirm.com/cases/123',
            urgency: 'normal' as const
        },
        welcome: {
            type: 'welcome' as const,
            tenantName: 'Test Legal Firm',
            recipientName: 'John Test',
            role: 'Client',
            inviterName: 'Jane Test Attorney',
            loginUrl: 'https://portal.testfirm.com',
            supportEmail: '<EMAIL>'
        },
        passwordReset: {
            type: 'password-reset' as const,
            tenantName: 'Test Legal Firm',
            recipientName: 'John Test',
            resetUrl: 'https://portal.testfirm.com/reset?token=test-token',
            expirationTime: '24 hours',
            securityTip: 'Never share this link with others'
        },
        caseUrgent: {
            type: 'case-urgent' as const,
            tenantName: 'Test Legal Firm',
            recipientName: 'John Test',
            caseNumber: 'TEST-2024-001',
            urgency: 'high' as const,
            message: 'Immediate action required for court filing',
            courtDate: '2024-01-15',
            handlerName: 'Jane Test Attorney'
        }
    };
}
