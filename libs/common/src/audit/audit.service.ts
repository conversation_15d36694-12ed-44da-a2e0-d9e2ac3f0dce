import { Injectable, Logger, Optional, Inject } from '@nestjs/common';
import { PermissionAuditAction, ResourceType } from '../permissions/permission.constants';

/**
 * Interface for permission audit log entry
 */
interface PermissionAuditLog {
    userId: string;
    tenantId: string;
    action: PermissionAuditAction;
    resourceType: ResourceType | string;
    resourceId: string;
    timestamp: string;
    result: PermissionAuditAction;
    permissions?: string[];
    ipAddress?: string;
    userAgent?: string;
    sessionId?: string;
    details?: Record<string, any>;
}

/**
 * Interface for case audit service
 * This allows the AuditService to optionally integrate with case-specific audit services
 */
interface ICaseAuditService {
    logAction(
        caseId: string,
        action: string,
        userId: string,
        userName: string,
        ipAddress: string,
        details?: Record<string, any>
    ): Promise<void>;
}

/**
 * Token for dependency injection of ICaseAuditService
 */
export const CASE_AUDIT_SERVICE = Symbol('CASE_AUDIT_SERVICE');

/**
 * Service for auditing permission checks and access attempts
 */
@Injectable()
export class AuditService {
    private readonly logger = new Logger(AuditService.name);

    constructor(
        @Optional()
        @Inject(CASE_AUDIT_SERVICE)
        private readonly caseAuditService?: ICaseAuditService
    ) {}

    /**
     * Log permission check attempt
     * @param auditLog Permission audit log entry
     */
    async logPermissionCheck(auditLog: PermissionAuditLog): Promise<void> {
        try {
            // Log to application logger
            this.logger.log(
                `Permission ${auditLog.action} for user ${auditLog.userId} on ${auditLog.resourceType}:${auditLog.resourceId} in tenant ${auditLog.tenantId}`
            );

            // Store audit log in database
            this.storeAuditLog(auditLog);

            // If this is a case-related permission, also log to case audit
            if (auditLog.resourceType === ResourceType.CASE && auditLog.resourceId !== 'unknown') {
                await this.logCasePermissionCheck(auditLog);
            }
        } catch (error) {
            this.logger.error(`Failed to log permission check: ${error.message}`, error.stack);
            // Don't throw error to avoid disrupting the main operation
        }
    }

    /**
     * Store audit log entry in database
     * @param auditLog Permission audit log entry
     */
    private storeAuditLog(auditLog: PermissionAuditLog): void {
        // TODO: Implement database storage for audit logs
        // For now, just log to console
        this.logger.debug('Audit log stored:', JSON.stringify(auditLog, null, 2));
    }

    /**
     * Log case-specific permission check
     * @param auditLog Permission audit log entry
     */
    private async logCasePermissionCheck(auditLog: PermissionAuditLog): Promise<void> {
        // Skip if no case audit service is available
        if (!this.caseAuditService) {
            this.logger.debug('No case audit service available, skipping case-specific audit log');
            return;
        }

        try {
            // Log to case audit service
            await this.caseAuditService.logAction(
                auditLog.resourceId,
                'PERMISSION_CHECK',
                auditLog.userId,
                auditLog.userId, // Use userId as name for now
                auditLog.ipAddress || 'unknown',
                {
                    action: auditLog.action,
                    result: auditLog.result,
                    permissions: auditLog.permissions,
                    timestamp: auditLog.timestamp,
                    resourceType: auditLog.resourceType,
                    details: auditLog.details
                }
            );
        } catch (error) {
            this.logger.warn(`Failed to log case permission check: ${error.message}`);
        }
    }

    /**
     * Log user access attempt
     * @param userId User ID
     * @param tenantId Tenant ID
     * @param resourceType Resource type
     * @param resourceId Resource ID
     * @param action Action attempted
     * @param result Result of the attempt
     * @param details Additional details
     */
    async logUserAccess(
        userId: string,
        tenantId: string,
        resourceType: ResourceType,
        resourceId: string,
        action: string,
        result: 'SUCCESS' | 'FAILURE',
        details?: Record<string, any>
    ): Promise<void> {
        await this.logPermissionCheck({
            userId,
            tenantId,
            action:
                result === 'SUCCESS'
                    ? PermissionAuditAction.PERMISSION_GRANTED
                    : PermissionAuditAction.PERMISSION_DENIED,
            resourceType,
            resourceId,
            timestamp: new Date().toISOString(),
            result:
                result === 'SUCCESS'
                    ? PermissionAuditAction.PERMISSION_GRANTED
                    : PermissionAuditAction.PERMISSION_DENIED,
            details: { ...details, originalAction: action }
        });
    }

    /**
     * Log bulk permission check results
     * @param auditLogs Array of permission audit log entries
     */
    async logBulkPermissionChecks(auditLogs: PermissionAuditLog[]): Promise<void> {
        for (const auditLog of auditLogs) {
            await this.logPermissionCheck(auditLog);
        }
    }

    /**
     * Get audit logs for a specific resource
     * @param resourceType Resource type
     * @param resourceId Resource ID
     * @param tenantId Tenant ID
     * @param limit Maximum number of logs to return
     * @returns Array of audit log entries
     */
    getResourceAuditLogs(
        resourceType: ResourceType,
        resourceId: string,
        tenantId: string,
        limit: number = 100
    ): PermissionAuditLog[] {
        // TODO: Implement database query for audit logs
        // For now, return empty array
        this.logger.debug(
            `Getting audit logs for ${resourceType}:${resourceId} in tenant ${tenantId} (limit: ${limit})`
        );
        return [];
    }

    /**
     * Get audit logs for a specific user
     * @param userId User ID
     * @param tenantId Tenant ID
     * @param limit Maximum number of logs to return
     * @returns Array of audit log entries
     */
    getUserAuditLogs(userId: string, tenantId: string, limit: number = 100): PermissionAuditLog[] {
        // TODO: Implement database query for audit logs
        // For now, return empty array
        this.logger.debug(
            `Getting audit logs for user ${userId} in tenant ${tenantId} (limit: ${limit})`
        );
        return [];
    }
}
