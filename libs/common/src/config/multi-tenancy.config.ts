import { registerAs } from '@nestjs/config';
import {
    MultiTenancyConfig,
    RawMultiTenancyEnv
} from './interfaces/multi-tenancy.config.interface';

/**
 * Multi-tenancy configuration
 * Loads raw environment variables, validates them, and returns a fully validated config
 */
export const multiTenancyConfig = registerAs('multiTenancy', (): MultiTenancyConfig => {
    // Load raw environment variables
    const rawEnv: RawMultiTenancyEnv = {
        MULTI_TENANCY_MAX_CONNECTIONS: process.env.MULTI_TENANCY_MAX_CONNECTIONS,
        MULTI_TENANCY_CONNECTION_TTL: process.env.MULTI_TENANCY_CONNECTION_TTL,
        MULTI_TENANCY_AUTO_CREATE_SCHEMA: process.env.MULTI_TENANCY_AUTO_CREATE_SCHEMA,
        MULTI_TENANCY_AUTO_RUN_MIGRATIONS: process.env.MULTI_TENANCY_AUTO_RUN_MIGRATIONS,
        MULTI_TENANCY_MIGRATIONS_DIR: process.env.MULTI_TENANCY_MIGRATIONS_DIR
    };

    // Return fully validated config
    return {
        maxConnections: parseInt(rawEnv.MULTI_TENANCY_MAX_CONNECTIONS || '50', 10),
        connectionTtl: parseInt(rawEnv.MULTI_TENANCY_CONNECTION_TTL || '3600000', 10), // 1 hour default
        autoCreateSchema: rawEnv.MULTI_TENANCY_AUTO_CREATE_SCHEMA !== 'false', // Default to true
        autoRunMigrations: rawEnv.MULTI_TENANCY_AUTO_RUN_MIGRATIONS === 'false', // Default to false
        migrationsDir: rawEnv.MULTI_TENANCY_MIGRATIONS_DIR
    };
});
