/**
 * UK Bank Holidays Configuration
 *
 * Note: This should ideally be stored in a database or fetched from a government API
 * like https://www.gov.uk/bank-holidays.json
 *
 * For now, this configuration provides holidays for multiple years.
 * Consider implementing an automated update system in the future.
 */

export interface BankHoliday {
    date: string; // YYYY-MM-DD format
    name: string;
    region?: 'england-and-wales' | 'scotland' | 'northern-ireland';
}

export interface YearlyBankHolidays {
    year: number;
    holidays: BankHoliday[];
}

export const UK_BANK_HOLIDAYS: YearlyBankHolidays[] = [
    {
        year: 2024,
        holidays: [
            { date: '2024-01-01', name: "New Year's Day" },
            { date: '2024-03-29', name: 'Good Friday' },
            { date: '2024-04-01', name: 'Easter Monday' },
            { date: '2024-05-06', name: 'Early May Bank Holiday' },
            { date: '2024-05-27', name: 'Spring Bank Holiday' },
            { date: '2024-08-26', name: 'Summer Bank Holiday' },
            { date: '2024-12-25', name: 'Christmas Day' },
            { date: '2024-12-26', name: 'Boxing Day' }
        ]
    },
    {
        year: 2025,
        holidays: [
            { date: '2025-01-01', name: "New Year's Day" },
            { date: '2025-04-18', name: 'Good Friday' },
            { date: '2025-04-21', name: 'Easter Monday' },
            { date: '2025-05-05', name: 'Early May Bank Holiday' },
            { date: '2025-05-26', name: 'Spring Bank Holiday' },
            { date: '2025-08-25', name: 'Summer Bank Holiday' },
            { date: '2025-12-25', name: 'Christmas Day' },
            { date: '2025-12-26', name: 'Boxing Day' }
        ]
    },
    {
        year: 2026,
        holidays: [
            { date: '2026-01-01', name: "New Year's Day" },
            { date: '2026-04-03', name: 'Good Friday' },
            { date: '2026-04-06', name: 'Easter Monday' },
            { date: '2026-05-04', name: 'Early May Bank Holiday' },
            { date: '2026-05-25', name: 'Spring Bank Holiday' },
            { date: '2026-08-31', name: 'Summer Bank Holiday' },
            { date: '2026-12-25', name: 'Christmas Day' },
            { date: '2026-12-28', name: 'Boxing Day (substitute)' } // Boxing Day falls on Saturday
        ]
    }
];

/**
 * Get bank holidays for a specific year
 */
export function getBankHolidaysForYear(year: number): BankHoliday[] {
    const yearData = UK_BANK_HOLIDAYS.find((data) => data.year === year);
    return yearData?.holidays || [];
}

/**
 * Get all bank holiday dates as Date objects for a specific year
 */
export function getBankHolidayDatesForYear(year: number): Date[] {
    const holidays = getBankHolidaysForYear(year);
    return holidays.map((holiday) => new Date(holiday.date));
}

/**
 * Check if a specific date is a bank holiday
 */
export function isBankHoliday(date: Date): boolean {
    const year = date.getFullYear();
    const dateString = date.toISOString().split('T')[0]; // YYYY-MM-DD format

    const holidays = getBankHolidaysForYear(year);
    return holidays.some((holiday) => holiday.date === dateString);
}

/**
 * Get available years for bank holiday data
 */
export function getAvailableYears(): number[] {
    return UK_BANK_HOLIDAYS.map((data) => data.year).sort();
}

/**
 * Get all bank holidays within a date range
 */
export function getBankHolidaysInRange(startDate: Date, endDate: Date): BankHoliday[] {
    const startYear = startDate.getFullYear();
    const endYear = endDate.getFullYear();
    const holidays: BankHoliday[] = [];

    for (let year = startYear; year <= endYear; year++) {
        const yearHolidays = getBankHolidaysForYear(year);
        holidays.push(
            ...yearHolidays.filter((holiday) => {
                const holidayDate = new Date(holiday.date);
                return holidayDate >= startDate && holidayDate <= endDate;
            })
        );
    }

    return holidays;
}
