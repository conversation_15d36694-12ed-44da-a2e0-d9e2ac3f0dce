import { Module } from '@nestjs/common';
import { ConfigModule as NestConfigModule, ConfigService } from '@nestjs/config';
import { validationSchema } from './validation.schema';
import { appConfig } from './app.config';
import { authConfig } from './auth.config';
import { databaseConfig } from './database.config';
import { serviceConfig } from './service.config';
import { multiTenancyConfig } from './multi-tenancy.config';
import { getPublicDataSourceOptions } from './database.config';
import { redisConfig } from './redis.config';

/**
 * Centralized configuration module
 * Loads environment variables from .env files and validates them
 */
@Module({
    imports: [
        NestConfigModule.forRoot({
            isGlobal: true,
            load: [
                appConfig,
                databaseConfig,
                authConfig,
                serviceConfig,
                multiTenancyConfig,
                getPublicDataSourceOptions,
                redisConfig
            ],
            envFilePath: [
                `.env.${process.env.NODE_ENV || 'development'}.local`,
                `.env.${process.env.NODE_ENV || 'development'}`,
                '.env.local',
                '.env'
            ],
            validationSchema,
            validationOptions: {
                abortEarly: false,
                allowUnknown: true
            },
            expandVariables: true,
            cache: true
        })
    ],
    providers: [ConfigService],
    exports: [ConfigService]
})
export class ConfigModule {}
