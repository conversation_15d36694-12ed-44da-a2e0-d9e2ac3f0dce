/**
 * Raw authentication environment variables interface
 * This represents the raw, possibly undefined environment variables
 */
export interface RawAuthEnv {
    // NestJS API service Keycloak variables
    KEYCLOAK_SERVER_URL?: string;
    KEYCLOAK_REALM?: string;
    KEYCLOAK_CLIENT_ID?: string;
    KEY<PERSON>OAK_CLIENT_SECRET?: string;

    // Docker Keycloak container variables
    KEYCLOAK_HOST?: string;
    KEYCLOAK_ADMIN?: string;
    KEYCLOAK_ADMIN_PASSWORD?: string;
    KEYCLOAK_HOSTNAME_PORT?: string;
}

/**
 * Authentication configuration interface
 * This represents the fully validated configuration
 */
export interface AuthConfig {
    // NestJS API service Keycloak configuration
    serverUrl: string;
    realm: string;
    clientId: string;
    clientSecret: string;

    // Docker Keycloak container configuration
    host: string;
    admin: string;
    adminPassword: string;
    hostnamePort: number;
}
