/**
 * Raw database environment variables interface
 * This represents the raw, possibly undefined environment variables
 */
export interface RawDatabaseEnv {
    POSTGRES_HOST?: string;
    POSTGRES_PORT?: string;
    POSTGRES_USER?: string;
    POSTGRES_PASSWORD?: string;
    POSTGRES_DB?: string;
    POSTGRES_SSL?: string;
    POSTGRES_MAX_CONNECTIONS?: string;
    POSTGRES_IDLE_TIMEOUT_MS?: string;
    POSTGRES_CONNECTION_TIMEOUT_MS?: string;
}

/**
 * Database configuration interface
 * This represents the fully validated configuration
 */
export interface DatabaseConfig {
    host: string | undefined;
    port: number | undefined;
    username: string | undefined;
    password: string | undefined;
    database: string | undefined;
    ssl: boolean | undefined;
    maxConnections: number | undefined;
    idleTimeoutMs: number | undefined;
    connectionTimeoutMs: number | undefined;
}
