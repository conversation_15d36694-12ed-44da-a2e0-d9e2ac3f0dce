/**
 * Raw service environment variables interface
 * This represents the raw, possibly undefined environment variables
 */
export interface RawServiceEnv {
    CORE_PORT?: string;
    COMMUNICATION_PORT?: string;
    DOCUMENT_ENGINE_PORT?: string;
    AUTH_PORT?: string;
    CASE_MANAGEMENT_PORT?: string;
    TASK_MANAGEMENT_PORT?: string;
    QUOTE_ENGINE_PORT?: string;
}

/**
 * Service configuration interface for microservices
 * This represents the fully validated configuration
 */
export interface ServiceConfig {
    core: {
        port: number;
        prefix: string;
    };
    communication: {
        port: number;
        prefix: string;
    };
    documentEngine: {
        port: number;
        prefix: string;
    };
    auth: {
        port: number;
        prefix: string;
    };
    caseManagement: {
        port: number;
        prefix: string;
    };
    taskManagement: {
        port: number;
        prefix: string;
    };
    quoteEngine: {
        port: number;
        prefix: string;
    };
}
