export interface RateCardFormat {
    id: string;
    name: string;
    description: string;
    structure: 'multi-column' | 'optimus-three-column' | 'simple-table' | 'custom';
    hasNewBuild: boolean;
    hasLtdCompany: boolean;
    hasClientFees: boolean;
    pricingModel: 'single-tier' | 'two-tier' | 'dynamic';
    vatHandling: 'exclusive' | 'inclusive' | 'mixed';
}

export interface RateCardProvider {
    code: string;
    name: string;
    format: string;
    version: string;
    effectiveDate: string;
    priority: number;
    isDefault: boolean;
    metadata?: Record<string, any>;
}

export const RATE_CARD_FORMATS: Record<string, RateCardFormat> = {
    arrow: {
        id: 'arrow',
        name: 'Arrow Conveyancing Format',
        description: 'Multi-column layout with detailed disbursements and LTD company fees',
        structure: 'multi-column',
        hasNewBuild: true,
        hasLtdCompany: true,
        hasClientFees: false,
        pricingModel: 'single-tier',
        vatHandling: 'exclusive'
    },
    optimus: {
        id: 'optimus',
        name: 'Optimus Three-Column Format',
        description: 'Standard three-column layout used by multiple providers',
        structure: 'optimus-three-column',
        hasNewBuild: false,
        hasLtdCompany: false,
        hasClientFees: true,
        pricingModel: 'two-tier',
        vatHandling: 'mixed'
    },
    'optimus-three-column': {
        id: 'optimus-three-column',
        name: 'Optimus Three-Column Format',
        description: 'Standard three-column layout with purchase, sale, and remortgage',
        structure: 'optimus-three-column',
        hasNewBuild: false,
        hasLtdCompany: false,
        hasClientFees: true,
        pricingModel: 'two-tier',
        vatHandling: 'mixed'
    },
    'optimus-two-column': {
        id: 'optimus-two-column',
        name: 'Optimus Two-Column Format',
        description: 'Two-column layout with purchase and sale only (no remortgage)',
        structure: 'optimus-three-column',
        hasNewBuild: false,
        hasLtdCompany: false,
        hasClientFees: true,
        pricingModel: 'two-tier',
        vatHandling: 'mixed'
    },
    'optimus-auction-sale': {
        id: 'optimus-auction-sale',
        name: 'Optimus Auction Sale Format',
        description: 'Auction sale format with 4-tier pricing structure',
        structure: 'multi-column',
        hasNewBuild: false,
        hasLtdCompany: false,
        hasClientFees: true,
        pricingModel: 'dynamic',
        vatHandling: 'exclusive'
    },
    'pepper-remortgage-only': {
        id: 'pepper-remortgage-only',
        name: 'Pepper Remortgage Only Format',
        description: 'Remortgage-only format with all-inclusive pricing',
        structure: 'simple-table',
        hasNewBuild: false,
        hasLtdCompany: false,
        hasClientFees: true,
        pricingModel: 'single-tier',
        vatHandling: 'mixed'
    },
    'molo-multi-representation': {
        id: 'molo-multi-representation',
        name: 'Molo Multi-Representation Format',
        description: 'Complex format with multiple representation types and fee structures',
        structure: 'multi-column',
        hasNewBuild: false,
        hasLtdCompany: true,
        hasClientFees: true,
        pricingModel: 'dynamic',
        vatHandling: 'mixed'
    },
    simple: {
        id: 'simple',
        name: 'Simple Table Format',
        description: 'Basic table structure for straightforward rate cards',
        structure: 'simple-table',
        hasNewBuild: false,
        hasLtdCompany: false,
        hasClientFees: false,
        pricingModel: 'single-tier',
        vatHandling: 'exclusive'
    }
};

export const RATE_CARD_PROVIDERS: RateCardProvider[] = [
    {
        code: 'arrow',
        name: 'Arrow',
        format: 'arrow',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 1,
        isDefault: true
    },
    {
        code: 'charles_cameron',
        name: 'Charles Cameron',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 2,
        isDefault: false
    },
    {
        code: 'ekeeper',
        name: 'eKeeper',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 3,
        isDefault: false
    },
    {
        code: 'fluent',
        name: 'Fluent',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 4,
        isDefault: false
    },
    {
        code: 'fort_advice',
        name: 'Fort Advice Bureau',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 5,
        isDefault: false
    },
    {
        code: 'gazeal',
        name: 'Gazeal',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 6,
        isDefault: false
    },
    {
        code: 'haysto',
        name: 'Haysto',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 7,
        isDefault: false
    },
    {
        code: 'independent',
        name: 'Independent',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 8,
        isDefault: false
    },
    {
        code: 'john_charcol',
        name: 'John Charcol',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 9,
        isDefault: false
    },
    {
        code: 'keyclub',
        name: 'Key Club',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 10,
        isDefault: false
    },
    {
        code: 'landc',
        name: 'L&C',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 11,
        isDefault: false
    },
    {
        code: 'leas',
        name: 'LEAS',
        format: 'optimus-two-column',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 12,
        isDefault: false
    },
    {
        code: 'mojo',
        name: 'Mojo',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 13,
        isDefault: false
    },
    {
        code: 'molo',
        name: 'Molo',
        format: 'molo-multi-representation',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 14,
        isDefault: false
    },
    {
        code: 'msm',
        name: 'MSM',
        format: 'optimus',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 15,
        isDefault: false
    },
    {
        code: 'optimus_bid',
        name: 'Optimus Bid',
        format: 'optimus-auction-sale',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 16,
        isDefault: false
    },
    {
        code: 'pepper',
        name: 'Pepper',
        format: 'pepper-remortgage-only',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 17,
        isDefault: false
    },
    {
        code: 'rayner',
        name: 'Rayner',
        format: 'optimus-three-column',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 18,
        isDefault: false
    },
    {
        code: 'remax',
        name: 'Remax',
        format: 'optimus-two-column',
        version: '1.0',
        effectiveDate: '2024-01-01',
        priority: 19,
        isDefault: false
    }
];

export const RATE_CARD_CONDITIONS = {
    // Property conditions
    is_new_build: 'New Build Property',
    leasehold: 'Leasehold Property',
    freehold: 'Freehold Property',
    unregistered: 'Unregistered Property',

    // Transaction conditions
    buying_with_mortgage: 'Buying with Mortgage',
    gifted_deposit: 'Gifted Deposit',
    shared_ownership: 'Shared Ownership',
    help_to_buy_isa: 'Help to Buy ISA',
    help_to_buy_equity: 'Help to Buy Equity Loan',
    right_to_buy: 'Right to Buy',
    buy_to_let: 'Buy to Let',
    concessionary: 'Concessionary Purchase',
    mortgage_redemption: 'Mortgage Redemption',

    // Special transaction types
    auction_sale: 'Auction Sale',
    pepper_remortgage: 'Pepper Remortgage',

    // Molo representation types
    sep_rep_individual: 'Separate Representation Individual',
    sep_rep_ltd_company: 'Separate Representation Ltd Company',
    dual_rep_individual: 'Dual Representation Individual',
    dual_rep_ltd_company: 'Dual Representation Ltd Company',
    independent_legal_advice: 'Independent Legal Advice',

    // Property types
    hmo: 'House in Multiple Occupation (HMO)',

    // Client conditions
    first_time_buyer: 'First Time Buyer',
    cash_buyer: 'Cash Buyer',
    company_or_trust: 'Company or Trust Purchase',
    property_in_wales: 'Property in Wales',
    property_in_scotland: 'Property in Scotland',
    property_in_ni: 'Property in Northern Ireland'
};

export const RATE_CARD_FEE_CATEGORIES = {
    legal_fee: 'Legal Fee (Residential Transaction)',
    disbursements: 'Disbursements',
    conditional_fees: 'Conditional Fees',
    supplements: 'Common Supplements',
    tax: 'Tax Calculations',
    admin_fees: 'Administrative Fees'
};

export const RATE_CARD_TRANSACTION_TYPES = {
    buy: 'Purchase',
    sell: 'Sale',
    remortgage: 'Remortgage',
    buy_sell: 'Buy and Sell',
    transfer_of_equity: 'Transfer of Equity'
};

export function getRateCardFormat(formatId: string): RateCardFormat | null {
    return RATE_CARD_FORMATS[formatId] || null;
}

export function getRateCardProvider(providerCode: string): RateCardProvider | null {
    return RATE_CARD_PROVIDERS.find((p) => p.code === providerCode) || null;
}

export function getDefaultRateCardProvider(): RateCardProvider | null {
    return RATE_CARD_PROVIDERS.find((p) => p.isDefault) || null;
}

export function getRateCardProvidersByFormat(formatId: string): RateCardProvider[] {
    return RATE_CARD_PROVIDERS.filter((p) => p.format === formatId);
}

export function validateRateCardCondition(condition: string): boolean {
    return condition in RATE_CARD_CONDITIONS;
}

export function getRateCardConditionLabel(condition: string): string {
    return RATE_CARD_CONDITIONS[condition as keyof typeof RATE_CARD_CONDITIONS] || condition;
}
