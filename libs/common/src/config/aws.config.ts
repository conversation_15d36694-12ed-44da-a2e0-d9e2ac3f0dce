import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Configuration service for AWS services
 * Provides access to AWS configuration parameters
 */
@Injectable()
export class AwsConfigService {
    constructor(private configService: ConfigService) {}

    /**
     * Get AWS region
     */
    get region(): string {
        return this.configService.get<string>('AWS_REGION', 'us-east-1');
    }

    /**
     * Get S3 bucket name for document storage
     */
    get documentBucketName(): string {
        return this.configService.get<string>('AWS_DOCUMENT_BUCKET_NAME', 'tk-lpm-documents');
    }

    /**
     * Get AWS access key ID
     */
    get accessKeyId(): string {
        return this.configService.get<string>('AWS_ACCESS_KEY_ID', '');
    }

    /**
     * Get AWS secret access key
     */
    get secretAccessKey(): string {
        return this.configService.get<string>('AWS_SECRET_ACCESS_KEY', '');
    }

    /**
     * Get S3 endpoint URL (for local development with MinIO)
     */
    get s3Endpoint(): string | undefined {
        return this.configService.get<string>('AWS_S3_ENDPOINT');
    }

    /**
     * Get S3 force path style option
     */
    get s3ForcePathStyle(): boolean {
        return this.configService.get<boolean>('AWS_S3_FORCE_PATH_STYLE', false);
    }

    /**
     * Check if S3 should use SSL
     */
    get s3UseSSL(): boolean {
        return this.configService.get<boolean>('AWS_S3_USE_SSL', true);
    }

    /**
     * Get presigned URL expiration time in seconds
     */
    get presignedUrlExpirationSeconds(): number {
        return this.configService.get<number>('AWS_S3_PRESIGNED_URL_EXPIRATION', 3600);
    }
}
