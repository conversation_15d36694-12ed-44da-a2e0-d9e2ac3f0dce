import { registerAs } from '@nestjs/config';
import { AppConfig, RawAppEnv } from './interfaces';

/**
 * Validates that all required environment variables are present
 * Throws an error if any required variable is missing
 * @param env Raw environment variables
 */
function validateAppEnv(env: RawAppEnv): void {
    // Define required environment variables
    const requiredVars: Array<keyof RawAppEnv> = ['CORE_PORT'];

    // Check each required variable
    for (const key of requiredVars) {
        if (env[key] === undefined) {
            throw new Error(`Missing required environment variable: ${key}`);
        }
    }
}

/**
 * Application configuration
 * Loads raw environment variables, validates them, and returns a fully validated config
 */
export const appConfig = registerAs('app', (): AppConfig => {
    // Load raw environment variables
    const rawEnv: RawAppEnv = {
        NODE_ENV: process.env.NODE_ENV,
        API_GLOBAL_PREFIX: process.env.API_GLOBAL_PREFIX,
        API_RATE_LIMIT_WINDOW_MS: process.env.API_RATE_LIMIT_WINDOW_MS,
        API_RATE_LIMIT_MAX: process.env.API_RATE_LIMIT_MAX,
        CORS_ENABLED: process.env.CORS_ENABLED,
        CORS_ORIGIN: process.env.CORS_ORIGIN,
        LOG_LEVEL: process.env.LOG_LEVEL,
        CORE_PORT: process.env.CORE_PORT
    };

    // Validate environment variables
    validateAppEnv(rawEnv);

    // After validation, we can safely assert that required variables are defined
    const nodeEnv = rawEnv.NODE_ENV || 'development';

    // Return fully validated config
    return {
        nodeEnv,
        isProduction: nodeEnv === 'production',
        isDevelopment: nodeEnv === 'development',
        isTest: nodeEnv === 'test',

        // API configuration
        apiGlobalPrefix: rawEnv.API_GLOBAL_PREFIX || 'api',
        rateLimitWindowMs: parseInt(rawEnv.API_RATE_LIMIT_WINDOW_MS || '60000', 10),
        rateLimitMax: parseInt(rawEnv.API_RATE_LIMIT_MAX || '100', 10),

        // CORS configuration
        corsEnabled: rawEnv.CORS_ENABLED === 'true',
        corsOrigin: rawEnv.CORS_ORIGIN || '*',

        // Logging
        logLevel: rawEnv.LOG_LEVEL || 'info',

        // Port - already validated as required
        port: parseInt(rawEnv.CORE_PORT!, 10)
    };
});
