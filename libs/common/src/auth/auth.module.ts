import { Module } from '@nestjs/common';

import { CommonModule } from '../common.module';
import { RepositoriesModule } from '../repositories/repositories.module';

import { JwtGuard } from '../guards/jwt.guard';

/**
 * Shared AuthModule that can be imported by other services
 */
@Module({
    imports: [CommonModule, RepositoriesModule],
    providers: [JwtGuard],
    exports: [JwtGuard]
})
export class AuthModule {}
