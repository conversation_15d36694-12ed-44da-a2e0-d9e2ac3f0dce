import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CacheModule } from '@nestjs/cache-manager';
import { redisStore } from 'cache-manager-redis-store';
import { CacheService } from './cache.service';
import { RedisConfig } from '../config/interfaces/redis.config.interface';
import { ConfigModule } from '../config/config.module';

/**
 * Global Redis cache module
 * Provides a centralized cache manager for all services
 */
@Global()
@Module({
    imports: [
        ConfigModule, // Import our custom ConfigModule
        CacheModule.registerAsync({
            isGlobal: true,
            imports: [ConfigModule],
            useFactory: (configService: ConfigService) => {
                // Get the validated Redis config - we know it exists because of validation
                const redisConfig = configService.get<RedisConfig>('redis')!;

                return {
                    store: redisStore,
                    host: redisConfig.host,
                    port: redisConfig.port,
                    password: redisConfig.password,
                    db: redisConfig.db,
                    ttl: 60 * 60 * 24, // 24 hours default TTL
                    tls: redisConfig.tls ? {} : undefined
                };
            },
            inject: [ConfigService]
        })
    ],
    providers: [CacheService],
    exports: [CacheService]
})
export class RedisModule {}
