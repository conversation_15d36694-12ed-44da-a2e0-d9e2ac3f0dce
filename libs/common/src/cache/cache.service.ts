import { Injectable, Inject } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import * as zlib from 'zlib';
import { instance as logger } from '@app/common/utils';

/**
 * Service for handling cache operations
 */
@Injectable()
export class CacheService {
    private static readonly ZLIB_HEADER = Buffer.from('ZLIB:');
    private readonly logger = logger;
    constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

    /**
     * Get a value from cache (with safe decompression)
     * @param key The cache key
     */
    async get<T>(key: string): Promise<T | undefined> {
        try {
            const raw = await this.cacheManager.get<Buffer>(key);
            if (!raw) return undefined;
            // Check for ZLIB header
            if (Buffer.isBuffer(raw) && raw.slice(0, 5).equals(CacheService.ZLIB_HEADER)) {
                try {
                    const compressed = raw.slice(5);
                    const json = zlib.inflateSync(compressed).toString();
                    return JSON.parse(json) as T;
                } catch (err) {
                    this.logger.warn(
                        `[CACHE][ERROR][Decompression] Failed to decompress or parse cache key=${key}: ${err?.message}`,
                        { error: err, key }
                    );
                    return undefined;
                }
            } else {
                // Not compressed with ZLIB header; try to parse as JSON, else return as-is
                try {
                    if (typeof raw === 'string' || Buffer.isBuffer(raw)) {
                        const str = Buffer.isBuffer(raw) ? raw.toString() : raw;
                        return JSON.parse(str) as T;
                    }
                    return raw as unknown as T;
                } catch (err) {
                    this.logger.warn(
                        `[CACHE][WARN][Get] Cache key=${key} is not compressed and not valid JSON. Returning raw value.`,
                        { error: err, key }
                    );
                    return raw as unknown as T;
                }
            }
        } catch (err) {
            this.logger.warn(
                `[CACHE][ERROR][Get] Failed to get cache key=${key}: ${err?.message}`,
                { error: err, key }
            );
            return undefined;
        }
    }

    /**
     * Set a value in cache (with safe compression and correct TTL)
     * @param key The cache key
     * @param value The value to cache
     * @param ttl Time to live in seconds
     */
    async set(key: string, value: any, ttl?: number): Promise<void> {
        try {
            const json = JSON.stringify(value);
            const compressed = zlib.deflateSync(json);
            const withHeader = Buffer.concat([CacheService.ZLIB_HEADER, compressed]);
            await this.cacheManager.set(key, withHeader, ttl);
        } catch (err) {
            this.logger.warn(
                `[CACHE][ERROR][Compression] Failed to compress/set cache key=${key}: ${err?.message}`,
                { error: err, key, ttl }
            );
        }
    }

    /**
     * Delete a value from cache
     * @param key The cache key
     */
    async del(key: string): Promise<void> {
        await this.cacheManager.del(key);
    }

    /**
     * Delete multiple values from cache
     * @param keys Array of cache keys
     */
    async delMultiple(keys: string[]): Promise<void> {
        await Promise.all(keys.map((key) => this.cacheManager.del(key)));
    }

    /**
     * Reset the cache (clear all entries)
     * Uses the cache manager's clear method to reset cache to fresh state
     */
    async reset(): Promise<void> {
        await this.cacheManager.clear();
    }

    /**
     * Get all cache keys from all stores
     * Note: This iterates through all stores and may be expensive
     */
    async getKeys(): Promise<string[]> {
        const allKeys: string[] = [];
        if (Array.isArray((this.cacheManager as any).stores)) {
            const stores = (this.cacheManager as any).stores;
            const keyPromises = stores.map((store: any) => {
                if (typeof store.keys === 'function') {
                    // For Redis and similar stores
                    return store.keys('*').catch((err: any) => {
                        this.logger.warn('Could not retrieve keys from store:', err);
                        return [];
                    });
                }
                return Promise.resolve([]);
            });
            const results = await Promise.all(keyPromises);
            results.forEach((keys) => allKeys.push(...keys));
        } else if (typeof (this.cacheManager as any).keys === 'function') {
            // Single store with keys() method
            try {
                const keys = await (this.cacheManager as any).keys('*');
                allKeys.push(...keys);
            } catch (err) {
                this.logger.warn('Could not retrieve keys from store:', err);
            }
        }
        // Remove duplicates
        return [...new Set(allKeys)];
    }

    /**
     * Check if a key exists in cache
     * @param key The cache key to check
     */
    async exists(key: string): Promise<boolean> {
        const value = await this.cacheManager.get(key);
        return value !== undefined && value !== null;
    }

    /**
     * Get cache size (number of entries) if supported
     */
    async size(): Promise<number> {
        try {
            const keys = await this.getKeys();
            return keys.length;
        } catch (error) {
            throw new Error('Cache size operation not supported: ' + error.message);
        }
    }
}
