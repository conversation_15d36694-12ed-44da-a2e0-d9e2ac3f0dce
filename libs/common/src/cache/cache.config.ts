/**
 * Cache configuration
 * Defines TTL values and cache key prefixes
 */

import { registerAs } from '@nestjs/config';

// Load version from env, default to 'v2'
const CACHE_KEY_VERSION_ENV = process.env.CACHE_KEY_VERSION || 'v2';
export const CACHE_KEY_VERSION = CACHE_KEY_VERSION_ENV;

export enum CacheKeyType {
    CASE_DETAILS = 'CASE_DETAILS',
    TASK_DETAILS = 'TASK_DETAILS'
}

// Cache key helpers (now require userId)
export const CACHE_KEYS = {
    CASE_BY_ID: (tenantId: string, caseId: string, userId: string) =>
        `${CACHE_KEY_VERSION}:case:${tenantId}:${caseId}:user:${userId}`,
    CASE_BY_NUMBER: (tenantId: string, caseNumber: string, userId: string) =>
        `${CACHE_KEY_VERSION}:case:${tenantId}:number:${caseNumber}:user:${userId}`,
    TASK_BY_ID: (tenantId: string, taskId: string, userId: string) =>
        `${CACHE_KEY_VERSION}:task:${tenantId}:${taskId}:user:${userId}`
};

/**
 * Validates that all required cache environment variables are present and valid
 * Throws an error if any required variable is missing or invalid
 * @param env Raw environment variables
 */
function validateCacheEnv(env: Record<string, string | undefined>): void {
    const requiredVars = ['CACHE_TTL_CASE_DETAILS', 'CACHE_TTL_TASK_DETAILS', 'CACHE_KEY_VERSION'];
    for (const key of requiredVars) {
        if (env[key] === undefined) {
            throw new Error(`Missing required environment variable: ${key}`);
        }
        if (
            (key === 'CACHE_TTL_CASE_DETAILS' || key === 'CACHE_TTL_TASK_DETAILS') &&
            isNaN(Number(env[key]))
        ) {
            throw new Error(`Invalid number for ${key}: ${env[key]}`);
        }
    }
}

/**
 * Cache configuration
 * Loads raw environment variables, validates them, and returns a fully validated config
 */
export const cacheConfig = registerAs('cache', () => {
    const rawEnv = {
        CACHE_TTL_CASE_DETAILS: process.env.CACHE_TTL_CASE_DETAILS,
        CACHE_TTL_TASK_DETAILS: process.env.CACHE_TTL_TASK_DETAILS,
        CACHE_KEY_VERSION: process.env.CACHE_KEY_VERSION || 'v2'
    };
    validateCacheEnv(rawEnv);
    return {
        ttl: {
            caseDetails: parseInt(rawEnv.CACHE_TTL_CASE_DETAILS!, 10),
            taskDetails: parseInt(rawEnv.CACHE_TTL_TASK_DETAILS!, 10)
        },
        version: rawEnv.CACHE_KEY_VERSION
    };
});
