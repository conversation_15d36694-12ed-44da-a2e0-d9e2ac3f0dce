import { Test, TestingModule } from '@nestjs/testing';
import { TenantContextService } from '../tenant-context.service';
import { REQUEST } from '@nestjs/core';

describe('TenantContextService', () => {
    let service: TenantContextService;
    const mockRequest = {};

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TenantContextService,
                {
                    provide: REQUEST,
                    useValue: mockRequest
                }
            ]
        }).compile();

        service = await module.resolve<TenantContextService>(TenantContextService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    it('should default to public when tenant is not set', () => {
        expect(service.getTenantId()).toBe('public');
        expect(service.getTenantMetadata()).toBeNull();
    });

    it('should set and get tenant information', () => {
        const tenantId = 'test-tenant';
        const sanitizedTenantId = 'test_tenant';
        const tenantMetadata = {
            id: tenantId,
            realm: 'test-realm',
            displayName: 'Test Tenant',
            enabled: true
        };

        service.setTenant(tenantId, tenantMetadata);

        expect(service.hasTenant()).toBe(true);
        expect(service.getTenantId()).toBe(sanitizedTenantId);
        expect(service.getTenantMetadata()).toEqual(tenantMetadata);
        expect(service.getTenantSchema()).toBe(`tenant_${sanitizedTenantId}`);
    });
});
