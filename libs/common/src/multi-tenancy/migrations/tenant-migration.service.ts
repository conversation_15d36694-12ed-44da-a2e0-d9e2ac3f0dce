import { Injectable, Logger } from '@nestjs/common';
import { getSchemaNameFromTenantId } from '../sanitize-tenant-id.util';
import { TenantConnectionService } from '../tenant-connection.service';

/**
 * Service for managing tenant schema migrations
 */
@Injectable()
export class TenantMigrationService {
    private readonly logger = new Logger(TenantMigrationService.name);

    constructor(private readonly tenantConnectionService: TenantConnectionService) {}

    /**
     * Creates a schema for a tenant
     */
    async createTenantSchema(tenantId: string): Promise<boolean> {
        const schemaName = getSchemaNameFromTenantId(tenantId);
        const publicDs = this.tenantConnectionService.getPublicDataSource();

        try {
            const exists = await publicDs.query(
                `SELECT EXISTS (SELECT 1 FROM pg_namespace WHERE nspname = $1)`,
                [schemaName]
            );

            if (exists[0]?.exists) {
                this.logger.log(`Schema ${schemaName} already exists for tenant ${tenantId}`);
                return false;
            }

            await publicDs.query(`CREATE SCHEMA IF NOT EXISTS "${schemaName}"`);
            this.logger.log(`Created schema ${schemaName} for tenant ${tenantId}`);
            return true;
        } catch (error) {
            this.logger.error(
                `Error creating schema for tenant ${tenantId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Drops a tenant schema
     */
    async dropTenantSchema(tenantId: string, cascade = false): Promise<boolean> {
        const schemaName = getSchemaNameFromTenantId(tenantId);
        const publicDs = this.tenantConnectionService.getPublicDataSource();

        try {
            const exists = await publicDs.query(
                `SELECT EXISTS (SELECT 1 FROM pg_namespace WHERE nspname = $1)`,
                [schemaName]
            );

            if (!exists[0]?.exists) {
                this.logger.log(`Schema ${schemaName} does not exist for tenant ${tenantId}`);
                return false;
            }

            await this.tenantConnectionService.closeTenantConnection(tenantId);

            const cascadeOpt = cascade ? 'CASCADE' : '';
            await publicDs.query(`DROP SCHEMA IF EXISTS "${schemaName}" ${cascadeOpt}`);
            this.logger.log(`Dropped schema ${schemaName} for tenant ${tenantId}`);
            return true;
        } catch (error) {
            this.logger.error(
                `Error dropping schema for tenant ${tenantId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Runs migrations for a tenant
     */
    async runTenantMigrations(tenantId: string, migrationDir: string): Promise<void> {
        try {
            await this.createTenantSchema(tenantId);
            await this.tenantConnectionService.runTenantMigrations(tenantId, migrationDir);
            this.logger.log(`Ran migrations for tenant ${tenantId}`);
        } catch (error) {
            this.logger.error(
                `Error running migrations for tenant ${tenantId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Lists all tenant schemas
     */
    async listTenantSchemas(): Promise<string[]> {
        const publicDs = this.tenantConnectionService.getPublicDataSource();
        try {
            const result = await publicDs.query(
                `SELECT nspname FROM pg_namespace WHERE nspname LIKE 'tenant_%'`
            );
            return result.map((r) => r.nspname);
        } catch (error) {
            this.logger.error(`Error listing tenant schemas: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Runs migrations for all tenants
     */
    async runMigrationsForAllTenants(migrationDir: string): Promise<void> {
        try {
            const schemas = await this.listTenantSchemas();
            for (const schema of schemas) {
                const tenantId = schema.replace('tenant_', '');
                await this.runTenantMigrations(tenantId, migrationDir);
            }
            this.logger.log(`Ran migrations for all ${schemas.length} tenants`);
        } catch (error) {
            this.logger.error(
                `Error running migrations for all tenants: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }
}
