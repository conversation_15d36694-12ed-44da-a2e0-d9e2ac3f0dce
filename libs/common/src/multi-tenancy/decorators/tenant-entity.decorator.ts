import { SetMetadata } from '@nestjs/common';

/**
 * Metadata key for tenant entities
 */
export const TENANT_ENTITY_KEY = 'isTenantEntity';

/**
 * Metadata key for public entities
 */
export const PUBLIC_ENTITY_KEY = 'isPublicEntity';

/**
 * Decorator to mark an entity as tenant-specific
 * Tenant entities are stored in tenant-specific schemas
 */
export const TenantEntity = () => SetMetadata(TENANT_ENTITY_KEY, true);

/**
 * Decorator to mark an entity as public
 * Public entities are stored in the public schema
 */
export const PublicEntity = () => SetMetadata(PUBLIC_ENTITY_KEY, true);

/**
 * Utility function to check if an entity is a public entity
 * @param entity The entity class
 * @returns True if the entity is a public entity, false otherwise
 */
export function isPublicEntity(entity: Function): boolean {
    return Reflect.getMetadata(PUBLIC_ENTITY_KEY, entity) === true;
}
