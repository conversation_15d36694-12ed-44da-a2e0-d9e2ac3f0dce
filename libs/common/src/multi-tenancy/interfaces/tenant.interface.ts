/**
 * Interface for tenant metadata
 */
export interface TenantMetadata {
    /**
     * Unique identifier for the tenant
     */
    id: string;

    /**
     * Realm name for the tenant (used in Keycloak)
     */
    realm: string;

    /**
     * Display name for the tenant
     */
    displayName: string;

    /**
     * Whether the tenant is enabled
     */
    enabled: boolean;

    /**
     * Additional tenant properties
     */
    [key: string]: any;
}

/**
 * Options for the multi-tenancy module
 */
export interface MultiTenancyOptions {
    /**
     * Maximum number of tenant connections to keep in the cache
     * @default 50
     */
    maxConnections?: number;

    /**
     * Time to live for tenant connections in milliseconds
     * @default 3600000 (1 hour)
     */
    connectionTtl?: number;

    /**
     * Whether to automatically create schemas for tenants
     * @default true
     */
    autoCreateSchema?: boolean;

    /**
     * Whether to automatically run migrations for tenant schemas
     * @default false
     */
    autoRunMigrations?: boolean;

    /**
     * Directory containing migration files
     * Only used if autoRunMigrations is true
     */
    migrationsDir?: string;
}
