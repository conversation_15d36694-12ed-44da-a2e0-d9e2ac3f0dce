import { Injectable, Scope, Inject } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { getSchemaNameFromTenantId, sanitizeTenantId } from './sanitize-tenant-id.util';

/**
 * Interface representing tenant metadata
 */
export interface TenantMetadata {
    id: string;
    realm: string;
    displayName: string;
    enabled: boolean;
    [key: string]: any;
}

/**
 * Request-scoped service that stores the current tenant context
 * This service is injected into other services/repositories to access tenant information
 */
@Injectable({ scope: Scope.REQUEST })
export class TenantContextService {
    private tenantId: string | null = null;
    private tenantMetadata: TenantMetadata | null = null;

    constructor(@Inject(REQUEST) private readonly request: Request) {}

    /**
     * Sets the current tenant ID and metadata
     * @param tenantId The tenant ID
     * @param metadata Additional tenant metadata
     */
    setTenant(tenantId: string, metadata: TenantMetadata): void {
        // Sanitize the tenant ID to ensure it's valid for PostgreSQL schema names
        this.tenantId = sanitizeTenantId(tenantId);
        this.tenantMetadata = metadata;
    }

    /**
     * Gets the current tenant ID
     * @returns The current tenant ID
     */
    getTenantId(): string {
        if (!this.tenantId) {
            return 'public';
        }
        return this.tenantId;
    }

    /**
     * Gets the current tenant metadata
     * @returns The current tenant metadata
     */
    getTenantMetadata(): TenantMetadata | null {
        if (!this.tenantMetadata) {
            return null;
        }
        return this.tenantMetadata;
    }

    /**
     * Checks if a tenant context is set
     * @returns True if tenant context is set, false otherwise
     */
    hasTenant(): boolean {
        return !!this.tenantId && !!this.tenantMetadata;
    }

    /**
     * Gets the schema name for the current tenant
     * @returns The schema name for the current tenant
     */
    getTenantSchema(): string {
        if (!this.getTenantId()) {
            throw new Error('No tenant ID set');
        }

        return getSchemaNameFromTenantId(this.getTenantId());
    }
}
