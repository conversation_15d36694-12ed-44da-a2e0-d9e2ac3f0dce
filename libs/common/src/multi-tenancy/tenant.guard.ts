import {
    Injectable,
    CanActivate,
    ExecutionContext,
    Logger,
    UnauthorizedException,
    BadRequestException,
    NotFoundException
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { TenantContextService } from './tenant-context.service';
import { TenantConnectionService } from './tenant-connection.service';

/**
 * Interface for the tenant entity in the database
 */
interface TenantEntity {
    id: string;
    realm: string;
    displayName: string;
    enabled: boolean;
    [key: string]: any;
}

/**
 * Metadata key for public routes that don't require tenant identification
 */
export const IS_PUBLIC_TENANT_KEY = 'isPublicTenant';

/**
 * Guard that extracts and validates the tenant ID from the request
 * Must be used after authentication guards to ensure the user is authenticated
 */
@Injectable()
export class TenantGuard implements CanActivate {
    private readonly logger = new Logger(TenantGuard.name);

    constructor(
        private readonly reflector: Reflector,
        private readonly tenantContextService: TenantContextService,
        private readonly tenantConnectionService: TenantConnectionService
    ) {}

    /**
     * Validates the tenant ID in the request
     * @param context The execution context
     * @returns True if the tenant is valid, false otherwise
     */
    async canActivate(context: ExecutionContext): Promise<boolean> {
        // Check if the endpoint is marked as public (no tenant required)
        const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_TENANT_KEY, [
            context.getHandler(),
            context.getClass()
        ]);

        if (isPublic) {
            return true;
        }

        const request = context.switchToHttp().getRequest();

        // Extract tenant ID from header first, then fallback to cookie
        const headerTenantId = request.headers['x-tenant-id'];
        const cookieTenantId = request.cookies?.selected_tenant_id;
        const tenantId = headerTenantId || cookieTenantId;

        this.logger.debug(
            `Tenant Guard - Header tenant ID: ${headerTenantId}, Cookie tenant ID: ${cookieTenantId}, Selected: ${tenantId}`
        );

        if (!tenantId) {
            this.logger.warn('No tenant ID provided in request headers or cookies');
            throw new BadRequestException('Tenant ID is required');
        }

        try {
            // Sanitize tenant ID to prevent SQL injection
            // Note: The sanitization is now handled by the TenantContextService
            // Verify tenant exists in the database
            const tenant = await this.findTenantById(tenantId);

            if (!tenant) {
                this.logger.warn(`Tenant with ID ${tenantId} not found`);
                throw new NotFoundException(`Tenant with ID ${tenantId} not found`);
            }

            // Set the tenant context
            this.tenantContextService.setTenant(tenant.id, {
                id: tenant.id,
                realm: tenant.realm,
                displayName: tenant.displayName || tenant.realm,
                enabled: tenant.enabled
            });

            // Add tenant to request for easy access in controllers
            request.tenant = tenant;

            this.logger.debug(`Tenant context set to ${tenant.id} (${tenant.realm})`);
            return true;
        } catch (error) {
            if (
                error instanceof BadRequestException ||
                error instanceof NotFoundException ||
                error instanceof UnauthorizedException
            ) {
                throw error;
            }

            this.logger.error(`Error validating tenant: ${error.message}`, error.stack);
            throw new BadRequestException('Invalid tenant ID');
        }
    }

    /**
     * Finds a tenant by ID in the database
     * @param tenantId The tenant ID
     * @returns The tenant entity or null if not found
     */
    private async findTenantById(tenantId: string): Promise<TenantEntity | null> {
        const ds = this.tenantConnectionService.getPublicDataSource();
        const rows = await ds.query('SELECT * FROM public.tenants WHERE id = $1', [tenantId]);
        return rows[0] || null;
    }
}
