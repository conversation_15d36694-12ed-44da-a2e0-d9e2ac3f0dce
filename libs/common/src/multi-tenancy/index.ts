// Export all multi-tenancy components
export * from './tenant-connection.service';
export * from './tenant.guard';
export * from './base-tenant.repository';
export * from './sanitize-tenant-id.util';
export * from './decorators/tenant-entity.decorator';
export * from './migrations/tenant-migration.service';

// Re-export TenantContextService but exclude TenantMetadata to avoid ambiguity
import { TenantContextService } from './tenant-context.service';
export { TenantContextService };

// Export from interfaces with explicit naming
export * from './interfaces/tenant.interface';
