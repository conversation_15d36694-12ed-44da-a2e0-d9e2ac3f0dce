import { Injectable, Logger, OnModule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { getSchemaNameFromTenantId } from './sanitize-tenant-id.util';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import { ALL_ENTITIES, PUBLIC_ENTITIES, TENANT_ENTITIES } from '../typeorm/entities';
import { PUBLIC_MIGRATIONS, TENANT_MIGRATIONS } from '../typeorm/migrations';

/**
 * Service that manages DataSource instances for each tenant
 */
@Injectable()
export class TenantConnectionService implements OnModuleInit, OnModuleDestroy {
    private readonly logger = new Logger(TenantConnectionService.name);
    private readonly defaultDataSourceOptions: PostgresConnectionOptions;
    private publicDataSource: DataSource | null = null;
    private isInitialized = false;
    private readonly tenantedEntities: Function[] = [];
    private readonly publicEntities: Function[] = [];
    private readonly tenantConnections = new Map<string, DataSource>();

    constructor(private readonly configService: ConfigService) {
        // Create default DataSource options with hardcoded values to avoid issues
        this.defaultDataSourceOptions = {
            type: 'postgres',
            host: process.env.POSTGRES_HOST,
            port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
            username: process.env.POSTGRES_USER,
            password: String(process.env.POSTGRES_PASSWORD),
            database: process.env.POSTGRES_DB,
            entities: ALL_ENTITIES,
            synchronize: false,
            ssl: false,
            logging: true
        };

        // Create public DataSource with hardcoded options to ensure it works
        this.publicDataSource = new DataSource({
            ...this.defaultDataSourceOptions,
            name: 'public-connection',
            schema: 'public',
            entities: PUBLIC_ENTITIES,
            migrations: PUBLIC_MIGRATIONS
        });
    }

    /**
     * Initialize the service
     */
    async onModuleInit() {
        if (this.isInitialized) {
            return;
        }

        try {
            if (!this.publicDataSource) {
                throw new Error('Public DataSource not properly initialized');
            }

            if (!this.publicDataSource.isInitialized) {
                await this.publicDataSource.initialize();
                this.logger.log('Public DataSource initialized successfully');
            }

            this.isInitialized = true;
        } catch (error) {
            this.logger.error('Failed to initialize public data source:', error);
            throw error;
        }
    }

    /**
     * Clean up resources when the module is destroyed
     */
    async onModuleDestroy() {
        try {
            // Close public connection
            if (this.publicDataSource?.isInitialized) {
                await this.publicDataSource.destroy();
                this.logger.log('Public DataSource closed');
            }

            // Close all tenant connections
            for (const [tenantId, connection] of this.tenantConnections.entries()) {
                if (connection.isInitialized) {
                    await connection.destroy();
                    this.logger.log(`Closed connection for tenant ${tenantId}`);
                }
            }
            this.tenantConnections.clear();
        } catch (error) {
            this.logger.error('Error during cleanup:', error);
            throw error;
        }
    }

    /**
     * Register entities that should be available in tenant schemas
     * @param entities Array of entity classes
     */
    registerTenantedEntities(entities: Function[]) {
        this.tenantedEntities.push(...entities);
    }

    /**
     * Register entities that should be available in the public schema
     * @param entities Array of entity classes
     */
    registerPublicEntities(entities: Function[]) {
        this.publicEntities.push(...entities);

        // If the public DataSource is already initialized, we need to close and reinitialize it
        if (this.publicDataSource?.isInitialized) {
            this.publicDataSource.destroy().then(() => {
                this.publicDataSource?.initialize();
            });
        }
    }

    /**
     * Gets the DataSource for the public schema
     * @returns The public DataSource
     */
    getPublicDataSource(): DataSource {
        if (!this.publicDataSource?.isInitialized) {
            throw new Error('Public DataSource is not initialized');
        }

        return this.publicDataSource;
    }

    /**
     * Gets the DataSource for a specific tenant
     * @param tenantId The tenant ID
     * @returns The tenant-specific DataSource
     */
    async getTenantDataSource(tenantId: string): Promise<DataSource> {
        if (!tenantId) {
            this.logger.error('Tenant ID is required');
            throw new Error('Tenant ID is required');
        }

        // Check existing connection
        const existingConnection = this.tenantConnections.get(tenantId);
        if (existingConnection?.isInitialized) {
            return existingConnection;
        }

        // Create new connection with hardcoded values for stability
        const schemaName = getSchemaNameFromTenantId(tenantId);

        try {
            const connection = new DataSource({
                ...this.defaultDataSourceOptions,
                username: process.env.POSTGRES_USER,
                password: String(process.env.POSTGRES_PASSWORD),
                schema: schemaName,
                name: `tenant-${tenantId}-connection`,
                entities: TENANT_ENTITIES,
                migrations: TENANT_MIGRATIONS
            });

            await connection.initialize();
            await this.ensureSchemaExists(connection, schemaName);

            this.tenantConnections.set(tenantId, connection);
            this.logger.log(`Initialized connection for tenant ${tenantId}`);

            return connection;
        } catch (error) {
            this.logger.error(`Failed to initialize connection for tenant ${tenantId}:`, error);
            throw error;
        }
    }

    private async ensureSchemaExists(connection: DataSource, schema: string): Promise<void> {
        try {
            await connection.query(`CREATE SCHEMA IF NOT EXISTS "${schema}"`);
        } catch (error) {
            this.logger.error(`Failed to ensure schema exists: ${schema}`, error);
            throw error;
        }
    }

    /**
     * Closes the connection for a specific tenant
     * @param tenantId The tenant ID
     */
    async closeTenantConnection(tenantId: string): Promise<void> {
        const connection = this.tenantConnections.get(tenantId);
        if (connection?.isInitialized) {
            try {
                await connection.destroy();
                this.tenantConnections.delete(tenantId);
                this.logger.log(`Closed connection for tenant ${tenantId}`);
            } catch (error) {
                this.logger.error(`Failed to close connection for tenant ${tenantId}:`, error);
                throw error;
            }
        }
    }

    /**
     * Runs migrations for a specific tenant
     * @param tenantId The tenant ID
     */
    async runTenantMigrations(tenantId: string, migrationsDir: string): Promise<void> {
        if (!tenantId || !migrationsDir) {
            throw new Error('Tenant ID and migrations directory are required');
        }

        const schemaName = getSchemaNameFromTenantId(tenantId);
        const connection = await this.getTenantDataSource(tenantId);

        try {
            // Set search_path to tenant schema
            await connection.query(`SET search_path TO "${schemaName}"`);

            // Create migrations table in tenant schema if it doesn't exist
            await connection.query(`
        CREATE TABLE IF NOT EXISTS "${schemaName}"."typeorm_migrations" (
          "id" SERIAL NOT NULL,
          "timestamp" bigint NOT NULL,
          "name" character varying NOT NULL,
          CONSTRAINT "PK_typeorm_migrations" PRIMARY KEY ("id")
        )
      `);

            // Run migrations
            await connection.runMigrations({
                transaction: 'all'
            });

            // Reset search_path
            await connection.query(`SET search_path TO public`);

            this.logger.log(`Successfully ran migrations for tenant ${tenantId}`);
        } catch (error) {
            // Reset search_path even on error
            await connection.query(`SET search_path TO public`).catch(() => {});

            this.logger.error(`Failed to run migrations for tenant ${tenantId}:`, error);
            throw error;
        }
    }

    diagnoseConnections() {
        const publicConnDetails = this.publicDataSource
            ? {
                  name: this.publicDataSource?.['name'],
                  database: this.publicDataSource?.['options']?.['database'],
                  host: this.publicDataSource?.['options']?.['host'],
                  isInitialized: this.publicDataSource?.['isInitialized']
              }
            : 'Not initialized';

        this.logger.log(`Public connection: ${JSON.stringify(publicConnDetails)}`);

        // Log any other configured connections
        this.logger.log(`Tenant connections: ${this.tenantConnections.size}`);
        for (const [id, conn] of this.tenantConnections.entries()) {
            this.logger.log(`Tenant ${id}: ${conn.options.database} (${conn.options?.['schema']})`);
        }
    }
}
