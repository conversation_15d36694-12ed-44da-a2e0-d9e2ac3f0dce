/**
 * Utility functions for sanitizing tenant IDs to prevent SQL injection
 * and ensure valid schema names
 */

/**
 * Sanitizes a tenant ID for use in schema names
 * Removes any characters that could be used for SQL injection
 * and ensures the schema name is valid in PostgreSQL
 *
 * @param tenantId The raw tenant ID
 * @returns The sanitized tenant ID
 * @throws Error if tenant ID is invalid or potentially malicious
 */
export function sanitizeTenantId(tenantId: string): string {
    // Check if tenantId is empty or undefined
    if (!tenantId) {
        throw new Error('Tenant ID cannot be empty');
    }

    // Check if tenantId is too long (PostgreSQL has a 63 character limit for identifiers)
    if (tenantId.length > 50) {
        // Leave some room for the 'tenant_' prefix
        throw new Error('Tenant ID is too long');
    }

    // Check if tenantId contains only alphanumeric characters, hyphens, and underscores
    // This is a strict validation to prevent SQL injection
    if (!/^[a-zA-Z0-9_-]+$/.test(tenantId)) {
        throw new Error('Tenant ID contains invalid characters');
    }

    // Convert to lowercase for consistency and replace hyphens with underscores
    // PostgreSQL identifiers cannot contain hyphens without being quoted
    const sanitized = tenantId.toLowerCase().replace(/-/g, '_');

    return sanitized;
}

/**
 * Validates if a schema name is valid for PostgreSQL
 *
 * @param schemaName The schema name to validate
 * @returns True if the schema name is valid, false otherwise
 */
export function isValidSchemaName(schemaName: string): boolean {
    // PostgreSQL schema naming rules:
    // - Must start with a letter or underscore
    // - Can contain letters, numbers, and underscores (no hyphens)
    // - Maximum length is 63 characters
    return !!schemaName && schemaName.length <= 63 && /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(schemaName);
}

/**
 * Generates a schema name from a tenant ID
 * Ensures the schema name is valid for PostgreSQL by:
 * 1. Replacing hyphens with underscores
 * 2. Ensuring it starts with a letter or underscore
 *
 * @param tenantId The tenant ID
 * @returns The schema name
 */
export function getSchemaNameFromTenantId(tenantId: string): string {
    const sanitized = sanitizeTenantId(tenantId);

    // PostgreSQL identifiers must start with a letter or underscore
    // Since we're adding 'tenant_' prefix, this is only a concern if
    // the sanitized ID starts with a number
    if (/^[0-9]/.test(sanitized)) {
        return `tenant_x${sanitized}`;
    }

    return `tenant_${sanitized}`;
}

/**
 * Extracts a tenant ID from a schema name
 *
 * @param schemaName The schema name
 * @returns The tenant ID or null if the schema name is not a tenant schema
 */
export function getTenantIdFromSchemaName(schemaName: string): string | null {
    if (!schemaName.startsWith('tenant_')) {
        return null;
    }

    // Remove 'tenant_' prefix
    let tenantId = schemaName.substring(7);

    // If it starts with 'x' followed by a number, remove the 'x'
    // This handles the case where we added 'x' to numeric tenant IDs
    if (tenantId.match(/^x[0-9]/)) {
        tenantId = tenantId.substring(1);
    }

    return tenantId;
}
