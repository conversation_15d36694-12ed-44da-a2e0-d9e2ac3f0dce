import { Controller, Get } from '@nestjs/common';
import { HealthAggregatorService } from './health-aggregator.service';
import { HealthAggregateResult } from './health.types';

@Controller('health')
export class HealthAggregatorController {
    constructor(private readonly healthAggregator: HealthAggregatorService) {}

    @Get()
    getHealth(): { status: string; details: Record<string, any> } {
        return this.healthAggregator.getHealthStatus();
    }

    @Get('db')
    checkDb(): Promise<{ status: string; databases: Record<string, any> }> {
        return this.healthAggregator.getDbSnapshot();
    }

    @Get('all')
    async checkAllServices(): Promise<HealthAggregateResult> {
        return this.healthAggregator.getHealth();
    }
}
