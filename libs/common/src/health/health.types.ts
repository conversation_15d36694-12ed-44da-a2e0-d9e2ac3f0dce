// health.types.ts
/**
 * Health check type definitions
 */

/**
 * Health states for services and components
 */
export enum HealthState {
    UP = 'up',
    DOWN = 'down',
    WARNING = 'warning',
    ERROR = 'error',
    UNKNOWN = 'unknown'
}

/**
 * Service health report structure
 */
export interface ServiceReport {
    name: string;
    api?: HealthState;
    db?: HealthState;
    error?: string;
    details?: Record<string, any>;
}

/**
 * Aggregated health check result structure
 */
export interface HealthAggregateResult {
    overall: HealthState;
    core: ServiceReport;
    services: Record<string, ServiceReport>;
}

/**
 * Aggregates multiple service reports into a single health state
 */
export function aggregateStatus(reports: ServiceReport[]): HealthState {
    // If any service is down, the overall status is down
    if (reports.some((report) => report.api === HealthState.DOWN)) {
        return HealthState.DOWN;
    }

    // If any service has an error, the overall status is error
    if (reports.some((report) => report.api === HealthState.ERROR)) {
        return HealthState.ERROR;
    }

    // If any service has a warning, the overall status is warning
    if (reports.some((report) => report.api === HealthState.WARNING)) {
        return HealthState.WARNING;
    }

    // Otherwise, everything is up
    return HealthState.UP;
}
