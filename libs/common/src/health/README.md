# Health Check System

This module provides a centralized health check system for the NestJS microservices architecture.

## Features

- Basic health check endpoint for each microservice
- Database connection health check
- Aggregated health status for the entire system
- Standardized response format
- Specific API and database status indicators

## Endpoints

All endpoints are available under the configured API prefix (e.g., `/api`):

### Core Service

- `GET /api/health` - Basic health status check
- `GET /api/health/db` - Database connection health check
- `GET /api/health/all` - Aggregated health status of all microservices

### Microservices

Each microservice exposes:

- `GET /api/health` - Basic health status check
- `GET /api/health/db` - Database connection health check

## Response Examples

### Basic Health Check (`/api/health`)

```json
{
  "status": "ok",
  "details": {
    "api": {
      "status": "up",
      "message": "API is operational"
    }
  }
}
```

### Database Health Check (`/api/health/db`)

This endpoint returns the status of all database connections across microservices:

```json
{
  "status": "up",
  "databases": {
    "core": {
      "status": "up"
    },
    "auth": {
      "status": "down",
      "error": "Database connection not configured or unavailable"
    },
    "communication": {
      "status": "up"
    },
    "document-engine": {
      "status": "up"
    },
    "case-management": {
      "status": "up"
    }
  }
}
```

When one or more databases are down:

```json
{
  "status": "down",
  "databases": {
    "core": {
      "status": "down",
      "error": "Database connection not configured or unavailable"
    },
    "auth": {
      "status": "up"
    },
    "communication": {
      "status": "up"
    }
  }
}
```

### Aggregated Health Check (`/api/health/all`)

```json
{
  "core": {
    "status": "ok",
    "api": "up",
    "db": "down",
    "details": {
      "api": {
        "status": "up",
        "message": "API is operational"
      },
      "database": {
        "status": "down",
        "message": "Database connection not configured or unavailable"
      }
    }
  },
  "services": {
    "communication": {
      "name": "communication",
      "status": "ok",
      "api": "up",
      "details": {
        "status": "ok",
        "details": {
          "api": {
            "status": "up",
            "message": "API is operational"
          }
        }
      }
    },
    "document-engine": {
      "name": "document-engine",
      "status": "warning",
      "api": "up",
      "error": "Health check endpoint not found (404)"
    },
    "auth": {
      "name": "auth",
      "status": "ok",
      "api": "up",
      "details": {
        "status": "ok",
        "details": {
          "api": {
            "status": "up",
            "message": "API is operational"
          }
        }
      }
    },
    "case-management": {
      "name": "case-management",
      "status": "error",
      "api": "down",
      "error": "Microservice not running"
    }
  },
  "overallStatus": "error"
}
```

## Implementation

The health check system is built on the `@nestjs/terminus` package, which provides standardized health indicators. Each microservice has its own health module that extends the common health module from the shared library.

The core application contains an aggregator service that collects health information from all microservices and presents a unified view.

## Service Status Indicators

The health check system provides specific status indicators:

- **Service Status**:
  - `ok` - Service is fully operational
  - `warning` - Service is running but may have configuration issues (e.g., missing health endpoint)
  - `error` - Service has critical issues

- **API Status**:
  - `up` - API is operational
  - `down` - Service is not running
  - `error` - Service is experiencing errors

- **Database Status**:
  - `up` - Database connection is working
  - `down` - Database connection is not available or not configured
  - `unknown` - Database status could not be determined

## Troubleshooting

Common health check issues:

1. **"Health check endpoint not found (404)"**: 
   - The microservice is running but the health endpoint could not be found
   - Check that the correct API prefix is used (see environment variables)
   - Verify the health module is registered in the microservice's app module

2. **"Microservice not running"**:
   - The microservice is not running or not responding
   - Start the microservice using the appropriate yarn command
   - Check for errors in the microservice's logs

3. **"Database connection not configured or unavailable"**:
   - The database connection is not available or not properly configured
   - This is expected in development environments without a database
   - In production, verify database connection settings

## Environment Configuration

The health aggregator uses environment variables to determine the ports of each microservice:

- `COMMUNICATION_PORT` (default: 3001)
- `DOCUMENT_ENGINE_PORT` (default: 3002)
- `AUTH_PORT` (default: 3003)
- `CASE_MANAGEMENT_PORT` (default: 3004)

API prefix configuration:
- `app.apiGlobalPrefix` (default: 'api')

To change these defaults, set the appropriate environment variables in your deployment configuration. 