import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HealthService } from './health.service';
import { HealthController } from './health.controller';
import { HealthAggregatorService } from './health-aggregator.service';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';

@Module({
    imports: [TerminusModule, HttpModule, ConfigModule],
    controllers: [HealthController],
    providers: [HealthService, HealthAggregatorService],
    exports: [HealthService, HealthAggregatorService]
})
export class HealthModule {}
