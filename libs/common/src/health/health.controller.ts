import { Controller, Get } from '@nestjs/common';
import { HealthCheckResult } from '@nestjs/terminus';
import { HealthService } from './health.service';
import { Config } from '../config/config';

@Controller(Config.CORE_PREFIX + '/health')
export class HealthController {
    constructor(protected readonly healthService: HealthService) {}

    @Get()
    getHealth(): { status: string; details: Record<string, any> } {
        return this.healthService.getStatus();
    }

    @Get('db')
    checkDb(): Promise<HealthCheckResult> {
        return this.healthService.checkDbHealth();
    }
}
