import { Injectable, Logger } from '@nestjs/common';
import { HealthCheckResult, HealthCheckService, TypeOrmHealthIndicator } from '@nestjs/terminus';

@Injectable()
export class HealthService {
    private readonly logger = new Logger(HealthService.name);

    constructor(
        private health: HealthCheckService,
        private db: TypeOrmHealthIndicator
    ) {}

    /**
     * Checks the database connection health
     */
    async checkDbHealth(): Promise<HealthCheckResult> {
        try {
            return await this.health.check([
                () => this.db.pingCheck('database', { timeout: 5000 })
            ]);
        } catch (error) {
            this.logger.warn(`Database health check failed: ${error.message}`);

            // Return a custom health check response instead of throwing an error
            return {
                status: 'ok',
                info: {
                    api: { status: 'up', message: 'API is operational' }
                },
                error: {
                    database: {
                        status: 'down',
                        message: 'Database connection not configured or unavailable'
                    }
                },
                details: {
                    api: { status: 'up', message: 'API is operational' },
                    database: {
                        status: 'down',
                        message: 'Database connection not configured or unavailable'
                    }
                }
            };
        }
    }

    /**
     * Simple check to verify service is running
     */
    getStatus(): { status: string; details: Record<string, unknown> } {
        return {
            status: 'ok',
            details: {
                api: {
                    status: 'up',
                    message: 'API is operational'
                }
            }
        };
    }
}
