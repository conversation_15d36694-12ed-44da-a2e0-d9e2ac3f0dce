/**
 * Generic permission actions that can be applied to any resource
 * These are the base actions that can be performed on any resource type
 */
export enum Permission {
    // Basic CRUD operations
    CREATE = 'CREATE',
    READ = 'READ',
    UPDATE = 'UPDATE',
    DELETE = 'DELETE',

    // View permissions (more granular than READ)
    VIEW_LIST = 'VIEW_LIST',
    VIEW_DETAILS = 'VIEW_DETAILS',
    VIEW_HISTORY = 'VIEW_HISTORY',
    VIEW_AUDIT = 'VIEW_AUDIT',

    // Edit permissions (more granular than UPDATE)
    EDIT_BASIC = 'EDIT_BASIC',
    EDIT_ADVANCED = 'EDIT_ADVANCED',
    EDIT_SENSITIVE = 'EDIT_SENSITIVE',

    // Assignment and ownership
    ASSIGN = 'ASSIGN',
    UNASSIGN = 'UNASSIGN',
    REASSIGN = 'REASSIGN',
    TRANSFER_OWNERSHIP = 'TRANSFER_OWNERSHIP',

    // Status management
    CHANGE_STATUS = 'CHANGE_STATUS',
    APPROVE = 'APPROVE',
    REJECT = 'REJECT',
    ARCHIVE = 'ARCHIVE',
    RESTORE = 'RESTORE',

    // Collaboration
    SHARE = 'SHARE',
    COMMENT = 'COMMENT',
    COLLABORATE = 'COLLABORATE',

    // Export and import
    EXPORT = 'EXPORT',
    IMPORT = 'IMPORT',
    BULK_IMPORT = 'BULK_IMPORT',
    BULK_EXPORT = 'BULK_EXPORT',

    // Advanced operations
    DUPLICATE = 'DUPLICATE',
    MERGE = 'MERGE',
    SPLIT = 'SPLIT',

    // Administrative
    MANAGE_PERMISSIONS = 'MANAGE_PERMISSIONS',
    MANAGE_SETTINGS = 'MANAGE_SETTINGS',
    MANAGE_USERS = 'MANAGE_USERS',
    MANAGE_ROLES = 'MANAGE_ROLES',

    // System-wide permissions
    SYSTEM_ADMIN = 'SYSTEM_ADMIN',
    TENANT_ADMIN = 'TENANT_ADMIN',
    FULL_ACCESS = 'FULL_ACCESS'
}

/**
 * Resource-specific permission combinations
 * These combine base permissions with specific resource contexts
 */
export class ResourcePermissions {
    /**
     * Generate permission string for a specific resource and action
     * @param resourceType The type of resource (e.g., 'CASE', 'DOCUMENT', 'USER')
     * @param permission The base permission action
     * @returns Formatted permission string
     */
    static create(resourceType: string, permission: Permission): string {
        return `${resourceType}:${permission}`;
    }

    /**
     * Generate multiple permissions for a resource
     * @param resourceType The type of resource
     * @param permissions Array of base permissions
     * @returns Array of formatted permission strings
     */
    static createMultiple(resourceType: string, permissions: Permission[]): string[] {
        return permissions.map((permission) => this.create(resourceType, permission));
    }

    /**
     * Parse permission string to extract resource type and action
     * @param permissionString The permission string to parse
     * @returns Object with resourceType and permission
     */
    static parse(permissionString: string): { resourceType: string; permission: Permission } {
        const [resourceType, permission] = permissionString.split(':');
        return { resourceType, permission: permission as Permission };
    }

    /**
     * Check if a permission string matches a resource type
     * @param permissionString The permission string to check
     * @param resourceType The resource type to match against
     * @returns Whether the permission applies to the resource type
     */
    static matchesResourceType(permissionString: string, resourceType: string): boolean {
        return permissionString.startsWith(`${resourceType}:`);
    }
}

/**
 * Predefined permission sets for common resource types
 */
export const RESOURCE_PERMISSIONS = {
    CASE: {
        VIEW_CASE: ResourcePermissions.create('CASE', Permission.READ),
        VIEW_DETAILS: ResourcePermissions.create('CASE', Permission.VIEW_DETAILS),
        VIEW_HISTORY: ResourcePermissions.create('CASE', Permission.VIEW_HISTORY),
        VIEW_AUDIT: ResourcePermissions.create('CASE', Permission.VIEW_AUDIT),

        CREATE_CASE: ResourcePermissions.create('CASE', Permission.CREATE),
        EDIT_CASE: ResourcePermissions.create('CASE', Permission.EDIT_BASIC),
        EDIT_ADVANCED: ResourcePermissions.create('CASE', Permission.EDIT_ADVANCED),
        UPDATE_CASE: ResourcePermissions.create('CASE', Permission.UPDATE),
        DELETE_CASE: ResourcePermissions.create('CASE', Permission.DELETE),

        ASSIGN_CASE: ResourcePermissions.create('CASE', Permission.ASSIGN),
        CHANGE_STATUS: ResourcePermissions.create('CASE', Permission.CHANGE_STATUS),
        APPROVE_CASE: ResourcePermissions.create('CASE', Permission.APPROVE),
        REJECT_CASE: ResourcePermissions.create('CASE', Permission.REJECT),

        COMMENT_CASE: ResourcePermissions.create('CASE', Permission.COMMENT),
        EXPORT_CASE: ResourcePermissions.create('CASE', Permission.EXPORT),
        ARCHIVE_CASE: ResourcePermissions.create('CASE', Permission.ARCHIVE),

        MANAGE_ALL: ResourcePermissions.create('CASE', Permission.FULL_ACCESS)
    },

    DOCUMENT: {
        VIEW_DOCUMENT: ResourcePermissions.create('DOCUMENT', Permission.READ),
        VIEW_DETAILS: ResourcePermissions.create('DOCUMENT', Permission.VIEW_DETAILS),

        CREATE_DOCUMENT: ResourcePermissions.create('DOCUMENT', Permission.CREATE),
        EDIT_DOCUMENT: ResourcePermissions.create('DOCUMENT', Permission.EDIT_BASIC),
        UPDATE_DOCUMENT: ResourcePermissions.create('DOCUMENT', Permission.UPDATE),
        DELETE_DOCUMENT: ResourcePermissions.create('DOCUMENT', Permission.DELETE),

        SHARE_DOCUMENT: ResourcePermissions.create('DOCUMENT', Permission.SHARE),
        EXPORT_DOCUMENT: ResourcePermissions.create('DOCUMENT', Permission.EXPORT),

        MANAGE_ALL: ResourcePermissions.create('DOCUMENT', Permission.FULL_ACCESS)
    },

    USER: {
        VIEW_USER: ResourcePermissions.create('USER', Permission.READ),
        VIEW_PROFILE: ResourcePermissions.create('USER', Permission.VIEW_DETAILS),

        CREATE_USER: ResourcePermissions.create('USER', Permission.CREATE),
        EDIT_USER: ResourcePermissions.create('USER', Permission.EDIT_BASIC),
        EDIT_ADVANCED: ResourcePermissions.create('USER', Permission.EDIT_ADVANCED),
        UPDATE_USER: ResourcePermissions.create('USER', Permission.UPDATE),
        DELETE_USER: ResourcePermissions.create('USER', Permission.DELETE),

        MANAGE_PERMISSIONS: ResourcePermissions.create('USER', Permission.MANAGE_PERMISSIONS),
        MANAGE_ROLES: ResourcePermissions.create('USER', Permission.MANAGE_ROLES),

        MANAGE_ALL: ResourcePermissions.create('USER', Permission.FULL_ACCESS)
    },

    TASK: {
        VIEW_TASK: ResourcePermissions.create('TASK', Permission.READ),
        VIEW_DETAILS: ResourcePermissions.create('TASK', Permission.VIEW_DETAILS),

        CREATE_TASK: ResourcePermissions.create('TASK', Permission.CREATE),
        EDIT_TASK: ResourcePermissions.create('TASK', Permission.EDIT_BASIC),
        UPDATE_TASK: ResourcePermissions.create('TASK', Permission.UPDATE),
        DELETE_TASK: ResourcePermissions.create('TASK', Permission.DELETE),

        ASSIGN_TASK: ResourcePermissions.create('TASK', Permission.ASSIGN),
        CHANGE_STATUS: ResourcePermissions.create('TASK', Permission.CHANGE_STATUS),

        MANAGE_ALL: ResourcePermissions.create('TASK', Permission.FULL_ACCESS)
    },

    REPORT: {
        VIEW_REPORT: ResourcePermissions.create('REPORT', Permission.READ),
        VIEW_DETAILS: ResourcePermissions.create('REPORT', Permission.VIEW_DETAILS),

        CREATE_REPORT: ResourcePermissions.create('REPORT', Permission.CREATE),
        EDIT_REPORT: ResourcePermissions.create('REPORT', Permission.EDIT_BASIC),
        DELETE_REPORT: ResourcePermissions.create('REPORT', Permission.DELETE),

        EXPORT_REPORT: ResourcePermissions.create('REPORT', Permission.EXPORT),
        SHARE_REPORT: ResourcePermissions.create('REPORT', Permission.SHARE),

        MANAGE_ALL: ResourcePermissions.create('REPORT', Permission.FULL_ACCESS)
    },

    SYSTEM: {
        SYSTEM_ADMIN: ResourcePermissions.create('SYSTEM', Permission.SYSTEM_ADMIN),
        TENANT_ADMIN: ResourcePermissions.create('SYSTEM', Permission.TENANT_ADMIN),
        MANAGE_SETTINGS: ResourcePermissions.create('SYSTEM', Permission.MANAGE_SETTINGS),
        MANAGE_USERS: ResourcePermissions.create('SYSTEM', Permission.MANAGE_USERS),
        MANAGE_ROLES: ResourcePermissions.create('SYSTEM', Permission.MANAGE_ROLES),

        FULL_ACCESS: ResourcePermissions.create('SYSTEM', Permission.FULL_ACCESS)
    }
};

/**
 * Type definitions for better type safety
 */
export type CasePermission = keyof typeof RESOURCE_PERMISSIONS.CASE;
export type DocumentPermission = keyof typeof RESOURCE_PERMISSIONS.DOCUMENT;
export type UserPermission = keyof typeof RESOURCE_PERMISSIONS.USER;
export type TaskPermission = keyof typeof RESOURCE_PERMISSIONS.TASK;
export type ReportPermission = keyof typeof RESOURCE_PERMISSIONS.REPORT;
export type SystemPermission = keyof typeof RESOURCE_PERMISSIONS.SYSTEM;

export type AnyPermission =
    | CasePermission
    | DocumentPermission
    | UserPermission
    | TaskPermission
    | ReportPermission
    | SystemPermission;
