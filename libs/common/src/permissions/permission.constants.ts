/**
 * Metadata keys for permission-based authorization
 */
export const PERMISSIONS_KEY = 'permissions';
export const REQUIRE_ALL_PERMISSIONS_KEY = 'requireAllPermissions';
export const PERMISSION_RESOURCE_KEY = 'permissionResource';
export const PERMISSION_CONTEXT_KEY = 'permissionContext';
export const PERMISSION_SCOPE_KEY = 'permissionScope';
export const PERMISSION_CONDITIONS_KEY = 'permissionConditions';

/**
 * Audit action types for permission checks
 */
export enum PermissionAuditAction {
    PERMISSION_GRANTED = 'PERMISSION_GRANTED',
    PERMISSION_DENIED = 'PERMISSION_DENIED',
    PERMISSION_CHECK_FAILED = 'PERMISSION_CHECK_FAILED',
    PERMISSION_BYPASSED = 'PERMISSION_BYPASSED',
    PERMISSION_CACHED = 'PERMISSION_CACHED',
    PERMISSION_EVALUATED = 'PERMISSION_EVALUATED'
}

/**
 * Resource types for permission system - centralized enum for type safety
 * Values are lowercase for database storage and API consistency
 */
export enum ResourceType {
    CASE = 'case',
    DOCUMENT = 'document',
    TASK = 'task',
    CLIENT = 'client',
    USER = 'user',
    REPORT = 'report',
    SYSTEM = 'system',
    WORKFLOW = 'workflow',
    TEMPLATE = 'template',
    NOTIFICATION = 'notification',
    AUDIT = 'audit',
    SETTING = 'setting',
    INTEGRATION = 'integration',
    DASHBOARD = 'dashboard',
    ANALYTICS = 'analytics',
    CUSTOM = 'custom'
}

/**
 * Context types for permission evaluation - now more comprehensive
 */
export enum PermissionContext {
    // Tenant-based contexts
    TENANT = 'TENANT',
    MULTI_TENANT = 'MULTI_TENANT',

    // Resource-based contexts
    OWNERSHIP = 'OWNERSHIP',
    ASSIGNMENT = 'ASSIGNMENT',
    COLLABORATION = 'COLLABORATION',
    HIERARCHY = 'HIERARCHY',

    // Time-based contexts
    TEMPORARY = 'TEMPORARY',
    SCHEDULED = 'SCHEDULED',
    EXPIRED = 'EXPIRED',

    // Condition-based contexts
    CONDITIONAL = 'CONDITIONAL',
    DYNAMIC = 'DYNAMIC',
    INHERITED = 'INHERITED',

    // Legacy contexts for backward compatibility
    CASE_ASSIGNMENT = 'CASE_ASSIGNMENT',
    DOCUMENT_OWNERSHIP = 'DOCUMENT_OWNERSHIP',
    TASK_ASSIGNMENT = 'TASK_ASSIGNMENT',
    CLIENT_RELATIONSHIP = 'CLIENT_RELATIONSHIP'
}

/**
 * Permission scopes for different access levels
 */
export enum PermissionScope {
    GLOBAL = 'GLOBAL',
    TENANT = 'TENANT',
    DEPARTMENT = 'DEPARTMENT',
    TEAM = 'TEAM',
    PERSONAL = 'PERSONAL',
    RESOURCE = 'RESOURCE',
    FIELD = 'FIELD'
}

/**
 * Permission evaluation strategies
 */
export enum PermissionStrategy {
    ALLOW_ALL = 'ALLOW_ALL',
    DENY_ALL = 'DENY_ALL',
    ROLE_BASED = 'ROLE_BASED',
    ATTRIBUTE_BASED = 'ATTRIBUTE_BASED',
    DYNAMIC = 'DYNAMIC',
    HYBRID = 'HYBRID'
}

/**
 * Permission condition types
 */
export interface PermissionCondition {
    type: 'TIME' | 'LOCATION' | 'ATTRIBUTE' | 'CUSTOM';
    operator:
        | 'EQUALS'
        | 'NOT_EQUALS'
        | 'CONTAINS'
        | 'GREATER_THAN'
        | 'LESS_THAN'
        | 'BETWEEN'
        | 'IN'
        | 'NOT_IN';
    value: any;
    field?: string;
    customValidator?: string;
}

/**
 * Permission evaluation result
 */
export interface PermissionEvaluationResult {
    granted: boolean;
    reason?: string;
    conditions?: PermissionCondition[];
    context?: Record<string, any>;
    expiresAt?: Date;
    metadata?: Record<string, any>;
}
