import { Injectable, Inject, forwardRef, BadRequestException, Logger } from '@nestjs/common';
import { Permission } from './enums/permission.enum';
import { TenantRoleRepository } from '@app/common/repositories/tenant-role.repository';
import {
    ROLE_GROUPS,
    RoleGroupDefaultPermissions,
    RoleGroupDefinition
} from './role-group-permissions.defaults';
import { ResourceType } from './permission.constants';

/**
 * Service to load and manage Role Group permissions for resources (Case, Document, etc.)
 * Uses tenant-aware repository pattern. Defaults come from central definition.
 */
@Injectable()
export class RoleGroupPermissionService {
    private readonly logger = new Logger(RoleGroupPermissionService.name);

    constructor(
        @Inject(forwardRef(() => TenantRoleRepository))
        private readonly tenantRoleRepository: TenantRoleRepository
    ) {}

    /**
     * Get allowed actions for a role group on a resource for the current tenant
     * Falls back to central defaults if not set in DB
     */
    async getAllowedActions(
        roleGroupKey: string,
        resource: ResourceType
    ): Promise<Set<Permission>> {
        // 1. Try to load from DB (tenant-specific)
        const role = await this.tenantRoleRepository.findByName(roleGroupKey);
        this.logger.debug(
            `[getAllowedActions] Looking for role: ${roleGroupKey}, found:`,
            role ? { name: role.name, permissions: role.permissions } : 'not found'
        );

        if (role && role.permissions && role.permissions[resource]) {
            const dbPermissions = new Set(role.permissions[resource] as Permission[]);
            this.logger.debug(
                `[getAllowedActions] Using DB permissions for ${roleGroupKey}:${resource}:`,
                Array.from(dbPermissions)
            );
            return dbPermissions;
        }

        // 2. Fallback to central defaults
        const group = ROLE_GROUPS.find((g) => g.key === roleGroupKey);
        if (group && group.defaultPermissions[resource]) {
            const defaultPermissions = new Set(group.defaultPermissions[resource]);
            this.logger.debug(
                `[getAllowedActions] Using default permissions for ${roleGroupKey}:${resource}:`,
                Array.from(defaultPermissions)
            );
            return defaultPermissions;
        }

        this.logger.debug(
            `[getAllowedActions] No permissions found for ${roleGroupKey}:${resource}`
        );
        return new Set();
    }

    /**
     * Set allowed actions for a role group on a resource (Super Admin only)
     * Updates the DB for the current tenant
     */
    async setAllowedActions(
        roleGroupKey: string,
        resource: ResourceType,
        actions: Permission[]
    ): Promise<void> {
        let role = await this.tenantRoleRepository.findByName(roleGroupKey);
        if (!role) {
            // Create role group if it doesn't exist
            role = await this.tenantRoleRepository.create({
                name: roleGroupKey,
                description: roleGroupKey,
                permissions: { [resource]: actions }
            });
        } else {
            // Update permissions for the resource
            role.permissions = role.permissions || {};
            role.permissions[resource] = actions;
            await this.tenantRoleRepository.save(role);
        }
    }

    /**
     * Get all role groups and their permissions for the current tenant
     * Returns both default role groups and dynamically created ones from the database
     */
    async getAllRoleGroups(): Promise<
        { key: string; label: string; permissions: RoleGroupDefaultPermissions }[]
    > {
        const allRoles = await this.tenantRoleRepository.find();
        const roleGroups: {
            key: string;
            label: string;
            permissions: RoleGroupDefaultPermissions;
        }[] = [];

        // Track processed role groups to avoid duplicates
        const processedKeys = new Set<string>();

        // 1. Process default role groups from ROLE_GROUPS constant
        for (const group of ROLE_GROUPS) {
            // Look for role group permissions stored with the role group key directly
            const dbRole = allRoles.find((r) => r.name === group.key);
            let permissions: RoleGroupDefaultPermissions = group.defaultPermissions;

            if (dbRole?.permissions) {
                // Use database permissions if they exist, otherwise fall back to defaults
                permissions = { ...group.defaultPermissions };
                for (const resource of Object.keys(dbRole.permissions)) {
                    permissions[resource] = dbRole.permissions[resource].map(
                        (p) => p as Permission
                    );
                }
            }

            roleGroups.push({
                key: group.key,
                label: group.label,
                permissions
            });

            processedKeys.add(group.key);
        }

        // 2. Process dynamically created role groups from database
        // Look for roles ending with '_user' that aren't already processed
        const dynamicUserRoles = allRoles.filter(
            (role) =>
                role.name.endsWith('_user') && !processedKeys.has(role.name.replace('_user', ''))
        );

        for (const userRole of dynamicUserRoles) {
            const roleGroupKey = userRole.name.replace('_user', '');

            const permissions: RoleGroupDefaultPermissions = {};
            if (userRole.permissions) {
                for (const resource of Object.keys(userRole.permissions)) {
                    permissions[resource] = userRole.permissions[resource].map(
                        (p) => p as Permission
                    );
                }
            }

            roleGroups.push({
                key: roleGroupKey,
                label: userRole.description?.replace(' User', '') || roleGroupKey, // Extract label from description
                permissions
            });

            processedKeys.add(roleGroupKey);
        }

        return roleGroups;
    }

    /**
     * Create a new role group with permissions (Super Admin only)
     * This creates the role group definition and the corresponding user/admin roles
     */
    async createRoleGroup(
        roleGroupDefinition: RoleGroupDefinition
    ): Promise<{ key: string; label: string; rolesCreated: string[] }> {
        // Check if role group already exists
        const existingGroup = ROLE_GROUPS.find((g) => g.key === roleGroupDefinition.key);
        if (existingGroup) {
            throw new BadRequestException(`Role group '${roleGroupDefinition.key}' already exists`);
        }

        // Check if roles already exist in database
        const userRoleName = `${roleGroupDefinition.key}_user`;
        const adminRoleName = `${roleGroupDefinition.key}_admin`;

        const existingUserRole = await this.tenantRoleRepository.findByName(userRoleName);
        const existingAdminRole = await this.tenantRoleRepository.findByName(adminRoleName);

        if (existingUserRole || existingAdminRole) {
            throw new BadRequestException(
                `Roles for group '${roleGroupDefinition.key}' already exist`
            );
        }

        const rolesCreated: string[] = [];

        // Create user role with permissions
        const userRole = await this.tenantRoleRepository.create({
            name: userRoleName,
            description: `${roleGroupDefinition.label} User`,
            permissions: roleGroupDefinition.defaultPermissions
        });
        rolesCreated.push(userRole.name);

        // Create admin role with same permissions (admin privileges are handled at system level)
        const adminRole = await this.tenantRoleRepository.create({
            name: adminRoleName,
            description: `${roleGroupDefinition.label} Admin`,
            permissions: roleGroupDefinition.defaultPermissions
        });
        rolesCreated.push(adminRole.name);

        return {
            key: roleGroupDefinition.key,
            label: roleGroupDefinition.label,
            rolesCreated
        };
    }

    /**
     * Check if a role group exists
     */
    async roleGroupExists(key: string): Promise<boolean> {
        const existsInDefaults = ROLE_GROUPS.some((g) => g.key === key);
        if (existsInDefaults) return true;

        // Check if roles exist in database
        const userRole = await this.tenantRoleRepository.findByName(`${key}_user`);
        const adminRole = await this.tenantRoleRepository.findByName(`${key}_admin`);

        return !!(userRole || adminRole);
    }

    /**
     * Get available resource types for permissions
     */
    getAvailableResourceTypes(): ResourceType[] {
        return Object.values(ResourceType);
    }

    /**
     * Get available permissions for a resource type
     */
    getAvailablePermissions(): Permission[] {
        return Object.values(Permission);
    }
}
