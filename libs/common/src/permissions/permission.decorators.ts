import { SetMetadata } from '@nestjs/common';
import { PERMISSIONS_KEY, ResourceType } from './permission.constants';
import { Permission } from './enums/permission.enum';

/**
 * Decorator to require a specific permission action on a resource
 * Usage: @HasPermission(ResourceType.CASE, Permission.READ)
 */
export const HasPermission = (resource: ResourceType, action: Permission) =>
    SetMetadata(PERMISSIONS_KEY, [{ resource, action }]);

/**
 * Decorator to require Super Admin
 * Usage: @IsSuperAdmin()
 */
export const IS_SUPER_ADMIN_KEY = 'isSuperAdmin';
export const IsSuperAdmin = () => SetMetadata(IS_SUPER_ADMIN_KEY, true);

/**
 * Decorator to require Role Group Admin
 * Usage: @IsRoleGroupAdmin()
 */
export const IS_ROLE_GROUP_ADMIN_KEY = 'isRoleGroupAdmin';
export const IsRoleGroupAdmin = () => SetMetadata(IS_ROLE_GROUP_ADMIN_KEY, true);
