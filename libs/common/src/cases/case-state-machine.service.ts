import {
    Injectable,
    Logger,
    BadRequestException,
    NotFoundException,
    ForbiddenException,
    UnprocessableEntityException,
    HttpException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Between, In, LessThan, Not } from 'typeorm';
import { CaseStatus } from '@app/common/enums/case-status.enum';
import { Case, CaseAssignment, CaseAudit, CaseNote } from '../typeorm/entities';
import { CaseAuditAction } from '../typeorm/entities/tenant/case-audit.entity';
import { KeycloakUserRepresentation } from 'apps/auth/src/interfaces/keycloak-admin.interface';
import { CaseAssignmentRepository } from 'apps/case-management/src/repositories/case-assignment.repository';
import { TenantConnectionService } from '../multi-tenancy/tenant-connection.service';
import { TenantContextService } from '../multi-tenancy/tenant-context.service';

// Audit operation status enum
export enum AuditOperationStatus {
    SUCCESS = 'SUCCESS',
    FAILURE = 'FAILURE',
    WARNING = 'WARNING',
    CANCELLED = 'CANCELLED'
}

/**
 * Manages case state transitions with strict permission checks and audit logging
 */
@Injectable()
export class CaseStateMachineService {
    private readonly logger = new Logger(CaseStateMachineService.name);

    // Define valid transitions
    private readonly validTransitions: Record<CaseStatus, CaseStatus[]> = {
        [CaseStatus.DRAFT]: [CaseStatus.SUBMITTED],
        [CaseStatus.SUBMITTED]: [CaseStatus.UNDER_REVIEW, CaseStatus.DRAFT],
        [CaseStatus.UNDER_REVIEW]: [CaseStatus.ASSIGNED, CaseStatus.REJECTED],
        [CaseStatus.ASSIGNED]: [CaseStatus.IN_PROGRESS],
        [CaseStatus.IN_PROGRESS]: [CaseStatus.ON_HOLD, CaseStatus.PENDING_APPROVAL],
        [CaseStatus.ON_HOLD]: [CaseStatus.IN_PROGRESS],
        [CaseStatus.PENDING_APPROVAL]: [CaseStatus.APPROVED, CaseStatus.REJECTED],
        [CaseStatus.REJECTED]: [CaseStatus.IN_PROGRESS],
        [CaseStatus.APPROVED]: [CaseStatus.RESOLVED],
        [CaseStatus.RESOLVED]: [CaseStatus.CLOSED],
        [CaseStatus.CLOSED]: [CaseStatus.REOPENED],
        [CaseStatus.REOPENED]: [CaseStatus.IN_PROGRESS],
        [CaseStatus.ARCHIVED]: [] // No transitions from ARCHIVED (terminal state)
    };

    // Map from status to audit action
    private readonly statusToAuditAction: Record<CaseStatus, CaseAuditAction> = {
        [CaseStatus.DRAFT]: CaseAuditAction.STATUS_DRAFT,
        [CaseStatus.SUBMITTED]: CaseAuditAction.STATUS_SUBMITTED,
        [CaseStatus.UNDER_REVIEW]: CaseAuditAction.STATUS_UNDER_REVIEW,
        [CaseStatus.ASSIGNED]: CaseAuditAction.STATUS_ASSIGNED,
        [CaseStatus.IN_PROGRESS]: CaseAuditAction.STATUS_IN_PROGRESS,
        [CaseStatus.ON_HOLD]: CaseAuditAction.STATUS_ON_HOLD,
        [CaseStatus.PENDING_APPROVAL]: CaseAuditAction.STATUS_PENDING_APPROVAL,
        [CaseStatus.APPROVED]: CaseAuditAction.STATUS_APPROVED,
        [CaseStatus.REJECTED]: CaseAuditAction.STATUS_REJECTED,
        [CaseStatus.RESOLVED]: CaseAuditAction.STATUS_RESOLVED,
        [CaseStatus.CLOSED]: CaseAuditAction.STATUS_CLOSED,
        [CaseStatus.REOPENED]: CaseAuditAction.STATUS_REOPENED,
        [CaseStatus.ARCHIVED]: CaseAuditAction.STATUS_ARCHIVED
    };

    // Required fields for each status transition
    private readonly requiredFields: Record<string, string[]> = {
        [`${CaseStatus.DRAFT}:${CaseStatus.SUBMITTED}`]: ['title', 'description'],
        [`${CaseStatus.UNDER_REVIEW}:${CaseStatus.ASSIGNED}`]: ['assignedTo', 'notes'],
        [`${CaseStatus.PENDING_APPROVAL}:${CaseStatus.APPROVED}`]: ['notes'],
        [`${CaseStatus.PENDING_APPROVAL}:${CaseStatus.REJECTED}`]: ['notes'],
        [`${CaseStatus.CLOSED}:${CaseStatus.REOPENED}`]: ['reopenReason']
    };

    constructor(
        @InjectRepository(Case)
        private readonly caseRepository: Repository<Case>,
        @InjectRepository(CaseAssignment)
        private readonly caseAssignmentRepository: CaseAssignmentRepository,
        @InjectRepository(CaseAudit)
        private readonly caseAuditRepository: Repository<CaseAudit>,
        @InjectRepository(CaseNote)
        private readonly caseNoteRepository: Repository<CaseNote>,
        private readonly dataSource: DataSource,
        private readonly tenantContextService: TenantContextService,
        private readonly tenantConnectionService: TenantConnectionService
    ) {}

    /**
     * Get all valid next states for a given current state
     */
    getValidNextStates(currentState: CaseStatus): CaseStatus[] {
        return this.validTransitions[currentState] || [];
    }

    /**
     * Get all possible next states for a given current state and user
     * Filters transitions based on user permissions
     */
    getAvailableTransitions(
        _caseId: string,
        currentState: CaseStatus
    ): Partial<Record<CaseStatus, string>> {
        const validNextStates = this.getValidNextStates(currentState);
        // TODO: Use assignee check when implementing permission logic
        // const isAssignee = await this.checkIsAssigneeOrCreator(caseId, user['systemUserId']);

        // Filter transitions based on user permissions
        const availableTransitions: Partial<Record<CaseStatus, string>> = {};

        for (const nextState of validNextStates) {
            const canTransition = true; // TODO: implement new permission logic

            if (canTransition) {
                const label = this.getTransitionLabel(nextState);
                availableTransitions[nextState] = label;
            }
        }

        return availableTransitions;
    }

    /**
     * Check if transition is valid
     */
    isValidTransition(fromState: CaseStatus, toState: CaseStatus): boolean {
        return this.validTransitions[fromState]?.includes(toState) ?? false;
    }

    /**
     * Get user-friendly label for a transition
     */
    private getTransitionLabel(status: CaseStatus): string {
        const labels: Record<CaseStatus, string> = {
            [CaseStatus.DRAFT]: 'Save as Draft',
            [CaseStatus.SUBMITTED]: 'Submit',
            [CaseStatus.UNDER_REVIEW]: 'Start Review',
            [CaseStatus.ASSIGNED]: 'Assign Case',
            [CaseStatus.IN_PROGRESS]: 'Start Working',
            [CaseStatus.ON_HOLD]: 'Put on Hold',
            [CaseStatus.PENDING_APPROVAL]: 'Request Approval',
            [CaseStatus.APPROVED]: 'Approve',
            [CaseStatus.REJECTED]: 'Reject',
            [CaseStatus.RESOLVED]: 'Mark as Resolved',
            [CaseStatus.CLOSED]: 'Close Case',
            [CaseStatus.REOPENED]: 'Reopen Case',
            [CaseStatus.ARCHIVED]: 'Archive Case'
        };

        return labels[status] || status;
    }

    /**
     * Check if user is assignee or creator of case
     */
    private async checkIsAssigneeOrCreator(caseId: string, userId: string): Promise<boolean> {
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId },
            select: ['id', 'createdBy']
        });

        if (!caseEntity) {
            return false;
        }

        // Check if user is creator
        if (caseEntity.createdBy === userId) {
            return true;
        }

        // Check if user has active assignment
        return false; // TODO: implement new permission logic
    }

    /**
     * Check if user is assignee or creator of case
     */
    private async checkIsAssigneeOrCreatorWithTransaction(
        caseRepository: Repository<Case>,
        caseId: string,
        userId: string
    ): Promise<boolean> {
        const caseEntity = await caseRepository.findOne({
            where: { id: caseId },
            select: ['id', 'createdBy']
        });

        if (!caseEntity) {
            return false;
        }

        // Check if user is creator
        if (caseEntity.createdBy === userId) {
            return true;
        }

        // Check if user has active assignment
        return false; // TODO: implement new permission logic
    }

    async transition(
        caseId: string,
        targetState: CaseStatus,
        user: KeycloakUserRepresentation,
        data?: Record<string, any>,
        ipAddress?: string
    ): Promise<Case> {
        // Use transaction for consistency
        const tenantId = this.tenantContextService.getTenantId();
        const tenantDataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
        const queryRunner = tenantDataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        // Track operation status
        let operationStatus = AuditOperationStatus.FAILURE;
        let errorDetails = '';
        let caseAuditRepositoryTransaction;
        try {
            // Get repositories within this transaction
            const caseRepositoryTransaction = queryRunner.manager.getRepository(Case);
            caseAuditRepositoryTransaction = queryRunner.manager.getRepository(CaseAudit);
            const caseNoteRepositoryTransaction = queryRunner.manager.getRepository(CaseNote);
            const caseAssignmentRepositoryTransaction =
                queryRunner.manager.getRepository(CaseAssignment);

            // Find the case
            const caseEntity = await caseRepositoryTransaction.findOne({
                where: { id: caseId },
                relations: ['client', 'assignments', 'notes', 'attachments']
            });
            if (!caseEntity) {
                throw new NotFoundException(`Case with ID ${caseId} not found`);
            }

            const currentState = caseEntity.status;

            // Check if transition is valid
            if (!this.isValidTransition(currentState, targetState)) {
                // Log invalid transition attempt (using transaction manager)
                errorDetails = `Invalid transition from ${currentState} to ${targetState}`;
                await this.createAuditRecordWithTransaction(
                    caseAuditRepositoryTransaction,
                    caseId,
                    CaseAuditAction.ACCESS_DENIED,
                    user['systemUserId'] as string,
                    user?.email ?? user.username,
                    {
                        action: 'invalid_transition',
                        fromState: currentState,
                        toState: targetState,
                        reason: errorDetails
                    },
                    AuditOperationStatus.FAILURE,
                    ipAddress
                );

                throw new BadRequestException(errorDetails);
            }

            // Check if user has required permissions
            // TODO: Use assignee check when implementing permission logic
            // const isAssignee = await this.checkIsAssigneeOrCreatorWithTransaction(
            //     caseRepositoryTransaction,
            //     caseId,
            //     user['systemUserId'] as string
            // );
            const hasPermission = true; // TODO: implement new permission logic

            if (!hasPermission) {
                // Create an audit record for the access denial (using transaction manager)
                errorDetails = `Insufficient permissions to transition from ${currentState} to ${targetState}`;
                await this.createAuditRecordWithTransaction(
                    caseAuditRepositoryTransaction,
                    caseId,
                    CaseAuditAction.ACCESS_DENIED,
                    user['systemUserId'] as string,
                    user?.email ?? user.username,
                    {
                        action: 'state_transition',
                        fromState: currentState,
                        toState: targetState,
                        reason: errorDetails
                    },
                    AuditOperationStatus.FAILURE,
                    ipAddress
                );

                throw new ForbiddenException(
                    `You do not have permission to transition this case from ${currentState} to ${targetState}`
                );
            }

            // Validate required fields
            this.validateRequiredFields(currentState, targetState, data);

            // Execute transition guards
            this.executeGuards(caseEntity, currentState, targetState, user['systemUserId'], data);

            // Store the previous state for audit
            const previousState = caseEntity.status;

            // Update the case status
            caseEntity.status = targetState;

            await this.applyStateSpecificUpdatesWithTransaction(
                caseAssignmentRepositoryTransaction,
                caseAuditRepositoryTransaction,
                caseEntity,
                previousState,
                targetState,
                user['systemUserId'] as string,
                user?.email ?? user.username,
                data,
                ipAddress
            );

            // Create audit record for the UPDATED action (using transaction manager)
            await this.createAuditRecordWithTransaction(
                caseAuditRepositoryTransaction,
                caseId,
                CaseAuditAction.UPDATED,
                user['systemUserId'] as string,
                user?.email ?? user.username,
                {
                    changes: {
                        status: {
                            from: previousState,
                            to: targetState
                        }
                    }
                },
                AuditOperationStatus.SUCCESS,
                ipAddress
            );

            // Save the case (using transaction manager)
            const savedCase = await caseRepositoryTransaction.save(caseEntity);

            // Create audit record for the STATUS_CHANGED action (using transaction manager)
            await this.createAuditRecordWithTransaction(
                caseAuditRepositoryTransaction,
                caseId,
                CaseAuditAction.STATUS_CHANGED,
                user['systemUserId'] as string,
                user?.email ?? user.username,
                {
                    fromState: previousState,
                    toState: targetState,
                    source: 'user_action'
                },
                AuditOperationStatus.SUCCESS,
                ipAddress
            );

            // Create audit record for the specific state change (using transaction manager)
            await this.createAuditRecordWithTransaction(
                caseAuditRepositoryTransaction,
                caseId,
                this.statusToAuditAction[targetState],
                user['systemUserId'] as string,
                user?.email ?? user.username,
                {
                    fromState: previousState,
                    toState: targetState,
                    notes: data?.notes
                },
                AuditOperationStatus.SUCCESS,
                ipAddress
            );

            // Add note if provided (using transaction manager)
            if (data?.notes) {
                await this.createNoteWithTransaction(
                    caseNoteRepositoryTransaction,
                    caseAuditRepositoryTransaction,
                    caseId,
                    user['systemUserId'] as string,
                    user?.email ?? (user.username as string),
                    data.notes,
                    data.isPrivate === true
                );
            }

            // Set operation as successful
            operationStatus = AuditOperationStatus.SUCCESS;

            // Commit transaction
            await queryRunner.commitTransaction();

            this.logger.log(
                `Case ${caseId} transitioned from ${previousState} to ${targetState} by user ${user['systemUserId'] as string}`
            );

            return savedCase;
        } catch (error) {
            // Rollback transaction
            await queryRunner.rollbackTransaction();
            this.logger.error(`Error in transition: ${error.message}`, error.stack);

            errorDetails = error.message;

            if (error instanceof HttpException) {
                throw error;
            }

            throw new BadRequestException(`Failed to transition case: ${error.message}`);
        } finally {
            // Record final audit of the operation with status if it failed
            if (operationStatus !== AuditOperationStatus.SUCCESS) {
                await this.createAuditRecordWithTransaction(
                    caseAuditRepositoryTransaction,
                    caseId,
                    CaseAuditAction.STATUS_CHANGED,
                    user['systemUserId'],
                    user?.email ?? user.username,
                    {
                        targetState,
                        error: errorDetails
                    },
                    operationStatus,
                    ipAddress
                );
            }

            // Release query runner
            await queryRunner.release();
        }
    }

    private async createAuditRecordWithTransaction(
        caseAuditRepository: Repository<CaseAudit>,
        caseId: string,
        action: CaseAuditAction,
        performedBy: string,
        performedByName?: string,
        details?: Record<string, any>,
        status: AuditOperationStatus = AuditOperationStatus.SUCCESS,
        ipAddress?: string
    ): Promise<CaseAudit> {
        const audit = caseAuditRepository.create({
            caseId,
            action,
            performedBy,
            performedByName,
            performedAt: new Date(),
            ipAddress,
            details: {
                ...details,
                status
            }
        });

        return caseAuditRepository.save(audit);
    }

    /**
     * Create a case assignment
     */
    async createAssignmentWithTransaction(
        caseAssignmentRepositoryTransaction: Repository<CaseAssignment>,
        caseAuditRepositoryTransaction: Repository<CaseAudit>,
        caseId: string,
        userId: string,
        userName: string,
        assignedBy: string,
        assignedByName?: string,
        notes?: string
    ): Promise<CaseAssignment> {
        try {
            // Find any existing active assignment for this user on this case
            const existingAssignment = await caseAssignmentRepositoryTransaction.findOne({
                where: {
                    caseId,
                    userId,
                    isActive: true
                }
            });

            // If there's an existing active assignment, deactivate it
            if (existingAssignment) {
                // Update the entity directly
                existingAssignment.isActive = false;
                await caseAssignmentRepositoryTransaction.save(existingAssignment);
            }

            // Create new assignment entity directly without using repository.create()
            const newAssignment: Partial<CaseAssignment> = {
                caseId,
                userId,
                userName,
                assignedBy,
                notes,
                isActive: true,
                assignedAt: new Date()
            };

            // Save the new assignment
            const savedAssignment = await caseAssignmentRepositoryTransaction.save(newAssignment);

            // Create audit for new assignment
            await this.createAuditRecordWithTransaction(
                caseAuditRepositoryTransaction,
                caseId,
                CaseAuditAction.ASSIGNED,
                assignedBy,
                assignedByName,
                {
                    userId,
                    userName,
                    notes,
                    assignmentId: savedAssignment.id
                },
                AuditOperationStatus.SUCCESS
            );

            return savedAssignment;
        } catch (error) {
            this.logger.error(`Error creating assignment: ${error.message}`, error.stack);

            // Log the failure
            await this.createAuditRecordWithTransaction(
                caseAuditRepositoryTransaction,
                caseId,
                CaseAuditAction.ASSIGNED,
                assignedBy,
                assignedByName,
                {
                    userId,
                    userName,
                    notes,
                    error: error.message
                },
                AuditOperationStatus.FAILURE
            );

            throw error;
        }
    }

    //create note within a transaction
    private async createNoteWithTransaction(
        caseNoteRepository: Repository<CaseNote>,
        caseAuditRepository: Repository<CaseAudit>,
        caseId: string,
        userId: string,
        userName: string,
        content: string,
        isPrivate: boolean = false
    ): Promise<CaseNote> {
        try {
            const note = caseNoteRepository.create({
                caseId,
                content,
                createdBy: userId,
                createdByName: userName,
                isPrivate
            });

            const savedNote = await caseNoteRepository.save(note);

            // Add audit record for note creation
            await this.createAuditRecordWithTransaction(
                caseAuditRepository,
                caseId,
                CaseAuditAction.NOTE_ADDED,
                userId,
                userName,
                {
                    noteId: savedNote.id,
                    isPrivate,
                    contentLength: content.length
                },
                AuditOperationStatus.SUCCESS
            );

            return savedNote;
        } catch (error) {
            this.logger.error(`Error creating note: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Validate required fields for a transition
     */
    private validateRequiredFields(
        fromState: CaseStatus,
        toState: CaseStatus,
        data?: Record<string, any>
    ): void {
        const transitionKey = `${fromState}:${toState}`;
        const requiredFields = this.requiredFields[transitionKey] || [];

        if (!data) {
            if (requiredFields.length > 0) {
                throw new BadRequestException(
                    `Missing required fields for transition: ${requiredFields.join(', ')}`
                );
            }
            return;
        }

        const missingFields = requiredFields.filter((field) => {
            if (field === 'assignedTo') {
                // Special case for assignedTo - could be in data.assignments
                return !data.assignedTo && (!data.assignments || data.assignments.length === 0);
            }
            return !data[field] || data[field].toString().trim() === '';
        });

        if (missingFields.length > 0) {
            throw new BadRequestException(`Missing required fields: ${missingFields.join(', ')}`);
        }
    }

    /**
     * Execute validation guards for state transitions
     */
    private executeGuards(
        caseEntity: Case,
        fromState: CaseStatus,
        toState: CaseStatus,
        _userId: string,
        data?: Record<string, any>
    ): void {
        const transitionKey = `${fromState}:${toState}`;

        switch (transitionKey) {
            case `${CaseStatus.DRAFT}:${CaseStatus.SUBMITTED}`:
                if (!caseEntity.title || caseEntity.title.trim() === '') {
                    throw new BadRequestException('Title is required to submit a case');
                }
                if (!caseEntity.description || caseEntity.description.trim() === '') {
                    throw new BadRequestException('Description is required to submit a case');
                }
                break;

            case `${CaseStatus.UNDER_REVIEW}:${CaseStatus.ASSIGNED}`:
                if (!data?.assignedTo && (!data?.assignments || !data.assignments.length)) {
                    throw new BadRequestException(
                        'At least one assignee is required when moving to ASSIGNED state'
                    );
                }
                break;

            case `${CaseStatus.IN_PROGRESS}:${CaseStatus.PENDING_APPROVAL}`:
                // Ensure required fields are present before sending for approval
                if (!caseEntity.title || caseEntity.title.trim() === '') {
                    throw new BadRequestException('Title is required before requesting approval');
                }
                if (!caseEntity.description || caseEntity.description.trim() === '') {
                    throw new BadRequestException(
                        'Description is required before requesting approval'
                    );
                }
                break;

            case `${CaseStatus.PENDING_APPROVAL}:${CaseStatus.APPROVED}`:
                // Require approval notes
                if (!data?.notes) {
                    throw new BadRequestException('Approval notes are required');
                }
                break;

            case `${CaseStatus.PENDING_APPROVAL}:${CaseStatus.REJECTED}`:
                // Require rejection reason
                if (!data?.notes) {
                    throw new BadRequestException('Rejection reason is required');
                }
                break;

            case `${CaseStatus.CLOSED}:${CaseStatus.REOPENED}`:
                if (!data?.reopenReason) {
                    throw new BadRequestException('Reason for reopening is required');
                }
                break;
        }
    }

    /**
     * Apply state-specific updates to the case
     * Simplified to work with actual Case entity structure and avoid redundant metadata
     */
    private async applyStateSpecificUpdates(
        caseEntity: Case,
        _fromState: CaseStatus,
        toState: CaseStatus,
        userId: string,
        userDisplayName?: string,
        data?: Record<string, any>,
        ipAddress?: string
    ): Promise<void> {
        let resolutionTime: number | null = 0;
        // For each state transition, handle necessary actions and create focused audit records
        switch (toState) {
            case CaseStatus.SUBMITTED:
                if (!caseEntity.caseNumber) {
                    throw new UnprocessableEntityException(`Case number is required.`);
                }
                break;

            case CaseStatus.ASSIGNED:
                // Handle assignment for multiple users
                if (data?.assignments && Array.isArray(data.assignments)) {
                    // Multiple assignments - create assignments for each user
                    for (const assignment of data.assignments) {
                        await this.createAssignment(
                            caseEntity.id,
                            assignment.userId,
                            assignment.userName || 'Unknown User',
                            userId,
                            userDisplayName,
                            assignment.notes
                        );
                    }
                } else if (data?.assignedTo) {
                    // Single assignment
                    await this.createAssignment(
                        caseEntity.id,
                        data.assignedTo,
                        data.userName || 'Unknown User',
                        userId,
                        userDisplayName,
                        data.notes
                    );
                }
                break;

            case CaseStatus.ON_HOLD:
                if (data?.holdReason) {
                    await this.createAuditRecord(
                        caseEntity.id,
                        CaseAuditAction.STATUS_ON_HOLD,
                        userId,
                        userDisplayName,
                        {
                            holdReason: data.holdReason
                        },
                        AuditOperationStatus.SUCCESS,
                        ipAddress
                    );
                }
                break;

            case CaseStatus.RESOLVED:
                // Calculate resolution time when a case is resolved
                resolutionTime = await this.calculateResolutionTime(caseEntity.id, new Date());

                if (resolutionTime || data?.notes) {
                    await this.createAuditRecord(
                        caseEntity.id,
                        CaseAuditAction.STATUS_RESOLVED,
                        userId,
                        userDisplayName,
                        {
                            resolutionNotes: data?.notes,
                            resolutionTime
                        },
                        AuditOperationStatus.SUCCESS,
                        ipAddress
                    );
                }
                break;

            case CaseStatus.REOPENED:
                // Track the reopen reason
                if (data?.reopenReason) {
                    await this.createAuditRecord(
                        caseEntity.id,
                        CaseAuditAction.STATUS_REOPENED,
                        userId,
                        userDisplayName,
                        {
                            reopenReason: data.reopenReason
                        },
                        AuditOperationStatus.SUCCESS,
                        ipAddress
                    );
                }
                break;
        }
    }

    private async applyStateSpecificUpdatesWithTransaction(
        caseAssignmentRepositoryTransaction: Repository<CaseAssignment>,
        caseAuditRepositoryTransaction: Repository<CaseAudit>,
        caseEntity: Case,
        _fromState: CaseStatus,
        toState: CaseStatus,
        userId: string,
        userDisplayName?: string,
        data?: Record<string, any>,
        ipAddress?: string
    ): Promise<void> {
        let resolutionTime: number | null = 0;
        // For each state transition, handle necessary actions and create focused audit records
        switch (toState) {
            case CaseStatus.SUBMITTED:
                if (!caseEntity.caseNumber) {
                    throw new UnprocessableEntityException(`Case number is required.`);
                }
                break;

            case CaseStatus.ASSIGNED:
                // Handle assignment for multiple users
                if (data?.assignments && Array.isArray(data.assignments)) {
                    // Multiple assignments - create assignments for each user
                    for (const assignment of data.assignments) {
                        await this.createAssignmentWithTransaction(
                            caseAssignmentRepositoryTransaction,
                            caseAuditRepositoryTransaction,
                            caseEntity.id,
                            assignment.userId,
                            assignment.userName || 'Unknown User',
                            userId,
                            userDisplayName,
                            assignment.notes
                        );
                    }
                } else if (data?.assignedTo) {
                    // Single assignment
                    await this.createAssignmentWithTransaction(
                        caseAssignmentRepositoryTransaction,
                        caseAuditRepositoryTransaction,
                        caseEntity.id,
                        data.assignedTo,
                        data.userName || 'Unknown User',
                        userId,
                        userDisplayName,
                        data.notes
                    );
                }
                break;

            case CaseStatus.ON_HOLD:
                if (data?.holdReason) {
                    await this.createAuditRecordWithTransaction(
                        caseAuditRepositoryTransaction,
                        caseEntity.id,
                        CaseAuditAction.STATUS_ON_HOLD,
                        userId,
                        userDisplayName,
                        {
                            holdReason: data.holdReason
                        },
                        AuditOperationStatus.SUCCESS,
                        ipAddress
                    );
                }
                break;

            case CaseStatus.RESOLVED:
                // Calculate resolution time when a case is resolved
                resolutionTime = await this.calculateResolutionTime(caseEntity.id, new Date());

                if (resolutionTime || data?.notes) {
                    //Resolve case here
                }
                break;

            case CaseStatus.REOPENED:
                // Track the reopen reason
                if (data?.reopenReason) {
                    await this.createAuditRecordWithTransaction(
                        caseAuditRepositoryTransaction,
                        caseEntity.id,
                        CaseAuditAction.STATUS_REOPENED,
                        userId,
                        userDisplayName,
                        {
                            reopenReason: data.reopenReason
                        },
                        AuditOperationStatus.SUCCESS,
                        ipAddress
                    );
                }
                break;
        }
    }

    /**
     * Calculate resolution time based on submission audit record
     */
    private async calculateResolutionTime(
        caseId: string,
        resolvedAt: Date
    ): Promise<number | null> {
        try {
            // Find the submission audit record
            const submissionAudit = await this.caseAuditRepository.findOne({
                where: {
                    caseId,
                    action: CaseAuditAction.STATUS_SUBMITTED
                },
                order: { performedAt: 'ASC' }
            });

            if (submissionAudit && submissionAudit.performedAt) {
                const diffMs = resolvedAt.getTime() - submissionAudit.performedAt.getTime();
                const diffHours = diffMs / (1000 * 60 * 60);
                return Math.round(diffHours * 10) / 10; // Round to 1 decimal place
            }

            return null;
        } catch (error) {
            this.logger.error(`Error calculating resolution time: ${error.message}`);
            return null;
        }
    }

    /**
     * Create an audit record for a case event
     */
    async createAuditRecord(
        caseId: string,
        action: CaseAuditAction,
        performedBy: string,
        performedByName?: string,
        details?: Record<string, any>,
        status: AuditOperationStatus = AuditOperationStatus.SUCCESS,
        ipAddress?: string
    ): Promise<CaseAudit> {
        const audit = this.caseAuditRepository.create({
            caseId,
            action,
            performedBy,
            performedByName,
            performedAt: new Date(),
            ipAddress,
            details: {
                ...details,
                status // Include status in details for tracking
            }
        });

        return this.caseAuditRepository.save(audit);
    }

    /**
     * Create a case note
     */
    async createNote(
        caseId: string,
        userId: string,
        userName: string,
        content: string,
        isPrivate: boolean = false
    ): Promise<CaseNote> {
        try {
            const note = this.caseNoteRepository.create({
                caseId,
                content,
                createdBy: userId,
                createdByName: userName,
                isPrivate
            });

            const savedNote = await this.caseNoteRepository.save(note);

            // Add audit record for note creation
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.NOTE_ADDED,
                userId,
                userName,
                {
                    noteId: savedNote.id,
                    isPrivate,
                    contentLength: content.length
                },
                AuditOperationStatus.SUCCESS
            );

            return savedNote;
        } catch (error) {
            this.logger.error(`Error creating note: ${error.message}`, error.stack);

            // Log the failure
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.NOTE_ADDED,
                userId,
                userName,
                {
                    isPrivate,
                    contentLength: content?.length,
                    error: error.message
                },
                AuditOperationStatus.FAILURE
            );

            throw error;
        }
    }

    /**
     * Create a case assignment
     */
    async createAssignment(
        caseId: string,
        userId: string,
        userName: string,
        assignedBy: string,
        assignedByName?: string,
        notes?: string
    ): Promise<CaseAssignment> {
        try {
            // Find any existing active assignment for this user on this case
            const existingAssignment = await this.caseAssignmentRepository.findOne({
                where: {
                    caseId,
                    userId,
                    isActive: true
                }
            });

            // If there's an existing active assignment, deactivate it
            if (existingAssignment) {
                // Update the entity directly
                existingAssignment.isActive = false;
                await this.caseAssignmentRepository.save(existingAssignment);
            }

            // Create new assignment entity directly without using repository.create()
            const newAssignment: Partial<CaseAssignment> = {
                caseId,
                userId,
                userName,
                assignedBy,
                notes,
                isActive: true,
                assignedAt: new Date()
            };

            // Save the new assignment
            const savedAssignment = await this.caseAssignmentRepository.save(newAssignment);

            // Create audit for new assignment
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.ASSIGNED,
                assignedBy,
                assignedByName,
                {
                    userId,
                    userName,
                    notes,
                    assignmentId: savedAssignment.id
                },
                AuditOperationStatus.SUCCESS
            );

            return savedAssignment;
        } catch (error) {
            this.logger.error(`Error creating assignment: ${error.message}`, error.stack);

            // Log the failure
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.ASSIGNED,
                assignedBy,
                assignedByName,
                {
                    userId,
                    userName,
                    notes,
                    error: error.message
                },
                AuditOperationStatus.FAILURE
            );

            throw error;
        }
    }

    /**
     * Remove an assignment from a case
     */
    async removeAssignment(
        caseId: string,
        userId: string,
        removedBy: string,
        removedByName?: string,
        reason?: string
    ): Promise<void> {
        try {
            // Find active assignment for this user on this case
            const assignment = await this.caseAssignmentRepository.findActiveAssignment(
                caseId,
                userId
            );

            if (!assignment) {
                return; // No active assignment to remove
            }

            // Deactivate the assignment
            assignment.isActive = false;
            assignment.notes = reason || '';
            await this.caseAssignmentRepository.save(assignment);

            // Create audit record
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.UNASSIGNED,
                removedBy,
                removedByName,
                {
                    userId,
                    reason
                },
                AuditOperationStatus.SUCCESS
            );

            // Create a REASSIGNED audit if this is a reassignment (not just removal)
            if (reason === 'reassigned') {
                await this.createAuditRecord(
                    caseId,
                    CaseAuditAction.REASSIGNED,
                    removedBy,
                    removedByName,
                    {
                        previousUserId: userId,
                        reason
                    },
                    AuditOperationStatus.SUCCESS
                );
            }
        } catch (error) {
            this.logger.error(`Error removing assignment: ${error.message}`, error.stack);

            // Log the failure
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.UNASSIGNED,
                removedBy,
                removedByName,
                {
                    userId,
                    reason,
                    error: error.message
                },
                AuditOperationStatus.FAILURE
            );

            throw error;
        }
    }

    /**
     * Access a case and create an audit record
     */
    async accessCase(
        caseId: string,
        userId: string,
        userName: string,
        action: string = 'view',
        ipAddress?: string
    ): Promise<void> {
        await this.createAuditRecord(
            caseId,
            CaseAuditAction.ACCESSED,
            userId,
            userName,
            {
                action
            },
            AuditOperationStatus.SUCCESS,
            ipAddress
        );
    }

    /**
     * Record an export action for audit
     */
    async recordExport(
        caseId: string,
        userId: string,
        userName: string,
        format: string,
        ipAddress?: string
    ): Promise<void> {
        await this.createAuditRecord(
            caseId,
            CaseAuditAction.EXPORTED,
            userId,
            userName,
            {
                format
            },
            AuditOperationStatus.SUCCESS,
            ipAddress
        );
    }

    /**
     * Update a case and create appropriate audit records
     */
    async updateCase(
        caseId: string,
        userId: string,
        userName: string,
        updates: Partial<Case>,
        ipAddress?: string
    ): Promise<Case> {
        try {
            // Find the case
            const caseEntity = await this.caseRepository.findOne({
                where: { id: caseId }
            });

            if (!caseEntity) {
                throw new NotFoundException(`Case with ID ${caseId} not found`);
            }

            // Prepare changes for audit
            const changes: Record<string, { from: any; to: any }> = {};

            // Track what fields were changed
            for (const [key, value] of Object.entries(updates)) {
                if (caseEntity[key] !== value) {
                    changes[key] = {
                        from: caseEntity[key],
                        to: value
                    };
                }
            }

            // Apply updates
            Object.assign(caseEntity, updates);
            // TypeORM handles updatedAt automatically

            // Save the case
            const savedCase = await this.caseRepository.save(caseEntity);

            // Create audit record for the update
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.UPDATED,
                userId,
                userName,
                {
                    changes
                },
                AuditOperationStatus.SUCCESS,
                ipAddress
            );

            // If this was a client update, add a CLIENT_UPDATED audit entry
            if (updates.clientId || (updates as any).clientName) {
                await this.createAuditRecord(
                    caseId,
                    CaseAuditAction.CLIENT_UPDATED,
                    userId,
                    userName,
                    {
                        clientId: updates.clientId,
                        clientName: (updates as any).clientName
                    },
                    AuditOperationStatus.SUCCESS,
                    ipAddress
                );
            }

            return savedCase;
        } catch (error) {
            this.logger.error(`Error updating case: ${error.message}`, error.stack);

            // Log the failure
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.UPDATED,
                userId,
                userName,
                {
                    error: error.message
                },
                AuditOperationStatus.FAILURE,
                ipAddress
            );

            throw error;
        }
    }

    /**
     * Set a deadline for a case
     */
    async setDeadline(
        caseId: string,
        deadline: Date,
        userId: string,
        userName: string,
        notes?: string,
        ipAddress?: string
    ): Promise<Case> {
        try {
            // Find the case
            const caseEntity = await this.caseRepository.findOne({
                where: { id: caseId }
            });

            if (!caseEntity) {
                throw new NotFoundException(`Case with ID ${caseId} not found`);
            }

            // Set deadline
            const oldDeadline = caseEntity.deadline;
            caseEntity.deadline = deadline;
            // TypeORM handles updatedAt automatically

            // Save the case
            const savedCase = await this.caseRepository.save(caseEntity);

            // Create audit record for the deadline setting
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.UPDATED,
                userId,
                userName,
                {
                    changes: {
                        deadline: {
                            from: oldDeadline,
                            to: deadline
                        }
                    },
                    notes,
                    action: 'set_deadline'
                },
                AuditOperationStatus.SUCCESS,
                ipAddress
            );

            // Create specific REMINDER_SET audit
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.REMINDER_SET,
                userId,
                userName,
                {
                    deadline,
                    notes
                },
                AuditOperationStatus.SUCCESS,
                ipAddress
            );

            return savedCase;
        } catch (error) {
            this.logger.error(`Error setting deadline: ${error.message}`, error.stack);

            // Log the failure
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.REMINDER_SET,
                userId,
                userName,
                {
                    error: error.message,
                    deadline
                },
                AuditOperationStatus.FAILURE,
                ipAddress
            );

            throw error;
        }
    }

    /**
     * Log a deadline approaching event
     */
    async logDeadlineApproaching(
        caseId: string,
        daysRemaining: number,
        ipAddress?: string
    ): Promise<void> {
        try {
            const caseEntity = await this.caseRepository.findOne({
                where: { id: caseId }
            });

            if (!caseEntity || !caseEntity.deadline) {
                return;
            }

            // Create audit record for the deadline approaching
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.DEADLINE_APPROACHING,
                'system',
                'System',
                {
                    deadline: caseEntity.deadline,
                    daysRemaining
                },
                AuditOperationStatus.SUCCESS,
                ipAddress
            );
        } catch (error) {
            this.logger.error(`Error logging deadline approaching: ${error.message}`, error.stack);
        }
    }

    /**
     * Log a deadline missed event
     */
    async logDeadlineMissed(caseId: string, ipAddress?: string): Promise<void> {
        try {
            const caseEntity = await this.caseRepository.findOne({
                where: { id: caseId }
            });

            if (!caseEntity || !caseEntity.deadline) {
                return;
            }

            // Create audit record for the deadline missed
            await this.createAuditRecord(
                caseId,
                CaseAuditAction.DEADLINE_MISSED,
                'system',
                'System',
                {
                    deadline: caseEntity.deadline,
                    daysPast: Math.floor(
                        (new Date().getTime() - caseEntity.deadline.getTime()) /
                            (1000 * 60 * 60 * 24)
                    )
                },
                AuditOperationStatus.SUCCESS,
                ipAddress
            );
        } catch (error) {
            this.logger.error(`Error logging deadline missed: ${error.message}`, error.stack);
        }
    }

    /**
     * Get case history from audit trail
     */
    async getCaseHistory(
        caseId: string,
        paginationOptions?: { skip?: number; take?: number }
    ): Promise<{ audits: CaseAudit[]; total: number }> {
        const options = {
            where: { caseId },
            order: { performedAt: 'DESC' as const }, // Type assertion to fix the issue
            skip: paginationOptions?.skip || 0,
            take: paginationOptions?.take || 50
        };

        const [audits, total] = await this.caseAuditRepository.findAndCount(options);

        return { audits, total };
    }

    /**
     * Get timeline of state changes for a case
     */
    async getStateChangeTimeline(caseId: string): Promise<CaseAudit[]> {
        // Get all status change audit records
        const statusAudits = await this.caseAuditRepository.find({
            where: [
                { caseId, action: CaseAuditAction.STATUS_CHANGED },
                { caseId, action: CaseAuditAction.STATUS_DRAFT },
                { caseId, action: CaseAuditAction.STATUS_SUBMITTED },
                { caseId, action: CaseAuditAction.STATUS_UNDER_REVIEW },
                { caseId, action: CaseAuditAction.STATUS_ASSIGNED },
                { caseId, action: CaseAuditAction.STATUS_IN_PROGRESS },
                { caseId, action: CaseAuditAction.STATUS_ON_HOLD },
                { caseId, action: CaseAuditAction.STATUS_PENDING_APPROVAL },
                { caseId, action: CaseAuditAction.STATUS_APPROVED },
                { caseId, action: CaseAuditAction.STATUS_REJECTED },
                { caseId, action: CaseAuditAction.STATUS_RESOLVED },
                { caseId, action: CaseAuditAction.STATUS_CLOSED },
                { caseId, action: CaseAuditAction.STATUS_REOPENED }
            ],
            order: { performedAt: 'ASC' }
        });

        return statusAudits;
    }

    /**
     * Check all cases with approaching or missed deadlines
     */
    async checkDeadlines(): Promise<void> {
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const threeDaysFromNow = new Date(now);
        threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

        const oneWeekFromNow = new Date(now);
        oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);

        try {
            // Check for missed deadlines
            const missedCases = await this.caseRepository.find({
                where: {
                    deadline: LessThan(now),
                    status: Not(In([CaseStatus.CLOSED, CaseStatus.RESOLVED]))
                }
            });

            for (const caseEntity of missedCases) {
                await this.logDeadlineMissed(caseEntity.id);
            }

            // Check for approaching deadlines - 1 day
            const oneDayCases = await this.caseRepository.find({
                where: {
                    deadline: Between(now, tomorrow),
                    status: Not(In([CaseStatus.CLOSED, CaseStatus.RESOLVED]))
                }
            });

            for (const caseEntity of oneDayCases) {
                await this.logDeadlineApproaching(caseEntity.id, 1);
            }

            // Check for approaching deadlines - 3 days
            const threeDayCases = await this.caseRepository.find({
                where: {
                    deadline: Between(tomorrow, threeDaysFromNow),
                    status: Not(In([CaseStatus.CLOSED, CaseStatus.RESOLVED]))
                }
            });

            for (const caseEntity of threeDayCases) {
                await this.logDeadlineApproaching(caseEntity.id, 3);
            }

            // Check for approaching deadlines - 1 week
            const oneWeekCases = await this.caseRepository.find({
                where: {
                    deadline: Between(threeDaysFromNow, oneWeekFromNow),
                    status: Not(In([CaseStatus.CLOSED, CaseStatus.RESOLVED]))
                }
            });

            for (const caseEntity of oneWeekCases) {
                await this.logDeadlineApproaching(caseEntity.id, 7);
            }
        } catch (error) {
            this.logger.error(`Error checking deadlines: ${error.message}`, error.stack);

            // Create a system audit record for the failure
            await this.createAuditRecord(
                'system', // Using 'system' as caseId to indicate this is a system-wide operation
                CaseAuditAction.UPDATED,
                'system',
                'System',
                {
                    action: 'check_deadlines',
                    error: error.message
                },
                AuditOperationStatus.FAILURE
            );
        }
    }
}
