import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpStatusCode, HttpStatusText } from '../enums/http-status.enum';
import { ApiResponse } from '../interfaces/api-response.interface';

/**
 * Configuration options for the SuccessResponseInterceptor
 */
export interface SuccessResponseInterceptorOptions {
    /**
     * Routes to exclude from transformation (e.g., health checks)
     */
    excludePaths?: string[];
}

/**
 * Interceptor that transforms successful responses into a standardized format
 */
@Injectable()
export class SuccessResponseInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
    private readonly excludePaths: string[];

    /**
     * Creates a new SuccessResponseInterceptor
     * @param options Configuration options
     */
    constructor(options: SuccessResponseInterceptorOptions = {}) {
        this.excludePaths = options.excludePaths || ['/health', '/ping'];
    }

    /**
     * Intercepts the response and transforms it into a standardized format
     */
    intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse<T>> {
        // Get the request
        const request = context.switchToHttp().getRequest();
        const { url } = request;

        // Skip transformation for excluded paths
        if (this.excludePaths.some((path) => url.startsWith(path))) {
            return next.handle();
        }

        // Get the response
        const response = context.switchToHttp().getResponse();
        const statusCode = response.statusCode || HttpStatusCode.OK;

        return next.handle().pipe(
            map((data) => {
                // Check if the response is already in our format
                if (
                    data &&
                    typeof data === 'object' &&
                    'code' in data &&
                    'status' in data &&
                    'message' in data &&
                    'data' in data
                ) {
                    return data;
                }

                // Handle empty responses
                if (data === undefined || data === null) {
                    return {
                        code: statusCode,
                        status: HttpStatusText[statusCode as HttpStatusCode] || 'Success',
                        message: 'Success',
                        data: null
                    };
                }

                // Handle responses with metadata
                if (data && typeof data === 'object' && 'data' in data && 'meta' in data) {
                    return {
                        code: statusCode,
                        status: HttpStatusText[statusCode as HttpStatusCode] || 'Success',
                        message: 'Success',
                        data: data.data,
                        meta: data.meta
                    };
                }

                // Standard transformation
                return {
                    code: statusCode,
                    status: HttpStatusText[statusCode as HttpStatusCode] || 'Success',
                    message: 'Success',
                    data
                };
            })
        );
    }
}
