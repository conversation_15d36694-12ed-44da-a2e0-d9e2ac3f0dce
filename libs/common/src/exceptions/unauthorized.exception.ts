import { HttpStatusCode } from '../enums/http-status.enum';
import { BaseException, ExceptionMetadata } from './base.exception';

/**
 * Exception for unauthorized access errors
 */
export class UnauthorizedException extends BaseException {
    /**
     * Creates a new UnauthorizedException
     * @param message Error message
     * @param errorCode Optional error code
     * @param meta Optional additional metadata
     */
    constructor(message = 'Unauthorized', errorCode?: string, meta?: ExceptionMetadata) {
        super(message, HttpStatusCode.UNAUTHORIZED, errorCode, meta);
    }
}
