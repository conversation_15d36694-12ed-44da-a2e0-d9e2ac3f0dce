import { HttpException } from '@nestjs/common';
import { HttpStatusCode, HttpStatusText } from '../enums/http-status.enum';

/**
 * Type for exception metadata
 */
export type ExceptionMetadata = Record<string, unknown>;

/**
 * Base exception class for all custom exceptions
 * Extends NestJS HttpException to provide a consistent error format
 */
export class BaseException extends HttpException {
    /**
     * Error code for categorizing errors (optional)
     */
    private readonly errorCode?: string;

    /**
     * Additional error metadata (optional)
     */
    private readonly meta?: ExceptionMetadata;

    /**
     * Creates a new BaseException
     * @param message Error message
     * @param statusCode HTTP status code
     * @param errorCode Optional error code for categorizing errors
     * @param meta Optional additional error metadata
     */
    constructor(
        message: string,
        statusCode: HttpStatusCode = HttpStatusCode.INTERNAL_SERVER_ERROR,
        errorCode?: string,
        meta?: ExceptionMetadata
    ) {
        super(
            {
                code: statusCode,
                status: HttpStatusText[statusCode],
                message,
                data: null,
                meta: {
                    ...(meta || {}),
                    ...(errorCode ? { errorCode } : {})
                }
            },
            statusCode
        );

        this.errorCode = errorCode;
        this.meta = meta;
    }

    /**
     * Gets the error code
     */
    getErrorCode(): string | undefined {
        return this.errorCode;
    }

    /**
     * Gets the error metadata
     */
    getMetadata(): ExceptionMetadata | undefined {
        return this.meta;
    }
}
