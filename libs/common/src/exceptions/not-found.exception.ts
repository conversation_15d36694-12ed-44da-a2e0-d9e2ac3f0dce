import { HttpStatusCode } from '../enums/http-status.enum';
import { BaseException, ExceptionMetadata } from './base.exception';

/**
 * Exception for resource not found errors
 */
export class NotFoundException extends BaseException {
    /**
     * Creates a new NotFoundException
     * @param message Error message
     * @param errorCode Optional error code
     * @param meta Optional additional metadata
     */
    constructor(message = 'Resource not found', errorCode?: string, meta?: ExceptionMetadata) {
        super(message, HttpStatusCode.NOT_FOUND, errorCode, meta);
    }
}
