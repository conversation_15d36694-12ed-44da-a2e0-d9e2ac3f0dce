import { HttpStatusCode } from '../enums/http-status.enum';
import { BaseException, ExceptionMetadata } from './base.exception';

/**
 * Exception for bad request errors
 */
export class BadRequestException extends BaseException {
    /**
     * Creates a new BadRequestException
     * @param message Error message
     * @param errorCode Optional error code
     * @param meta Optional additional metadata
     */
    constructor(message = 'Bad request', errorCode?: string, meta?: ExceptionMetadata) {
        super(message, HttpStatusCode.BAD_REQUEST, errorCode, meta);
    }
}
