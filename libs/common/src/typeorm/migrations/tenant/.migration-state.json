{"lastMigration": "1761559226790-added_fields_on_Case.ts", "timestamp": 1761559226797, "entities": [{"tableName": "tenant_roles", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": true, "unique": true, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "permissions", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "enabled", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "users", "type": "many-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "user_profiles"}}], "indices": []}, {"tableName": "user_profiles", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "username", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "email", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "first_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "department", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "last_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "enabled", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "email_verified", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "keycloak_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "additional_info", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "'{}'", "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "user_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "roles", "type": "many-to-many", "joinColumns": [{"databaseName": "user_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "tenant_roles"}}], "indices": []}, {"tableName": "case_notes", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "title", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "255"}, {"databaseName": "content", "type": "text", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "is_pinned", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "is_private", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "case_attachments", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "filename", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "url", "type": "text", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "file_size", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "mime_type", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "uploaded_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "uploaded_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "uploaded_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "document_type", "type": "case_attachments_document_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["PLEADING", "CONTRACT", "EVIDENCE", "CORRESPONDENCE", "COURT_ORDER", "INVOICE", "MEMO", "RESEARCH", "OTHER"], "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "case_audit", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "action", "type": "case_audit_action_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["CREATED", "UPDATED", "CLIENT_UPDATED", "STATUS_CHANGED", "STATUS_DRAFT", "STATUS_SUBMITTED", "STATUS_APPROVED", "STATUS_DECLINED", "STATUS_IN_PROGRESS", "STATUS_ON_HOLD", "STATUS_CLOSED", "STATUS_ARCHIVED", "ASSIGNED", "UNASSIGNED", "REASSIGNED", "NOTE_ADDED", "NOTE_UPDATED", "NOTE_DELETED", "ATTACHMENT_ADDED", "ATTACHMENT_UPDATED", "ATTACHMENT_REMOVED", "CONTACT_ADDED", "CONTACT_UPDATED", "CONTACT_DELETED", "PAYMENT_ADDED", "CASE_RELATION_ADDED", "CASE_RELATION_DELETED", "ACCESSED", "ACCESS_DENIED", "EXPORTED", "CLIENT_CREATED", "PROPERTY_CREATED", "PROPERTY_UPDATED", "REMINDER_SET", "DEADLINE_APPROACHING", "DEADLINE_MISSED", "STATUS_UNDER_REVIEW", "STATUS_ASSIGNED", "STATUS_PENDING_APPROVAL", "STATUS_REJECTED", "STATUS_RESOLVED", "STATUS_REOPENED", "PERMISSION_CHECK"], "length": ""}, {"databaseName": "performed_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "performed_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "performed_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "ip_address", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "details", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "case_contacts", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "email", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "phone", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "address", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "type", "type": "case_contacts_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["CLIENT", "CO_COUNSEL", "OPPOSING_COUNSEL", "WITNESS", "EXPERT", "JUDGE", "OTHER"], "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "additional_info", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "case_events", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "category", "type": "case_events_category_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["INTAKE", "PLEADINGS", "DISCOVERY", "MOTIONS", "HEARINGS", "TRIAL", "POST_TRIAL", "DEADLINE", "ADMINISTRATIVE", "COMMUNICATION", "OTHER"], "length": ""}, {"databaseName": "type", "type": "case_events_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["INITIAL_CONSULTATION", "CONFLICT_CHECK", "ENGAGEMENT_LETTER", "CASE_OPENED", "STATUTE_OF_LIMITATIONS", "COMPLAINT_FILED", "SUMMONS_ISSUED", "SUMMONS_SERVED", "ANSWER_FILED", "COUNTERCLAIM_FILED", "CROSS_CLAIM_FILED", "AMENDED_PLEADING", "INTERROGATORIES_SERVED", "INTERROGATORIES_ANSWERED", "DOCUMENT_REQUEST_SERVED", "DOCUMENT_PRODUCTION", "DEPOSITION_SCHEDULED", "DEPOSITION_COMPLETED", "EXPERT_DISCLOSURE", "DISCOVERY_CUTOFF", "DISCOVERY_DISPUTE", "MOTION_TO_COMPEL", "MOTION_TO_DISMISS", "SUMMARY_JUDGMENT_MOTION", "MOTION_HEARING", "MOTION_IN_LIMINE", "CASE_MANAGEMENT_CONFERENCE", "STATUS_CONFERENCE", "SETTLEMENT_CONFERENCE", "MEDIATION", "PRE_TRIAL_CONFERENCE", "TRIAL_START", "TRIAL_END", "WITNESS_LIST_FILED", "EXHIBIT_LIST_FILED", "JURY_SELECTION", "OPENING_ARGUMENTS", "CLOSING_ARGUMENTS", "VERDICT", "JUDGMENT_ENTERED", "POST_TRIAL_MOTION", "NOTICE_OF_APPEAL", "APPELLATE_BRIEF", "ORAL_ARGUMENTS", "APPELLATE_DECISION", "COURT_DEADLINE", "INTERNAL_DEADLINE", "RESPONSE_DEADLINE", "COURT_FEE_PAYMENT", "BILLING_MILESTONE", "COMPLIANCE_AUDIT", "CLIENT_MEETING", "CLIENT_CALL", "TEAM_MEETING", "OPPOSING_COUNSEL_COMMUNICATION", "COURT_COMMUNICATION", "OTHER"], "length": ""}, {"databaseName": "title", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "event_date", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "case_payments", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "payment_date", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "reference_number", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "method", "type": "case_payments_method_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["BANK_TRANSFER", "CREDIT_CARD", "DEBIT_CARD", "CHEQUE", "CASH", "OTHER"], "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "amount", "type": "numeric", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 10, "scale": 2}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_by", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "clients", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "email", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "phone", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "address", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "additional_info", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "cases", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "properties", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "full_address", "type": "text", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "address_line_1", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "address_line_2", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "city", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "county", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "postal_code", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "country", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "property_type", "type": "properties_property_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["RESIDENTIAL", "COMMERCIAL", "LAND", "LEASEHOLD", "FREEHOLD", "SHARED_OWNERSHIP", "OTHER"], "length": ""}, {"databaseName": "tenure", "type": "properties_tenure_enum", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["FREEHOLD", "LEASEHOLD", "COMMONHOLD", "SHARED_FREEHOLD"], "length": ""}, {"databaseName": "status", "type": "properties_status_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "AVAILABLE", "enum": ["AVAILABLE", "UNDER_OFFER", "SOLD", "WITHDRAWN", "LET", "VACANT"], "length": ""}, {"databaseName": "bedrooms", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "bathrooms", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "receptions", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "floor_area_sqft", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 10, "scale": 2}, {"databaseName": "floor_area_sqm", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 10, "scale": 2}, {"databaseName": "land_area_sqft", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 10, "scale": 2}, {"databaseName": "land_area_sqm", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 10, "scale": 2}, {"databaseName": "year_built", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "parking_spaces", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "garage", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "garden", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "balcony", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "current_value", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 12, "scale": 2}, {"databaseName": "purchase_price", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 12, "scale": 2}, {"databaseName": "asking_price", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 12, "scale": 2}, {"databaseName": "agreed_price", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 12, "scale": 2}, {"databaseName": "valuation_date", "type": "date", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "valuer_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "valuation_reference", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "title_number", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "land_registry_reference", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "deed_type", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "restrictive_covenants", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "easements", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "lease_expiry_date", "type": "date", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "lease_years_remaining", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "ground_rent", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 10, "scale": 2}, {"databaseName": "service_charge", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 10, "scale": 2}, {"databaseName": "management_company", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "council_tax_band", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "council_tax_amount", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 8, "scale": 2}, {"databaseName": "local_authority", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "planning_permission_required", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "listed_building", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "conservation_area", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "epc_rating", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "epc_expiry_date", "type": "date", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "gas_supply", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "electricity_supply", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "water_supply", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "sewerage_connected", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "broadband_available", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "buildings_insurance_required", "type": "boolean", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "buildings_insurance_provider", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "buildings_insurance_policy_number", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "buildings_insurance_amount", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 12, "scale": 2}, {"databaseName": "survey_type", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "survey_date", "type": "date", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "surveyor_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "survey_value", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 12, "scale": 2}, {"databaseName": "survey_issues", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "latitude", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "longitude", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "nearest_station", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "station_distance_miles", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 5, "scale": 2}, {"databaseName": "school_catchment", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "amenities", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "[]", "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "internal_notes", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "special_conditions", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "fixtures_and_fittings", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "property_metadata", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "{}", "length": ""}, {"databaseName": "estate_agent_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "estate_agent_contact", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_contact", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_firm", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_contact", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_reference", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_client_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_address_line_1", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_address_line_2", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_city", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_county", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_postal_code", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_country", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_phone", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_email", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "vendor_solicitor_dx", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "last_modified_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "cases", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "rate_cards", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "provider_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "100"}, {"databaseName": "provider_code", "type": "rate_cards_provider_code_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["arrow", "yopa", "trussle", "tmg", "sesame", "remax", "rayner", "pepper", "optimus", "optimus_bid", "msm", "molo", "mojo", "leas", "landc", "keyclub", "john_charcol", "independent", "haysto", "gazeal", "fort_advice", "fluent", "ekeeper", "charles_cameron", "custom"], "length": ""}, {"databaseName": "display_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "200"}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "version", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "1.0", "length": "20"}, {"databaseName": "effective_date", "type": "date", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "expiry_date", "type": "date", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "status", "type": "rate_cards_status_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "active", "enum": ["active", "inactive", "draft"], "length": ""}, {"databaseName": "is_default", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "priority", "type": "integer", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": true, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": true, "default": "now()", "length": ""}], "relations": [], "indices": []}, {"tableName": "cases", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_number", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": true, "unique": true, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "title", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "status", "type": "cases_status_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "DRAFT", "enum": ["DRAFT", "SUBMITTED", "UNDER_REVIEW", "ASSIGNED", "IN_PROGRESS", "ON_HOLD", "PENDING_APPROVAL", "APPROVED", "REJECTED", "RESOLVED", "CLOSED", "REOPENED", "ARCHIVED"], "length": ""}, {"databaseName": "priority", "type": "cases_priority_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "MEDIUM", "enum": ["LOW", "MEDIUM", "HIGH", "URGENT"], "length": ""}, {"databaseName": "type", "type": "cases_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["LITIGATION", "CORPORATE", "CONVEYANCING", "INTELLECTUAL_PROPERTY", "FAMILY", "CRIMINAL", "PURCHASE", "SALE", "REMORTGAGE", "OTHER", "REAL_ESTATE"], "length": ""}, {"databaseName": "client_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "property_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "rate_card_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "deadline", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "exchange_date", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "completion_date", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "deposit_amount", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 15, "scale": 2}, {"databaseName": "chain_position", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "mortgage_required", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "mortgage_amount", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 15, "scale": 2}, {"databaseName": "lender_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "conveyancing_metadata", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "{}", "length": ""}], "relations": [{"propertyName": "client", "type": "many-to-one", "joinColumns": [{"databaseName": "client_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "clients"}}, {"propertyName": "property", "type": "many-to-one", "joinColumns": [{"databaseName": "property_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "properties"}}, {"propertyName": "rateCard", "type": "many-to-one", "joinColumns": [{"databaseName": "rate_card_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "rate_cards"}}, {"propertyName": "assignments", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_assignments"}}, {"propertyName": "notes", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_notes"}}, {"propertyName": "attachments", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_attachments"}}, {"propertyName": "auditTrail", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_audit"}}, {"propertyName": "contacts", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_contacts"}}, {"propertyName": "events", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_events"}}, {"propertyName": "payments", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "case_payments"}}], "indices": []}, {"tableName": "case_assignments", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "user_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "user_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "assigned_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "assigned_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "is_active", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "notes", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}, {"propertyName": "user", "type": "many-to-one", "joinColumns": [{"databaseName": "user_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "user_profiles"}}], "indices": []}, {"tableName": "case_relations", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "related_case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "type", "type": "case_relations_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "RELATED", "enum": ["PARENT", "CHILD", "RELATED", "PREDECESSOR", "SUCCESSOR"], "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "notes", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}, {"propertyName": "relatedCase", "type": "many-to-one", "joinColumns": [{"databaseName": "related_case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": []}, {"tableName": "task_dependencies", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "task_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "depends_on_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "task", "type": "many-to-one", "joinColumns": [{"databaseName": "task_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "tasks"}}, {"propertyName": "dependsOn", "type": "many-to-one", "joinColumns": [{"databaseName": "depends_on_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "tasks"}}], "indices": []}, {"tableName": "task_history", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "task_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "from_status", "type": "task_status", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enumName": "task_status", "length": ""}, {"databaseName": "to_status", "type": "task_status", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enumName": "task_status", "length": ""}, {"databaseName": "changed_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "changed_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "changed_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "task", "type": "many-to-one", "joinColumns": [{"databaseName": "task_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "tasks"}}], "indices": []}, {"tableName": "milestones", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "status", "type": "milestones_status_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "PENDING", "enum": ["PENDING", "IN_PROGRESS", "COMPLETED"], "length": ""}, {"databaseName": "progress_percentage", "type": "numeric", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": "", "precision": 5, "scale": 2}, {"databaseName": "target_date", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "completion_date", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "sort_order", "type": "integer", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": ""}, {"databaseName": "is_default", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}, {"propertyName": "tasks", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "tasks"}}], "indices": []}, {"tableName": "tasks", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "title", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "status", "type": "task_status", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OPEN", "enum": ["OPEN", "IN_PROGRESS", "BLOCKED", "DONE"], "enumName": "task_status", "length": ""}, {"databaseName": "priority", "type": "task_priority", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "MEDIUM", "enum": ["HIGHEST", "HIGH", "MEDIUM", "LOW", "LOWEST"], "enumName": "task_priority", "length": ""}, {"databaseName": "due_date", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "milestone_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "assignee_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "is_default", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}, {"propertyName": "milestone", "type": "many-to-one", "joinColumns": [{"databaseName": "milestone_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "milestones"}}, {"propertyName": "dependencies", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "task_dependencies"}}, {"propertyName": "dependents", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "task_dependencies"}}, {"propertyName": "statusHistory", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "task_history"}}], "indices": []}, {"tableName": "fee_categories", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "display_order", "type": "integer", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": ""}, {"databaseName": "active", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": true, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": true, "default": "now()", "length": ""}], "relations": [{"propertyName": "items", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "fee_items"}}], "indices": []}, {"tableName": "fee_items", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "label", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "category_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "range_start", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 15, "scale": 2}, {"databaseName": "range_end", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 15, "scale": 2}, {"databaseName": "condition_slug", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "net_fee", "type": "numeric", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": "", "precision": 10, "scale": 2}, {"databaseName": "vat_fee", "type": "numeric", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": "", "precision": 10, "scale": 2}, {"databaseName": "total_fee", "type": "numeric", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": "", "precision": 10, "scale": 2}, {"databaseName": "vat_type", "type": "fee_items_vat_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "inc", "enum": ["inc", "exc", "no"], "length": ""}, {"databaseName": "applicable_for", "type": "text", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "per_party", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "dynamic", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "active", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "version", "type": "integer", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 1, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": true, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": true, "default": "now()", "length": ""}], "relations": [{"propertyName": "category", "type": "many-to-one", "joinColumns": [{"databaseName": "category_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "fee_categories"}}], "indices": [{"name": "IDX_9c1620e5241e7c1a1f3fff974e", "columnNames": ["condition_slug"], "isUnique": false}, {"name": "IDX_e468bac14faacccda8afd853e2", "columnNames": ["applicable_for"], "isUnique": false}, {"name": "IDX_07909683c386c4cf1cd8a50dea", "columnNames": ["category_id", "active"], "isUnique": false}]}, {"tableName": "rate_card_fee_items", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "rate_card_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "label", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "200"}, {"databaseName": "fee_type", "type": "rate_card_fee_items_fee_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["legal_fee", "disbursement", "conditional_fee", "tax", "admin_fee"], "length": ""}, {"databaseName": "category_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "100"}, {"databaseName": "range_start", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 15, "scale": 2}, {"databaseName": "range_end", "type": "numeric", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 15, "scale": 2}, {"databaseName": "condition_slug", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "100"}, {"databaseName": "net_fee", "type": "numeric", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 10, "scale": 2}, {"databaseName": "vat_fee", "type": "numeric", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": "", "precision": 10, "scale": 2}, {"databaseName": "total_fee", "type": "numeric", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 10, "scale": 2}, {"databaseName": "vat_type", "type": "rate_card_fee_items_vat_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "exc", "enum": ["inc", "exc", "no"], "length": ""}, {"databaseName": "applicable_for", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "50"}, {"databaseName": "per_party", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "dynamic", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "active", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "display_order", "type": "integer", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": ""}, {"databaseName": "notes", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": true, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": true, "default": "now()", "length": ""}], "relations": [{"propertyName": "rateCard", "type": "many-to-one", "joinColumns": [{"databaseName": "rate_card_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "rate_cards"}}], "indices": []}, {"tableName": "documents", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "folder_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "s3_key", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "s3_bucket", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "file_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "file_extension", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "mime_type", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "size_in_bytes", "type": "bigint", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "checksum", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "last_modified_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}, {"propertyName": "folder", "type": "many-to-one", "joinColumns": [{"databaseName": "folder_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "document_folders"}}], "indices": []}, {"tableName": "document_folders", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "parent_folder_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "path", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}, {"propertyName": "parentFolder", "type": "many-to-one", "joinColumns": [{"databaseName": "parent_folder_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "document_folders"}}, {"propertyName": "childFolders", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "document_folders"}}, {"propertyName": "documents", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "documents"}}], "indices": []}, {"tableName": "document_access", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "document_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "user_id", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "permission_level", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "read", "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "expires_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "document", "type": "many-to-one", "joinColumns": [{"databaseName": "document_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "documents"}}], "indices": []}, {"tableName": "document_audit", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "document_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "action_type", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "action_details", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "performed_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "performed_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "ip_address", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "user_agent", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "document_version_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "document", "type": "many-to-one", "joinColumns": [{"databaseName": "document_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "documents"}}], "indices": []}, {"tableName": "document_workflows", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "document_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "workflow_type", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "current_state", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "workflow_data", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "completed_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "due_date", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "assigned_to", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "document", "type": "many-to-one", "joinColumns": [{"databaseName": "document_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "documents"}}], "indices": []}, {"tableName": "promo_codes", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "code", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "discount", "type": "integer", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "expiration_date", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "status", "type": "promo_codes_status_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "active", "enum": ["active", "inactive", "expired"], "length": ""}, {"databaseName": "usage_limit", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "usage_count", "type": "integer", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": true, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": true, "default": "now()", "length": ""}], "relations": [], "indices": [{"name": "IDX_c702327bdf1b286f73c4f1fc9b", "columnNames": ["code", "status"], "isUnique": false}]}, {"tableName": "quotes", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "quote_number", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "session_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "transaction_type", "type": "quotes_transaction_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["buy", "sell", "remortgage"], "length": ""}, {"databaseName": "property_value", "type": "numeric", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 15, "scale": 2}, {"databaseName": "property_address", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "property_conditions", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "client_details", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "quote_breakdown", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "total_amount", "type": "numeric", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": "", "precision": 15, "scale": 2}, {"databaseName": "promo_code", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "discount_amount", "type": "numeric", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": "", "precision": 15, "scale": 2}, {"databaseName": "status", "type": "quotes_status_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "draft", "enum": ["draft", "sent", "accepted", "expired"], "length": ""}, {"databaseName": "expires_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": true, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": true, "default": "now()", "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}], "indices": [{"name": "IDX_f2d0db8ce3af5ac77786282fdd", "columnNames": ["created_at"], "isUnique": false}, {"name": "IDX_e1fc1db17214b25db2adc52ffd", "columnNames": ["status"], "isUnique": false}, {"name": "IDX_bb4f78e8dad7ac6945681a9be8", "columnNames": ["case_id"], "isUnique": false}, {"name": "IDX_2a1a9f328079970d52f2a6b28e", "columnNames": ["session_id"], "isUnique": false}]}, {"tableName": "template_party_associations", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "template_id", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "party_type", "type": "template_party_associations_party_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["CLIENT", "LAND_REGISTRY", "ATTENDANCE_NOTES_BILLING", "SOLICITOR", "AGENT_SOLICITOR", "BANK_BUILDING_SOCIETY_SOLICITOR", "INDIVIDUAL_SOLICITOR", "LANDLORD_SOLICITOR", "MANAGEMENT_COMPANY", "OTHER"], "length": ""}], "relations": [], "indices": []}, {"tableName": "document_templates", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "template_type", "type": "document_templates_template_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["CLIENT_ONBOARDING", "CASE_DOCUMENTS", "LEGAL_CORRESPONDENCE", "BUSINESS_DOCUMENTS"], "length": ""}, {"databaseName": "category", "type": "document_templates_category_enum", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["WELCOME_PACK", "ENGAGEMENT_LETTER", "RETAINER_AGREEMENT", "CASE_OPENING_LETTER", "STATUS_UPDATE", "COURT_FILING", "DEMAND_LETTER", "SETTLEMENT_OFFER", "LEGAL_OPINION", "INVOICE", "PAYMENT_REMINDER", "TIME_SHEET", "SOLICITOR_CORRESPONDENCE", "EXCHANGE_CONFIRMATION", "COMPLETION_NOTICE", "PURCHASE_COMMUNICATION", "SALE_COMMUNICATION", "REMORTGAGE_COMMUNICATION", "CONTRACT_REQUEST", "OTHER"], "length": ""}, {"databaseName": "file_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "file_path", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "s3_key", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "s3_bucket", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "mime_type", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "length": ""}, {"databaseName": "size_in_bytes", "type": "bigint", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "checksum", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "required_tokens", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "[]", "length": ""}, {"databaseName": "optional_tokens", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "[]", "length": ""}, {"databaseName": "detected_tokens", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "[]", "length": ""}, {"databaseName": "generation_triggers", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "[]", "length": ""}, {"databaseName": "allowed_case_types", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "[]", "length": ""}, {"databaseName": "status", "type": "document_templates_status_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "DRAFT", "enum": ["DRAFT", "ACTIVE", "INACTIVE", "ARCHIVED"], "length": ""}, {"databaseName": "is_active", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "{}", "length": ""}, {"databaseName": "auto_attach_to_case", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "auto_email_to_client", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "email_template_type", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "output_file_name_template", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "last_modified_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "last_used_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "usage_count", "type": "integer", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": ""}], "relations": [{"propertyName": "generations", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "document_generations"}}, {"propertyName": "partyAssociations", "type": "one-to-many", "joinColumns": [], "inverseEntityMetadata": {"tableName": "template_party_associations"}}], "indices": []}, {"tableName": "document_generations", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "template_id", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "case_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "client_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "generated_document_id", "type": "uuid", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "status", "type": "document_generations_status_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "PENDING", "enum": ["PENDING", "PROCESSING", "COMPLETED", "FAILED", "CANCELLED"], "length": ""}, {"databaseName": "priority", "type": "document_generations_priority_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "NORMAL", "enum": ["LOW", "NORMAL", "HIGH", "URGENT"], "length": ""}, {"databaseName": "trigger_type", "type": "document_generations_trigger_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["MANUAL", "CASE_CREATED", "CASE_STATUS_CHANGE", "CLIENT_ONBOARDED", "SCHEDULED"], "length": ""}, {"databaseName": "template_data", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "{}", "length": ""}, {"databaseName": "generation_options", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "{}", "length": ""}, {"databaseName": "job_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "queue_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "document-generation", "length": ""}, {"databaseName": "started_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "completed_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "processing_time_ms", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "error_message", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "error_details", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "retry_count", "type": "integer", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": ""}, {"databaseName": "max_retries", "type": "integer", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 3, "length": ""}, {"databaseName": "output_file_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "output_s3_key", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "output_s3_bucket", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "output_file_size", "type": "bigint", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "output_checksum", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "email_sent", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "email_sent_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "email_message_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "execution_log", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "[]", "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "{}", "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "last_modified_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "case", "type": "many-to-one", "joinColumns": [{"databaseName": "case_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "cases"}}, {"propertyName": "generatedDocument", "type": "many-to-one", "joinColumns": [{"databaseName": "generated_document_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "documents"}}], "indices": []}, {"tableName": "quote_audit", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "quote_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "action", "type": "quote_audit_action_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["CREATED", "UPDATED", "STATUS_CHANGED", "STATUS_DRAFT", "STATUS_SENT", "STATUS_ACCEPTED", "STATUS_EXPIRED", "CALCULATED", "RECALCULATED", "PROMO_CODE_APPLIED", "PROMO_CODE_REMOVED", "CONVERTED_TO_CASE", "EMAIL_SENT", "EMAIL_OPENED", "EMAIL_CLICKED", "VIEWED", "DOWNLOADED", "SHARED", "EXPORTED", "ASSIGNED", "UNASSIGNED", "NOTE_ADDED", "NOTE_UPDATED", "NOTE_DELETED", "ATTACHMENT_ADDED", "ATTACHMENT_REMOVED", "CLIENT_UPDATED", "PROPERTY_DETAILS_UPDATED", "QUOTE_SENT_TO_CLIENT", "QUOTE_ACCEPTED_BY_CLIENT", "QUOTE_REJECTED_BY_CLIENT", "FOLLOW_UP_SENT", "REMINDER_SET", "DEADLINE_APPROACHING", "DEADLINE_MISSED"], "length": ""}, {"databaseName": "performed_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "performed_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "performed_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "ip_address", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "details", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "user_agent", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "session_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [{"propertyName": "quote", "type": "many-to-one", "joinColumns": [{"databaseName": "quote_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "quotes"}}], "indices": []}, {"tableName": "quote_attachments", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "quote_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "document_id", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "filename", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "url", "type": "text", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "file_size", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "mime_type", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "uploaded_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "uploaded_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "uploaded_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "attachment_type", "type": "quote_attachments_attachment_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["QUOTE_PDF", "CLIENT_ID", "PROPERTY_REPORT", "LEGAL_SEARCH_RESULT", "SURVEY_REPORT", "MORTGAGE_OFFER", "INSURANCE_DOCUMENT", "CONTRACT_DRAFT", "BANK_STATEMENT", "PAYSLIP", "UTILITY_BILL", "COUNCIL_TAX_BILL", "LAND_REGISTRY_DOCUMENT", "SEARCH_REPORT", "VALUATION_REPORT", "SURVEY_REPORT_BUILDING", "SURVEY_REPORT_STRUCTURAL", "ENERGY_PERFORMANCE_CERTIFICATE", "FLOOD_RISK_REPORT", "OTHER"], "length": ""}, {"databaseName": "is_public", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "is_required", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "expires_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "quote", "type": "many-to-one", "joinColumns": [{"databaseName": "quote_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "quotes"}}], "indices": []}, {"tableName": "quote_communications", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "quote_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "type", "type": "quote_communications_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "enum": ["EMAIL", "CALL", "SMS", "NOTE", "SYSTEM_UPDATE", "DOCUMENT_UPLOAD", "STATUS_CHANGE", "REMINDER", "FOLLOW_UP", "QUOTE_SENT", "QUOTE_VIEWED", "QUOTE_ACCEPTED", "QUOTE_REJECTED", "PAYMENT_RECEIVED", "APPOINTMENT_SCHEDULED", "APPOINTMENT_COMPLETED", "CLIENT_INQUIRY", "STAFF_RESPONSE", "ESCALATION", "RESOLUTION"], "length": ""}, {"databaseName": "direction", "type": "quote_communications_direction_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "INTERNAL", "enum": ["INBOUND", "OUTBOUND", "INTERNAL", "SYSTEM"], "length": ""}, {"databaseName": "priority", "type": "quote_communications_priority_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "MEDIUM", "enum": ["LOW", "MEDIUM", "HIGH", "URGENT"], "length": ""}, {"databaseName": "subject", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "body", "type": "text", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "sender", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "sender_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "sender_email", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "recipient", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "recipient_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "recipient_email", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "sent_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "is_read", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "read_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "read_by", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "read_by_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "attachments", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "is_archived", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "archived_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "archived_by", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "follow_up_required", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "follow_up_date", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "follow_up_assigned_to", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "follow_up_assigned_to_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "quote", "type": "many-to-one", "joinColumns": [{"databaseName": "quote_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "quotes"}}], "indices": []}, {"tableName": "quote_client_call_notes", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "quote_id", "type": "uuid", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "call_type", "type": "quote_client_call_notes_call_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "OTHER", "enum": ["INBOUND", "OUTBOUND", "FOLLOW_UP", "CONSULTATION", "QUOTE_DISCUSSION", "OBJECTION_HANDLING", "CLOSING", "SUPPORT", "COMPLAINT", "OTHER"], "length": ""}, {"databaseName": "call_outcome", "type": "quote_client_call_notes_call_outcome_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "SUCCESSFUL", "enum": ["SUCCESSFUL", "NO_ANSWER", "BUSY", "VOICEMAIL", "CALLBACK_REQUESTED", "APPOINTMENT_SCHEDULED", "QUOTE_REQUESTED", "QUOTE_ACCEPTED", "QUOTE_REJECTED", "OBJECTION_RAISED", "OBJECTION_RESOLVED", "COMPLAINT_RESOLVED", "FOLLOW_UP_REQUIRED", "UNSUCCESSFUL"], "length": ""}, {"databaseName": "call_priority", "type": "quote_client_call_notes_call_priority_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "MEDIUM", "enum": ["LOW", "MEDIUM", "HIGH", "URGENT"], "length": ""}, {"databaseName": "call_date", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "call_duration", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "client_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "client_phone", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "client_email", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "staff_member", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "staff_member_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "callSummary", "type": "text", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "discussionPoints", "type": "text", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "clientConcerns", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "objectionsRaised", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "objectionsHandled", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "nextSteps", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "follow_up_required", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "follow_up_date", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "follow_up_assigned_to", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "follow_up_assigned_to_name", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "followUpNotes", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "quote_related", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "quote_discussed", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "quote_feedback", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "client_satisfaction", "type": "integer", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "callMetadata", "type": "jsonb", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "is_archived", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": false, "length": ""}, {"databaseName": "archived_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "archived_by", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}], "relations": [{"propertyName": "quote", "type": "many-to-one", "joinColumns": [{"databaseName": "quote_id", "referencedColumn": "id"}], "inverseEntityMetadata": {"tableName": "quotes"}}], "indices": []}, {"tableName": "custom_tokens", "columns": [{"databaseName": "id", "type": "uuid", "isNullable": false, "isPrimary": true, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "token_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": true, "unique": true, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "description", "type": "text", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "token_type", "type": "custom_tokens_token_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "CUSTOM", "enum": ["SYSTEM", "CUSTOM"], "length": ""}, {"databaseName": "data_type", "type": "custom_tokens_data_type_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "STRING", "enum": ["STRING", "NUMBER", "DATE", "BOOLEAN", "CURRENCY", "EMAIL", "PHONE", "ADDRESS"], "length": ""}, {"databaseName": "status", "type": "custom_tokens_status_enum", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "ACTIVE", "enum": ["ACTIVE", "INACTIVE", "DEPRECATED"], "length": ""}, {"databaseName": "is_active", "type": "boolean", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": true, "length": ""}, {"databaseName": "entity_name", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "field_path", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "transformation_config", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "{}", "length": ""}, {"databaseName": "validation_config", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "{}", "length": ""}, {"databaseName": "category", "type": "character varying", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "tags", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "[]", "length": ""}, {"databaseName": "usage_count", "type": "integer", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": 0, "length": ""}, {"databaseName": "last_used_at", "type": "timestamp", "isNullable": true, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "compatible_template_types", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "[]", "length": ""}, {"databaseName": "compatible_case_types", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "[]", "length": ""}, {"databaseName": "metadata", "type": "jsonb", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "{}", "length": ""}, {"databaseName": "created_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}, {"databaseName": "created_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "updated_at", "type": "timestamp", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "default": "now()", "length": ""}, {"databaseName": "last_modified_by", "type": "character varying", "isNullable": false, "isPrimary": false, "isUnique": false, "unique": false, "isCreateDate": false, "isUpdateDate": false, "length": ""}], "relations": [], "indices": [{"name": "IDX_6ab42e41b96333d93bb3231cd3", "columnNames": ["token_name"], "isUnique": true}, {"name": "IDX_e03ed9c4345d3c68ed3c414f2b", "columnNames": ["entity_name", "field_path"], "isUnique": false}, {"name": "IDX_e5ed1327099b32ebcceb2cb1ee", "columnNames": ["token_type", "status", "is_active"], "isUnique": false}]}]}