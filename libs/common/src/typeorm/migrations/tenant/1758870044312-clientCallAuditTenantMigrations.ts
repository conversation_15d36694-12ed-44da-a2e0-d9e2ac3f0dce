import { MigrationInterface, QueryRunner } from 'typeorm';

export class ClientCallAuditTenantMigrations1758870044312 implements MigrationInterface {
    name = 'ClientCallAuditTenantMigrations1758870044312';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TYPE "quote_communications_type_enum" AS ENUM('EMAIL', 'CALL', 'SMS', 'NOTE', 'SYSTEM_UPDATE', 'DOCUMENT_UPLOAD', 'STATUS_CHANGE', 'REMINDER', 'FOLLOW_UP', 'QUOTE_SENT', 'QUOTE_VIEWED', 'QUOTE_ACCEPTED', 'QUOTE_REJECTED', 'PAYMENT_RECEIVED', 'APPOINTMENT_SCHEDULED', 'APPOINTMENT_COMPLETED', 'CLIENT_INQUIRY', 'STAFF_RESPONSE', 'ESCALATION', 'RESOLUTION')`
        );

        await queryRunner.query(
            `CREATE TYPE "quote_communications_direction_enum" AS ENUM('INBOUND', 'OUTBOUND', 'INTERNAL', 'SYSTEM')`
        );

        await queryRunner.query(
            `CREATE TYPE "quote_communications_priority_enum" AS ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT')`
        );

        await queryRunner.query(
            `CREATE TYPE "quote_client_call_notes_call_type_enum" AS ENUM('INBOUND', 'OUTBOUND', 'FOLLOW_UP', 'CONSULTATION', 'QUOTE_DISCUSSION', 'OBJECTION_HANDLING', 'CLOSING', 'SUPPORT', 'COMPLAINT', 'OTHER')`
        );

        await queryRunner.query(
            `CREATE TYPE "quote_client_call_notes_call_outcome_enum" AS ENUM('SUCCESSFUL', 'NO_ANSWER', 'BUSY', 'VOICEMAIL', 'CALLBACK_REQUESTED', 'APPOINTMENT_SCHEDULED', 'QUOTE_REQUESTED', 'QUOTE_ACCEPTED', 'QUOTE_REJECTED', 'OBJECTION_RAISED', 'OBJECTION_RESOLVED', 'COMPLAINT_RESOLVED', 'FOLLOW_UP_REQUIRED', 'UNSUCCESSFUL')`
        );

        await queryRunner.query(
            `CREATE TYPE "quote_client_call_notes_call_priority_enum" AS ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT')`
        );

        await queryRunner.query(`CREATE TABLE "quote_communications" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "quote_id" UUID NOT NULL,
            "type" "quote_communications_type_enum" NOT NULL,
            "direction" "quote_communications_direction_enum" NOT NULL DEFAULT 'INTERNAL',
            "priority" "quote_communications_priority_enum" NOT NULL DEFAULT 'MEDIUM',
            "subject" CHARACTER VARYING NOT NULL,
            "body" TEXT NOT NULL,
            "sender" CHARACTER VARYING NOT NULL,
            "sender_name" CHARACTER VARYING,
            "sender_email" CHARACTER VARYING,
            "recipient" CHARACTER VARYING,
            "recipient_name" CHARACTER VARYING,
            "recipient_email" CHARACTER VARYING,
            "sent_at" TIMESTAMP NOT NULL DEFAULT now(),
            "is_read" BOOLEAN NOT NULL DEFAULT false,
            "read_at" TIMESTAMP,
            "read_by" CHARACTER VARYING,
            "read_by_name" CHARACTER VARYING,
            "attachments" JSONB,
            "metadata" JSONB,
            "is_archived" BOOLEAN NOT NULL DEFAULT false,
            "archived_at" TIMESTAMP,
            "archived_by" CHARACTER VARYING,
            "follow_up_required" BOOLEAN NOT NULL DEFAULT false,
            "follow_up_date" TIMESTAMP,
            "follow_up_assigned_to" CHARACTER VARYING,
            "follow_up_assigned_to_name" CHARACTER VARYING,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "FK_78732063" FOREIGN KEY ("quote_id") REFERENCES "quotes"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "quote_client_call_notes" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "quote_id" UUID NOT NULL,
            "call_type" "quote_client_call_notes_call_type_enum" NOT NULL DEFAULT 'OTHER',
            "call_outcome" "quote_client_call_notes_call_outcome_enum" NOT NULL DEFAULT 'SUCCESSFUL',
            "call_priority" "quote_client_call_notes_call_priority_enum" NOT NULL DEFAULT 'MEDIUM',
            "call_date" TIMESTAMP NOT NULL,
            "call_duration" INTEGER,
            "client_name" CHARACTER VARYING NOT NULL,
            "client_phone" CHARACTER VARYING,
            "client_email" CHARACTER VARYING,
            "staff_member" CHARACTER VARYING NOT NULL,
            "staff_member_name" CHARACTER VARYING NOT NULL,
            "callSummary" TEXT NOT NULL,
            "discussionPoints" TEXT NOT NULL,
            "clientConcerns" TEXT,
            "objectionsRaised" TEXT,
            "objectionsHandled" TEXT,
            "nextSteps" TEXT,
            "follow_up_required" BOOLEAN NOT NULL DEFAULT false,
            "follow_up_date" TIMESTAMP,
            "follow_up_assigned_to" CHARACTER VARYING,
            "follow_up_assigned_to_name" CHARACTER VARYING,
            "followUpNotes" TEXT,
            "quote_related" BOOLEAN NOT NULL DEFAULT true,
            "quote_discussed" BOOLEAN NOT NULL DEFAULT false,
            "quote_feedback" TEXT,
            "client_satisfaction" INTEGER,
            "callMetadata" JSONB,
            "is_archived" BOOLEAN NOT NULL DEFAULT false,
            "archived_at" TIMESTAMP,
            "archived_by" CHARACTER VARYING,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "FK_5a6062ac" FOREIGN KEY ("quote_id") REFERENCES "quotes"("id")
        )`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "quote_client_call_notes"`);

        await queryRunner.query(`DROP TABLE "quote_communications"`);

        await queryRunner.query(`DROP TYPE "quote_communications_type_enum"`);

        await queryRunner.query(`DROP TYPE "quote_communications_direction_enum"`);

        await queryRunner.query(`DROP TYPE "quote_communications_priority_enum"`);

        await queryRunner.query(`DROP TYPE "quote_client_call_notes_call_type_enum"`);

        await queryRunner.query(`DROP TYPE "quote_client_call_notes_call_outcome_enum"`);

        await queryRunner.query(`DROP TYPE "quote_client_call_notes_call_priority_enum"`);
    }
}
