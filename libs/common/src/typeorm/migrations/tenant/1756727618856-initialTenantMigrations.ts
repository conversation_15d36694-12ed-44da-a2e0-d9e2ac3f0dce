import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialTenantMigrations1756727618856 implements MigrationInterface {
    name = 'InitialTenantMigrations1756727618856';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TYPE "case_attachments_document_type_enum" AS ENUM('PLEADING', 'CONTRACT', 'EVIDENCE', 'CORRESPONDENCE', 'COURT_ORDER', 'INVOICE', 'MEMO', 'RESEARCH', 'OTHER')`
        );

        await queryRunner.query(
            `CREATE TYPE "case_audit_action_enum" AS ENUM('CREATED', 'UPDATED', 'CLIENT_UPDATED', 'STATUS_CHANGED', 'STATUS_DRAFT', 'STATUS_SUBMITTED', 'STATUS_APPROVED', 'STATUS_DECLINED', 'STATUS_IN_PROGRESS', 'STATUS_ON_HOLD', 'STATUS_CLOSED', 'STATUS_ARCHIVED', 'ASSIGNED', 'UNASSIGNED', 'REASSIGNED', 'NOTE_ADDED', 'NOTE_UPDATED', 'NOTE_DELETED', 'ATTACHMENT_ADDED', 'ATTACHMENT_UPDATED', 'ATTACHMENT_REMOVED', 'CONTACT_ADDED', 'CONTACT_UPDATED', 'CONTACT_DELETED', 'CASE_RELATION_ADDED', 'CASE_RELATION_DELETED', 'ACCESSED', 'ACCESS_DENIED', 'EXPORTED', 'CLIENT_CREATED', 'REMINDER_SET', 'DEADLINE_APPROACHING', 'DEADLINE_MISSED', 'STATUS_UNDER_REVIEW', 'STATUS_ASSIGNED', 'STATUS_PENDING_APPROVAL', 'STATUS_REJECTED', 'STATUS_RESOLVED', 'STATUS_REOPENED', 'PERMISSION_CHECK')`
        );

        await queryRunner.query(
            `CREATE TYPE "case_contacts_type_enum" AS ENUM('CLIENT', 'CO_COUNSEL', 'OPPOSING_COUNSEL', 'WITNESS', 'EXPERT', 'JUDGE', 'OTHER')`
        );

        await queryRunner.query(
            `CREATE TYPE "case_events_category_enum" AS ENUM('INTAKE', 'PLEADINGS', 'DISCOVERY', 'MOTIONS', 'HEARINGS', 'TRIAL', 'POST_TRIAL', 'DEADLINE', 'ADMINISTRATIVE', 'COMMUNICATION', 'OTHER')`
        );

        await queryRunner.query(
            `CREATE TYPE "case_events_type_enum" AS ENUM('INITIAL_CONSULTATION', 'CONFLICT_CHECK', 'ENGAGEMENT_LETTER', 'CASE_OPENED', 'STATUTE_OF_LIMITATIONS', 'COMPLAINT_FILED', 'SUMMONS_ISSUED', 'SUMMONS_SERVED', 'ANSWER_FILED', 'COUNTERCLAIM_FILED', 'CROSS_CLAIM_FILED', 'AMENDED_PLEADING', 'INTERROGATORIES_SERVED', 'INTERROGATORIES_ANSWERED', 'DOCUMENT_REQUEST_SERVED', 'DOCUMENT_PRODUCTION', 'DEPOSITION_SCHEDULED', 'DEPOSITION_COMPLETED', 'EXPERT_DISCLOSURE', 'DISCOVERY_CUTOFF', 'DISCOVERY_DISPUTE', 'MOTION_TO_COMPEL', 'MOTION_TO_DISMISS', 'SUMMARY_JUDGMENT_MOTION', 'MOTION_HEARING', 'MOTION_IN_LIMINE', 'CASE_MANAGEMENT_CONFERENCE', 'STATUS_CONFERENCE', 'SETTLEMENT_CONFERENCE', 'MEDIATION', 'PRE_TRIAL_CONFERENCE', 'TRIAL_START', 'TRIAL_END', 'WITNESS_LIST_FILED', 'EXHIBIT_LIST_FILED', 'JURY_SELECTION', 'OPENING_ARGUMENTS', 'CLOSING_ARGUMENTS', 'VERDICT', 'JUDGMENT_ENTERED', 'POST_TRIAL_MOTION', 'NOTICE_OF_APPEAL', 'APPELLATE_BRIEF', 'ORAL_ARGUMENTS', 'APPELLATE_DECISION', 'COURT_DEADLINE', 'INTERNAL_DEADLINE', 'RESPONSE_DEADLINE', 'COURT_FEE_PAYMENT', 'BILLING_MILESTONE', 'COMPLIANCE_AUDIT', 'CLIENT_MEETING', 'CLIENT_CALL', 'TEAM_MEETING', 'OPPOSING_COUNSEL_COMMUNICATION', 'COURT_COMMUNICATION', 'OTHER')`
        );

        await queryRunner.query(
            `CREATE TYPE "properties_property_type_enum" AS ENUM('RESIDENTIAL', 'COMMERCIAL', 'LAND', 'LEASEHOLD', 'FREEHOLD', 'SHARED_OWNERSHIP', 'OTHER')`
        );

        await queryRunner.query(
            `CREATE TYPE "properties_tenure_enum" AS ENUM('FREEHOLD', 'LEASEHOLD', 'COMMONHOLD', 'SHARED_FREEHOLD')`
        );

        await queryRunner.query(
            `CREATE TYPE "properties_status_enum" AS ENUM('AVAILABLE', 'UNDER_OFFER', 'SOLD', 'WITHDRAWN', 'LET', 'VACANT')`
        );

        await queryRunner.query(
            `CREATE TYPE "cases_status_enum" AS ENUM('DRAFT', 'SUBMITTED', 'UNDER_REVIEW', 'ASSIGNED', 'IN_PROGRESS', 'ON_HOLD', 'PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'RESOLVED', 'CLOSED', 'REOPENED', 'ARCHIVED')`
        );

        await queryRunner.query(
            `CREATE TYPE "cases_priority_enum" AS ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT')`
        );

        await queryRunner.query(
            `CREATE TYPE "cases_type_enum" AS ENUM('LITIGATION', 'CORPORATE', 'CONVEYANCING', 'INTELLECTUAL_PROPERTY', 'FAMILY', 'CRIMINAL', 'PURCHASE', 'SALE', 'REMORTGAGE', 'OTHER', 'REAL_ESTATE')`
        );

        await queryRunner.query(
            `CREATE TYPE "case_relations_type_enum" AS ENUM('PARENT', 'CHILD', 'RELATED', 'PREDECESSOR', 'SUCCESSOR')`
        );

        await queryRunner.query(
            `CREATE TYPE "milestones_status_enum" AS ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED')`
        );

        await queryRunner.query(
            `CREATE TYPE "task_status" AS ENUM('OPEN', 'IN_PROGRESS', 'BLOCKED', 'DONE')`
        );

        await queryRunner.query(
            `CREATE TYPE "task_priority" AS ENUM('HIGHEST', 'HIGH', 'MEDIUM', 'LOW', 'LOWEST')`
        );

        await queryRunner.query(
            `CREATE TYPE "rate_cards_provider_code_enum" AS ENUM('arrow', 'yopa', 'trussle', 'tmg', 'sesame', 'remax', 'rayner', 'pepper', 'optimus', 'optimus_bid', 'msm', 'molo', 'mojo', 'leas', 'landc', 'keyclub', 'john_charcol', 'independent', 'haysto', 'gazeal', 'fort_advice', 'fluent', 'ekeeper', 'charles_cameron', 'custom')`
        );

        await queryRunner.query(
            `CREATE TYPE "rate_cards_status_enum" AS ENUM('active', 'inactive', 'draft')`
        );

        await queryRunner.query(
            `CREATE TYPE "fee_items_vat_type_enum" AS ENUM('inc', 'exc', 'no')`
        );

        await queryRunner.query(
            `CREATE TYPE "rate_card_fee_items_fee_type_enum" AS ENUM('legal_fee', 'disbursement', 'conditional_fee', 'tax', 'admin_fee')`
        );

        await queryRunner.query(
            `CREATE TYPE "rate_card_fee_items_vat_type_enum" AS ENUM('inc', 'exc', 'no')`
        );

        await queryRunner.query(
            `CREATE TYPE "promo_codes_status_enum" AS ENUM('active', 'inactive', 'expired')`
        );

        await queryRunner.query(
            `CREATE TYPE "quotes_transaction_type_enum" AS ENUM('buy', 'sell', 'remortgage')`
        );

        await queryRunner.query(
            `CREATE TYPE "quotes_status_enum" AS ENUM('draft', 'sent', 'accepted', 'expired')`
        );

        await queryRunner.query(
            `CREATE TYPE "template_party_associations_party_type_enum" AS ENUM('CLIENT', 'LAND_REGISTRY', 'ATTENDANCE_NOTES_BILLING', 'SOLICITOR', 'AGENT_SOLICITOR', 'BANK_BUILDING_SOCIETY_SOLICITOR', 'INDIVIDUAL_SOLICITOR', 'LANDLORD_SOLICITOR', 'MANAGEMENT_COMPANY', 'OTHER')`
        );

        await queryRunner.query(
            `CREATE TYPE "document_templates_template_type_enum" AS ENUM('CLIENT_ONBOARDING', 'CASE_DOCUMENTS', 'LEGAL_CORRESPONDENCE', 'BUSINESS_DOCUMENTS')`
        );

        await queryRunner.query(
            `CREATE TYPE "document_templates_category_enum" AS ENUM('WELCOME_PACK', 'ENGAGEMENT_LETTER', 'RETAINER_AGREEMENT', 'CASE_OPENING_LETTER', 'STATUS_UPDATE', 'COURT_FILING', 'DEMAND_LETTER', 'SETTLEMENT_OFFER', 'LEGAL_OPINION', 'INVOICE', 'PAYMENT_REMINDER', 'TIME_SHEET', 'SOLICITOR_CORRESPONDENCE', 'EXCHANGE_CONFIRMATION', 'COMPLETION_NOTICE', 'PURCHASE_COMMUNICATION', 'SALE_COMMUNICATION', 'REMORTGAGE_COMMUNICATION', 'CONTRACT_REQUEST', 'OTHER')`
        );

        await queryRunner.query(
            `CREATE TYPE "document_templates_status_enum" AS ENUM('DRAFT', 'ACTIVE', 'INACTIVE', 'ARCHIVED')`
        );

        await queryRunner.query(
            `CREATE TYPE "document_generations_status_enum" AS ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED')`
        );

        await queryRunner.query(
            `CREATE TYPE "document_generations_priority_enum" AS ENUM('LOW', 'NORMAL', 'HIGH', 'URGENT')`
        );

        await queryRunner.query(
            `CREATE TYPE "document_generations_trigger_type_enum" AS ENUM('MANUAL', 'CASE_CREATED', 'CASE_STATUS_CHANGE', 'CLIENT_ONBOARDED', 'SCHEDULED')`
        );

        await queryRunner.query(
            `CREATE TYPE "custom_tokens_token_type_enum" AS ENUM('SYSTEM', 'CUSTOM')`
        );

        await queryRunner.query(
            `CREATE TYPE "custom_tokens_data_type_enum" AS ENUM('STRING', 'NUMBER', 'DATE', 'BOOLEAN', 'CURRENCY', 'EMAIL', 'PHONE', 'ADDRESS')`
        );

        await queryRunner.query(
            `CREATE TYPE "custom_tokens_status_enum" AS ENUM('ACTIVE', 'INACTIVE', 'DEPRECATED')`
        );

        await queryRunner.query(`CREATE TABLE "tenant_roles" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "description" CHARACTER VARYING,
            "permissions" JSONB,
            "enabled" BOOLEAN NOT NULL DEFAULT true,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "UQ_7b9f6e7e" UNIQUE ("name")
        )`);

        await queryRunner.query(`CREATE TABLE "user_profiles" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "username" CHARACTER VARYING NOT NULL,
            "email" CHARACTER VARYING NOT NULL,
            "first_name" CHARACTER VARYING,
            "department" CHARACTER VARYING,
            "last_name" CHARACTER VARYING,
            "enabled" BOOLEAN NOT NULL DEFAULT true,
            "email_verified" BOOLEAN NOT NULL DEFAULT false,
            "keycloak_id" CHARACTER VARYING,
            "additional_info" JSONB DEFAULT '{}',
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "user_id" CHARACTER VARYING
        )`);

        await queryRunner.query(`CREATE TABLE "clients" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "email" CHARACTER VARYING,
            "phone" CHARACTER VARYING,
            "address" TEXT,
            "additional_info" JSONB,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now()
        )`);

        await queryRunner.query(`CREATE TABLE "properties" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "full_address" TEXT NOT NULL,
            "address_line_1" CHARACTER VARYING NOT NULL,
            "address_line_2" CHARACTER VARYING,
            "city" CHARACTER VARYING,
            "county" CHARACTER VARYING,
            "postal_code" CHARACTER VARYING NOT NULL,
            "country" CHARACTER VARYING,
            "property_type" "properties_property_type_enum" NOT NULL,
            "tenure" "properties_tenure_enum",
            "status" "properties_status_enum" NOT NULL DEFAULT 'AVAILABLE',
            "bedrooms" INTEGER,
            "bathrooms" INTEGER,
            "receptions" INTEGER,
            "floor_area_sqft" NUMERIC(10,2),
            "floor_area_sqm" NUMERIC(10,2),
            "land_area_sqft" NUMERIC(10,2),
            "land_area_sqm" NUMERIC(10,2),
            "year_built" INTEGER,
            "parking_spaces" INTEGER,
            "garage" BOOLEAN,
            "garden" BOOLEAN,
            "balcony" BOOLEAN,
            "current_value" NUMERIC(12,2),
            "purchase_price" NUMERIC(12,2),
            "asking_price" NUMERIC(12,2),
            "agreed_price" NUMERIC(12,2),
            "valuation_date" DATE,
            "valuer_name" CHARACTER VARYING,
            "valuation_reference" CHARACTER VARYING,
            "title_number" CHARACTER VARYING,
            "land_registry_reference" CHARACTER VARYING,
            "deed_type" CHARACTER VARYING,
            "restrictive_covenants" TEXT,
            "easements" TEXT,
            "lease_expiry_date" DATE,
            "lease_years_remaining" INTEGER,
            "ground_rent" NUMERIC(10,2),
            "service_charge" NUMERIC(10,2),
            "management_company" CHARACTER VARYING,
            "council_tax_band" CHARACTER VARYING,
            "council_tax_amount" NUMERIC(8,2),
            "local_authority" CHARACTER VARYING,
            "planning_permission_required" BOOLEAN,
            "listed_building" BOOLEAN,
            "conservation_area" BOOLEAN,
            "epc_rating" CHARACTER VARYING,
            "epc_expiry_date" DATE,
            "gas_supply" BOOLEAN,
            "electricity_supply" BOOLEAN,
            "water_supply" BOOLEAN,
            "sewerage_connected" BOOLEAN,
            "broadband_available" BOOLEAN,
            "buildings_insurance_required" BOOLEAN,
            "buildings_insurance_provider" CHARACTER VARYING,
            "buildings_insurance_policy_number" CHARACTER VARYING,
            "buildings_insurance_amount" NUMERIC(12,2),
            "survey_type" CHARACTER VARYING,
            "survey_date" DATE,
            "surveyor_name" CHARACTER VARYING,
            "survey_value" NUMERIC(12,2),
            "survey_issues" TEXT,
            "latitude" INTEGER,
            "longitude" INTEGER,
            "nearest_station" CHARACTER VARYING,
            "station_distance_miles" NUMERIC(5,2),
            "school_catchment" CHARACTER VARYING,
            "amenities" JSONB NOT NULL DEFAULT '[]',
            "description" TEXT,
            "internal_notes" TEXT,
            "special_conditions" TEXT,
            "fixtures_and_fittings" TEXT,
            "property_metadata" JSONB NOT NULL DEFAULT '{}',
            "estate_agent_name" CHARACTER VARYING,
            "estate_agent_contact" CHARACTER VARYING,
            "vendor_name" CHARACTER VARYING,
            "vendor_contact" CHARACTER VARYING,
            "vendor_solicitor_name" CHARACTER VARYING,
            "vendor_solicitor_firm" CHARACTER VARYING,
            "vendor_solicitor_contact" CHARACTER VARYING,
            "vendor_solicitor_reference" CHARACTER VARYING,
            "vendor_client_name" CHARACTER VARYING,
            "vendor_solicitor_address_line_1" CHARACTER VARYING,
            "vendor_solicitor_address_line_2" CHARACTER VARYING,
            "vendor_solicitor_city" CHARACTER VARYING,
            "vendor_solicitor_county" CHARACTER VARYING,
            "vendor_solicitor_postal_code" CHARACTER VARYING,
            "vendor_solicitor_country" CHARACTER VARYING,
            "vendor_solicitor_phone" CHARACTER VARYING,
            "vendor_solicitor_email" CHARACTER VARYING,
            "vendor_solicitor_dx" CHARACTER VARYING,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "last_modified_by" CHARACTER VARYING NOT NULL
        )`);

        await queryRunner.query(`CREATE TABLE "cases" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_number" CHARACTER VARYING NOT NULL,
            "title" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "status" "cases_status_enum" NOT NULL DEFAULT 'DRAFT',
            "priority" "cases_priority_enum" NOT NULL DEFAULT 'MEDIUM',
            "type" "cases_type_enum" NOT NULL DEFAULT 'OTHER',
            "client_id" UUID NOT NULL,
            "property_id" UUID,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "deadline" TIMESTAMP,
            "exchange_date" TIMESTAMP,
            "completion_date" TIMESTAMP,
            "deposit_amount" NUMERIC(15,2),
            "chain_position" CHARACTER VARYING,
            "mortgage_required" BOOLEAN NOT NULL DEFAULT false,
            "mortgage_amount" NUMERIC(15,2),
            "lender_name" CHARACTER VARYING,
            "conveyancing_metadata" JSONB NOT NULL DEFAULT '{}',
            CONSTRAINT "FK_64ab2bed" FOREIGN KEY ("client_id") REFERENCES "clients"("id"),
            CONSTRAINT "FK_74bac4c9" FOREIGN KEY ("property_id") REFERENCES "properties"("id"),
            CONSTRAINT "UQ_4271e43c" UNIQUE ("case_number")
        )`);

        await queryRunner.query(`CREATE TABLE "case_notes" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "content" TEXT NOT NULL,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_by_name" CHARACTER VARYING,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "is_pinned" BOOLEAN NOT NULL DEFAULT false,
            "is_private" BOOLEAN NOT NULL DEFAULT false,
            CONSTRAINT "FK_217b863d" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_attachments" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "filename" CHARACTER VARYING NOT NULL,
            "url" TEXT NOT NULL,
            "file_size" INTEGER,
            "mime_type" CHARACTER VARYING,
            "uploaded_by" CHARACTER VARYING NOT NULL,
            "uploaded_by_name" CHARACTER VARYING,
            "uploaded_at" TIMESTAMP NOT NULL DEFAULT now(),
            "description" TEXT,
            "document_type" "case_attachments_document_type_enum" NOT NULL DEFAULT 'OTHER',
            CONSTRAINT "FK_178c494" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_audit" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "action" "case_audit_action_enum" NOT NULL,
            "performed_by" CHARACTER VARYING NOT NULL,
            "performed_by_name" CHARACTER VARYING,
            "performed_at" TIMESTAMP NOT NULL DEFAULT now(),
            "ip_address" CHARACTER VARYING,
            "details" JSONB,
            CONSTRAINT "FK_4a1d1957" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_contacts" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "name" CHARACTER VARYING NOT NULL,
            "email" CHARACTER VARYING,
            "phone" CHARACTER VARYING,
            "address" TEXT,
            "type" "case_contacts_type_enum" NOT NULL DEFAULT 'OTHER',
            "created_by" CHARACTER VARYING NOT NULL,
            "created_by_name" CHARACTER VARYING,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "additional_info" JSONB,
            CONSTRAINT "FK_4e3a99cd" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_events" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "category" "case_events_category_enum" NOT NULL DEFAULT 'OTHER',
            "type" "case_events_type_enum" NOT NULL DEFAULT 'OTHER',
            "title" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "event_date" TIMESTAMP NOT NULL,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_by_name" CHARACTER VARYING,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "metadata" JSONB,
            CONSTRAINT "FK_5ea58bad" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_assignments" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "user_id" UUID NOT NULL,
            "user_name" CHARACTER VARYING,
            "assigned_by" CHARACTER VARYING NOT NULL,
            "assigned_at" TIMESTAMP NOT NULL DEFAULT now(),
            "is_active" BOOLEAN NOT NULL DEFAULT true,
            "notes" TEXT,
            CONSTRAINT "FK_578532fe" FOREIGN KEY ("case_id") REFERENCES "cases"("id"),
            CONSTRAINT "FK_7ea2d287" FOREIGN KEY ("user_id") REFERENCES "user_profiles"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_relations" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "related_case_id" UUID NOT NULL,
            "type" "case_relations_type_enum" NOT NULL DEFAULT 'RELATED',
            "created_by" CHARACTER VARYING NOT NULL,
            "created_by_name" CHARACTER VARYING,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "notes" TEXT,
            CONSTRAINT "FK_61dae6f3" FOREIGN KEY ("case_id") REFERENCES "cases"("id"),
            CONSTRAINT "FK_5ac055df" FOREIGN KEY ("related_case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "milestones" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "case_id" UUID NOT NULL,
            "status" "milestones_status_enum" NOT NULL DEFAULT 'PENDING',
            "progress_percentage" NUMERIC(5,2) NOT NULL DEFAULT 0,
            "target_date" TIMESTAMP,
            "completion_date" TIMESTAMP,
            "sort_order" INTEGER NOT NULL DEFAULT 0,
            "is_default" BOOLEAN NOT NULL DEFAULT false,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "FK_17760eb2" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "tasks" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "title" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "status" "task_status" NOT NULL DEFAULT 'OPEN',
            "priority" "task_priority" NOT NULL DEFAULT 'MEDIUM',
            "due_date" TIMESTAMP,
            "case_id" UUID NOT NULL,
            "milestone_id" UUID,
            "assignee_id" CHARACTER VARYING,
            "is_default" BOOLEAN NOT NULL DEFAULT false,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "FK_437d9cd9" FOREIGN KEY ("case_id") REFERENCES "cases"("id"),
            CONSTRAINT "FK_347db8a5" FOREIGN KEY ("milestone_id") REFERENCES "milestones"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "task_dependencies" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "task_id" UUID NOT NULL,
            "depends_on_id" UUID NOT NULL,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "FK_18f70887" FOREIGN KEY ("task_id") REFERENCES "tasks"("id"),
            CONSTRAINT "FK_3244b8d9" FOREIGN KEY ("depends_on_id") REFERENCES "tasks"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "task_history" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "task_id" UUID NOT NULL,
            "from_status" CHARACTER VARYING,
            "to_status" CHARACTER VARYING NOT NULL,
            "changed_by" CHARACTER VARYING NOT NULL,
            "changed_by_name" CHARACTER VARYING,
            "changed_at" TIMESTAMP NOT NULL DEFAULT now(),
            "metadata" JSONB,
            CONSTRAINT "FK_292ab0" FOREIGN KEY ("task_id") REFERENCES "tasks"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "rate_cards" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "provider_name" CHARACTER VARYING(100) NOT NULL,
            "provider_code" "rate_cards_provider_code_enum" NOT NULL,
            "display_name" CHARACTER VARYING(200) NOT NULL,
            "description" TEXT,
            "version" CHARACTER VARYING(20) NOT NULL DEFAULT '1.0',
            "effective_date" DATE NOT NULL,
            "expiry_date" DATE,
            "status" "rate_cards_status_enum" NOT NULL DEFAULT 'active',
            "is_default" BOOLEAN NOT NULL DEFAULT false,
            "priority" INTEGER NOT NULL DEFAULT 0,
            "metadata" JSONB,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now()
        )`);

        await queryRunner.query(`CREATE TABLE "fee_categories" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "display_order" INTEGER NOT NULL DEFAULT 0,
            "active" BOOLEAN NOT NULL DEFAULT true,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now()
        )`);

        await queryRunner.query(`CREATE TABLE "fee_items" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "label" CHARACTER VARYING NOT NULL,
            "category_id" UUID NOT NULL,
            "range_start" NUMERIC(15,2),
            "range_end" NUMERIC(15,2),
            "condition_slug" CHARACTER VARYING,
            "net_fee" NUMERIC(10,2) NOT NULL DEFAULT 0,
            "vat_fee" NUMERIC(10,2) NOT NULL DEFAULT 0,
            "total_fee" NUMERIC(10,2) NOT NULL DEFAULT 0,
            "vat_type" "fee_items_vat_type_enum" NOT NULL DEFAULT 'inc',
            "applicable_for" TEXT NOT NULL,
            "per_party" BOOLEAN NOT NULL DEFAULT false,
            "dynamic" BOOLEAN NOT NULL DEFAULT false,
            "active" BOOLEAN NOT NULL DEFAULT true,
            "version" INTEGER NOT NULL DEFAULT 1,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "FK_3ba3ce7c" FOREIGN KEY ("category_id") REFERENCES "fee_categories"("id")
        )`);

        await queryRunner.query(
            `CREATE INDEX "IDX_9c1620e5241e7c1a1f3fff974e" ON "fee_items" ("condition_slug")`
        );

        await queryRunner.query(
            `CREATE INDEX "IDX_e468bac14faacccda8afd853e2" ON "fee_items" ("applicable_for")`
        );

        await queryRunner.query(
            `CREATE INDEX "IDX_07909683c386c4cf1cd8a50dea" ON "fee_items" ("category_id", "active")`
        );

        await queryRunner.query(`CREATE TABLE "rate_card_fee_items" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "rate_card_id" UUID NOT NULL,
            "label" CHARACTER VARYING(200) NOT NULL,
            "fee_type" "rate_card_fee_items_fee_type_enum" NOT NULL,
            "category_name" CHARACTER VARYING(100) NOT NULL,
            "range_start" NUMERIC(15,2),
            "range_end" NUMERIC(15,2),
            "condition_slug" CHARACTER VARYING(100),
            "net_fee" NUMERIC(10,2) NOT NULL,
            "vat_fee" NUMERIC(10,2) NOT NULL DEFAULT 0,
            "total_fee" NUMERIC(10,2) NOT NULL,
            "vat_type" "rate_card_fee_items_vat_type_enum" NOT NULL DEFAULT 'exc',
            "applicable_for" CHARACTER VARYING(50) NOT NULL,
            "per_party" BOOLEAN NOT NULL DEFAULT false,
            "dynamic" BOOLEAN NOT NULL DEFAULT false,
            "active" BOOLEAN NOT NULL DEFAULT true,
            "display_order" INTEGER NOT NULL DEFAULT 0,
            "notes" TEXT,
            "metadata" JSONB,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "FK_5e67790d" FOREIGN KEY ("rate_card_id") REFERENCES "rate_cards"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "document_folders" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "case_id" UUID NOT NULL,
            "parent_folder_id" UUID,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "path" TEXT,
            CONSTRAINT "FK_d37fcec" FOREIGN KEY ("case_id") REFERENCES "cases"("id"),
            CONSTRAINT "FK_25804715" FOREIGN KEY ("parent_folder_id") REFERENCES "document_folders"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "documents" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "case_id" UUID NOT NULL,
            "folder_id" UUID,
            "s3_key" CHARACTER VARYING NOT NULL,
            "s3_bucket" CHARACTER VARYING NOT NULL,
            "file_name" CHARACTER VARYING NOT NULL,
            "file_extension" CHARACTER VARYING,
            "mime_type" CHARACTER VARYING NOT NULL,
            "size_in_bytes" BIGINT NOT NULL,
            "checksum" CHARACTER VARYING NOT NULL,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "last_modified_by" CHARACTER VARYING NOT NULL,
            CONSTRAINT "FK_2a6255fd" FOREIGN KEY ("case_id") REFERENCES "cases"("id"),
            CONSTRAINT "FK_23aee23b" FOREIGN KEY ("folder_id") REFERENCES "document_folders"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "document_access" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "document_id" UUID NOT NULL,
            "user_id" CHARACTER VARYING NOT NULL,
            "permission_level" CHARACTER VARYING NOT NULL DEFAULT 'read',
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "expires_at" TIMESTAMP,
            CONSTRAINT "FK_37fe29f8" FOREIGN KEY ("document_id") REFERENCES "documents"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "document_audit" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "document_id" UUID,
            "action_type" CHARACTER VARYING NOT NULL,
            "action_details" JSONB,
            "performed_by" CHARACTER VARYING NOT NULL,
            "performed_at" TIMESTAMP NOT NULL DEFAULT now(),
            "ip_address" CHARACTER VARYING,
            "user_agent" TEXT,
            "document_version_id" CHARACTER VARYING,
            CONSTRAINT "FK_3c86e5c9" FOREIGN KEY ("document_id") REFERENCES "documents"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "document_workflows" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "document_id" UUID NOT NULL,
            "workflow_type" CHARACTER VARYING NOT NULL,
            "current_state" CHARACTER VARYING NOT NULL,
            "workflow_data" JSONB,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "completed_at" TIMESTAMP,
            "due_date" TIMESTAMP,
            "assigned_to" CHARACTER VARYING,
            CONSTRAINT "FK_541bfc90" FOREIGN KEY ("document_id") REFERENCES "documents"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "promo_codes" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "code" CHARACTER VARYING NOT NULL,
            "discount" INTEGER NOT NULL,
            "expiration_date" TIMESTAMP,
            "status" "promo_codes_status_enum" NOT NULL DEFAULT 'active',
            "usage_limit" INTEGER,
            "usage_count" INTEGER NOT NULL DEFAULT 0,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now()
        )`);

        await queryRunner.query(
            `CREATE INDEX "IDX_c702327bdf1b286f73c4f1fc9b" ON "promo_codes" ("code", "status")`
        );

        await queryRunner.query(`CREATE TABLE "quotes" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "quote_number" CHARACTER VARYING NOT NULL,
            "case_id" UUID,
            "session_id" CHARACTER VARYING,
            "transaction_type" "quotes_transaction_type_enum" NOT NULL,
            "property_value" NUMERIC(15,2) NOT NULL,
            "property_address" JSONB NOT NULL,
            "property_conditions" JSONB NOT NULL,
            "client_details" JSONB NOT NULL,
            "quote_breakdown" JSONB NOT NULL,
            "total_amount" NUMERIC(15,2) NOT NULL,
            "promo_code" CHARACTER VARYING,
            "discount_amount" NUMERIC(15,2) NOT NULL DEFAULT 0,
            "status" "quotes_status_enum" NOT NULL DEFAULT 'draft',
            "expires_at" TIMESTAMP NOT NULL,
            "created_by" CHARACTER VARYING,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "FK_2bc9b9c2" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(
            `CREATE INDEX "IDX_f2d0db8ce3af5ac77786282fdd" ON "quotes" ("created_at")`
        );

        await queryRunner.query(
            `CREATE INDEX "IDX_e1fc1db17214b25db2adc52ffd" ON "quotes" ("status")`
        );

        await queryRunner.query(
            `CREATE INDEX "IDX_bb4f78e8dad7ac6945681a9be8" ON "quotes" ("case_id")`
        );

        await queryRunner.query(
            `CREATE INDEX "IDX_2a1a9f328079970d52f2a6b28e" ON "quotes" ("session_id")`
        );

        await queryRunner.query(`CREATE TABLE "template_party_associations" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "template_id" CHARACTER VARYING NOT NULL,
            "party_type" "template_party_associations_party_type_enum" NOT NULL
        )`);

        await queryRunner.query(`CREATE TABLE "document_templates" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "template_type" "document_templates_template_type_enum" NOT NULL,
            "category" "document_templates_category_enum",
            "file_name" CHARACTER VARYING NOT NULL,
            "file_path" CHARACTER VARYING NOT NULL,
            "s3_key" CHARACTER VARYING NOT NULL,
            "s3_bucket" CHARACTER VARYING NOT NULL,
            "mime_type" CHARACTER VARYING NOT NULL DEFAULT 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            "size_in_bytes" BIGINT NOT NULL,
            "checksum" CHARACTER VARYING NOT NULL,
            "required_tokens" JSONB NOT NULL DEFAULT '[]',
            "optional_tokens" JSONB NOT NULL DEFAULT '[]',
            "detected_tokens" JSONB NOT NULL DEFAULT '[]',
            "generation_triggers" JSONB NOT NULL DEFAULT '[]',
            "allowed_case_types" JSONB NOT NULL DEFAULT '[]',
            "status" "document_templates_status_enum" NOT NULL DEFAULT 'DRAFT',
            "is_active" BOOLEAN NOT NULL DEFAULT true,
            "metadata" JSONB NOT NULL DEFAULT '{}',
            "auto_attach_to_case" BOOLEAN NOT NULL DEFAULT false,
            "auto_email_to_client" BOOLEAN NOT NULL DEFAULT false,
            "email_template_type" CHARACTER VARYING,
            "output_file_name_template" CHARACTER VARYING,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "last_modified_by" CHARACTER VARYING NOT NULL,
            "last_used_at" TIMESTAMP,
            "usage_count" INTEGER NOT NULL DEFAULT 0
        )`);

        await queryRunner.query(`CREATE TABLE "document_generations" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "template_id" CHARACTER VARYING NOT NULL,
            "case_id" UUID,
            "client_id" CHARACTER VARYING,
            "generated_document_id" UUID,
            "status" "document_generations_status_enum" NOT NULL DEFAULT 'PENDING',
            "priority" "document_generations_priority_enum" NOT NULL DEFAULT 'NORMAL',
            "trigger_type" "document_generations_trigger_type_enum" NOT NULL,
            "template_data" JSONB NOT NULL DEFAULT '{}',
            "generation_options" JSONB NOT NULL DEFAULT '{}',
            "job_id" CHARACTER VARYING,
            "queue_name" CHARACTER VARYING NOT NULL DEFAULT 'document-generation',
            "started_at" TIMESTAMP,
            "completed_at" TIMESTAMP,
            "processing_time_ms" INTEGER,
            "error_message" TEXT,
            "error_details" JSONB,
            "retry_count" INTEGER NOT NULL DEFAULT 0,
            "max_retries" INTEGER NOT NULL DEFAULT 3,
            "output_file_name" CHARACTER VARYING NOT NULL,
            "output_s3_key" CHARACTER VARYING,
            "output_s3_bucket" CHARACTER VARYING,
            "output_file_size" BIGINT,
            "output_checksum" CHARACTER VARYING,
            "email_sent" BOOLEAN NOT NULL DEFAULT false,
            "email_sent_at" TIMESTAMP,
            "email_message_id" CHARACTER VARYING,
            "execution_log" JSONB NOT NULL DEFAULT '[]',
            "metadata" JSONB NOT NULL DEFAULT '{}',
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "last_modified_by" CHARACTER VARYING NOT NULL,
            CONSTRAINT "FK_514cbe7e" FOREIGN KEY ("case_id") REFERENCES "cases"("id"),
            CONSTRAINT "FK_6c02a707" FOREIGN KEY ("generated_document_id") REFERENCES "documents"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "custom_tokens" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "token_name" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "token_type" "custom_tokens_token_type_enum" NOT NULL DEFAULT 'CUSTOM',
            "data_type" "custom_tokens_data_type_enum" NOT NULL DEFAULT 'STRING',
            "status" "custom_tokens_status_enum" NOT NULL DEFAULT 'ACTIVE',
            "is_active" BOOLEAN NOT NULL DEFAULT true,
            "entity_name" CHARACTER VARYING NOT NULL,
            "field_path" CHARACTER VARYING NOT NULL,
            "transformation_config" JSONB NOT NULL DEFAULT '{}',
            "validation_config" JSONB NOT NULL DEFAULT '{}',
            "category" CHARACTER VARYING,
            "tags" JSONB NOT NULL DEFAULT '[]',
            "usage_count" INTEGER NOT NULL DEFAULT 0,
            "last_used_at" TIMESTAMP,
            "compatible_template_types" JSONB NOT NULL DEFAULT '[]',
            "compatible_case_types" JSONB NOT NULL DEFAULT '[]',
            "metadata" JSONB NOT NULL DEFAULT '{}',
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "last_modified_by" CHARACTER VARYING NOT NULL,
            CONSTRAINT "UQ_4e0e2c78" UNIQUE ("token_name"),
            CONSTRAINT "UQ_4e0e2c78" UNIQUE ("token_name")
        )`);

        await queryRunner.query(
            `CREATE INDEX "IDX_e03ed9c4345d3c68ed3c414f2b" ON "custom_tokens" ("entity_name", "field_path")`
        );

        await queryRunner.query(
            `CREATE INDEX "IDX_e5ed1327099b32ebcceb2cb1ee" ON "custom_tokens" ("token_type", "status", "is_active")`
        );

        await queryRunner.query(`CREATE TABLE "user_roles" (
            "user_id" uuid NOT NULL,
            "role_id" uuid NOT NULL,
            CONSTRAINT "PK_user_roles" PRIMARY KEY ("user_id", "role_id"),
            CONSTRAINT "FK_user_roles_user_id"
                FOREIGN KEY ("user_id")
                REFERENCES "user_profiles"("id")
                ON DELETE CASCADE,
            CONSTRAINT "FK_user_roles_role_id"
                FOREIGN KEY ("role_id")
                REFERENCES "tenant_roles"("id")
                ON DELETE CASCADE
        )`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "user_roles"`);

        await queryRunner.query(`DROP TABLE "custom_tokens"`);

        await queryRunner.query(`DROP TABLE "document_generations"`);

        await queryRunner.query(`DROP TABLE "document_templates"`);

        await queryRunner.query(`DROP TABLE "template_party_associations"`);

        await queryRunner.query(`DROP TABLE "quotes"`);

        await queryRunner.query(`DROP TABLE "promo_codes"`);

        await queryRunner.query(`DROP TABLE "document_workflows"`);

        await queryRunner.query(`DROP TABLE "document_audit"`);

        await queryRunner.query(`DROP TABLE "document_access"`);

        await queryRunner.query(`DROP TABLE "documents"`);

        await queryRunner.query(`DROP TABLE "document_folders"`);

        await queryRunner.query(`DROP TABLE "rate_card_fee_items"`);

        await queryRunner.query(`DROP TABLE "fee_items"`);

        await queryRunner.query(`DROP TABLE "fee_categories"`);

        await queryRunner.query(`DROP TABLE "rate_cards"`);

        await queryRunner.query(`DROP TABLE "task_history"`);

        await queryRunner.query(`DROP TABLE "task_dependencies"`);

        await queryRunner.query(`DROP TABLE "tasks"`);

        await queryRunner.query(`DROP TABLE "milestones"`);

        await queryRunner.query(`DROP TABLE "case_relations"`);

        await queryRunner.query(`DROP TABLE "case_assignments"`);

        await queryRunner.query(`DROP TABLE "case_events"`);

        await queryRunner.query(`DROP TABLE "case_contacts"`);

        await queryRunner.query(`DROP TABLE "case_audit"`);

        await queryRunner.query(`DROP TABLE "case_attachments"`);

        await queryRunner.query(`DROP TABLE "case_notes"`);

        await queryRunner.query(`DROP TABLE "cases"`);

        await queryRunner.query(`DROP TABLE "properties"`);

        await queryRunner.query(`DROP TABLE "clients"`);

        await queryRunner.query(`DROP TABLE "user_profiles"`);

        await queryRunner.query(`DROP TABLE "tenant_roles"`);

        await queryRunner.query(`DROP TYPE "case_attachments_document_type_enum"`);

        await queryRunner.query(`DROP TYPE "case_audit_action_enum"`);

        await queryRunner.query(`DROP TYPE "case_contacts_type_enum"`);

        await queryRunner.query(`DROP TYPE "case_events_category_enum"`);

        await queryRunner.query(`DROP TYPE "case_events_type_enum"`);

        await queryRunner.query(`DROP TYPE "properties_property_type_enum"`);

        await queryRunner.query(`DROP TYPE "properties_tenure_enum"`);

        await queryRunner.query(`DROP TYPE "properties_status_enum"`);

        await queryRunner.query(`DROP TYPE "cases_status_enum"`);

        await queryRunner.query(`DROP TYPE "cases_priority_enum"`);

        await queryRunner.query(`DROP TYPE "cases_type_enum"`);

        await queryRunner.query(`DROP TYPE "case_relations_type_enum"`);

        await queryRunner.query(`DROP TYPE "milestones_status_enum"`);

        await queryRunner.query(`DROP TYPE "task_status"`);

        await queryRunner.query(`DROP TYPE "task_priority"`);

        await queryRunner.query(`DROP TYPE "rate_cards_provider_code_enum"`);

        await queryRunner.query(`DROP TYPE "rate_cards_status_enum"`);

        await queryRunner.query(`DROP TYPE "fee_items_vat_type_enum"`);

        await queryRunner.query(`DROP TYPE "rate_card_fee_items_fee_type_enum"`);

        await queryRunner.query(`DROP TYPE "rate_card_fee_items_vat_type_enum"`);

        await queryRunner.query(`DROP TYPE "promo_codes_status_enum"`);

        await queryRunner.query(`DROP TYPE "quotes_transaction_type_enum"`);

        await queryRunner.query(`DROP TYPE "quotes_status_enum"`);

        await queryRunner.query(`DROP TYPE "template_party_associations_party_type_enum"`);

        await queryRunner.query(`DROP TYPE "document_templates_template_type_enum"`);

        await queryRunner.query(`DROP TYPE "document_templates_category_enum"`);

        await queryRunner.query(`DROP TYPE "document_templates_status_enum"`);

        await queryRunner.query(`DROP TYPE "document_generations_status_enum"`);

        await queryRunner.query(`DROP TYPE "document_generations_priority_enum"`);

        await queryRunner.query(`DROP TYPE "document_generations_trigger_type_enum"`);

        await queryRunner.query(`DROP TYPE "custom_tokens_token_type_enum"`);

        await queryRunner.query(`DROP TYPE "custom_tokens_data_type_enum"`);

        await queryRunner.query(`DROP TYPE "custom_tokens_status_enum"`);
    }
}
