import { MigrationInterface, QueryRunner } from 'typeorm';

export class QuoteAuditTenantMigrations1758659848455 implements MigrationInterface {
    name = 'QuoteAuditTenantMigrations1758659848455';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TYPE "quote_audit_action_enum" AS ENUM('CREATED', 'UPDATED', 'STATUS_CHANGED', 'STATUS_DRAFT', 'STATUS_SENT', 'STATUS_ACCEPTED', 'STATUS_EXPIRED', 'CALCULATED', 'RECALCULATED', 'PROMO_CODE_APPLIED', 'PROMO_CODE_REMOVED', 'CONVERTED_TO_CASE', 'EMAIL_SENT', 'EMAIL_OPENED', 'EMAIL_CLICKED', 'VIEWED', 'DOWNLOADED', 'SHARED', 'EXPORTED', 'ASSIGNED', 'UNASSIGNED', 'NOTE_ADDED', 'NOTE_UPDATED', 'NOTE_DELETED', 'ATTACHMENT_ADDED', 'ATTACHMENT_REMOVED', 'CLIENT_UPDATED', 'PROPERTY_DETAILS_UPDATED', 'QUOTE_SENT_TO_CLIENT', 'QUOTE_ACCEPTED_BY_CLIENT', 'QUOTE_REJECTED_BY_CLIENT', 'FOLLOW_UP_SENT', 'REMINDER_SET', 'DEADLINE_APPROACHING', 'DEADLINE_MISSED')`
        );

        await queryRunner.query(
            `CREATE TYPE "quote_attachments_attachment_type_enum" AS ENUM('QUOTE_PDF', 'CLIENT_ID', 'PROPERTY_REPORT', 'LEGAL_SEARCH_RESULT', 'SURVEY_REPORT', 'MORTGAGE_OFFER', 'INSURANCE_DOCUMENT', 'CONTRACT_DRAFT', 'BANK_STATEMENT', 'PAYSLIP', 'UTILITY_BILL', 'COUNCIL_TAX_BILL', 'LAND_REGISTRY_DOCUMENT', 'SEARCH_REPORT', 'VALUATION_REPORT', 'SURVEY_REPORT_BUILDING', 'SURVEY_REPORT_STRUCTURAL', 'ENERGY_PERFORMANCE_CERTIFICATE', 'FLOOD_RISK_REPORT', 'OTHER')`
        );

        await queryRunner.query(`CREATE TABLE "quote_audit" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "quote_id" UUID NOT NULL,
            "action" "quote_audit_action_enum" NOT NULL,
            "performed_by" CHARACTER VARYING NOT NULL,
            "performed_by_name" CHARACTER VARYING,
            "performed_at" TIMESTAMP NOT NULL DEFAULT now(),
            "ip_address" CHARACTER VARYING,
            "details" JSONB,
            "user_agent" TEXT,
            "session_id" CHARACTER VARYING,
            CONSTRAINT "FK_771b8a3b" FOREIGN KEY ("quote_id") REFERENCES "quotes"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "quote_attachments" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "quote_id" UUID NOT NULL,
            "document_id" CHARACTER VARYING,
            "filename" CHARACTER VARYING NOT NULL,
            "url" TEXT NOT NULL,
            "file_size" INTEGER,
            "mime_type" CHARACTER VARYING,
            "uploaded_by" CHARACTER VARYING NOT NULL,
            "uploaded_by_name" CHARACTER VARYING,
            "uploaded_at" TIMESTAMP NOT NULL DEFAULT now(),
            "description" TEXT,
            "attachment_type" "quote_attachments_attachment_type_enum" NOT NULL DEFAULT 'OTHER',
            "is_public" BOOLEAN NOT NULL DEFAULT false,
            "is_required" BOOLEAN NOT NULL DEFAULT false,
            "expires_at" TIMESTAMP,
            "metadata" JSONB,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "FK_34b5a0b0" FOREIGN KEY ("quote_id") REFERENCES "quotes"("id")
        )`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "quote_attachments"`);

        await queryRunner.query(`DROP TABLE "quote_audit"`);

        await queryRunner.query(`DROP TYPE "quote_audit_action_enum"`);

        await queryRunner.query(`DROP TYPE "quote_attachments_attachment_type_enum"`);
    }
}
