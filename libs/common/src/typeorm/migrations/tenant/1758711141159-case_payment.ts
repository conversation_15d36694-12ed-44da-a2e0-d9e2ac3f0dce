import { MigrationInterface, QueryRunner } from 'typeorm';

export class Case_payment1758711141159 implements MigrationInterface {
    name = 'Case_payment1758711141159';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TYPE "case_payments_method_enum" AS ENUM('BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'CHEQUE', 'CASH', 'OTHER')`
        );

        await queryRunner.query(`CREATE TABLE "case_payments" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "payment_date" TIMESTAMP NOT NULL,
            "reference_number" CHARACTER VARYING NOT NULL,
            "method" "case_payments_method_enum" NOT NULL DEFAULT 'OTHER',
            "description" TEXT,
            "amount" NUMERIC(10,2) NOT NULL,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_by" CHARACTER VARYING,
            "updated_at" TIMESTAMP,
            CONSTRAINT "FK_73d03159" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(
            `ALTER TABLE "case_audit" ALTER COLUMN "action" TYPE "case_audit_action_enum"`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "case_payments"`);

        await queryRunner.query(`DROP TYPE "case_payments_method_enum"`);
    }
}
