import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddedTitleCaseNote1759948732816 implements MigrationInterface {
    name = 'AddedTitleCaseNote1759948732816';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "case_notes" ADD COLUMN "title" CHARACTER VARYING(255) NOT NULL`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "case_notes" DROP COLUMN "title"`);
    }
}
