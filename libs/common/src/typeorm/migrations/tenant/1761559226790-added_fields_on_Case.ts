import { MigrationInterface, QueryRunner } from 'typeorm';

export class Added_fields_on_Case1761559226790 implements MigrationInterface {
    name = 'Added_fields_on_Case1761559226790';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cases" ADD COLUMN "rate_card_id" UUID`);

        await queryRunner.query(
            `ALTER TABLE "case_audit" ALTER COLUMN "action" TYPE "case_audit_action_enum"`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cases" DROP COLUMN "rate_card_id"`);
    }
}
