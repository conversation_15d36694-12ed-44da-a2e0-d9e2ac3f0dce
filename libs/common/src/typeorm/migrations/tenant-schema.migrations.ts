import { Injectable, Logger } from '@nestjs/common';
import * as path from 'path';
import { publicDataSource } from '../../config/typeorm/typeorm.config.public';
import {
    getSchemaNameFromTenantId,
    getTenantIdFromSchemaName
} from '../../multi-tenancy/sanitize-tenant-id.util';
import { TenantConnectionService } from '../../multi-tenancy/tenant-connection.service';

/**
 * Service for managing tenant schema migrations using TypeORM migrations
 */
@Injectable()
export class TenantSchemaMigrationService {
    private readonly logger = new Logger(TenantSchemaMigrationService.name);
    private readonly migrationsDir: string;

    constructor(private readonly tenantConnectionService: TenantConnectionService) {
        // Set the migrations directory path
        this.migrationsDir = path.resolve(__dirname);
        this.logger.log(`Migrations directory: ${this.migrationsDir}`);
    }

    /**
     * Gets the schema name for a tenant
     * @param tenantId The tenant ID
     * @returns The schema name
     */
    private getSchemaName(tenantId: string): string {
        return getSchemaNameFromTenantId(tenantId);
    }

    /**
     * Creates a schema for a tenant and runs initial migrations using TypeORM migrations
     * @param tenantId The tenant ID
     * @returns True if the schema was created, false if it already exists
     */
    async createTenantSchema(tenantId: string): Promise<boolean> {
        try {
            // Get the schema name for the tenant
            const schemaName = this.getSchemaName(tenantId);

            // Check if schema exists
            const schemaExists = await this.checkSchemaExists(schemaName);

            if (!schemaExists) {
                // Get a data source for the tenant - this will create the schema if it doesn't exist
                // The TenantConnectionService handles schema creation internally
                await this.tenantConnectionService.getTenantDataSource(tenantId);

                // Run initial migrations using TypeORM migrations
                await this.runTenantMigrations(tenantId);
                this.logger.log(`Created schema and ran migrations for tenant ${tenantId}`);
                return true;
            }

            // Schema already exists, check if migrations are up to date
            const status = await this.getMigrationStatus(tenantId);
            const pendingMigrations = status.filter((m) => !m.executed);

            if (pendingMigrations.length > 0) {
                this.logger.log(
                    `Schema exists for tenant ${tenantId}, but has ${pendingMigrations.length} pending migrations`
                );
                await this.runTenantMigrations(tenantId);
                return true;
            }

            this.logger.log(`Schema already exists for tenant ${tenantId} and is up to date`);
            return false;
        } catch (error) {
            this.logger.error(
                `Error creating schema for tenant ${tenantId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Checks if a schema exists
     * @param schemaName The schema name
     * @returns True if the schema exists, false otherwise
     */
    private async checkSchemaExists(schemaName: string): Promise<boolean> {
        try {
            // Use the public data source to check if the schema exists
            if (!publicDataSource.isInitialized) {
                await publicDataSource.initialize();
            }

            const result = await publicDataSource.query(
                `SELECT EXISTS (SELECT 1 FROM pg_namespace WHERE nspname = $1)`,
                [schemaName]
            );

            return result[0].exists;
        } catch (error) {
            this.logger.error(`Error checking if schema exists: ${error.message}`, error.stack);
            throw error;
        }
    }

    // Schema creation is now handled by the TenantConnectionService

    /**
     * Runs migrations for a tenant schema using TypeORM migrations
     * @param tenantId The tenant ID
     */
    async runTenantMigrations(tenantId: string): Promise<void> {
        try {
            // Use the TenantConnectionService to run migrations
            await this.tenantConnectionService.runTenantMigrations(tenantId, this.migrationsDir);
            this.logger.log(`Migrations completed for tenant ${tenantId}`);
        } catch (error) {
            this.logger.error(
                `Error running migrations for tenant ${tenantId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Runs migrations for all tenant schemas
     * @returns Object containing success and failure counts
     */
    async runMigrationsForAllTenants(): Promise<{
        success: number;
        failed: number;
        failures: Record<string, string>;
    }> {
        const failures: Record<string, string> = {};
        let successCount = 0;
        let failedCount = 0;

        try {
            // Get all tenant schemas from the database
            const schemas = await this.getAllTenantSchemas();
            this.logger.log(`Found ${schemas.length} tenant schemas`);

            // Run migrations for each schema
            for (const schema of schemas) {
                // Extract tenant ID from schema name using our utility function
                const tenantId = getTenantIdFromSchemaName(schema);

                if (!tenantId) {
                    this.logger.warn(`Could not extract tenant ID from schema name: ${schema}`);
                    continue;
                }

                try {
                    await this.runTenantMigrations(tenantId);
                    successCount++;
                } catch (error) {
                    failedCount++;
                    failures[tenantId] = error.message;
                    this.logger.error(
                        `Failed to run migrations for tenant ${tenantId}: ${error.message}`
                    );
                    // Continue with other tenants even if one fails
                }
            }

            this.logger.log(
                `Completed migrations: ${successCount} successful, ${failedCount} failed`
            );

            if (failedCount > 0) {
                this.logger.warn(`Failed migrations: ${JSON.stringify(failures)}`);
            }

            return { success: successCount, failed: failedCount, failures };
        } catch (error) {
            this.logger.error(
                `Error running migrations for all tenants: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Gets all tenant schemas from the database
     * @returns Array of schema names
     */
    private async getAllTenantSchemas(): Promise<string[]> {
        try {
            // Use the public data source to get all tenant schemas
            if (!publicDataSource.isInitialized) {
                await publicDataSource.initialize();
            }

            const result = await publicDataSource.query(
                `SELECT nspname FROM pg_namespace WHERE nspname LIKE 'tenant_%'`
            );

            return result.map((row: { nspname: string }) => row.nspname);
        } catch (error) {
            this.logger.error(`Error getting tenant schemas: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Gets migration status for a tenant
     * @param tenantId The tenant ID
     * @returns Array of migration status objects
     */
    async getMigrationStatus(tenantId: string): Promise<{ name: string; executed: boolean }[]> {
        try {
            // Get a data source for the tenant
            const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);

            try {
                // Make sure migrations are loaded
                await dataSource.showMigrations();

                // Get executed migrations
                const executedMigrations = await dataSource.query(
                    `SELECT name FROM typeorm_migrations ORDER BY timestamp ASC`
                );

                // Get all migrations from the migrations directory
                const migrationFiles = dataSource.migrations
                    .map((migration: any) => {
                        // Ensure migration has a name
                        if (!migration.name) {
                            this.logger.warn(
                                `Migration without name found: ${JSON.stringify(migration)}`
                            );
                            return null;
                        }

                        return {
                            name: migration.name,
                            instance: migration
                        };
                    })
                    .filter((m: any): m is { name: string; instance: any } => m !== null);

                // Combine into a single status array
                return migrationFiles.map((migration: { name: string; instance: any }) => {
                    const name = migration.name;
                    const executed = executedMigrations.some(
                        (m: { name: string }) => m.name === name
                    );

                    return { name, executed };
                });
            } finally {
                // We don't close the connection here since it's managed by the TenantConnectionService
            }
        } catch (error) {
            this.logger.error(
                `Error getting migration status for tenant ${tenantId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Revert the last migration for a tenant
     * @param tenantId The tenant ID
     * @returns The name of the reverted migration or null if no migrations to revert
     */
    async revertLastMigration(tenantId: string): Promise<string | null> {
        try {
            // Get a data source for the tenant
            const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);

            try {
                // Get executed migrations
                const executedMigrations = await dataSource.query(
                    `SELECT name FROM typeorm_migrations ORDER BY timestamp DESC LIMIT 1`
                );

                if (executedMigrations.length === 0) {
                    this.logger.log(`No migrations to revert for tenant ${tenantId}`);
                    return null;
                }

                // Revert the last migration
                await dataSource.undoLastMigration();

                const revertedMigration = executedMigrations[0].name;
                this.logger.log(`Reverted migration ${revertedMigration} for tenant ${tenantId}`);

                return revertedMigration;
            } finally {
                // We don't close the connection here since it's managed by the TenantConnectionService
            }
        } catch (error) {
            this.logger.error(
                `Error reverting last migration for tenant ${tenantId}: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }
}
