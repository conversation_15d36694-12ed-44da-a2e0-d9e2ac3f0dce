import { Injectable, Logger } from '@nestjs/common';
import * as path from 'path';
import { publicDataSource } from '../../config/typeorm/typeorm.config.public';

@Injectable()
export class PublicSchemaMigrationService {
    private readonly logger = new Logger(PublicSchemaMigrationService.name);
    private readonly migrationsDir: string;

    constructor() {
        this.migrationsDir = path.resolve(__dirname, 'public');
        this.logger.log(`Public migrations directory: ${this.migrationsDir}`);
    }

    /**
     * Runs migrations for the public schema
     */
    async runPublicMigrations(): Promise<void> {
        try {
            if (!publicDataSource.isInitialized) {
                await publicDataSource.initialize();
            }

            // Run migrations
            await publicDataSource.runMigrations();
            this.logger.log('Public schema migrations completed successfully');
        } catch (error) {
            // console.log('error', error);
            this.logger.error(
                `Failed to run public schema migrations: ${error.message}`,
                error.stack
            );
            throw error;
        }
    }

    /**
     * Gets migration status for public schema
     */
    async getMigrationStatus(): Promise<{ name: string; executed: boolean }[]> {
        // console.log({ publicDataSource})
        try {
            if (!publicDataSource.isInitialized) {
                await publicDataSource.initialize();
            }

            // Get executed migrations
            const executedMigrations = await publicDataSource.query(
                `SELECT name FROM public.typeorm_migrations ORDER BY timestamp ASC`
            );

            // Get all migrations from the migrations directory
            const migrationFiles =
                publicDataSource?.migrations
                    ?.filter((migration): migration is any => {
                        if (!migration.name) {
                            this.logger.warn(
                                `Found migration without name: ${JSON.stringify(migration)}`
                            );
                            return false;
                        }
                        return true;
                    })
                    .map((migration) => ({
                        name: migration.name,
                        instance: migration
                    })) ?? [];

            // Combine into a single status array
            return migrationFiles.map((migration) => ({
                name: migration.name,
                executed: executedMigrations.some((m) => m.name === migration.name)
            }));
        } catch (error) {
            this.logger.error(`Error getting migration status: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Checks if public schema is ready (all migrations executed)
     */
    async isPublicSchemaReady(): Promise<boolean> {
        try {
            const status = await this.getMigrationStatus();
            return status.every((m) => m.executed);
        } catch (error) {
            this.logger.error(`Error checking public schema status: ${error.message}`, error.stack);
            return false;
        }
    }
}
