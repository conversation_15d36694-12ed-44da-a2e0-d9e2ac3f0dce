import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Case } from './case.entity';

/**
 * Enum for document types
 */
export enum DocumentType {
    PLEADING = 'PLEADING',
    CONTRACT = 'CONTRACT',
    EVIDENCE = 'EVIDENCE',
    CORRESPONDENCE = 'CORRESPONDENCE',
    COURT_ORDER = 'COURT_ORDER',
    INVOICE = 'INVOICE',
    MEMO = 'MEMO',
    RESEARCH = 'RESEARCH',
    OTHER = 'OTHER'
}

/**
 * CaseAttachment entity representing a file attachment for a case
 * This is a tenant-specific entity stored in the tenant's schema
 * Note: The actual file storage is handled by a separate document service
 */
@TenantEntity()
@Entity('case_attachments')
export class CaseAttachment {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case, (caseEntity) => caseEntity.attachments)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column()
    filename: string;

    @Column({ type: 'text' })
    url: string;

    @Column({ name: 'file_size', nullable: true })
    fileSize: number;

    @Column({ name: 'mime_type', nullable: true })
    mimeType: string;

    @Column({ name: 'uploaded_by' })
    uploadedBy: string;

    @Column({ name: 'uploaded_by_name', nullable: true })
    uploadedByName: string;

    @Column({ name: 'uploaded_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    uploadedAt: Date;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({
        type: 'enum',
        enum: DocumentType,
        default: DocumentType.OTHER,
        name: 'document_type'
    })
    documentType: DocumentType;
}
