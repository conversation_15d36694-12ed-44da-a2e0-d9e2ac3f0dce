import { Column, Entity, Join<PERSON><PERSON>umn, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Case } from './case.entity';

export enum CaseAuditAction {
    CREATED = 'CREATED',
    UPDATED = 'UPDATED',
    CLIENT_UPDATED = 'CLIENT_UPDATED',
    STATUS_CHANGED = 'STATUS_CHANGED',
    STATUS_DRAFT = 'STATUS_DRAFT',
    STATUS_SUBMITTED = 'STATUS_SUBMITTED',
    STATUS_APPROVED = 'STATUS_APPROVED',
    STATUS_DECLINED = 'STATUS_DECLINED',
    STATUS_IN_PROGRESS = 'STATUS_IN_PROGRESS',
    STATUS_ON_HOLD = 'STATUS_ON_HOLD',
    STATUS_CLOSED = 'STATUS_CLOSED',
    STATUS_ARCHIVED = 'STATUS_ARCHIVED',
    ASSIGNED = 'ASSIGNED',
    UNASSIGNED = 'UNASSIGNED',
    REASSIGNED = 'REASSIGNED',
    NOTE_ADDED = 'NOTE_ADDED',
    NOTE_UPDATED = 'NOTE_UPDATED',
    NOTE_DELETED = 'NOTE_DELETED',
    ATTACHMENT_ADDED = 'ATTACHMENT_ADDED',
    ATTACHMENT_UPDATED = 'ATTACHMENT_UPDATED',
    ATTACHMENT_REMOVED = 'ATTACHMENT_REMOVED',
    CONTACT_ADDED = 'CONTACT_ADDED',
    CONTACT_UPDATED = 'CONTACT_UPDATED',
    CONTACT_DELETED = 'CONTACT_DELETED',
    PAYMENT_ADDED = 'PAYMENT_ADDED',
    CASE_RELATION_ADDED = 'CASE_RELATION_ADDED',
    CASE_RELATION_DELETED = 'CASE_RELATION_DELETED',
    ACCESSED = 'ACCESSED',
    ACCESS_DENIED = 'ACCESS_DENIED',
    EXPORTED = 'EXPORTED',
    CLIENT_CREATED = 'CLIENT_CREATED',
    PROPERTY_CREATED = 'PROPERTY_CREATED',
    PROPERTY_UPDATED = 'PROPERTY_UPDATED',
    REMINDER_SET = 'REMINDER_SET',
    DEADLINE_APPROACHING = 'DEADLINE_APPROACHING',
    DEADLINE_MISSED = 'DEADLINE_MISSED',
    STATUS_UNDER_REVIEW = 'STATUS_UNDER_REVIEW',
    STATUS_ASSIGNED = 'STATUS_ASSIGNED',
    STATUS_PENDING_APPROVAL = 'STATUS_PENDING_APPROVAL',
    STATUS_REJECTED = 'STATUS_REJECTED',
    STATUS_RESOLVED = 'STATUS_RESOLVED',
    STATUS_REOPENED = 'STATUS_REOPENED',
    PERMISSION_CHECK = 'PERMISSION_CHECK'
}

/**
 * CaseAudit entity representing an audit log entry for a case
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('case_audit')
export class CaseAudit {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case, (caseEntity) => caseEntity.auditTrail)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column({
        type: 'enum',
        enum: CaseAuditAction
    })
    action: CaseAuditAction;

    @Column({ name: 'performed_by' })
    performedBy: string;

    @Column({ name: 'performed_by_name', nullable: true })
    performedByName: string;

    @Column({ name: 'performed_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    performedAt: Date;

    @Column({ name: 'ip_address', nullable: true })
    ipAddress: string;

    @Column({ type: 'jsonb', nullable: true })
    details: Record<string, any>;
}
