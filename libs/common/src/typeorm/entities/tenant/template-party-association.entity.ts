import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';

export enum PartyType {
    // Always visible parties
    CLIENT = 'CLIENT',
    LAND_REGISTRY = 'LAND_REGISTRY',
    ATTENDANCE_NOTES_BILLING = 'ATTENDANCE_NOTES_BILLING',

    // Must be added as parties first
    SOLICITOR = 'SOLICITOR',
    AGENT_SOLICITOR = 'AGENT_SOLICITOR',
    BANK_BUILDING_SOCIETY_SOLICITOR = 'BANK_BUILDING_SOCIETY_SOLICITOR',
    INDIVIDUAL_SOLICITOR = 'INDIVIDUAL_SOLICITOR',
    LANDLORD_SOLICITOR = 'LANDLORD_SOLICITOR',
    MANAGEMENT_COMPANY = 'MANAGEMENT_COMPANY',
    OTHER = 'OTHER'
}

@TenantEntity()
@Entity('template_party_associations')
export class TemplatePartyAssociation {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'template_id' })
    templateId: string;

    @Column({
        type: 'enum',
        enum: PartyType,
        name: 'party_type'
    })
    partyType: PartyType;
}
