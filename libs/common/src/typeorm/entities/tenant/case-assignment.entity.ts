import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Case } from './case.entity';
import { UserProfile } from './user-profile.entity';

/**
 * CaseAssignment entity representing an assignment of a case to a user
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('case_assignments')
export class CaseAssignment {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case, (caseEntity) => caseEntity.assignments)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column({ name: 'user_id', type: 'uuid' })
    userId: string;

    @Column({ name: 'user_name', nullable: true })
    userName: string;

    @Column({ name: 'assigned_by' })
    assignedBy: string;

    @Column({ name: 'assigned_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    assignedAt: Date;

    @Column({ name: 'is_active', default: true })
    isActive: boolean;

    @Column({ type: 'text', nullable: true })
    notes: string;

    @ManyToOne(() => UserProfile, { nullable: true })
    @JoinColumn({ name: 'user_id', referencedColumnName: 'id' })
    user: UserProfile;
}
