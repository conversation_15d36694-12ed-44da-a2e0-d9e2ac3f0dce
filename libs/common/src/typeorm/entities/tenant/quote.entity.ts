import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn,
    Index
} from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Case } from './case.entity';

export interface PropertyAddress {
    buildingNumber?: string;
    buildingName?: string;
    street?: string;
    town?: string;
    county?: string;
    postCode?: string;
    plotNumber?: string;
}

export interface PropertyConditions {
    isNewBuild?: boolean;
    propertyInWales?: boolean;
    leasehold?: boolean;
    companyOrTrust?: boolean;
    buyingWithMortgage?: boolean;
    sharedOwnership?: boolean;
    giftedDeposit?: boolean;
    helpToBuyIsa?: boolean;
    mortgageRedemption?: boolean;
    transferOfEquity?: boolean;
    buyToLet?: boolean;
}

export interface ClientDetails {
    numberOfBuyers?: number;
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
}

export interface QuoteBreakdown {
    categories: Array<{
        categoryId: string;
        categoryName: string;
        items: Array<{
            id: string;
            label: string;
            netFee: number;
            vatFee: number;
            totalFee: number;
            vatType: string;
            perParty: boolean;
        }>;
        totalNet: number;
        totalVat: number;
        total: number;
    }>;
    grandTotalNet: number;
    grandTotalVat: number;
    grandTotal: number;
    reference: string;
}

export enum QuoteStatus {
    DRAFT = 'draft',
    SENT = 'sent',
    ACCEPTED = 'accepted',
    EXPIRED = 'expired'
}

export enum TransactionType {
    BUY = 'buy',
    SELL = 'sell',
    REMORTGAGE = 'remortgage'
}

/**
 * Quote entity representing a conveyancing quote in the system
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('quotes')
@Index(['sessionId'])
@Index(['caseId'])
@Index(['status'])
@Index(['createdAt'])
export class Quote {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'quote_number', unique: true })
    quoteNumber: string;

    @Column({ name: 'case_id', nullable: true })
    caseId: string;

    @ManyToOne(() => Case, { nullable: true })
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column({ name: 'session_id', nullable: true })
    sessionId: string; // For unauthenticated users

    @Column({
        name: 'transaction_type',
        type: 'enum',
        enum: TransactionType
    })
    transactionType: TransactionType;

    @Column({
        name: 'property_value',
        type: 'decimal',
        precision: 15,
        scale: 2
    })
    propertyValue: number;

    @Column({
        name: 'property_address',
        type: 'jsonb'
    })
    propertyAddress: PropertyAddress;

    @Column({
        name: 'property_conditions',
        type: 'jsonb'
    })
    propertyConditions: PropertyConditions;

    @Column({
        name: 'client_details',
        type: 'jsonb'
    })
    clientDetails: ClientDetails;

    @Column({
        name: 'quote_breakdown',
        type: 'jsonb'
    })
    quoteBreakdown: QuoteBreakdown;

    @Column({
        name: 'total_amount',
        type: 'decimal',
        precision: 15,
        scale: 2
    })
    totalAmount: number;

    @Column({ name: 'promo_code', nullable: true })
    promoCode: string;

    @Column({
        name: 'discount_amount',
        type: 'decimal',
        precision: 15,
        scale: 2,
        default: 0
    })
    discountAmount: number;

    @Column({
        name: 'status',
        type: 'enum',
        enum: QuoteStatus,
        default: QuoteStatus.DRAFT
    })
    status: QuoteStatus;

    @Column({ name: 'expires_at', type: 'timestamp' })
    expiresAt: Date;

    @Column({ name: 'created_by', nullable: true })
    createdBy: string;

    @CreateDateColumn({ name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt: Date;
}
