import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    CreateDateColumn,
    UpdateDateColumn
} from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';

export enum RateCardStatus {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    DRAFT = 'draft'
}

export enum RateCardProvider {
    ARROW = 'arrow',
    YOPA = 'yopa',
    TRUSSLE = 'trussle',
    TMG = 'tmg',
    SESAME = 'sesame',
    REMAX = 'remax',
    RAYNER = 'rayner',
    PEPPER = 'pepper',
    OPTIMUS = 'optimus',
    OPTIMUS_BID = 'optimus_bid',
    MSM = 'msm',
    MOLO = 'molo',
    MOJO = 'mojo',
    LEAS = 'leas',
    LANDC = 'landc',
    KEYCLUB = 'keyclub',
    JOHN_CHARCOL = 'john_charcol',
    INDEPENDENT = 'independent',
    HAYSTO = 'haysto',
    GAZEAL = 'gazeal',
    FORT_ADVICE = 'fort_advice',
    FLUENT = 'fluent',
    EKEEPER = 'ekeeper',
    CHARLES_CAMERON = 'charles_cameron',
    CUSTOM = 'custom'
}

@Entity('rate_cards')
@TenantEntity()
export class RateCard {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'provider_name', type: 'varchar', length: 100 })
    providerName: string;

    @Column({ name: 'provider_code', type: 'enum', enum: RateCardProvider })
    providerCode: RateCardProvider;

    @Column({ name: 'display_name', type: 'varchar', length: 200 })
    displayName: string;

    @Column({ name: 'description', type: 'text', nullable: true })
    description?: string;

    @Column({ name: 'version', type: 'varchar', length: 20, default: '1.0' })
    version: string;

    @Column({ name: 'effective_date', type: 'date' })
    effectiveDate: Date;

    @Column({ name: 'expiry_date', type: 'date', nullable: true })
    expiryDate?: Date;

    @Column({ name: 'status', type: 'enum', enum: RateCardStatus, default: RateCardStatus.ACTIVE })
    status: RateCardStatus;

    @Column({ name: 'is_default', type: 'boolean', default: false })
    isDefault: boolean;

    @Column({ name: 'priority', type: 'int', default: 0 })
    priority: number;

    @Column({ name: 'metadata', type: 'jsonb', nullable: true })
    metadata?: Record<string, any>;

    @CreateDateColumn({ name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt: Date;
}
