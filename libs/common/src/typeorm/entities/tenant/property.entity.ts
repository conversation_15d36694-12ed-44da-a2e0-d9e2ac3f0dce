import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';

export enum PropertyType {
    RESIDENTIAL = 'RESIDENTIAL',
    COMMERCIAL = 'COMMERCIAL',
    LAND = 'LAND',
    LEASEHOLD = 'LEASEHOLD',
    FREEHOLD = 'FREEHOLD',
    SHARED_OWNERSHIP = 'SHARED_OWNERSHIP',
    OTHER = 'OTHER'
}

export enum PropertyTenure {
    FREEHOLD = 'FREEHOLD',
    LEASEHOLD = 'LEASEHOLD',
    COMMONHOLD = 'COMMONHOLD',
    SHARED_FREEHOLD = 'SHARED_FREEHOLD'
}

export enum PropertyStatus {
    AVAILABLE = 'AVAILABLE',
    UNDER_OFFER = 'UNDER_OFFER',
    SOLD = 'SOLD',
    WITHDRAWN = 'WITHDRAWN',
    LET = 'LET',
    VACANT = 'VACANT'
}

/**
 * Property entity representing real estate properties in conveyancing transactions.
 *
 * This entity stores all property-specific information separate from case data,
 * allowing for better data normalization and reusability across multiple cases.
 *
 * Features:
 * - Complete property details (address, type, tenure, etc.)
 * - Valuation and pricing information
 * - Land registry and legal details
 * - Physical characteristics (bedrooms, bathrooms, etc.)
 * - Energy performance and council tax information
 * - Utilities and services details
 */
@TenantEntity()
@Entity('properties')
export class Property {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    // Address Information
    @Column({ name: 'full_address', type: 'text' })
    fullAddress: string;

    @Column({ name: 'address_line_1' })
    addressLine1: string;

    @Column({ name: 'address_line_2', nullable: true })
    addressLine2: string;

    @Column({ nullable: true })
    city: string;

    @Column({ nullable: true })
    county: string;

    @Column({ name: 'postal_code' })
    postalCode: string;

    @Column({ nullable: true })
    country: string;

    // Property Classification
    @Column({
        type: 'enum',
        enum: PropertyType,
        name: 'property_type'
    })
    propertyType: PropertyType;

    @Column({
        type: 'enum',
        enum: PropertyTenure,
        nullable: true
    })
    tenure: PropertyTenure;

    @Column({
        type: 'enum',
        enum: PropertyStatus,
        default: PropertyStatus.AVAILABLE
    })
    status: PropertyStatus;

    // Property Details
    @Column({ nullable: true })
    bedrooms: number;

    @Column({ nullable: true })
    bathrooms: number;

    @Column({ nullable: true })
    receptions: number;

    @Column({ name: 'floor_area_sqft', type: 'decimal', precision: 10, scale: 2, nullable: true })
    floorAreaSqft: number;

    @Column({ name: 'floor_area_sqm', type: 'decimal', precision: 10, scale: 2, nullable: true })
    floorAreaSqm: number;

    @Column({ name: 'land_area_sqft', type: 'decimal', precision: 10, scale: 2, nullable: true })
    landAreaSqft: number;

    @Column({ name: 'land_area_sqm', type: 'decimal', precision: 10, scale: 2, nullable: true })
    landAreaSqm: number;

    @Column({ name: 'year_built', nullable: true })
    yearBuilt: number;

    @Column({ name: 'parking_spaces', nullable: true })
    parkingSpaces: number;

    @Column({ nullable: true })
    garage: boolean;

    @Column({ nullable: true })
    garden: boolean;

    @Column({ nullable: true })
    balcony: boolean;

    // Valuation Information
    @Column({ name: 'current_value', type: 'decimal', precision: 12, scale: 2, nullable: true })
    currentValue: number;

    @Column({ name: 'purchase_price', type: 'decimal', precision: 12, scale: 2, nullable: true })
    purchasePrice: number;

    @Column({ name: 'asking_price', type: 'decimal', precision: 12, scale: 2, nullable: true })
    askingPrice: number;

    @Column({ name: 'agreed_price', type: 'decimal', precision: 12, scale: 2, nullable: true })
    agreedPrice: number;

    @Column({ name: 'valuation_date', type: 'date', nullable: true })
    valuationDate: Date;

    @Column({ name: 'valuer_name', nullable: true })
    valuerName: string;

    @Column({ name: 'valuation_reference', nullable: true })
    valuationReference: string;

    // Legal Information
    @Column({ name: 'title_number', nullable: true })
    titleNumber: string;

    @Column({ name: 'land_registry_reference', nullable: true })
    landRegistryReference: string;

    @Column({ name: 'deed_type', nullable: true })
    deedType: string;

    @Column({ name: 'restrictive_covenants', type: 'text', nullable: true })
    restrictiveCovenants: string;

    @Column({ name: 'easements', type: 'text', nullable: true })
    easements: string;

    @Column({ name: 'lease_expiry_date', type: 'date', nullable: true })
    leaseExpiryDate: Date;

    @Column({ name: 'lease_years_remaining', nullable: true })
    leaseYearsRemaining: number;

    @Column({ name: 'ground_rent', type: 'decimal', precision: 10, scale: 2, nullable: true })
    groundRent: number;

    @Column({ name: 'service_charge', type: 'decimal', precision: 10, scale: 2, nullable: true })
    serviceCharge: number;

    @Column({ name: 'management_company', nullable: true })
    managementCompany: string;

    // Council and Statutory Information
    @Column({ name: 'council_tax_band', nullable: true })
    councilTaxBand: string;

    @Column({ name: 'council_tax_amount', type: 'decimal', precision: 8, scale: 2, nullable: true })
    councilTaxAmount: number;

    @Column({ name: 'local_authority', nullable: true })
    localAuthority: string;

    @Column({ name: 'planning_permission_required', nullable: true })
    planningPermissionRequired: boolean;

    @Column({ name: 'listed_building', nullable: true })
    listedBuilding: boolean;

    @Column({ name: 'conservation_area', nullable: true })
    conservationArea: boolean;

    // Energy and Environmental
    @Column({ name: 'epc_rating', nullable: true })
    epcRating: string;

    @Column({ name: 'epc_expiry_date', type: 'date', nullable: true })
    epcExpiryDate: Date;

    @Column({ name: 'gas_supply', nullable: true })
    gasSupply: boolean;

    @Column({ name: 'electricity_supply', nullable: true })
    electricitySupply: boolean;

    @Column({ name: 'water_supply', nullable: true })
    waterSupply: boolean;

    @Column({ name: 'sewerage_connected', nullable: true })
    sewerageConnected: boolean;

    @Column({ name: 'broadband_available', nullable: true })
    broadbandAvailable: boolean;

    // Insurance Information
    @Column({ name: 'buildings_insurance_required', nullable: true })
    buildingsInsuranceRequired: boolean;

    @Column({ name: 'buildings_insurance_provider', nullable: true })
    buildingsInsuranceProvider: string;

    @Column({ name: 'buildings_insurance_policy_number', nullable: true })
    buildingsInsurancePolicyNumber: string;

    @Column({
        name: 'buildings_insurance_amount',
        type: 'decimal',
        precision: 12,
        scale: 2,
        nullable: true
    })
    buildingsInsuranceAmount: number;

    // Survey Information
    @Column({ name: 'survey_type', nullable: true })
    surveyType: string;

    @Column({ name: 'survey_date', type: 'date', nullable: true })
    surveyDate: Date;

    @Column({ name: 'surveyor_name', nullable: true })
    surveyorName: string;

    @Column({ name: 'survey_value', type: 'decimal', precision: 12, scale: 2, nullable: true })
    surveyValue: number;

    @Column({ name: 'survey_issues', type: 'text', nullable: true })
    surveyIssues: string;

    // Location and Amenities
    @Column({ nullable: true })
    latitude: number;

    @Column({ nullable: true })
    longitude: number;

    @Column({ name: 'nearest_station', nullable: true })
    nearestStation: string;

    @Column({
        name: 'station_distance_miles',
        type: 'decimal',
        precision: 5,
        scale: 2,
        nullable: true
    })
    stationDistanceMiles: number;

    @Column({ name: 'school_catchment', nullable: true })
    schoolCatchment: string;

    @Column({ name: 'amenities', type: 'jsonb', default: '[]' })
    amenities: string[];

    // Additional Information
    @Column({ name: 'description', type: 'text', nullable: true })
    description: string;

    @Column({ name: 'internal_notes', type: 'text', nullable: true })
    internalNotes: string;

    @Column({ name: 'special_conditions', type: 'text', nullable: true })
    specialConditions: string;

    @Column({ name: 'fixtures_and_fittings', type: 'text', nullable: true })
    fixturesAndFittings: string;

    @Column({ name: 'property_metadata', type: 'jsonb', default: '{}' })
    propertyMetadata: Record<string, any>;

    // Key Contacts
    @Column({ name: 'estate_agent_name', nullable: true })
    estateAgentName: string;

    @Column({ name: 'estate_agent_contact', nullable: true })
    estateAgentContact: string;

    @Column({ name: 'vendor_name', nullable: true })
    vendorName: string;

    @Column({ name: 'vendor_contact', nullable: true })
    vendorContact: string;

    @Column({ name: 'vendor_solicitor_name', nullable: true })
    vendorSolicitorName: string;

    @Column({ name: 'vendor_solicitor_firm', nullable: true })
    vendorSolicitorFirm: string;

    @Column({ name: 'vendor_solicitor_contact', nullable: true })
    vendorSolicitorContact: string;

    @Column({ name: 'vendor_solicitor_reference', nullable: true })
    vendorSolicitorReference: string;

    @Column({ name: 'vendor_client_name', nullable: true })
    vendorClientName: string;

    @Column({ name: 'vendor_solicitor_address_line_1', nullable: true })
    vendorSolicitorAddressLine1: string;

    @Column({ name: 'vendor_solicitor_address_line_2', nullable: true })
    vendorSolicitorAddressLine2: string;

    @Column({ name: 'vendor_solicitor_city', nullable: true })
    vendorSolicitorCity: string;

    @Column({ name: 'vendor_solicitor_county', nullable: true })
    vendorSolicitorCounty: string;

    @Column({ name: 'vendor_solicitor_postal_code', nullable: true })
    vendorSolicitorPostalCode: string;

    @Column({ name: 'vendor_solicitor_country', nullable: true })
    vendorSolicitorCountry: string;

    @Column({ name: 'vendor_solicitor_phone', nullable: true })
    vendorSolicitorPhone: string;

    @Column({ name: 'vendor_solicitor_email', nullable: true })
    vendorSolicitorEmail: string;

    @Column({ name: 'vendor_solicitor_dx', nullable: true })
    vendorSolicitorDX: string;

    // Audit Fields
    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ name: 'last_modified_by' })
    lastModifiedBy: string;

    // Relationships
    @OneToMany('Case', 'propertyId')
    cases: any[];
    address: string | undefined;
    price: number | undefined;
    vendor: string | undefined;
}
