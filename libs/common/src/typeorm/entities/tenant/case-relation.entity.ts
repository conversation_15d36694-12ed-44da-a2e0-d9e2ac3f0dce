import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Case } from './case.entity';

/**
 * Enum for relation types
 */
export enum RelationType {
    PARENT = 'PARENT',
    CHILD = 'CHILD',
    RELATED = 'RELATED',
    PREDECESSOR = 'PREDECESSOR',
    SUCCESSOR = 'SUCCESSOR'
}

/**
 * CaseRelation entity representing a relationship between two cases
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('case_relations')
export class CaseRelation {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column({ name: 'related_case_id' })
    relatedCaseId: string;

    @ManyToOne(() => Case)
    @JoinColumn({ name: 'related_case_id' })
    relatedCase: Case;

    @Column({
        type: 'enum',
        enum: RelationType,
        default: RelationType.RELATED
    })
    type: RelationType;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_by_name', nullable: true })
    createdByName: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ type: 'text', nullable: true })
    notes: string;
}
