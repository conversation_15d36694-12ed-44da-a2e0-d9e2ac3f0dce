import {
    Column,
    Entity,
    OneToMany,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn
} from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { FeeItem } from './fee-item.entity';

/**
 * Fee Category entity representing a category of fees in the quote system
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('fee_categories')
export class FeeCategory {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ name: 'display_order', default: 0 })
    displayOrder: number;

    @Column({ default: true })
    active: boolean;

    @OneToMany(() => FeeItem, (item) => item.category)
    items: FeeItem[];

    @CreateDateColumn({ name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt: Date;
}
