import {
    Column,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn,
    Index
} from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { FeeCategory } from './fee-category.entity';

export enum VatType {
    INC = 'inc',
    EXC = 'exc',
    NO = 'no'
}

/**
 * Fee Item entity representing individual fee components in the quote system
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('fee_items')
@Index(['categoryId', 'active'])
@Index(['applicableFor'])
@Index(['conditionSlug'])
export class FeeItem {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    label: string;

    @Column({ name: 'category_id' })
    categoryId: string;

    @ManyToOne(() => FeeCategory, (category) => category.items)
    @JoinColumn({ name: 'category_id' })
    category: FeeCategory;

    @Column({
        name: 'range_start',
        type: 'decimal',
        precision: 15,
        scale: 2,
        nullable: true
    })
    rangeStart: number;

    @Column({
        name: 'range_end',
        type: 'decimal',
        precision: 15,
        scale: 2,
        nullable: true
    })
    rangeEnd: number;

    @Column({ name: 'condition_slug', nullable: true })
    conditionSlug: string;

    @Column({
        name: 'net_fee',
        type: 'decimal',
        precision: 10,
        scale: 2,
        default: 0
    })
    netFee: number;

    @Column({
        name: 'vat_fee',
        type: 'decimal',
        precision: 10,
        scale: 2,
        default: 0
    })
    vatFee: number;

    @Column({
        name: 'total_fee',
        type: 'decimal',
        precision: 10,
        scale: 2,
        default: 0
    })
    totalFee: number;

    @Column({
        name: 'vat_type',
        type: 'enum',
        enum: VatType,
        default: VatType.INC
    })
    vatType: VatType;

    @Column({ name: 'applicable_for', type: 'text' })
    applicableFor: string; // Comma-separated: 'buy,sell,remortgage'

    @Column({ name: 'per_party', default: false })
    perParty: boolean;

    @Column({ default: false })
    dynamic: boolean; // For tax calculations

    @Column({ default: true })
    active: boolean;

    @Column({ name: 'version', default: 1 })
    version: number;

    @CreateDateColumn({ name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt: Date;
}
