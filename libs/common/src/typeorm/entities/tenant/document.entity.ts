import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { DocumentFolder } from './document-folder.entity';
import { Case } from './case.entity';

/**
 * Document entity representing a document stored in S3
 * This is a tenant-specific entity stored in the tenant's schema
 * Documents are now case-centric with simplified structure
 */
@TenantEntity()
@Entity('documents')
export class Document {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column({ name: 'folder_id', nullable: true })
    folderId: string;

    @ManyToOne(() => DocumentFolder, (folder) => folder.documents)
    @JoinColumn({ name: 'folder_id' })
    folder: DocumentFolder;

    @Column({ name: 's3_key' })
    s3Key: string;

    @Column({ name: 's3_bucket' })
    s3Bucket: string;

    @Column({ name: 'file_name' })
    fileName: string;

    @Column({ name: 'file_extension', nullable: true })
    fileExtension: string;

    @Column({ name: 'mime_type' })
    mimeType: string;

    @Column({ name: 'size_in_bytes', type: 'bigint' })
    sizeInBytes: string;

    @Column({ name: 'checksum' })
    checksum: string;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ name: 'last_modified_by' })
    lastModifiedBy: string;
}
