import { Column, Entity, Join<PERSON>olumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Quote } from './quote.entity';

export enum QuoteAuditAction {
    CREATED = 'CREATED',
    UPDATED = 'UPDATED',
    STATUS_CHANGED = 'STATUS_CHANGED',
    STATUS_DRAFT = 'STATUS_DRAFT',
    STATUS_SENT = 'STATUS_SENT',
    STATUS_ACCEPTED = 'STATUS_ACCEPTED',
    STATUS_EXPIRED = 'STATUS_EXPIRED',
    CALCULATED = 'CALCULATED',
    RECALCULATED = 'RECALCULATED',
    PROMO_CODE_APPLIED = 'PROMO_CODE_APPLIED',
    PROMO_CODE_REMOVED = 'PROMO_CODE_REMOVED',
    CONVERTED_TO_CASE = 'CONVERTED_TO_CASE',
    EMAIL_SENT = 'EMAIL_SENT',
    EMAIL_OPENED = 'EMAIL_OPENED',
    EMAIL_CLICKED = 'EMAIL_CLICKED',
    VIEWED = 'VIEWED',
    DOWNLOADED = 'DOWNLOADED',
    SHARED = 'SHARED',
    EXPORTED = 'EXPORTED',
    ASSIGNED = 'ASSIGNED',
    UNASSIGNED = 'UNASSIGNED',
    NOTE_ADDED = 'NOTE_ADDED',
    NOTE_UPDATED = 'NOTE_UPDATED',
    NOTE_DELETED = 'NOTE_DELETED',
    ATTACHMENT_ADDED = 'ATTACHMENT_ADDED',
    ATTACHMENT_REMOVED = 'ATTACHMENT_REMOVED',
    CLIENT_UPDATED = 'CLIENT_UPDATED',
    PROPERTY_DETAILS_UPDATED = 'PROPERTY_DETAILS_UPDATED',
    QUOTE_SENT_TO_CLIENT = 'QUOTE_SENT_TO_CLIENT',
    QUOTE_ACCEPTED_BY_CLIENT = 'QUOTE_ACCEPTED_BY_CLIENT',
    QUOTE_REJECTED_BY_CLIENT = 'QUOTE_REJECTED_BY_CLIENT',
    FOLLOW_UP_SENT = 'FOLLOW_UP_SENT',
    REMINDER_SET = 'REMINDER_SET',
    DEADLINE_APPROACHING = 'DEADLINE_APPROACHING',
    DEADLINE_MISSED = 'DEADLINE_MISSED'
}

/**
 * QuoteAudit entity representing an audit log entry for a quote
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('quote_audit')
export class QuoteAudit {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'quote_id' })
    quoteId: string;

    @ManyToOne(() => Quote)
    @JoinColumn({ name: 'quote_id' })
    quote: Quote;

    @Column({
        type: 'enum',
        enum: QuoteAuditAction
    })
    action: QuoteAuditAction;

    @Column({ name: 'performed_by' })
    performedBy: string;

    @Column({ name: 'performed_by_name', nullable: true })
    performedByName: string;

    @Column({ name: 'performed_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    performedAt: Date;

    @Column({ name: 'ip_address', nullable: true })
    ipAddress: string;

    @Column({ type: 'jsonb', nullable: true })
    details: Record<string, any>;

    @Column({ name: 'user_agent', type: 'text', nullable: true })
    userAgent: string;

    @Column({ name: 'session_id', nullable: true })
    sessionId: string; // For tracking unauthenticated user actions
}
