import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Document } from './document.entity';
import { Case } from './case.entity';

/**
 * DocumentFolder entity representing a folder within a case for document organization
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('document_folders')
export class DocumentFolder {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column({ name: 'parent_folder_id', nullable: true })
    parentFolderId: string;

    @ManyToOne(() => DocumentFolder, (folder) => folder.childFolders)
    @JoinColumn({ name: 'parent_folder_id' })
    parentFolder: DocumentFolder;

    @OneToMany(() => DocumentFolder, (folder) => folder.parentFolder)
    childFolders: DocumentFolder[];

    @OneToMany(() => Document, (document) => document.folder)
    documents: Document[];

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ name: 'path', type: 'text', nullable: true })
    path: string;
}
