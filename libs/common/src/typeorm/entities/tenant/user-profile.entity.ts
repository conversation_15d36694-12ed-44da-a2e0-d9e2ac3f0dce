import { Column, <PERSON><PERSON><PERSON>, JoinTable, ManyToMany, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '../../../multi-tenancy/decorators/tenant-entity.decorator';
import { TenantRole } from './tenant-role.entity';

@TenantEntity()
@Entity('user_profiles')
export class UserProfile {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ unique: true })
    username: string;

    @Column({ unique: true })
    email: string;

    @Column({ name: 'first_name', nullable: true })
    firstName: string;

    @Column({ name: 'department', nullable: true })
    department: string;

    @Column({ name: 'last_name', nullable: true })
    lastName: string;

    @Column({ default: true })
    enabled: boolean;

    @Column({ name: 'email_verified', default: false })
    emailVerified: boolean;

    @Column({ name: 'keycloak_id', nullable: true })
    keycloakId: string;

    @Column({ name: 'additional_info', type: 'jsonb', nullable: true, default: () => "'{}'" })
    additionalInfo: Record<string, any>;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ name: 'user_id', nullable: true, unique: true })
    userId: string;

    @ManyToMany(() => TenantRole, (role) => role.users, { eager: false, nullable: true })
    @JoinTable({
        name: 'user_roles',
        joinColumn: { name: 'user_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' }
    })
    roles: TenantRole[];
}
