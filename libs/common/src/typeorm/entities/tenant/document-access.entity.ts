import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Document } from './document.entity';

/**
 * DocumentAccess entity representing access permissions for a document
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('document_access')
export class DocumentAccess {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'document_id' })
    documentId: string;

    @ManyToOne(() => Document)
    @JoinColumn({ name: 'document_id' })
    document: Document;

    @Column({ name: 'user_id' })
    userId: string;

    @Column({ name: 'permission_level', default: 'read' })
    permissionLevel: string; // read, write, admin

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ name: 'expires_at', type: 'timestamp', nullable: true })
    expiresAt: Date;
}
