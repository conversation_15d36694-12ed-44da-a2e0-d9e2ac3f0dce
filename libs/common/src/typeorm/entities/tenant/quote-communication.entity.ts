import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Quote } from './quote.entity';

export enum CommunicationType {
    EMAIL = 'EMAIL',
    CALL = 'CALL',
    SMS = 'SMS',
    NOTE = 'NOTE',
    SYSTEM_UPDATE = 'SYSTEM_UPDATE',
    DOCUMENT_UPLOAD = 'DOCUMENT_UPLOAD',
    STATUS_CHANGE = 'STATUS_CHANGE',
    REMINDER = 'REMINDER',
    FOLLOW_UP = 'FOLLOW_UP',
    QUOTE_SENT = 'QUOTE_SENT',
    QUOTE_VIEWED = 'QUOTE_VIEWED',
    QUOTE_ACCEPTED = 'QUOTE_ACCEPTED',
    QUOTE_REJECTED = 'QUOTE_REJECTED',
    PAYMENT_RECEIVED = 'PAYMENT_RECEIVED',
    APPOINTMENT_SCHEDULED = 'APPOINTMENT_SCHEDULED',
    APPOINTMENT_COMPLETED = 'APPOINTMENT_COMPLETED',
    CLIENT_INQUIRY = 'CLIENT_INQUIRY',
    STAFF_RESPONSE = 'STAFF_RESPONSE',
    ESCALATION = 'ESCALATION',
    RESOLUTION = 'RESOLUTION'
}

export enum CommunicationDirection {
    INBOUND = 'INBOUND', // From client to staff
    OUTBOUND = 'OUTBOUND', // From staff to client
    INTERNAL = 'INTERNAL', // Between staff members
    SYSTEM = 'SYSTEM' // System-generated
}

export enum CommunicationPriority {
    LOW = 'LOW',
    MEDIUM = 'MEDIUM',
    HIGH = 'HIGH',
    URGENT = 'URGENT'
}

/**
 * QuoteCommunication entity representing a communication or activity log entry for a quote
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('quote_communications')
export class QuoteCommunication {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'quote_id' })
    quoteId: string;

    @ManyToOne(() => Quote)
    @JoinColumn({ name: 'quote_id' })
    quote: Quote;

    @Column({
        type: 'enum',
        enum: CommunicationType
    })
    type: CommunicationType;

    @Column({
        type: 'enum',
        enum: CommunicationDirection,
        default: CommunicationDirection.INTERNAL
    })
    direction: CommunicationDirection;

    @Column({
        type: 'enum',
        enum: CommunicationPriority,
        default: CommunicationPriority.MEDIUM
    })
    priority: CommunicationPriority;

    @Column()
    subject: string;

    @Column({ type: 'text' })
    body: string;

    @Column()
    sender: string;

    @Column({ name: 'sender_name', nullable: true })
    senderName: string;

    @Column({ name: 'sender_email', nullable: true })
    senderEmail: string;

    @Column({ nullable: true })
    recipient: string;

    @Column({ name: 'recipient_name', nullable: true })
    recipientName: string;

    @Column({ name: 'recipient_email', nullable: true })
    recipientEmail: string;

    @Column({ name: 'sent_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    sentAt: Date;

    @Column({ name: 'is_read', default: false })
    isRead: boolean;

    @Column({ name: 'read_at', type: 'timestamp', nullable: true })
    readAt: Date;

    @Column({ name: 'read_by', nullable: true })
    readBy: string;

    @Column({ name: 'read_by_name', nullable: true })
    readByName: string;

    @Column({ type: 'jsonb', nullable: true })
    attachments: string[]; // Array of attachment IDs

    @Column({ type: 'jsonb', nullable: true })
    metadata: Record<string, any>; // Additional data like email tracking, call duration, etc.

    @Column({ name: 'is_archived', default: false })
    isArchived: boolean;

    @Column({ name: 'archived_at', type: 'timestamp', nullable: true })
    archivedAt: Date;

    @Column({ name: 'archived_by', nullable: true })
    archivedBy: string;

    @Column({ name: 'follow_up_required', default: false })
    followUpRequired: boolean;

    @Column({ name: 'follow_up_date', type: 'timestamp', nullable: true })
    followUpDate: Date;

    @Column({ name: 'follow_up_assigned_to', nullable: true })
    followUpAssignedTo: string;

    @Column({ name: 'follow_up_assigned_to_name', nullable: true })
    followUpAssignedToName: string;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}
