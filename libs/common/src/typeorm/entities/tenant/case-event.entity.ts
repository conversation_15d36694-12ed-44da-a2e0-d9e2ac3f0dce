import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Case } from './case.entity';

/**
 * Enum for case event categories
 */
export enum CaseEventCategory {
    // Case Initiation & Intake
    INTAKE = 'INTAKE',

    // Pleadings Phase
    PLEADINGS = 'PLEADINGS',

    // Discovery Phase
    DISCOVERY = 'DISCOVERY',

    // Pre-Trial Motions
    MOTIONS = 'MOTIONS',

    // Hearings & Conferences
    HEARINGS = 'HEARINGS',

    // Trial Phase
    TRIAL = 'TRIAL',

    // Post-Trial & Appeals
    POST_TRIAL = 'POST_TRIAL',

    // Deadlines
    DEADLINE = 'DEADLINE',

    // Administrative & Compliance
    ADMINISTRATIVE = 'ADMINISTRATIVE',

    // Communication & Updates
    COMMUNICATION = 'COMMUNICATION',

    // Other
    OTHER = 'OTHER'
}

/**
 * Enum for case event types
 */
export enum CaseEventType {
    // Case Initiation & Intake
    INITIAL_CONSULTATION = 'INITIAL_CONSULTATION',
    CONFLICT_CHECK = 'CONFLICT_CHECK',
    ENGAGEMENT_LETTER = 'ENGAGEMENT_LETTER',
    CASE_OPENED = 'CASE_OPENED',
    STATUTE_OF_LIMITATIONS = 'STATUTE_OF_LIMITATIONS',

    // Pleadings Phase
    COMPLAINT_FILED = 'COMPLAINT_FILED',
    SUMMONS_ISSUED = 'SUMMONS_ISSUED',
    SUMMONS_SERVED = 'SUMMONS_SERVED',
    ANSWER_FILED = 'ANSWER_FILED',
    COUNTERCLAIM_FILED = 'COUNTERCLAIM_FILED',
    CROSS_CLAIM_FILED = 'CROSS_CLAIM_FILED',
    AMENDED_PLEADING = 'AMENDED_PLEADING',

    // Discovery Phase
    INTERROGATORIES_SERVED = 'INTERROGATORIES_SERVED',
    INTERROGATORIES_ANSWERED = 'INTERROGATORIES_ANSWERED',
    DOCUMENT_REQUEST_SERVED = 'DOCUMENT_REQUEST_SERVED',
    DOCUMENT_PRODUCTION = 'DOCUMENT_PRODUCTION',
    DEPOSITION_SCHEDULED = 'DEPOSITION_SCHEDULED',
    DEPOSITION_COMPLETED = 'DEPOSITION_COMPLETED',
    EXPERT_DISCLOSURE = 'EXPERT_DISCLOSURE',
    DISCOVERY_CUTOFF = 'DISCOVERY_CUTOFF',
    DISCOVERY_DISPUTE = 'DISCOVERY_DISPUTE',
    MOTION_TO_COMPEL = 'MOTION_TO_COMPEL',

    // Pre-Trial Motions
    MOTION_TO_DISMISS = 'MOTION_TO_DISMISS',
    SUMMARY_JUDGMENT_MOTION = 'SUMMARY_JUDGMENT_MOTION',
    MOTION_HEARING = 'MOTION_HEARING',
    MOTION_IN_LIMINE = 'MOTION_IN_LIMINE',

    // Hearings & Conferences
    CASE_MANAGEMENT_CONFERENCE = 'CASE_MANAGEMENT_CONFERENCE',
    STATUS_CONFERENCE = 'STATUS_CONFERENCE',
    SETTLEMENT_CONFERENCE = 'SETTLEMENT_CONFERENCE',
    MEDIATION = 'MEDIATION',
    PRE_TRIAL_CONFERENCE = 'PRE_TRIAL_CONFERENCE',

    // Trial Phase
    TRIAL_START = 'TRIAL_START',
    TRIAL_END = 'TRIAL_END',
    WITNESS_LIST_FILED = 'WITNESS_LIST_FILED',
    EXHIBIT_LIST_FILED = 'EXHIBIT_LIST_FILED',
    JURY_SELECTION = 'JURY_SELECTION',
    OPENING_ARGUMENTS = 'OPENING_ARGUMENTS',
    CLOSING_ARGUMENTS = 'CLOSING_ARGUMENTS',
    VERDICT = 'VERDICT',
    JUDGMENT_ENTERED = 'JUDGMENT_ENTERED',

    // Post-Trial & Appeals
    POST_TRIAL_MOTION = 'POST_TRIAL_MOTION',
    NOTICE_OF_APPEAL = 'NOTICE_OF_APPEAL',
    APPELLATE_BRIEF = 'APPELLATE_BRIEF',
    ORAL_ARGUMENTS = 'ORAL_ARGUMENTS',
    APPELLATE_DECISION = 'APPELLATE_DECISION',

    // Deadlines
    COURT_DEADLINE = 'COURT_DEADLINE',
    INTERNAL_DEADLINE = 'INTERNAL_DEADLINE',
    RESPONSE_DEADLINE = 'RESPONSE_DEADLINE',

    // Administrative & Compliance
    COURT_FEE_PAYMENT = 'COURT_FEE_PAYMENT',
    BILLING_MILESTONE = 'BILLING_MILESTONE',
    COMPLIANCE_AUDIT = 'COMPLIANCE_AUDIT',

    // Communication & Updates
    CLIENT_MEETING = 'CLIENT_MEETING',
    CLIENT_CALL = 'CLIENT_CALL',
    TEAM_MEETING = 'TEAM_MEETING',
    OPPOSING_COUNSEL_COMMUNICATION = 'OPPOSING_COUNSEL_COMMUNICATION',
    COURT_COMMUNICATION = 'COURT_COMMUNICATION',

    // Other
    OTHER = 'OTHER'
}

/**
 * CaseEvent entity representing a timeline event for a case
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('case_events')
export class CaseEvent {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case, (caseEntity) => caseEntity.events)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column({
        type: 'enum',
        enum: CaseEventCategory,
        default: CaseEventCategory.OTHER
    })
    category: CaseEventCategory;

    @Column({
        type: 'enum',
        enum: CaseEventType,
        default: CaseEventType.OTHER
    })
    type: CaseEventType;

    @Column()
    title: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ name: 'event_date', type: 'timestamp' })
    eventDate: Date;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_by_name', nullable: true })
    createdByName: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ type: 'jsonb', name: 'metadata', nullable: true })
    metadata: Record<string, any>;
}
