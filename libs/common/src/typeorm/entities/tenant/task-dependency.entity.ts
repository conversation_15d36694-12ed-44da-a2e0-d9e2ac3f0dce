import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Task } from './task.entity';

/**
 * TaskDependency entity representing a dependency between tasks
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('task_dependencies')
@Unique(['taskId', 'dependsOnId']) // Prevent duplicate dependencies
export class TaskDependency {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'task_id' })
    taskId: string;

    @ManyToOne(() => Task, (task) => task.dependencies, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'task_id' })
    task: Task;

    @Column({ name: 'depends_on_id' })
    dependsOnId: string;

    @ManyToOne(() => Task, (task) => task.dependents, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'depends_on_id' })
    dependsOn: Task;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;
}
