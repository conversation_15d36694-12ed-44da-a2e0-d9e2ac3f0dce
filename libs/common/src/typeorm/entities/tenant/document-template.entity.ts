import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { CaseType } from './case.entity';
import { TemplatePartyAssociation } from './template-party-association.entity';

export enum DocumentTemplateType {
    CLIENT_ONBOARDING = 'CLIENT_ONBOARDING',
    CASE_DOCUMENTS = 'CASE_DOCUMENTS',
    LEGAL_CORRESPONDENCE = 'LEGAL_CORRESPONDENCE',
    BUSINESS_DOCUMENTS = 'BUSINESS_DOCUMENTS'
}

export enum DocumentTemplateCategory {
    WELCOME_PACK = 'WELCOME_PACK',
    ENGAGEMENT_LETTER = 'ENGAGEMENT_LETTER',
    RETAINER_AGREEMENT = 'RETAINER_AGREEMENT',
    CASE_OPENING_LETTER = 'CASE_OPENING_LETTER',
    STATUS_UPDATE = 'STATUS_UPDATE',
    COURT_FILING = 'COURT_FILING',
    DEMAND_LETTER = 'DEMAND_LETTER',
    SETTLEMENT_OFFER = 'SETTLEMENT_OFFER',
    LEGAL_OPINION = 'LEGAL_OPINION',
    INVOICE = 'INVOICE',
    PAYMENT_REMINDER = 'PAYMENT_REMINDER',
    TIME_SHEET = 'TIME_SHEET',
    SOLICITOR_CORRESPONDENCE = 'SOLICITOR_CORRESPONDENCE',
    EXCHANGE_CONFIRMATION = 'EXCHANGE_CONFIRMATION',
    COMPLETION_NOTICE = 'COMPLETION_NOTICE',
    PURCHASE_COMMUNICATION = 'PURCHASE_COMMUNICATION',
    SALE_COMMUNICATION = 'SALE_COMMUNICATION',
    REMORTGAGE_COMMUNICATION = 'REMORTGAGE_COMMUNICATION',
    CONTRACT_REQUEST = 'CONTRACT_REQUEST',
    OTHER = 'OTHER'
}

export enum DocumentTemplateStatus {
    DRAFT = 'DRAFT',
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    ARCHIVED = 'ARCHIVED'
}

export enum DocumentGenerationTrigger {
    MANUAL = 'MANUAL',
    CASE_CREATED = 'CASE_CREATED',
    CASE_STATUS_CHANGE = 'CASE_STATUS_CHANGE',
    CLIENT_ONBOARDED = 'CLIENT_ONBOARDED',
    SCHEDULED = 'SCHEDULED'
}

/**
 * DocumentTemplate entity representing DOCX templates with token placeholders for automated document generation.
 *
 * Features:
 * - Token-based content replacement ({{client.name}}, {{case.number}}, etc.)
 * - Version control and template inheritance
 * - Automated generation triggers (case creation, status changes, etc.)
 * - Integration with S3 storage and email system
 * - Usage analytics and audit trail
 *
 * This is a tenant-specific entity stored in the tenant's schema.
 */
@TenantEntity()
@Entity('document_templates')
export class DocumentTemplate {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({
        type: 'enum',
        enum: DocumentTemplateType,
        name: 'template_type'
    })
    templateType: DocumentTemplateType;

    @Column({
        type: 'enum',
        enum: DocumentTemplateCategory,
        nullable: true
    })
    category: DocumentTemplateCategory;

    @Column({ name: 'file_name' })
    fileName: string;

    @Column({ name: 'file_path' })
    filePath: string;

    @Column({ name: 's3_key' })
    s3Key: string;

    @Column({ name: 's3_bucket' })
    s3Bucket: string;

    @Column({
        name: 'mime_type',
        default: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })
    mimeType: string;

    @Column({ name: 'size_in_bytes', type: 'bigint' })
    sizeInBytes: string;

    @Column({ name: 'checksum' })
    checksum: string;

    @Column({ type: 'jsonb', name: 'required_tokens', default: '[]' })
    requiredTokens: string[];

    @Column({ type: 'jsonb', name: 'optional_tokens', default: '[]' })
    optionalTokens: string[];

    @Column({ type: 'jsonb', name: 'detected_tokens', default: '[]' })
    detectedTokens: string[];

    @Column({ type: 'jsonb', name: 'generation_triggers', default: '[]' })
    generationTriggers: DocumentGenerationTrigger[];

    @Column({ type: 'jsonb', name: 'allowed_case_types', default: '[]' })
    allowedCaseTypes: CaseType[];

    @Column({
        type: 'enum',
        enum: DocumentTemplateStatus,
        default: DocumentTemplateStatus.DRAFT
    })
    status: DocumentTemplateStatus;

    @Column({ name: 'is_active', default: true })
    isActive: boolean;

    @OneToMany('DocumentGeneration', 'templateId')
    generations: any[];

    @OneToMany(() => TemplatePartyAssociation, (association) => association.templateId)
    partyAssociations: TemplatePartyAssociation[];

    @Column({ type: 'jsonb', name: 'metadata', default: '{}' })
    metadata: Record<string, any>;

    @Column({ name: 'auto_attach_to_case', default: false })
    autoAttachToCase: boolean;

    @Column({ name: 'auto_email_to_client', default: false })
    autoEmailToClient: boolean;

    @Column({ name: 'email_template_type', nullable: true })
    emailTemplateType: string;

    @Column({ name: 'output_file_name_template', nullable: true })
    outputFileNameTemplate: string;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ name: 'last_modified_by' })
    lastModifiedBy: string;

    @Column({ name: 'last_used_at', type: 'timestamp', nullable: true })
    lastUsedAt: Date;

    @Column({ name: 'usage_count', default: 0 })
    usageCount: number;
}
