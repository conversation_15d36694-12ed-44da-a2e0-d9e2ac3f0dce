import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Case } from './case.entity';

/**
 * Enum for contact types
 */
export enum ContactType {
    CLIENT = 'CLIENT',
    CO_COUNSEL = 'CO_COUNSEL',
    OPPOSING_COUNSEL = 'OPPOSING_COUNSEL',
    WITNESS = 'WITNESS',
    EXPERT = 'EXPERT',
    JUDGE = 'JUDGE',
    OTHER = 'OTHER'
}

/**
 * CaseContact entity representing a contact associated with a case
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('case_contacts')
export class CaseContact {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case, (caseEntity) => caseEntity.contacts)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column()
    name: string;

    @Column({ nullable: true })
    email: string;

    @Column({ nullable: true })
    phone: string;

    @Column({ type: 'text', nullable: true })
    address: string;

    @Column({
        type: 'enum',
        enum: ContactType,
        default: ContactType.OTHER
    })
    type: ContactType;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_by_name', nullable: true })
    createdByName: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ type: 'jsonb', name: 'additional_info', nullable: true })
    additionalInfo: Record<string, any>;
}
