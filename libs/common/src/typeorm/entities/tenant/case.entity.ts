import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { CaseAssignment } from './case-assignment.entity';
import { CaseNote } from './case-note.entity';
import { CaseAttachment } from './case-attachment.entity';
import { CaseAudit } from './case-audit.entity';
import { CaseContact } from './case-contact.entity';
import { CaseEvent } from './case-event.entity';
import { CasePayment } from './case-payment.entity';
import { Client } from './client.entity';
import { Property } from './property.entity';
import { RateCard } from './rate-card.entity';
import { CaseStatus } from '@app/common/enums/case-status.enum';

export enum CasePriority {
    LOW = 'LOW',
    MEDIUM = 'MEDIUM',
    HIGH = 'HIGH',
    URGENT = 'URGENT'
}

export enum CaseType {
    LITIGATION = 'LITIGATION',
    CORPORATE = 'CORPORATE',
    CONVEYANCING = 'CONVEYANCING',
    INTELLECTUAL_PROPERTY = 'INTELLECTUAL_PROPERTY',
    FAMILY = 'FAMILY',
    CRIMINAL = 'CRIMINAL',
    PURCHASE = 'PURCHASE',
    SALE = 'SALE',
    REMORTGAGE = 'REMORTGAGE',
    OTHER = 'OTHER',
    REAL_ESTATE = 'REAL_ESTATE'
}

/**
 * Case entity representing a legal case in the system
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('cases')
export class Case {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'case_number', unique: true })
    caseNumber: string;

    @Column()
    title: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({
        type: 'enum',
        enum: CaseStatus,
        default: CaseStatus.DRAFT
    })
    status: CaseStatus;

    @Column({
        type: 'enum',
        enum: CasePriority,
        default: CasePriority.MEDIUM
    })
    priority: CasePriority;

    @Column({
        type: 'enum',
        enum: CaseType,
        default: CaseType.OTHER
    })
    type: CaseType;

    @Column({ name: 'client_id' })
    clientId: string;

    @ManyToOne(() => Client, (client) => client.cases)
    @JoinColumn({ name: 'client_id' })
    client: Client;

    @Column({ name: 'property_id', nullable: true })
    propertyId: string;

    @ManyToOne(() => Property, (property) => property.cases, { nullable: true })
    @JoinColumn({ name: 'property_id' })
    property: Property;

    @Column({ name: 'rate_card_id', nullable: true })
    rateCardId: string;

    @ManyToOne(() => RateCard, { nullable: true })
    @JoinColumn({ name: 'rate_card_id' })
    rateCard: RateCard;

    @OneToMany(() => CaseAssignment, (assignment) => assignment.case)
    assignments: CaseAssignment[];

    @OneToMany(() => CaseNote, (note) => note.case)
    notes: CaseNote[];

    @OneToMany(() => CaseAttachment, (attachment) => attachment.case)
    attachments: CaseAttachment[];

    @OneToMany(() => CaseAudit, (audit) => audit.case)
    auditTrail: CaseAudit[];

    @OneToMany(() => CaseContact, (contact) => contact.case)
    contacts: CaseContact[];

    @OneToMany(() => CaseEvent, (event) => event.case)
    events: CaseEvent[];

    @OneToMany(() => CasePayment, (payment) => payment.case)
    payments: CasePayment[];

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ name: 'deadline', type: 'timestamp', nullable: true })
    deadline: Date | null;

    // Conveyancing-specific fields (financial and process-related)
    @Column({ name: 'exchange_date', type: 'timestamp', nullable: true })
    exchangeDate: Date | null;

    @Column({ name: 'completion_date', type: 'timestamp', nullable: true })
    completionDate: Date | null;

    @Column({ name: 'deposit_amount', type: 'decimal', precision: 15, scale: 2, nullable: true })
    depositAmount: number | null;

    @Column({ name: 'chain_position', type: 'varchar', nullable: true })
    chainPosition: string | null;

    @Column({ name: 'mortgage_required', type: 'boolean', default: false })
    mortgageRequired: boolean;

    @Column({ name: 'mortgage_amount', type: 'decimal', precision: 15, scale: 2, nullable: true })
    mortgageAmount: number | null;

    @Column({ name: 'lender_name', type: 'varchar', nullable: true })
    lenderName: string | null;

    @Column({ type: 'jsonb', name: 'conveyancing_metadata', default: '{}' })
    conveyancingMetadata: {
        titleNumber?: string;
        tenureType?: string;
        buildingInsuranceProvider?: string;
        keyCollection?: string;
        specialConditions?: string[];
        [key: string]: any;
    };
}
