import {
    Column,
    Entity,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn,
    Index
} from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';

export enum PromoCodeStatus {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
    EXPIRED = 'expired'
}

/**
 * Promo Code entity representing discount codes in the quote system
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('promo_codes')
@Index(['code', 'status'])
export class PromoCode {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ unique: true })
    code: string;

    @Column({ type: 'int' })
    discount: number; // Percentage

    @Column({ name: 'expiration_date', type: 'timestamp', nullable: true })
    expirationDate: Date;

    @Column({
        name: 'status',
        type: 'enum',
        enum: PromoCodeStatus,
        default: PromoCodeStatus.ACTIVE
    })
    status: PromoCodeStatus;

    @Column({ name: 'usage_limit', nullable: true })
    usageLimit: number;

    @Column({ name: 'usage_count', default: 0 })
    usageCount: number;

    @CreateDateColumn({ name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt: Date;
}
