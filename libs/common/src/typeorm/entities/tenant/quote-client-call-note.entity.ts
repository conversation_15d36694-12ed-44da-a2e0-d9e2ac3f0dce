import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, <PERSON>T<PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Quote } from './quote.entity';

export enum CallType {
    INBOUND = 'INBOUND', // Client called in
    OUTBOUND = 'OUTBOUND', // Staff called client
    FOLLOW_UP = 'FOLLOW_UP', // Follow-up call
    CONSULTATION = 'CONSULTATION', // Initial consultation
    QUOTE_DISCUSSION = 'QUOTE_DISCUSSION', // Discussing quote details
    OBJECTION_HANDLING = 'OBJECTION_HANDLING', // Handling objections
    CLOSING = 'CLOSING', // Closing call
    SUPPORT = 'SUPPORT', // Support call
    COMPLAINT = 'COMPLAINT', // Complaint handling
    OTHER = 'OTHER' // Other call type
}

export enum CallOutcome {
    SUCCESSFUL = 'SUCCESSFUL', // Call completed successfully
    NO_ANSWER = 'NO_ANSWER', // No answer
    BUSY = 'BUSY', // Line busy
    VOICEMAIL = 'VOICEMAIL', // Left voicemail
    CALLBACK_REQUESTED = 'CALLBACK_REQUESTED', // Client requested callback
    APPOINTMENT_SCHEDULED = 'APPOINTMENT_SCHEDULED', // Appointment scheduled
    QUOTE_REQUESTED = 'QUOTE_REQUESTED', // Quote requested
    QUOTE_ACCEPTED = 'QUOTE_ACCEPTED', // Quote accepted
    QUOTE_REJECTED = 'QUOTE_REJECTED', // Quote rejected
    OBJECTION_RAISED = 'OBJECTION_RAISED', // Objection raised
    OBJECTION_RESOLVED = 'OBJECTION_RESOLVED', // Objection resolved
    COMPLAINT_RESOLVED = 'COMPLAINT_RESOLVED', // Complaint resolved
    FOLLOW_UP_REQUIRED = 'FOLLOW_UP_REQUIRED', // Follow-up required
    UNSUCCESSFUL = 'UNSUCCESSFUL' // Call unsuccessful
}

export enum CallPriority {
    LOW = 'LOW',
    MEDIUM = 'MEDIUM',
    HIGH = 'HIGH',
    URGENT = 'URGENT'
}

/**
 * QuoteClientCallNote entity representing a detailed note about a client call
 * This is a tenant-specific entity stored in the tenant's schema
 * Separate from general communication logs - focused on call-specific details
 */
@TenantEntity()
@Entity('quote_client_call_notes')
export class QuoteClientCallNote {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'quote_id' })
    quoteId: string;

    @ManyToOne(() => Quote)
    @JoinColumn({ name: 'quote_id' })
    quote: Quote;

    @Column({
        type: 'enum',
        enum: CallType,
        default: CallType.OTHER,
        name: 'call_type'
    })
    callType: CallType;

    @Column({
        type: 'enum',
        enum: CallOutcome,
        default: CallOutcome.SUCCESSFUL,
        name: 'call_outcome'
    })
    callOutcome: CallOutcome;

    @Column({
        type: 'enum',
        enum: CallPriority,
        default: CallPriority.MEDIUM,
        name: 'call_priority'
    })
    callPriority: CallPriority;

    @Column({ name: 'call_date', type: 'timestamp' })
    callDate: Date;

    @Column({ name: 'call_duration', nullable: true })
    callDuration: number; // Duration in minutes

    @Column({ name: 'client_name' })
    clientName: string;

    @Column({ name: 'client_phone', nullable: true })
    clientPhone: string;

    @Column({ name: 'client_email', nullable: true })
    clientEmail: string;

    @Column({ name: 'staff_member' })
    staffMember: string;

    @Column({ name: 'staff_member_name' })
    staffMemberName: string;

    @Column({ type: 'text' })
    callSummary: string; // Brief summary of the call

    @Column({ type: 'text' })
    discussionPoints: string; // Detailed discussion points

    @Column({ type: 'text', nullable: true })
    clientConcerns: string; // Any concerns raised by client

    @Column({ type: 'text', nullable: true })
    objectionsRaised: string; // Objections raised during call

    @Column({ type: 'text', nullable: true })
    objectionsHandled: string; // How objections were handled

    @Column({ type: 'text', nullable: true })
    nextSteps: string; // Next steps discussed

    @Column({ name: 'follow_up_required', default: false })
    followUpRequired: boolean;

    @Column({ name: 'follow_up_date', type: 'timestamp', nullable: true })
    followUpDate: Date;

    @Column({ name: 'follow_up_assigned_to', nullable: true })
    followUpAssignedTo: string;

    @Column({ name: 'follow_up_assigned_to_name', nullable: true })
    followUpAssignedToName: string;

    @Column({ type: 'text', nullable: true })
    followUpNotes: string; // Notes for follow-up

    @Column({ name: 'quote_related', default: true })
    quoteRelated: boolean; // Whether call was related to quote

    @Column({ name: 'quote_discussed', default: false })
    quoteDiscussed: boolean; // Whether quote was discussed

    @Column({ name: 'quote_feedback', type: 'text', nullable: true })
    quoteFeedback: string; // Client feedback on quote

    @Column({ name: 'client_satisfaction', nullable: true })
    clientSatisfaction: number; // 1-5 rating

    @Column({ type: 'jsonb', nullable: true })
    callMetadata: Record<string, any>; // Additional call metadata

    @Column({ name: 'is_archived', default: false })
    isArchived: boolean;

    @Column({ name: 'archived_at', type: 'timestamp', nullable: true })
    archivedAt: Date;

    @Column({ name: 'archived_by', nullable: true })
    archivedBy: string;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}
