import {
    Entity,
    PrimaryGeneratedColumn,
    Column,
    ManyToOne,
    Join<PERSON><PERSON>umn,
    CreateDateColumn,
    UpdateDateColumn
} from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { RateCard } from './rate-card.entity';
import { VatType } from './fee-item.entity';

export enum FeeItemType {
    LEGAL_FEE = 'legal_fee',
    DISBURSEMENT = 'disbursement',
    CONDITIONAL_FEE = 'conditional_fee',
    TAX = 'tax',
    ADMIN_FEE = 'admin_fee'
}

@Entity('rate_card_fee_items')
@TenantEntity()
export class RateCardFeeItem {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'rate_card_id', type: 'uuid' })
    rateCardId: string;

    @ManyToOne(() => RateCard, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'rate_card_id' })
    rateCard: RateCard;

    @Column({ name: 'label', type: 'varchar', length: 200 })
    label: string;

    @Column({ name: 'fee_type', type: 'enum', enum: FeeItemType })
    feeType: FeeItemType;

    @Column({ name: 'category_name', type: 'varchar', length: 100 })
    categoryName: string;

    @Column({ name: 'range_start', type: 'decimal', precision: 15, scale: 2, nullable: true })
    rangeStart?: number;

    @Column({ name: 'range_end', type: 'decimal', precision: 15, scale: 2, nullable: true })
    rangeEnd?: number;

    @Column({ name: 'condition_slug', type: 'varchar', length: 100, nullable: true })
    conditionSlug?: string;

    @Column({ name: 'net_fee', type: 'decimal', precision: 10, scale: 2 })
    netFee: number;

    @Column({ name: 'vat_fee', type: 'decimal', precision: 10, scale: 2, default: 0 })
    vatFee: number;

    @Column({ name: 'total_fee', type: 'decimal', precision: 10, scale: 2 })
    totalFee: number;

    @Column({ name: 'vat_type', type: 'enum', enum: VatType, default: VatType.EXC })
    vatType: VatType;

    @Column({ name: 'applicable_for', type: 'varchar', length: 50 })
    applicableFor: string; // 'buy', 'sell', 'remortgage', 'buy,sell', etc.

    @Column({ name: 'per_party', type: 'boolean', default: false })
    perParty: boolean;

    @Column({ name: 'dynamic', type: 'boolean', default: false })
    dynamic: boolean;

    @Column({ name: 'active', type: 'boolean', default: true })
    active: boolean;

    @Column({ name: 'display_order', type: 'int', default: 0 })
    displayOrder: number;

    @Column({ name: 'notes', type: 'text', nullable: true })
    notes?: string;

    @Column({ name: 'metadata', type: 'jsonb', nullable: true })
    metadata?: Record<string, any>;

    @CreateDateColumn({ name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt: Date;
}
