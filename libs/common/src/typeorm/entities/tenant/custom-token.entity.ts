import { Column, Entity, PrimaryGeneratedColumn, Index } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';

export enum TokenType {
    SYSTEM = 'SYSTEM',
    CUSTOM = 'CUSTOM'
}

export enum TokenDataType {
    STRING = 'STRING',
    NUMBER = 'NUMBER',
    DATE = 'DATE',
    BOOLEAN = 'BOOLEAN',
    CURRENCY = 'CURRENCY',
    EMAIL = 'EMAIL',
    PHONE = 'PHONE',
    ADDRESS = 'ADDRESS'
}

export enum TokenStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    DEPRECATED = 'DEPRECATED'
}

/**
 * CustomToken entity for managing both system and user-defined tokens
 *
 * Features:
 * - Support for system-defined and user-created tokens
 * - Entity and field mapping for data resolution
 * - Data type definitions for validation and formatting
 * - Usage tracking and analytics
 * - Token categorization and organization
 *
 * This is a tenant-specific entity stored in the tenant's schema.
 */
@TenantEntity()
@Entity('custom_tokens')
@Index(['tokenType', 'status', 'isActive'])
@Index(['entityName', 'fieldPath'])
@Index(['tokenName'], { unique: true })
export class CustomToken {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'token_name', unique: true })
    tokenName: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({
        type: 'enum',
        enum: TokenType,
        name: 'token_type',
        default: TokenType.CUSTOM
    })
    tokenType: TokenType;

    @Column({
        type: 'enum',
        enum: TokenDataType,
        name: 'data_type',
        default: TokenDataType.STRING
    })
    dataType: TokenDataType;

    @Column({
        type: 'enum',
        enum: TokenStatus,
        default: TokenStatus.ACTIVE
    })
    status: TokenStatus;

    @Column({ name: 'is_active', default: true })
    isActive: boolean;

    // Entity and field mapping for data resolution
    @Column({ name: 'entity_name' })
    entityName: string;

    @Column({ name: 'field_path' })
    fieldPath: string;

    // Optional transformation configuration
    @Column({ type: 'jsonb', name: 'transformation_config', default: '{}' })
    transformationConfig: {
        format?: string; // Date format, currency format, etc.
        prefix?: string;
        suffix?: string;
        uppercase?: boolean;
        lowercase?: boolean;
        truncate?: number;
        defaultValue?: any;
    };

    // Validation configuration
    @Column({ type: 'jsonb', name: 'validation_config', default: '{}' })
    validationConfig: {
        required?: boolean;
        minLength?: number;
        maxLength?: number;
        pattern?: string;
        allowedValues?: string[];
    };

    // Token categorization
    @Column({ nullable: true })
    category: string;

    @Column({ type: 'jsonb', default: '[]' })
    tags: string[];

    // Usage analytics
    @Column({ name: 'usage_count', default: 0 })
    usageCount: number;

    @Column({ name: 'last_used_at', type: 'timestamp', nullable: true })
    lastUsedAt: Date;

    // Template compatibility
    @Column({ type: 'jsonb', name: 'compatible_template_types', default: '[]' })
    compatibleTemplateTypes: string[];

    @Column({ type: 'jsonb', name: 'compatible_case_types', default: '[]' })
    compatibleCaseTypes: string[];

    // System metadata
    @Column({ type: 'jsonb', name: 'metadata', default: '{}' })
    metadata: Record<string, any>;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ name: 'last_modified_by' })
    lastModifiedBy: string;

    // Computed properties
    get fullTokenName(): string {
        return `${this.tokenName}`;
    }

    get isSystemToken(): boolean {
        return this.tokenType === TokenType.SYSTEM;
    }

    get isCustomToken(): boolean {
        return this.tokenType === TokenType.CUSTOM;
    }
}
