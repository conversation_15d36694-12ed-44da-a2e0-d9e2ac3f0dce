import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Case } from './case.entity';
import { Task } from './task.entity';

/**
 * Enum for milestone status
 */
export enum MilestoneStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    COMPLETED = 'COMPLETED'
}

/**
 * Milestone entity representing a case milestone in the system
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('milestones')
export class Milestone {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @OneToMany(() => Task, (task) => task.milestone)
    tasks: Task[];

    @Column({
        type: 'enum',
        enum: MilestoneStatus,
        default: MilestoneStatus.PENDING
    })
    status: MilestoneStatus;

    @Column({ name: 'progress_percentage', type: 'decimal', precision: 5, scale: 2, default: 0 })
    progressPercentage: number;

    @Column({ name: 'target_date', type: 'timestamp', nullable: true })
    targetDate: Date | null;

    @Column({ name: 'completion_date', type: 'timestamp', nullable: true })
    completionDate: Date | null;

    @Column({ name: 'sort_order', default: 0 })
    sortOrder: number;

    @Column({ name: 'is_default', default: false })
    isDefault: boolean;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}
