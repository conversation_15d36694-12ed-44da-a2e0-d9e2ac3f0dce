import { Column, <PERSON>tity, ManyToMany, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '../../../multi-tenancy/decorators/tenant-entity.decorator';
import { UserProfile } from './user-profile.entity';

@TenantEntity()
@Entity('tenant_roles')
export class TenantRole {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ unique: true })
    name: string;

    @Column({ nullable: true })
    description: string;

    @Column({ type: 'jsonb', nullable: true })
    permissions: Record<string, string[]>;

    @Column({ default: true })
    enabled: boolean;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @ManyToMany(() => UserProfile, (user) => user.roles, {
        nullable: true,
        eager: false
    })
    users: UserProfile[];
}
