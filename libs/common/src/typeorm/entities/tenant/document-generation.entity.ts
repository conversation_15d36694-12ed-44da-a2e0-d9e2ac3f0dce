import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { DocumentGenerationTrigger } from './document-template.entity';
import { Case } from './case.entity';
import { Document } from './document.entity';

export enum DocumentGenerationStatus {
    PENDING = 'PENDING',
    PROCESSING = 'PROCESSING',
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED',
    CANCELLED = 'CANCELLED'
}

export enum DocumentGenerationPriority {
    LOW = 'LOW',
    NORMAL = 'NORMAL',
    HIGH = 'HIGH',
    URGENT = 'URGENT'
}

/**
 * DocumentGeneration entity for tracking document generation jobs and their lifecycle.
 *
 * Features:
 * - Complete job lifecycle tracking (pending, processing, completed, failed)
 * - Template data and generation options storage
 * - Queue integration with job ID and retry logic
 * - Output file management with S3 storage details
 * - Email notification tracking
 * - Detailed execution logging and error handling
 * - Performance metrics (processing time, usage analytics)
 *
 * This is a tenant-specific entity stored in the tenant's schema.
 */
@TenantEntity()
@Entity('document_generations')
export class DocumentGeneration {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'template_id' })
    templateId: string;

    // Note: Relation to DocumentTemplate is defined in DocumentTemplate entity
    // to avoid circular dependencies

    @Column({ name: 'case_id', nullable: true })
    caseId: string;

    @ManyToOne(() => Case, { nullable: true })
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column({ name: 'client_id', nullable: true })
    clientId: string;

    @Column({ name: 'generated_document_id', nullable: true })
    generatedDocumentId: string;

    @ManyToOne(() => Document, { nullable: true })
    @JoinColumn({ name: 'generated_document_id' })
    generatedDocument: Document;

    @Column({
        type: 'enum',
        enum: DocumentGenerationStatus,
        default: DocumentGenerationStatus.PENDING
    })
    status: DocumentGenerationStatus;

    @Column({
        type: 'enum',
        enum: DocumentGenerationPriority,
        default: DocumentGenerationPriority.NORMAL
    })
    priority: DocumentGenerationPriority;

    @Column({
        type: 'enum',
        enum: DocumentGenerationTrigger,
        name: 'trigger_type'
    })
    triggerType: DocumentGenerationTrigger;

    @Column({ type: 'jsonb', name: 'template_data', default: '{}' })
    templateData: Record<string, any>;

    @Column({ type: 'jsonb', name: 'generation_options', default: '{}' })
    generationOptions: {
        autoAttachToCase?: boolean;
        autoEmailToClient?: boolean;
        emailTemplateType?: string;
        outputFileName?: string;
        notifyOnCompletion?: boolean;
        generatePdf?: boolean;
    };

    @Column({ name: 'job_id', nullable: true })
    jobId: string;

    @Column({ name: 'queue_name', default: 'document-generation' })
    queueName: string;

    @Column({ name: 'started_at', type: 'timestamp', nullable: true })
    startedAt: Date;

    @Column({ name: 'completed_at', type: 'timestamp', nullable: true })
    completedAt: Date;

    @Column({ name: 'processing_time_ms', type: 'int', nullable: true })
    processingTimeMs: number;

    @Column({ name: 'error_message', type: 'text', nullable: true })
    errorMessage: string;

    @Column({ type: 'jsonb', name: 'error_details', nullable: true })
    errorDetails: Record<string, any>;

    @Column({ name: 'retry_count', default: 0 })
    retryCount: number;

    @Column({ name: 'max_retries', default: 3 })
    maxRetries: number;

    @Column({ name: 'output_file_name' })
    outputFileName: string;

    @Column({ name: 'output_s3_key', nullable: true })
    outputS3Key: string;

    @Column({ name: 'output_s3_bucket', nullable: true })
    outputS3Bucket: string;

    @Column({ name: 'output_file_size', type: 'bigint', nullable: true })
    outputFileSize: string;

    @Column({ name: 'output_checksum', nullable: true })
    outputChecksum: string;

    @Column({ name: 'email_sent', default: false })
    emailSent: boolean;

    @Column({ name: 'email_sent_at', type: 'timestamp', nullable: true })
    emailSentAt: Date;

    @Column({ name: 'email_message_id', nullable: true })
    emailMessageId: string;

    @Column({ type: 'jsonb', name: 'execution_log', default: '[]' })
    executionLog: Array<{
        timestamp: string;
        level: 'info' | 'warn' | 'error';
        message: string;
        details?: Record<string, any>;
    }>;

    @Column({ type: 'jsonb', name: 'metadata', default: '{}' })
    metadata: Record<string, any>;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ name: 'last_modified_by' })
    lastModifiedBy: string;
}
