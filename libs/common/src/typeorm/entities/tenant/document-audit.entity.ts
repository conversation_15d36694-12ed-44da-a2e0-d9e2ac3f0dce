import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Document } from './document.entity';

/**
 * DocumentAudit entity for tracking document operations
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('document_audit')
export class DocumentAudit {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'document_id', nullable: true })
    documentId: string;

    @ManyToOne(() => Document)
    @JoinColumn({ name: 'document_id' })
    document: Document;

    @Column({ name: 'action_type' })
    actionType: string; // create, update, delete, view, download, etc.

    @Column({ name: 'action_details', type: 'jsonb', nullable: true })
    actionDetails: Record<string, any>;

    @Column({ name: 'performed_by' })
    performedBy: string;

    @Column({ name: 'performed_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    performedAt: Date;

    @Column({ name: 'ip_address', nullable: true })
    ipAddress: string;

    @Column({ name: 'user_agent', type: 'text', nullable: true })
    userAgent: string;

    @Column({ name: 'document_version_id', nullable: true })
    documentVersionId: string;
}
