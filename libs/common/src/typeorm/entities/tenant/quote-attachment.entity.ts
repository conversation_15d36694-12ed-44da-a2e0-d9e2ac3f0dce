import { Column, Entity, Join<PERSON><PERSON>umn, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Quote } from './quote.entity';

export enum QuoteAttachmentType {
    QUOTE_PDF = 'QUOTE_PDF',
    CLIENT_ID = 'CLIENT_ID',
    PROPERTY_REPORT = 'PROPERTY_REPORT',
    LEGAL_SEARCH_RESULT = 'LEGAL_SEARCH_RESULT',
    SURVEY_REPORT = 'SURVEY_REPORT',
    MORTGAGE_OFFER = 'MORTGAGE_OFFER',
    INSURANCE_DOCUMENT = 'INSURANCE_DOCUMENT',
    CONTRACT_DRAFT = 'CONTRACT_DRAFT',
    BANK_STATEMENT = 'BANK_STATEMENT',
    PAYSLIP = 'PAYSLIP',
    UTILITY_BILL = 'UTILITY_BILL',
    COUNCIL_TAX_BILL = 'COUNCIL_TAX_BILL',
    LAND_REGISTRY_DOCUMENT = 'LAND_REGISTRY_DOCUMENT',
    SEARCH_REPORT = 'SEARCH_REPORT',
    VALUATION_REPORT = 'VALUATION_REPORT',
    SURVEY_REPORT_BUILDING = 'SURVEY_REPORT_BUILDING',
    SURVEY_REPORT_STRUCTURAL = 'SURVEY_REPORT_STRUCTURAL',
    ENERGY_PERFORMANCE_CERTIFICATE = 'ENERGY_PERFORMANCE_CERTIFICATE',
    FLOOD_RISK_REPORT = 'FLOOD_RISK_REPORT',
    OTHER = 'OTHER'
}

/**
 * QuoteAttachment entity representing a file attachment for a quote
 * This is a tenant-specific entity stored in the tenant's schema
 * Note: The actual file storage is handled by the document microservice
 */
@TenantEntity()
@Entity('quote_attachments')
export class QuoteAttachment {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'quote_id' })
    quoteId: string;

    @ManyToOne(() => Quote)
    @JoinColumn({ name: 'quote_id' })
    quote: Quote;

    @Column({ name: 'document_id', nullable: true })
    documentId: string; // Reference to Document entity in document microservice

    @Column()
    filename: string;

    @Column({ type: 'text' })
    url: string;

    @Column({ name: 'file_size', nullable: true })
    fileSize: number;

    @Column({ name: 'mime_type', nullable: true })
    mimeType: string;

    @Column({ name: 'uploaded_by' })
    uploadedBy: string;

    @Column({ name: 'uploaded_by_name', nullable: true })
    uploadedByName: string;

    @Column({ name: 'uploaded_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    uploadedAt: Date;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({
        type: 'enum',
        enum: QuoteAttachmentType,
        default: QuoteAttachmentType.OTHER,
        name: 'attachment_type'
    })
    attachmentType: QuoteAttachmentType;

    @Column({ name: 'is_public', default: false })
    isPublic: boolean; // Whether client can view this attachment

    @Column({ name: 'is_required', default: false })
    isRequired: boolean; // Whether this attachment is required for quote processing

    @Column({ name: 'expires_at', type: 'timestamp', nullable: true })
    expiresAt: Date; // For time-sensitive documents

    @Column({ type: 'jsonb', nullable: true })
    metadata: Record<string, any>; // Additional metadata like document version, etc.

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}
