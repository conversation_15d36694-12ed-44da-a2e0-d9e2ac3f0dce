import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Document } from './document.entity';

/**
 * DocumentWorkflow entity for managing document workflow states
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('document_workflows')
export class DocumentWorkflow {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'document_id' })
    documentId: string;

    @ManyToOne(() => Document)
    @JoinColumn({ name: 'document_id' })
    document: Document;

    @Column({ name: 'workflow_type' })
    workflowType: string; // approval, review, signature, etc.

    @Column({ name: 'current_state' })
    currentState: string; // draft, pending_review, approved, rejected, etc.

    @Column({ name: 'workflow_data', type: 'jsonb', nullable: true })
    workflowData: Record<string, any>;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ name: 'completed_at', type: 'timestamp', nullable: true })
    completedAt: Date;

    @Column({ name: 'due_date', type: 'timestamp', nullable: true })
    dueDate: Date;

    @Column({ name: 'assigned_to', nullable: true })
    assignedTo: string;
}
