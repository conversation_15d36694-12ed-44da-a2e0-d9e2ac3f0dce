import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Task, TaskStatus } from './task.entity';

/**
 * TaskHistory entity representing a history of task status changes
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('task_history')
export class TaskHistory {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'task_id' })
    taskId: string;

    @ManyToOne(() => Task, (task) => task.statusHistory, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'task_id' })
    task: Task;

    @Column({
        name: 'from_status',
        type: 'enum',
        enum: TaskStatus,
        enumName: 'task_status',
        nullable: true
    })
    fromStatus: TaskStatus | null;

    @Column({
        name: 'to_status',
        type: 'enum',
        enum: TaskStatus,
        enumName: 'task_status'
    })
    toStatus: TaskStatus;

    @Column({ name: 'changed_by', type: 'varchar' })
    changedBy: string;

    @Column({ name: 'changed_by_name', type: 'varchar', nullable: true })
    changedByName: string | null;

    @Column({ name: 'changed_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    changedAt: Date;

    @Column({ type: 'jsonb', name: 'metadata', nullable: true })
    metadata: Record<string, any> | null;
}
