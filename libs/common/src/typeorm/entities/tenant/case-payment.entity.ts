import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Case } from './case.entity';

export enum PaymentMethod {
    BANK_TRANSFER = 'BANK_TRANSFER',
    CREDIT_CARD = 'CREDIT_CARD',
    DEBIT_CARD = 'DEBIT_CARD',
    CHEQUE = 'CHEQUE',
    CASH = 'CASH',
    OTHER = 'OTHER'
}

/**
 * CasePayment entity representing a payment made on a case
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('case_payments')
export class CasePayment {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case, (case_) => case_.payments)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column({ type: 'timestamp', name: 'payment_date' })
    paymentDate: Date;

    @Column({ name: 'reference_number' })
    referenceNumber: string;

    @Column({
        type: 'enum',
        enum: PaymentMethod,
        default: PaymentMethod.OTHER
    })
    method: PaymentMethod;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ type: 'decimal', precision: 10, scale: 2 })
    amount: number;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_by', nullable: true })
    updatedBy: string;

    @Column({ name: 'updated_at', type: 'timestamp', nullable: true })
    updatedAt: Date;
}
