// Public schema entities
import { SystemUser } from './public/system-user.entity';
import { SystemRole } from './public/system-role.entity';
import { Tenant } from './public/tenant.entity';

// Tenant schema entities
import { UserProfile } from './tenant/user-profile.entity';
import { TenantRole } from './tenant/tenant-role.entity';

import { CaseAssignment } from './tenant/case-assignment.entity';
import { CaseAttachment } from './tenant/case-attachment.entity';
import { CaseAudit } from './tenant/case-audit.entity';
import { CaseContact } from './tenant/case-contact.entity';
import { CaseEvent } from './tenant/case-event.entity';
import { CaseNote } from './tenant/case-note.entity';
import { CaseRelation } from './tenant/case-relation.entity';
import { CasePayment, PaymentMethod } from './tenant/case-payment.entity';
import { Case, CaseType, CasePriority } from './tenant/case.entity';
import { Client } from './tenant/client.entity';
import { Property, PropertyType, PropertyTenure, PropertyStatus } from './tenant/property.entity';
import { Task } from './tenant/task.entity';
import { TaskDependency } from './tenant/task-dependency.entity';
import { TaskHistory } from './tenant/task-history.entity';
import { RateCard } from './tenant/rate-card.entity';
import { RateCardFeeItem } from './tenant/rate-card-fee-item.entity';

// Document entities
import { DocumentFolder } from './tenant/document-folder.entity';
import { Document } from './tenant/document.entity';
import { DocumentAccess } from './tenant/document-access.entity';
import { DocumentAudit } from './tenant/document-audit.entity';
import { DocumentWorkflow } from './tenant/document-workflow.entity';
import { Milestone } from './tenant/milestone.entity';

// Quote engine entities
import { FeeCategory } from './tenant/fee-category.entity';
import { FeeItem } from './tenant/fee-item.entity';
import { PromoCode } from './tenant/promo-code.entity';
import { Quote } from './tenant/quote.entity';
// Document template entities
import {
    DocumentTemplate,
    DocumentTemplateType,
    DocumentTemplateCategory,
    DocumentTemplateStatus,
    DocumentGenerationTrigger
} from './tenant/document-template.entity';
import {
    DocumentGeneration,
    DocumentGenerationStatus,
    DocumentGenerationPriority
} from './tenant/document-generation.entity';
import { TemplatePartyAssociation, PartyType } from './tenant/template-party-association.entity';
import {
    CustomToken,
    QuoteAttachment,
    QuoteAudit,
    QuoteClientCallNote,
    QuoteCommunication
} from './tenant';

export const PUBLIC_ENTITIES = [SystemUser, SystemRole, Tenant];

export const TENANT_ENTITIES = [
    UserProfile,
    TenantRole,
    Case,
    Client,
    Property,
    CaseAssignment,
    CaseNote,
    CaseAttachment,
    CaseAudit,
    CaseContact,
    CaseEvent,
    CaseRelation,
    CasePayment,
    Task,
    TaskDependency,
    TaskHistory,
    DocumentFolder,
    Document,
    Milestone,
    DocumentAccess,
    DocumentAudit,
    DocumentWorkflow,
    // Quote engine entities
    FeeCategory,
    FeeItem,
    PromoCode,
    Quote,
    RateCard,
    RateCardFeeItem,
    DocumentTemplate,
    DocumentGeneration,
    TemplatePartyAssociation,
    CustomToken,
    QuoteCommunication,
    QuoteAttachment,
    QuoteClientCallNote,
    QuoteAudit
];

export const ALL_ENTITIES = [...PUBLIC_ENTITIES, ...TENANT_ENTITIES];

export {
    SystemUser,
    SystemRole,
    Tenant,
    UserProfile,
    TenantRole,
    Case,
    CaseType,
    CasePriority,
    Client,
    Property,
    PropertyType,
    PropertyTenure,
    PropertyStatus,
    CaseAssignment,
    Milestone,
    CaseNote,
    CaseAttachment,
    CaseAudit,
    CaseContact,
    CaseEvent,
    CaseRelation,
    CasePayment,
    PaymentMethod,
    Task,
    TaskDependency,
    TaskHistory,
    DocumentFolder,
    Document,
    DocumentAccess,
    DocumentAudit,
    DocumentWorkflow,
    // Quote engine entities
    FeeCategory,
    FeeItem,
    PromoCode,
    Quote,
    RateCard,
    RateCardFeeItem,
    DocumentTemplate,
    DocumentTemplateType,
    DocumentTemplateCategory,
    DocumentTemplateStatus,
    DocumentGenerationTrigger,
    DocumentGeneration,
    DocumentGenerationStatus,
    DocumentGenerationPriority,
    TemplatePartyAssociation,
    PartyType,
    CustomToken,
    QuoteAttachment,
    QuoteClientCallNote,
    QuoteCommunication,
    QuoteAudit
};
