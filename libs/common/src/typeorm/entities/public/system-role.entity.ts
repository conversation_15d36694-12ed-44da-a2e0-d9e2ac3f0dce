import { Column, <PERSON><PERSON><PERSON>, JoinTable, ManyToMany, PrimaryGeneratedColumn } from 'typeorm';
import { SystemUser } from './system-user.entity';

@Entity({ schema: 'public', name: 'system_roles' })
export class SystemRole {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ unique: true })
    name: string;

    @Column({ nullable: true })
    description: string;

    @Column({ type: 'jsonb', nullable: true })
    permissions: Record<string, string[]>;

    @Column({ name: 'enabled', default: true })
    isActive: boolean;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @ManyToMany(() => SystemUser, (user) => user.systemRoles)
    @JoinTable({
        name: 'user_system_roles',
        joinColumn: { name: 'role_id' },
        inverseJoinColumn: { name: 'user_id' }
    })
    users: SystemUser[];
}
