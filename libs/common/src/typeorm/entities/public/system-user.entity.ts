import { Column, <PERSON><PERSON>ty, JoinTable, ManyToMany, PrimaryGeneratedColumn } from 'typeorm';
import { SystemRole } from './system-role.entity';
import { Tenant } from './tenant.entity';

@Entity({ schema: 'public', name: 'system_users' })
export class SystemUser {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ unique: true })
    email: string;

    @Column()
    password: string;

    @Column({ name: 'first_name', nullable: true })
    firstName: string;

    @Column({ name: 'last_name', nullable: true })
    lastName: string;

    @Column({ name: 'is_active', default: true })
    isActive: boolean;

    @Column({ name: 'email_verified', default: false })
    emailVerified: boolean;

    @Column({ name: 'remember_me', default: false })
    rememberMe: boolean;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @ManyToMany(() => SystemRole, (role) => role.users)
    @JoinTable({
        name: 'user_system_roles',
        schema: 'public',
        joinColumn: {
            name: 'user_id',
            referencedColumnName: 'id'
        },
        inverseJoinColumn: {
            name: 'role_id',
            referencedColumnName: 'id'
        }
    })
    systemRoles: SystemRole[];

    @ManyToMany(() => Tenant, (tenant) => tenant.users)
    @JoinTable({
        name: 'user_tenants',
        schema: 'public',
        joinColumn: {
            name: 'user_id',
            referencedColumnName: 'id'
        },
        inverseJoinColumn: {
            name: 'tenant_id',
            referencedColumnName: 'id'
        }
    })
    tenants: Tenant[];
}
