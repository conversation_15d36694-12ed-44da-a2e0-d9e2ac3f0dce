import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { appConfig } from './config/app.config';
import { serviceConfig } from './config/service.config';

/**
 * CoreCommonModule - Ultra-minimal dependencies for Core Gateway
 *
 * This module provides ONLY what Core Gateway needs to function:
 * - ConfigService (for reading service ports/hosts from env vars)
 * - HttpModule (for proxying HTTP requests)
 *
 * Excludes ALL potentially blocking modules:
 * - ❌ TypeORM (database)
 * - ❌ BullProvider (queues)
 * - ❌ RedisModule (caching)
 * - ❌ KeycloakService (auth)
 * - ❌ Auth/Database/Redis config validation
 *
 * Core only needs app and service config to know:
 * - What port to run on (PORT)
 * - What ports/hosts other services use (AUTH_PORT, AUTH_HOST, etc.)
 */
@Module({
    imports: [
        NestConfigModule.forRoot({
            isGlobal: true,
            load: [
                appConfig, // Loads PORT, NODE_ENV
                serviceConfig // Loads service ports and prefixes
            ],
            validationOptions: {
                abortEarly: false,
                allowUnknown: true // Don't fail on extra env vars
            },
            expandVariables: true,
            cache: true
        }),
        HttpModule.register({
            timeout: 30000,
            maxRedirects: 5
        })
    ],
    exports: [NestConfigModule, HttpModule]
})
export class CoreCommonModule {}
