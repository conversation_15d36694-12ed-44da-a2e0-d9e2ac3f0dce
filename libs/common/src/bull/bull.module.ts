import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QUEUE_NAMES } from '../constants/queue.constants';
import { BullModule } from '@nestjs/bullmq';
import { MessageProducerService } from '../communication/producers/communication.producer';

@Module({
    imports: [
        BullModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                return {
                    connection: {
                        host: configService.get('redis.host'),
                        port: configService.get('redis.port'),
                        password: configService.get('redis.password'),
                        maxRetriesPerRequest: 3,
                        retryDelayOnFailover: 100,
                        lazyConnect: true
                    },
                    defaultJobOptions: {
                        removeOnComplete: 50,
                        removeOnFail: 20,
                        attempts: 3,
                        backoff: {
                            type: 'exponential',
                            delay: 2000
                        }
                    }
                };
            }
        }),
        BullModule.registerQueue(
            { name: QUEUE_NAMES.EMAIL },
            { name: QUEUE_NAMES.NOTIFICATION },
            { name: QUEUE_NAMES.MULTI_CHANNEL }
        )
    ],
    providers: [MessageProducerService],
    exports: [BullModule, MessageProducerService]
})
export class BullProviderModule {}
