import json
import boto3
import psycopg2
import os

def lambda_handler(event, context):
    """
    Lambda function to create Keycloak database and user in RDS PostgreSQL
    """
    
    # Get secrets
    secrets_client = boto3.client('secretsmanager', region_name='us-east-1')
    
    # Get RDS credentials
    rds_secret = secrets_client.get_secret_value(SecretId='tk-lpm/rds/credentials')
    rds_creds = json.loads(rds_secret['SecretString'])
    
    # Get Keycloak credentials
    keycloak_secret = secrets_client.get_secret_value(SecretId='tk-lpm/keycloak/db-credentials')
    keycloak_creds = json.loads(keycloak_secret['SecretString'])
    
    try:
        # Connect to PostgreSQL as master user
        conn = psycopg2.connect(
            host=rds_creds['host'],
            port=rds_creds['port'],
            user=rds_creds['username'],
            password=rds_creds['password'],
            database='postgres'
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute(f"SELECT 1 FROM pg_database WHERE datname = '{keycloak_creds['dbname']}'")
        db_exists = cursor.fetchone()
        
        if not db_exists:
            print(f"Creating database: {keycloak_creds['dbname']}")
            cursor.execute(f"CREATE DATABASE {keycloak_creds['dbname']}")
        else:
            print(f"Database {keycloak_creds['dbname']} already exists")
        
        # Check if user exists
        cursor.execute(f"SELECT 1 FROM pg_roles WHERE rolname = '{keycloak_creds['username']}'")
        user_exists = cursor.fetchone()
        
        if not user_exists:
            print(f"Creating user: {keycloak_creds['username']}")
            cursor.execute(f"CREATE USER {keycloak_creds['username']} WITH PASSWORD '{keycloak_creds['password']}'")
        else:
            print(f"User {keycloak_creds['username']} already exists")
        
        # Grant privileges
        print("Granting privileges...")
        cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {keycloak_creds['dbname']} TO {keycloak_creds['username']}")
        
        cursor.close()
        conn.close()
        
        # Connect to Keycloak database to grant schema privileges
        conn2 = psycopg2.connect(
            host=rds_creds['host'],
            port=rds_creds['port'],
            user=rds_creds['username'],
            password=rds_creds['password'],
            database=keycloak_creds['dbname']
        )
        conn2.autocommit = True
        cursor2 = conn2.cursor()
        
        cursor2.execute(f"GRANT ALL ON SCHEMA public TO {keycloak_creds['username']}")
        cursor2.execute(f"ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO {keycloak_creds['username']}")
        cursor2.execute(f"ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO {keycloak_creds['username']}")
        
        cursor2.close()
        conn2.close()
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Keycloak database setup completed successfully',
                'database': keycloak_creds['dbname'],
                'user': keycloak_creds['username']
            })
        }
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'message': 'Error setting up Keycloak database',
                'error': str(e)
            })
        }

