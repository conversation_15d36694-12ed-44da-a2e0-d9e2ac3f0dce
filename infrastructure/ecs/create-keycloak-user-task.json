{"family": "create-keycloak-user", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-execution-role", "taskRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-role", "containerDefinitions": [{"name": "create-keycloak-user", "image": "postgres:15-alpine", "cpu": 256, "memory": 512, "essential": true, "command": ["sh", "-c", "PGPASSWORD='TK<PERSON>MPassword2025!' psql -h tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com -U postgres -d postgres <<'EOF'\nDROP USER IF EXISTS keycloak;\nCREATE USER keycloak WITH PASSWORD 'KeycloakPassword2025!';\nALTER DATABASE keycloak OWNER TO keycloak;\nGRANT ALL PRIVILEGES ON DATABASE keycloak TO keycloak;\nEOF\necho '✅ Keycloak user created with correct password'"], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/tk-lpm/init-keycloak-db", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}]}