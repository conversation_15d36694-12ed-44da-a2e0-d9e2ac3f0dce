{"family": "tk-lpm-keycloak-staging", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "executionRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-execution-role", "taskRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-role", "containerDefinitions": [{"name": "keycloak", "image": "quay.io/keycloak/keycloak:23.0", "cpu": 512, "memory": 1024, "essential": true, "portMappings": [{"containerPort": 8080, "protocol": "tcp", "name": "keycloak-8080-tcp"}], "environment": [{"name": "KC_DB", "value": "postgres"}, {"name": "KC_DB_URL_HOST", "value": "tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com"}, {"name": "KC_DB_URL_PORT", "value": "5432"}, {"name": "KC_DB_URL_DATABASE", "value": "keycloak"}, {"name": "KC_DB_USERNAME", "value": "keycloak"}, {"name": "KC_DB_PASSWORD", "value": "KeycloakPassword2025!"}, {"name": "KEYCLOAK_ADMIN", "value": "admin"}, {"name": "KEYCLOAK_ADMIN_PASSWORD", "value": "AdminPassword2025!"}, {"name": "KC_HTTP_ENABLED", "value": "true"}, {"name": "KC_HOSTNAME_STRICT", "value": "false"}, {"name": "KC_PROXY", "value": "edge"}], "command": ["start-dev"], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/tk-lpm/keycloak", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 120}}]}