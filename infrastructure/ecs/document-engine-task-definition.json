{"family": "tk-lpm-document-engine-staging", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "executionRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-execution-role", "taskRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-role", "containerDefinitions": [{"name": "document-engine", "image": "039612857103.dkr.ecr.us-east-1.amazonaws.com/tk-lpm/document-engine:staging", "cpu": 512, "memory": 1024, "essential": true, "portMappings": [{"containerPort": 3004, "protocol": "tcp", "name": "document-engine-3004-tcp"}], "environment": [{"name": "NODE_ENV", "value": "production"}, {"name": "PORT", "value": "3004"}, {"name": "CORE_PORT", "value": "3000"}, {"name": "AUTH_PORT", "value": "3001"}, {"name": "CASE_MANAGEMENT_PORT", "value": "3002"}, {"name": "COMMUNICATION_PORT", "value": "3003"}, {"name": "DOCUMENT_ENGINE_PORT", "value": "3004"}, {"name": "QUOTE_ENGINE_PORT", "value": "3005"}, {"name": "TASK_MANAGEMENT_PORT", "value": "3006"}, {"name": "POSTGRES_HOST", "value": "tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com"}, {"name": "POSTGRES_PORT", "value": "5432"}, {"name": "POSTGRES_DB", "value": "tklpm"}, {"name": "POSTGRES_USER", "value": "postgres"}, {"name": "POSTGRES_PASSWORD", "value": "TKLPMPassword2025!"}, {"name": "POSTGRES_SSL", "value": "false"}, {"name": "REDIS_HOST", "value": "tk-lpm-redis.qjixwc.0001.use1.cache.amazonaws.com"}, {"name": "REDIS_PORT", "value": "6379"}, {"name": "REDIS_USERNAME", "value": "default"}, {"name": "REDIS_PASSWORD", "value": "none"}, {"name": "REDIS_DB", "value": "0"}, {"name": "S3_BUCKET_NAME", "value": "tk-lpm-documents-039612857103"}, {"name": "AWS_REGION", "value": "us-east-1"}, {"name": "KEYCLOAK_SERVER_URL", "value": "http://keycloak.tk-lpm.staging.local:8080"}, {"name": "KEYCLOAK_REALM", "value": "master"}, {"name": "KEYCLOAK_CLIENT_ID", "value": "admin-cli"}, {"name": "KEYCLOAK_CLIENT_SECRET", "value": "AdminPassword2025!"}, {"name": "KEYCLOAK_HOST", "value": "keycloak.tk-lpm.staging.local"}, {"name": "KEYCLOAK_ADMIN", "value": "admin"}, {"name": "KEYCLOAK_ADMIN_PASSWORD", "value": "AdminPassword2025!"}, {"name": "KEYCLOAK_HOSTNAME_PORT", "value": "8080"}, {"name": "CACHE_TTL_CASE_DETAILS", "value": "300"}, {"name": "CACHE_TTL_TASK_DETAILS", "value": "300"}, {"name": "JWT_SECRET", "value": "your-jwt-secret-key-change-in-production"}, {"name": "JWT_EXPIRATION", "value": "1h"}, {"name": "REFRESH_TOKEN_SECRET", "value": "your-refresh-token-secret-change-in-production"}, {"name": "REFRESH_TOKEN_EXPIRATION", "value": "7d"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/tk-lpm/document-engine", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}]}