{"family": "postgres-client-oneoff", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-execution-role", "taskRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-role", "containerDefinitions": [{"name": "postgres-client", "image": "postgres:15-alpine", "cpu": 256, "memory": 512, "essential": true, "command": ["sh", "-c", "PGPASSWORD='TKLPMPassword2025!' psql -h tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com -U postgres -d postgres -c \"CREATE DATABASE keycloak;\" || echo 'Database exists'; PGPASSWORD='TKLPMPassword2025!' psql -h tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com -U postgres -d postgres -c \"CREATE USER keycloak WITH PASSWORD 'KeycloakPassword2025!';\" || echo 'User exists'; PGPASSWORD='TKLPMPassword2025!' psql -h tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com -U postgres -d postgres -c \"GRANT ALL PRIVILEGES ON DATABASE keycloak TO keycloak;\"; PGPASSWORD='TKLPMPassword2025!' psql -h tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com -U postgres -d keycloak -c \"GRANT ALL ON SCHEMA public TO keycloak; ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO keycloak; ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO keycloak;\"; echo 'Setup complete!'; sleep 10"], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/tk-lpm/postgres-client", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs", "awslogs-create-group": "true"}}}]}