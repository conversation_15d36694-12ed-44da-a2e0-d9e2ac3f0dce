{"family": "init-keycloak-db", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "256", "memory": "512", "executionRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-execution-role", "taskRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-role", "containerDefinitions": [{"name": "init-keycloak-db", "image": "postgres:15-alpine", "cpu": 256, "memory": 512, "essential": true, "command": ["sh", "-c", "PGPASSWORD='TKLPMPassword2025!' psql -h tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com -U postgres -d postgres -c \"CREATE DATABASE keycloak;\" || echo 'Database might already exist' && PGPASSWORD='TKLPMPassword2025!' psql -h tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com -U postgres -d postgres -c \"GRANT ALL PRIVILEGES ON DATABASE keycloak TO postgres;\""], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/tk-lpm/init-keycloak-db", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}]}