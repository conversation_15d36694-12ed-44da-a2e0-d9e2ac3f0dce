AWSTemplateFormatVersion: '2010-09-09'
Description: 'TK-LPM Backend - S3 Storage and Keycloak Infrastructure'

Parameters:
  EnvironmentName:
    Description: Environment name prefix
    Type: String
    Default: tk-lpm

  KeycloakDBName:
    Description: Keycloak database name
    Type: String
    Default: keycloak

  KeycloakDBUsername:
    Description: Keycloak database username
    Type: String
    Default: keycloak

  KeycloakDBPassword:
    Description: Keycloak database password
    Type: String
    NoEcho: true
    MinLength: 8
    Default: KeycloakPassword2025!

  KeycloakAdminUsername:
    Description: Keycloak admin username
    Type: String
    Default: admin

  KeycloakAdminPassword:
    Description: Keycloak admin password
    Type: String
    NoEcho: true
    MinLength: 8
    Default: AdminPassword2025!

Resources:
  # S3 Bucket for Document Storage
  DocumentStorageBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub ${EnvironmentName}-documents-${AWS::AccountId}
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      VersioningConfiguration:
        Status: Enabled
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldVersions
            Status: Enabled
            NoncurrentVersionExpirationInDays: 90
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - '*'
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - '*'
            MaxAge: 3000
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}-documents
        - Key: Environment
          Value: !Ref EnvironmentName

  # S3 Bucket Policy
  DocumentStorageBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref DocumentStorageBucket
      PolicyDocument:
        Statement:
          - Sid: AllowECSTasksAccess
            Effect: Allow
            Principal:
              AWS: !GetAtt ECSTaskExecutionRole.Arn
            Action:
              - s3:GetObject
              - s3:PutObject
              - s3:DeleteObject
              - s3:ListBucket
            Resource:
              - !GetAtt DocumentStorageBucket.Arn
              - !Sub ${DocumentStorageBucket.Arn}/*

  # IAM Role for ECS Task Execution
  ECSTaskExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${EnvironmentName}-ecs-task-execution-role
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ecs-tasks.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
      Policies:
        - PolicyName: SecretsManagerAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource:
                  - !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${EnvironmentName}/*
        - PolicyName: S3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:ListBucket
                Resource:
                  - !GetAtt DocumentStorageBucket.Arn
                  - !Sub ${DocumentStorageBucket.Arn}/*
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}-ecs-task-execution-role
        - Key: Environment
          Value: !Ref EnvironmentName

  # IAM Role for ECS Tasks (application runtime)
  ECSTaskRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${EnvironmentName}-ecs-task-role
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ecs-tasks.amazonaws.com
            Action:
              - sts:AssumeRole
      Policies:
        - PolicyName: S3FullAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:*
                Resource:
                  - !GetAtt DocumentStorageBucket.Arn
                  - !Sub ${DocumentStorageBucket.Arn}/*
        - PolicyName: SecretsManagerAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                Resource:
                  - !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${EnvironmentName}/*
        - PolicyName: CloudWatchLogs
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - !Sub arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/ecs/${EnvironmentName}/*
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}-ecs-task-role
        - Key: Environment
          Value: !Ref EnvironmentName

  # Keycloak Database Credentials Secret
  KeycloakDBSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub ${EnvironmentName}/keycloak/db-credentials
      Description: Keycloak database credentials
      SecretString: !Sub 
        - |
          {
            "username": "${KeycloakDBUsername}",
            "password": "${KeycloakDBPassword}",
            "dbname": "${KeycloakDBName}",
            "host": "${RDSHost}",
            "port": "5432",
            "url": "postgresql://${KeycloakDBUsername}:${KeycloakDBPassword}@${RDSHost}:5432/${KeycloakDBName}"
          }
        - RDSHost:
            Fn::ImportValue: !Sub ${EnvironmentName}-RDS-Endpoint
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}/keycloak/db-credentials
        - Key: Environment
          Value: !Ref EnvironmentName

  # Keycloak Admin Credentials Secret
  KeycloakAdminSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub ${EnvironmentName}/keycloak/admin-credentials
      Description: Keycloak admin credentials
      SecretString: !Sub |
        {
          "username": "${KeycloakAdminUsername}",
          "password": "${KeycloakAdminPassword}"
        }
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}/keycloak/admin-credentials
        - Key: Environment
          Value: !Ref EnvironmentName

  # S3 Configuration Secret
  S3ConfigSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub ${EnvironmentName}/s3/config
      Description: S3 bucket configuration
      SecretString: !Sub |
        {
          "bucketName": "${DocumentStorageBucket}",
          "region": "${AWS::Region}",
          "endpoint": "https://s3.${AWS::Region}.amazonaws.com"
        }
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}/s3/config
        - Key: Environment
          Value: !Ref EnvironmentName

  # CloudWatch Log Group for Keycloak
  KeycloakLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /ecs/${EnvironmentName}/keycloak
      RetentionInDays: 7
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}-keycloak-logs
        - Key: Environment
          Value: !Ref EnvironmentName

  # CloudWatch Log Groups for all services
  CoreLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /ecs/${EnvironmentName}/core
      RetentionInDays: 7

  AuthLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /ecs/${EnvironmentName}/auth
      RetentionInDays: 7

  CaseManagementLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /ecs/${EnvironmentName}/case-management
      RetentionInDays: 7

  CommunicationLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /ecs/${EnvironmentName}/communication
      RetentionInDays: 7

  DocumentEngineLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /ecs/${EnvironmentName}/document-engine
      RetentionInDays: 7

  QuoteEngineLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /ecs/${EnvironmentName}/quote-engine
      RetentionInDays: 7

  TaskManagementLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /ecs/${EnvironmentName}/task-management
      RetentionInDays: 7

Outputs:
  DocumentStorageBucketName:
    Description: S3 bucket name for document storage
    Value: !Ref DocumentStorageBucket
    Export:
      Name: !Sub ${EnvironmentName}-S3-Bucket-Name

  DocumentStorageBucketArn:
    Description: S3 bucket ARN
    Value: !GetAtt DocumentStorageBucket.Arn
    Export:
      Name: !Sub ${EnvironmentName}-S3-Bucket-ARN

  ECSTaskExecutionRoleArn:
    Description: ECS Task Execution Role ARN
    Value: !GetAtt ECSTaskExecutionRole.Arn
    Export:
      Name: !Sub ${EnvironmentName}-ECS-Task-Execution-Role-ARN

  ECSTaskRoleArn:
    Description: ECS Task Role ARN
    Value: !GetAtt ECSTaskRole.Arn
    Export:
      Name: !Sub ${EnvironmentName}-ECS-Task-Role-ARN

  KeycloakDBSecretArn:
    Description: Keycloak database credentials secret ARN
    Value: !Ref KeycloakDBSecret
    Export:
      Name: !Sub ${EnvironmentName}-Keycloak-DB-Secret-ARN

  KeycloakAdminSecretArn:
    Description: Keycloak admin credentials secret ARN
    Value: !Ref KeycloakAdminSecret
    Export:
      Name: !Sub ${EnvironmentName}-Keycloak-Admin-Secret-ARN

  S3ConfigSecretArn:
    Description: S3 configuration secret ARN
    Value: !Ref S3ConfigSecret
    Export:
      Name: !Sub ${EnvironmentName}-S3-Config-Secret-ARN

  KeycloakLogGroupName:
    Description: Keycloak CloudWatch Log Group name
    Value: !Ref KeycloakLogGroup
    Export:
      Name: !Sub ${EnvironmentName}-Keycloak-LogGroup-Name

