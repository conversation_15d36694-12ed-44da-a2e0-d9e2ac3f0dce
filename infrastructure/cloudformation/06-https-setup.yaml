AWSTemplateFormatVersion: '2010-09-09'
Description: 'HTTPS Setup for TK-LPM ALB with ACM Certificate'

Parameters:
  EnvironmentName:
    Type: String
    Default: staging
    AllowedValues:
      - dev
      - staging
      - production
    Description: Environment name

  DomainName:
    Type: String
    Description: 'Your custom domain (e.g., api.teklpm.com)'
    Default: 'api.teklpm-staging.com'

  ACMCertificateArn:
    Type: String
    Description: 'ARN of ACM certificate (must be in us-east-1)'
    Default: 'arn:aws:acm:us-east-1:039612857103:certificate/YOUR-CERT-ARN-HERE'

  CreateRoute53Record:
    Type: String
    Default: 'false'
    AllowedValues:
      - 'true'
      - 'false'
    Description: 'Create Route 53 DNS record? (only if using Route 53)'

  Route53HostedZoneId:
    Type: String
    Default: ''
    Description: 'Route 53 Hosted Zone ID (only needed if CreateRoute53Record is true)'

Conditions:
  CreateDNSRecord: !Equals [!Ref CreateRoute53Record, 'true']
  HasHostedZoneId: !Not [!Equals [!Ref Route53HostedZoneId, '']]

Resources:
  # HTTPS Listener (Port 443)
  HttpsListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      LoadBalancerArn:
        Fn::ImportValue: !Sub '${EnvironmentName}-alb-arn'
      Port: 443
      Protocol: HTTPS
      SslPolicy: ELBSecurityPolicy-TLS13-1-2-2021-06  # Latest TLS 1.3 policy
      Certificates:
        - CertificateArn: !Ref ACMCertificateArn
      DefaultActions:
        - Type: forward
          TargetGroupArn:
            Fn::ImportValue: !Sub '${EnvironmentName}-core-target-group-arn'

  # Update HTTP Listener to Redirect to HTTPS
  HttpToHttpsRedirect:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Properties:
      ListenerArn:
        Fn::ImportValue: !Sub '${EnvironmentName}-http-listener-arn'
      Priority: 1
      Conditions:
        - Field: path-pattern
          Values: ['*']
      Actions:
        - Type: redirect
          RedirectConfig:
            Protocol: HTTPS
            Port: 443
            Host: '#{host}'
            Path: '/#{path}'
            Query: '#{query}'
            StatusCode: HTTP_301  # Permanent redirect

  # Optional: Route 53 DNS Record
  DNSRecord:
    Type: AWS::Route53::RecordSet
    Condition: CreateDNSRecord
    Properties:
      HostedZoneId: !Ref Route53HostedZoneId
      Name: !Ref DomainName
      Type: A
      AliasTarget:
        HostedZoneId: !GetAtt 
          - Fn::ImportValue: !Sub '${EnvironmentName}-alb-arn'
          - CanonicalHostedZoneID
        DNSName: !GetAtt 
          - Fn::ImportValue: !Sub '${EnvironmentName}-alb-arn'
          - DNSName
        EvaluateTargetHealth: true

Outputs:
  HttpsListenerArn:
    Description: ARN of the HTTPS listener
    Value: !Ref HttpsListener
    Export:
      Name: !Sub '${EnvironmentName}-https-listener-arn'

  SecureURL:
    Description: HTTPS URL for the backend
    Value: !Sub 'https://${DomainName}'

  SetupInstructions:
    Description: Next steps
    Value: !Sub |
      1. If not using Route 53, create DNS CNAME record:
         Name: ${DomainName}
         Value: ${Fn::ImportValue: '${EnvironmentName}-alb-dns'}
      2. Update Postman base_url to: https://${DomainName}
      3. Update frontend API URLs to use HTTPS
      4. Test: curl https://${DomainName}/api/auth/health

