AWSTemplateFormatVersion: '2010-09-09'
Description: 'TK-LPM Backend - Application Load Balancer for Staging'

Parameters:
  EnvironmentName:
    Description: Environment name prefix
    Type: String
    Default: tk-lpm

Resources:
  # Application Load Balancer
  ApplicationLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub ${EnvironmentName}-staging-alb
      Type: application
      Scheme: internet-facing
      IpAddressType: ipv4
      Subnets:
        - Fn::ImportValue: !Sub ${EnvironmentName}-PublicSubnet1-ID
        - Fn::ImportValue: !Sub ${EnvironmentName}-PublicSubnet2-ID
      SecurityGroups:
        - Fn::ImportValue: !Sub ${EnvironmentName}-ALB-SG-ID
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}-staging-alb
        - Key: Environment
          Value: staging

  # Target Group for Core Gateway
  CoreTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub ${EnvironmentName}-core-staging
      Port: 3000
      Protocol: HTTP
      VpcId:
        Fn::ImportValue: !Sub ${EnvironmentName}-VPC-ID
      TargetType: ip
      HealthCheckEnabled: true
      HealthCheckPath: /health
      HealthCheckProtocol: HTTP
      HealthCheckIntervalSeconds: 30
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 3
      Matcher:
        HttpCode: 200
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}-core-staging
        - Key: Environment
          Value: staging

  # HTTP Listener (Port 80)
  HTTPListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 80
      Protocol: HTTP
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref CoreTargetGroup

  # Listener Rule for API routes
  APIListenerRule:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Properties:
      ListenerArn: !Ref HTTPListener
      Priority: 1
      Conditions:
        - Field: path-pattern
          Values:
            - /api/*
            - /health
      Actions:
        - Type: forward
          TargetGroupArn: !Ref CoreTargetGroup

Outputs:
  LoadBalancerArn:
    Description: ARN of the Application Load Balancer
    Value: !Ref ApplicationLoadBalancer
    Export:
      Name: !Sub ${EnvironmentName}-ALB-ARN

  LoadBalancerDNS:
    Description: DNS name of the Application Load Balancer
    Value: !GetAtt ApplicationLoadBalancer.DNSName
    Export:
      Name: !Sub ${EnvironmentName}-ALB-DNS

  LoadBalancerURL:
    Description: URL of the Application Load Balancer
    Value: !Sub http://${ApplicationLoadBalancer.DNSName}
    Export:
      Name: !Sub ${EnvironmentName}-ALB-URL

  CoreTargetGroupArn:
    Description: ARN of the Core Gateway Target Group
    Value: !Ref CoreTargetGroup
    Export:
      Name: !Sub ${EnvironmentName}-Core-TG-ARN

  HTTPListenerArn:
    Description: ARN of the HTTP Listener
    Value: !Ref HTTPListener
    Export:
      Name: !Sub ${EnvironmentName}-HTTP-Listener-ARN

