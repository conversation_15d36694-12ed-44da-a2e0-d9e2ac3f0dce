AWSTemplateFormatVersion: '2010-09-09'
Description: 'Service Discovery using AWS Cloud Map for TK-LPM microservices'

Parameters:
  EnvironmentName:
    Type: String
    Default: staging
    AllowedValues:
      - dev
      - staging
      - production
    Description: Environment name

Resources:
  # Private DNS Namespace for service discovery
  ServiceDiscoveryNamespace:
    Type: AWS::ServiceDiscovery::PrivateDnsNamespace
    Properties:
      Name: !Sub 'tk-lpm.${EnvironmentName}.local'
      Description: !Sub 'Service discovery namespace for TK-LPM ${EnvironmentName}'
      Vpc:
        Fn::ImportValue: !Sub '${EnvironmentName}-vpc-id'

  # Keycloak Service Discovery
  KeycloakServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: keycloak
      Description: 'Service discovery for Keycloak'
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 10
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 1

  # Core Service Discovery
  CoreServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: core
      Description: 'Service discovery for Core Gateway'
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 10
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 1

  # Auth Service Discovery
  AuthServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: auth
      Description: 'Service discovery for Auth service'
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 10
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 1

  # Case Management Service Discovery
  CaseManagementServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: case-management
      Description: 'Service discovery for Case Management'
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 10
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 1

  # Communication Service Discovery
  CommunicationServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: communication
      Description: 'Service discovery for Communication'
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 10
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 1

  # Document Engine Service Discovery
  DocumentEngineServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: document-engine
      Description: 'Service discovery for Document Engine'
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 10
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 1

  # Quote Engine Service Discovery
  QuoteEngineServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: quote-engine
      Description: 'Service discovery for Quote Engine'
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 10
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 1

  # Task Management Service Discovery
  TaskManagementServiceDiscovery:
    Type: AWS::ServiceDiscovery::Service
    Properties:
      Name: task-management
      Description: 'Service discovery for Task Management'
      DnsConfig:
        DnsRecords:
          - Type: A
            TTL: 10
        NamespaceId: !Ref ServiceDiscoveryNamespace
      HealthCheckCustomConfig:
        FailureThreshold: 1

Outputs:
  NamespaceId:
    Description: Service Discovery Namespace ID
    Value: !Ref ServiceDiscoveryNamespace
    Export:
      Name: !Sub '${EnvironmentName}-service-discovery-namespace-id'

  NamespaceName:
    Description: Service Discovery Namespace Name
    Value: !Sub 'tk-lpm.${EnvironmentName}.local'
    Export:
      Name: !Sub '${EnvironmentName}-service-discovery-namespace-name'

  KeycloakServiceId:
    Description: Keycloak Service Discovery ID
    Value: !Ref KeycloakServiceDiscovery
    Export:
      Name: !Sub '${EnvironmentName}-keycloak-service-discovery-id'

  CoreServiceId:
    Description: Core Service Discovery ID
    Value: !Ref CoreServiceDiscovery
    Export:
      Name: !Sub '${EnvironmentName}-core-service-discovery-id'

  AuthServiceId:
    Description: Auth Service Discovery ID
    Value: !Ref AuthServiceDiscovery
    Export:
      Name: !Sub '${EnvironmentName}-auth-service-discovery-id'

  CaseManagementServiceId:
    Description: Case Management Service Discovery ID
    Value: !Ref CaseManagementServiceDiscovery
    Export:
      Name: !Sub '${EnvironmentName}-case-management-service-discovery-id'

  CommunicationServiceId:
    Description: Communication Service Discovery ID
    Value: !Ref CommunicationServiceDiscovery
    Export:
      Name: !Sub '${EnvironmentName}-communication-service-discovery-id'

  DocumentEngineServiceId:
    Description: Document Engine Service Discovery ID
    Value: !Ref DocumentEngineServiceDiscovery
    Export:
      Name: !Sub '${EnvironmentName}-document-engine-service-discovery-id'

  QuoteEngineServiceId:
    Description: Quote Engine Service Discovery ID
    Value: !Ref QuoteEngineServiceDiscovery
    Export:
      Name: !Sub '${EnvironmentName}-quote-engine-service-discovery-id'

  TaskManagementServiceId:
    Description: Task Management Service Discovery ID
    Value: !Ref TaskManagementServiceDiscovery
    Export:
      Name: !Sub '${EnvironmentName}-task-management-service-discovery-id'

