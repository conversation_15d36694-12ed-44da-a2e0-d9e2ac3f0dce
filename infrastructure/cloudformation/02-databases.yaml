AWSTemplateFormatVersion: '2010-09-09'
Description: 'TK-LPM Backend - RDS PostgreSQL and ElastiCache Redis'

Parameters:
  EnvironmentName:
    Description: Environment name prefix
    Type: String
    Default: tk-lpm

  DBInstanceClass:
    Description: RDS instance type
    Type: String
    Default: db.t3.micro
    AllowedValues:
      - db.t3.micro
      - db.t3.small
      - db.t3.medium
      - db.t3.large

  DBAllocatedStorage:
    Description: Database storage size in GB
    Type: Number
    Default: 20
    MinValue: 20
    MaxValue: 100

  DBName:
    Description: PostgreSQL database name
    Type: String
    Default: tklpm
    AllowedPattern: '[a-zA-Z][a-zA-Z0-9]*'
    ConstraintDescription: Must begin with a letter and contain only alphanumeric characters

  DBUsername:
    Description: Database master username
    Type: String
    Default: postgres
    MinLength: 1
    MaxLength: 16
    AllowedPattern: '[a-zA-Z][a-zA-Z0-9]*'
    ConstraintDescription: Must begin with a letter and contain only alphanumeric characters

  DBPassword:
    Description: Database master password
    Type: String
    NoEcho: true
    MinLength: 8
    MaxLength: 41
    AllowedPattern: '[a-zA-Z0-9!@#$%^&*()_+=-]*'
    ConstraintDescription: Must contain only alphanumeric and special characters
    Default: TKLPMPassword2025!

  RedisNodeType:
    Description: ElastiCache Redis node type
    Type: String
    Default: cache.t3.micro
    AllowedValues:
      - cache.t3.micro
      - cache.t3.small
      - cache.t3.medium
      - cache.m6g.large

  RedisNumCacheNodes:
    Description: Number of cache nodes
    Type: Number
    Default: 1
    MinValue: 1
    MaxValue: 3

Resources:
  # DB Subnet Group
  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupName: !Sub ${EnvironmentName}-db-subnet-group
      DBSubnetGroupDescription: Subnet group for RDS PostgreSQL
      SubnetIds:
        - Fn::ImportValue: !Sub ${EnvironmentName}-PrivateSubnet1-ID
        - Fn::ImportValue: !Sub ${EnvironmentName}-PrivateSubnet2-ID
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}-db-subnet-group
        - Key: Environment
          Value: !Ref EnvironmentName

  # RDS PostgreSQL Instance
  PostgreSQLDatabase:
    Type: AWS::RDS::DBInstance
    DeletionPolicy: Snapshot
    UpdateReplacePolicy: Snapshot
    Properties:
      DBInstanceIdentifier: !Sub ${EnvironmentName}-postgres
      DBName: !Ref DBName
      Engine: postgres
      EngineVersion: '15.10'
      DBInstanceClass: !Ref DBInstanceClass
      AllocatedStorage: !Ref DBAllocatedStorage
      StorageType: gp3
      StorageEncrypted: true
      MasterUsername: !Ref DBUsername
      MasterUserPassword: !Ref DBPassword
      DBSubnetGroupName: !Ref DBSubnetGroup
      VPCSecurityGroups:
        - Fn::ImportValue: !Sub ${EnvironmentName}-RDS-SG-ID
      MultiAZ: false  # Set to true for production
      BackupRetentionPeriod: 7
      PreferredBackupWindow: '03:00-04:00'
      PreferredMaintenanceWindow: 'sun:04:00-sun:05:00'
      EnableCloudwatchLogsExports:
        - postgresql
      DeletionProtection: false  # Set to true for production
      PubliclyAccessible: false
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}-postgres
        - Key: Environment
          Value: !Ref EnvironmentName

  # Store DB credentials in Secrets Manager
  DBSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub ${EnvironmentName}/rds/credentials
      Description: PostgreSQL database credentials
      SecretString: !Sub |
        {
          "username": "${DBUsername}",
          "password": "${DBPassword}",
          "engine": "postgres",
          "host": "${PostgreSQLDatabase.Endpoint.Address}",
          "port": ${PostgreSQLDatabase.Endpoint.Port},
          "dbname": "${DBName}",
          "url": "postgresql://${DBUsername}:${DBPassword}@${PostgreSQLDatabase.Endpoint.Address}:${PostgreSQLDatabase.Endpoint.Port}/${DBName}"
        }
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}/rds/credentials
        - Key: Environment
          Value: !Ref EnvironmentName

  # ElastiCache Subnet Group
  CacheSubnetGroup:
    Type: AWS::ElastiCache::SubnetGroup
    Properties:
      CacheSubnetGroupName: !Sub ${EnvironmentName}-redis-subnet-group
      Description: Subnet group for ElastiCache Redis
      SubnetIds:
        - Fn::ImportValue: !Sub ${EnvironmentName}-PrivateSubnet1-ID
        - Fn::ImportValue: !Sub ${EnvironmentName}-PrivateSubnet2-ID
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}-redis-subnet-group
        - Key: Environment
          Value: !Ref EnvironmentName

  # ElastiCache Redis
  RedisCache:
    Type: AWS::ElastiCache::CacheCluster
    Properties:
      ClusterName: !Sub ${EnvironmentName}-redis
      Engine: redis
      EngineVersion: '7.1'
      CacheNodeType: !Ref RedisNodeType
      NumCacheNodes: !Ref RedisNumCacheNodes
      VpcSecurityGroupIds:
        - Fn::ImportValue: !Sub ${EnvironmentName}-Redis-SG-ID
      CacheSubnetGroupName: !Ref CacheSubnetGroup
      PreferredMaintenanceWindow: 'sun:05:00-sun:06:00'
      SnapshotRetentionLimit: 5
      SnapshotWindow: '03:00-04:00'
      AutoMinorVersionUpgrade: true
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}-redis
        - Key: Environment
          Value: !Ref EnvironmentName

  # Store Redis connection info in Secrets Manager
  RedisSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub ${EnvironmentName}/redis/credentials
      Description: Redis connection information
      SecretString: !Sub |
        {
          "host": "${RedisCache.RedisEndpoint.Address}",
          "port": ${RedisCache.RedisEndpoint.Port},
          "url": "redis://${RedisCache.RedisEndpoint.Address}:${RedisCache.RedisEndpoint.Port}"
        }
      Tags:
        - Key: Name
          Value: !Sub ${EnvironmentName}/redis/credentials
        - Key: Environment
          Value: !Ref EnvironmentName

Outputs:
  DBInstanceIdentifier:
    Description: RDS PostgreSQL Instance Identifier
    Value: !Ref PostgreSQLDatabase
    Export:
      Name: !Sub ${EnvironmentName}-RDS-Instance-ID

  DBEndpointAddress:
    Description: RDS PostgreSQL Endpoint Address
    Value: !GetAtt PostgreSQLDatabase.Endpoint.Address
    Export:
      Name: !Sub ${EnvironmentName}-RDS-Endpoint

  DBEndpointPort:
    Description: RDS PostgreSQL Endpoint Port
    Value: !GetAtt PostgreSQLDatabase.Endpoint.Port
    Export:
      Name: !Sub ${EnvironmentName}-RDS-Port

  DBName:
    Description: Database Name
    Value: !Ref DBName
    Export:
      Name: !Sub ${EnvironmentName}-RDS-DBName

  DBSecretArn:
    Description: ARN of DB credentials secret
    Value: !Ref DBSecret
    Export:
      Name: !Sub ${EnvironmentName}-RDS-Secret-ARN

  RedisEndpointAddress:
    Description: Redis Endpoint Address
    Value: !GetAtt RedisCache.RedisEndpoint.Address
    Export:
      Name: !Sub ${EnvironmentName}-Redis-Endpoint

  RedisEndpointPort:
    Description: Redis Endpoint Port
    Value: !GetAtt RedisCache.RedisEndpoint.Port
    Export:
      Name: !Sub ${EnvironmentName}-Redis-Port

  RedisSecretArn:
    Description: ARN of Redis credentials secret
    Value: !Ref RedisSecret
    Export:
      Name: !Sub ${EnvironmentName}-Redis-Secret-ARN

