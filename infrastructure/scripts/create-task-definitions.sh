#!/usr/bin/env bash
set -e

echo "📝 Creating ECS task definitions for all microservices..."

# Configuration
REGION="us-east-1"
ACCOUNT_ID="************"
ECR_BASE="${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com"
ENV="staging"

# Service configurations (service:port:cpu:memory)
SERVICES=(
  "core:3000:512:1024"
  "auth:3001:512:1024"
  "case-management:3002:512:1024"
  "communication:3003:512:1024"
  "document-engine:3004:512:1024"
  "quote-engine:3005:512:1024"
  "task-management:3006:256:512"
)

# Database and Redis endpoints
DB_HOST="tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com"
REDIS_HOST="tk-lpm-redis.qjixwc.0001.use1.cache.amazonaws.com"
S3_BUCKET="tk-lpm-documents-************"

# Create task definitions
for entry in "${SERVICES[@]}"; do
    IFS=':' read -r service port cpu memory <<< "$entry"
    
    echo "Creating task definition for: $service"
    
    cat > "infrastructure/ecs/${service}-task-definition.json" <<EOF
{
  "family": "tk-lpm-${service}-${ENV}",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "${cpu}",
  "memory": "${memory}",
  "executionRoleArn": "arn:aws:iam::${ACCOUNT_ID}:role/tk-lpm-ecs-task-execution-role",
  "taskRoleArn": "arn:aws:iam::${ACCOUNT_ID}:role/tk-lpm-ecs-task-role",
  "containerDefinitions": [
    {
      "name": "${service}",
      "image": "${ECR_BASE}/tk-lpm/${service}:${ENV}",
      "cpu": ${cpu},
      "memory": ${memory},
      "essential": true,
      "portMappings": [
        {
          "containerPort": ${port},
          "protocol": "tcp",
          "name": "${service}-${port}-tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        },
        {
          "name": "PORT",
          "value": "${port}"
        },
        {
          "name": "CORE_PORT",
          "value": "3000"
        },
        {
          "name": "AUTH_PORT",
          "value": "3001"
        },
        {
          "name": "CASE_MANAGEMENT_PORT",
          "value": "3002"
        },
        {
          "name": "COMMUNICATION_PORT",
          "value": "3003"
        },
        {
          "name": "DOCUMENT_ENGINE_PORT",
          "value": "3004"
        },
        {
          "name": "QUOTE_ENGINE_PORT",
          "value": "3005"
        },
        {
          "name": "TASK_MANAGEMENT_PORT",
          "value": "3006"
        },
        {
          "name": "POSTGRES_HOST",
          "value": "${DB_HOST}"
        },
        {
          "name": "POSTGRES_PORT",
          "value": "5432"
        },
        {
          "name": "POSTGRES_DB",
          "value": "tklpm"
        },
        {
          "name": "POSTGRES_USER",
          "value": "postgres"
        },
        {
          "name": "POSTGRES_PASSWORD",
          "value": "TKLPMPassword2025!"
        },
        {
          "name": "POSTGRES_SSL",
          "value": "false"
        },
        {
          "name": "REDIS_HOST",
          "value": "${REDIS_HOST}"
        },
        {
          "name": "REDIS_PORT",
          "value": "6379"
        },
        {
          "name": "REDIS_USERNAME",
          "value": "default"
        },
        {
          "name": "REDIS_PASSWORD",
          "value": "none"
        },
        {
          "name": "REDIS_DB",
          "value": "0"
        },
        {
          "name": "S3_BUCKET_NAME",
          "value": "${S3_BUCKET}"
        },
        {
          "name": "AWS_REGION",
          "value": "${REGION}"
        },
        {
          "name": "KEYCLOAK_SERVER_URL",
          "value": "http://keycloak.tk-lpm.staging.local:8080"
        },
        {
          "name": "KEYCLOAK_REALM",
          "value": "master"
        },
        {
          "name": "KEYCLOAK_CLIENT_ID",
          "value": "admin-cli"
        },
        {
          "name": "KEYCLOAK_CLIENT_SECRET",
          "value": "AdminPassword2025!"
        },
        {
          "name": "KEYCLOAK_HOST",
          "value": "keycloak.tk-lpm.staging.local"
        },
        {
          "name": "KEYCLOAK_ADMIN",
          "value": "admin"
        },
        {
          "name": "KEYCLOAK_ADMIN_PASSWORD",
          "value": "AdminPassword2025!"
        },
        {
          "name": "KEYCLOAK_HOSTNAME_PORT",
          "value": "8080"
        },
        {
          "name": "CACHE_TTL_CASE_DETAILS",
          "value": "300"
        },
        {
          "name": "CACHE_TTL_TASK_DETAILS",
          "value": "300"
        },
        {
          "name": "JWT_SECRET",
          "value": "your-jwt-secret-key-change-in-production"
        },
        {
          "name": "JWT_EXPIRATION",
          "value": "1h"
        },
        {
          "name": "REFRESH_TOKEN_SECRET",
          "value": "your-refresh-token-secret-change-in-production"
        },
        {
          "name": "REFRESH_TOKEN_EXPIRATION",
          "value": "7d"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/tk-lpm/${service}",
          "awslogs-region": "${REGION}",
          "awslogs-stream-prefix": "ecs",
          "awslogs-create-group": "true"
        }
      }
    }
  ]
}
EOF
    
    echo "✅ Created: infrastructure/ecs/${service}-task-definition.json"
done

echo ""
echo "🎉 All task definitions created successfully!"
echo ""
echo "Next steps:"
echo "1. Register task definitions: for file in infrastructure/ecs/*-task-definition.json; do aws ecs register-task-definition --cli-input-json file://\$file --region ${REGION}; done"
echo "2. Deploy services to ECS cluster"

