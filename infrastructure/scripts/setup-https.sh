#!/usr/bin/env bash
set -e

echo "🔒 HTTPS Setup for TK-LPM Backend"
echo "=================================="
echo ""

# Configuration
REGION="us-east-1"
ENVIRONMENT="staging"

echo "📋 HTTPS Setup Options:"
echo ""
echo "1️⃣  Request ACM Certificate (for your domain)"
echo "2️⃣  Add HTTPS Listener to ALB (after certificate is validated)"
echo "3️⃣  Configure HTTP to HTTPS Redirect"
echo "4️⃣  Create Route 53 DNS Record (if using Route 53)"
echo "5️⃣  Check Setup Status"
echo ""

read -p "Select option (1-5): " OPTION

case $OPTION in
  1)
    echo ""
    echo "📝 Request ACM Certificate"
    echo "=========================="
    echo ""
    read -p "Enter your domain (e.g., api.teklpm.com): " DOMAIN
    read -p "Include wildcard (*.$DOMAIN)? (y/n): " WILDCARD
    
    if [ "$WILDCARD" = "y" ]; then
      ROOT_DOMAIN=$(echo $DOMAIN | sed 's/^[^.]*\.//')
      echo ""
      echo "Requesting certificate for:"
      echo "  - $DOMAIN"
      echo "  - *.$ROOT_DOMAIN (wildcard)"
      echo ""
      
      CERT_ARN=$(aws acm request-certificate \
        --domain-name "$DOMAIN" \
        --subject-alternative-names "*.$ROOT_DOMAIN" \
        --validation-method DNS \
        --region $REGION \
        --output json | jq -r '.CertificateArn')
    else
      echo ""
      echo "Requesting certificate for: $DOMAIN"
      echo ""
      
      CERT_ARN=$(aws acm request-certificate \
        --domain-name "$DOMAIN" \
        --validation-method DNS \
        --region $REGION \
        --output json | jq -r '.CertificateArn')
    fi
    
    echo "✅ Certificate requested!"
    echo "ARN: $CERT_ARN"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Get validation DNS records:"
    echo "   aws acm describe-certificate --certificate-arn $CERT_ARN --region $REGION"
    echo ""
    echo "2. Add CNAME records to your DNS provider"
    echo ""
    echo "3. Wait 5-30 minutes for validation"
    echo ""
    echo "4. Run this script again with option 2"
    ;;
    
  2)
    echo ""
    echo "🔧 Add HTTPS Listener to ALB"
    echo "============================"
    echo ""
    
    # Get ALB ARN
    ALB_ARN=$(aws elbv2 describe-load-balancers \
      --region $REGION \
      --query "LoadBalancers[?contains(LoadBalancerName, 'tk-lpm-$ENVIRONMENT')].LoadBalancerArn" \
      --output text)
    
    echo "ALB: $ALB_ARN"
    echo ""
    
    # Get Target Group ARN
    TG_ARN=$(aws elbv2 describe-target-groups \
      --region $REGION \
      --query "TargetGroups[?contains(TargetGroupName, 'tk-lpm-core')].TargetGroupArn" \
      --output text)
    
    echo "Target Group: $TG_ARN"
    echo ""
    
    read -p "Enter ACM Certificate ARN: " CERT_ARN
    
    echo ""
    echo "Creating HTTPS listener..."
    
    LISTENER_ARN=$(aws elbv2 create-listener \
      --load-balancer-arn "$ALB_ARN" \
      --protocol HTTPS \
      --port 443 \
      --certificates CertificateArn="$CERT_ARN" \
      --ssl-policy ELBSecurityPolicy-TLS13-1-2-2021-06 \
      --default-actions Type=forward,TargetGroupArn="$TG_ARN" \
      --region $REGION \
      --output json | jq -r '.Listeners[0].ListenerArn')
    
    echo "✅ HTTPS listener created!"
    echo "Listener ARN: $LISTENER_ARN"
    echo ""
    echo "Run option 3 to enable HTTP→HTTPS redirect"
    ;;
    
  3)
    echo ""
    echo "🔀 Configure HTTP to HTTPS Redirect"
    echo "===================================="
    echo ""
    
    # Get HTTP Listener ARN
    ALB_ARN=$(aws elbv2 describe-load-balancers \
      --region $REGION \
      --query "LoadBalancers[?contains(LoadBalancerName, 'tk-lpm-$ENVIRONMENT')].LoadBalancerArn" \
      --output text)
    
    HTTP_LISTENER=$(aws elbv2 describe-listeners \
      --load-balancer-arn "$ALB_ARN" \
      --region $REGION \
      --query "Listeners[?Port==\`80\`].ListenerArn" \
      --output text)
    
    echo "HTTP Listener: $HTTP_LISTENER"
    echo ""
    echo "Configuring redirect..."
    
    aws elbv2 modify-listener \
      --listener-arn "$HTTP_LISTENER" \
      --default-actions Type=redirect,RedirectConfig="{Protocol=HTTPS,Port=443,StatusCode=HTTP_301}" \
      --region $REGION > /dev/null
    
    echo "✅ HTTP→HTTPS redirect enabled!"
    echo ""
    echo "All HTTP traffic will now redirect to HTTPS"
    ;;
    
  4)
    echo ""
    echo "🌐 Create Route 53 DNS Record"
    echo "=============================="
    echo ""
    
    # List hosted zones
    echo "Available Hosted Zones:"
    aws route53 list-hosted-zones --output table --query 'HostedZones[*].[Id,Name]'
    echo ""
    
    read -p "Enter Hosted Zone ID: " ZONE_ID
    read -p "Enter subdomain (e.g., api.teklpm.com): " DOMAIN
    
    # Get ALB DNS
    ALB_DNS=$(aws elbv2 describe-load-balancers \
      --region $REGION \
      --query "LoadBalancers[?contains(LoadBalancerName, 'tk-lpm-$ENVIRONMENT')].DNSName" \
      --output text)
    
    ALB_ZONE=$(aws elbv2 describe-load-balancers \
      --region $REGION \
      --query "LoadBalancers[?contains(LoadBalancerName, 'tk-lpm-$ENVIRONMENT')].CanonicalHostedZoneId" \
      --output text)
    
    echo ""
    echo "Creating DNS record: $DOMAIN → $ALB_DNS"
    echo ""
    
    aws route53 change-resource-record-sets \
      --hosted-zone-id "$ZONE_ID" \
      --change-batch "{
        \"Changes\": [{
          \"Action\": \"CREATE\",
          \"ResourceRecordSet\": {
            \"Name\": \"$DOMAIN\",
            \"Type\": \"A\",
            \"AliasTarget\": {
              \"HostedZoneId\": \"$ALB_ZONE\",
              \"DNSName\": \"$ALB_DNS\",
              \"EvaluateTargetHealth\": true
            }
          }
        }]
      }" > /dev/null
    
    echo "✅ DNS record created!"
    echo ""
    echo "Wait 5-10 minutes for DNS propagation"
    echo "Test: curl https://$DOMAIN/api/auth/health"
    ;;
    
  5)
    echo ""
    echo "📊 HTTPS Setup Status"
    echo "====================="
    echo ""
    
    # Check certificates
    echo "📜 ACM Certificates:"
    aws acm list-certificates --region $REGION --output table --query 'CertificateSummaryList[*].[DomainName,Status]'
    echo ""
    
    # Check ALB listeners
    ALB_ARN=$(aws elbv2 describe-load-balancers \
      --region $REGION \
      --query "LoadBalancers[?contains(LoadBalancerName, 'tk-lpm-$ENVIRONMENT')].LoadBalancerArn" \
      --output text)
    
    echo "🔌 ALB Listeners:"
    aws elbv2 describe-listeners \
      --load-balancer-arn "$ALB_ARN" \
      --region $REGION \
      --output table \
      --query 'Listeners[*].[Protocol,Port,SslPolicy]'
    echo ""
    
    # Check DNS
    ALB_DNS=$(aws elbv2 describe-load-balancers \
      --region $REGION \
      --query "LoadBalancers[?contains(LoadBalancerName, 'tk-lpm-$ENVIRONMENT')].DNSName" \
      --output text)
    
    echo "🌐 ALB DNS: $ALB_DNS"
    echo ""
    echo "Test with: curl http://$ALB_DNS/api/auth/health"
    ;;
    
  *)
    echo "Invalid option"
    exit 1
    ;;
esac

echo ""
echo "=" | head -c 80
echo ""
echo "✅ Done!"

