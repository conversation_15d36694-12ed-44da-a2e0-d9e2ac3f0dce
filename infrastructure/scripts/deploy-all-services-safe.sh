#!/bin/bash

###############################################################################
# Safe Sequential Service Deployment Script
# 
# This script deploys all microservices one at a time with proper delays
# to avoid AWS rate limits on network interface creation in public subnets.
#
# Usage: ./deploy-all-services-safe.sh [staging|dev|prod]
###############################################################################

set -e  # Exit on error

# Configuration
ENVIRONMENT="${1:-staging}"
CLUSTER="tk-lpm-${ENVIRONMENT}-cluster"
REGION="us-east-1"
DELAY_BETWEEN_DEPLOYMENTS=90  # seconds
MAX_WAIT_TIME=300  # 5 minutes per service

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Services to deploy (order matters - deploy critical services first)
# Note: keycloak is excluded as it's infrastructure and rarely needs redeployment
SERVICES=(
    "core"
    "auth"
    "quote-engine"
    "communication"
    "document-engine"
    "task-management"
    "case-management"
)

###############################################################################
# Functions
###############################################################################

print_header() {
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${BLUE}$1${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
}

log_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

log_success() {
    echo -e "${GREEN}✅${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

log_error() {
    echo -e "${RED}❌${NC} $1"
}

# Check if service exists
service_exists() {
    local service=$1
    aws ecs describe-services \
        --cluster "$CLUSTER" \
        --services "$service" \
        --region "$REGION" \
        --query 'services[0].status' \
        --output text 2>/dev/null | grep -q "ACTIVE"
}

# Get service running count
get_running_count() {
    local service=$1
    aws ecs describe-services \
        --cluster "$CLUSTER" \
        --services "$service" \
        --region "$REGION" \
        --query 'services[0].runningCount' \
        --output text 2>/dev/null || echo "0"
}

# Get service desired count
get_desired_count() {
    local service=$1
    aws ecs describe-services \
        --cluster "$CLUSTER" \
        --services "$service" \
        --region "$REGION" \
        --query 'services[0].desiredCount' \
        --output text 2>/dev/null || echo "0"
}

# Deploy a single service
deploy_service() {
    local service=$1
    local service_num=$2
    local total_services=$3
    
    print_header "[$service_num/$total_services] Deploying $service"
    
    # Check if service exists
    if ! service_exists "$service"; then
        log_warning "Service '$service' does not exist in cluster '$CLUSTER'. Skipping."
        return 0
    fi
    
    # Get current state
    local desired=$(get_desired_count "$service")
    local running=$(get_running_count "$service")
    
    log_info "Current state: $running/$desired tasks running"
    
    # Trigger deployment
    log_info "Triggering force new deployment..."
    aws ecs update-service \
        --cluster "$CLUSTER" \
        --service "$service" \
        --force-new-deployment \
        --region "$REGION" \
        --output text > /dev/null
    
    log_success "Deployment triggered for $service"
    
    # Wait for service to stabilize
    log_info "Waiting for $service to stabilize (max ${MAX_WAIT_TIME}s)..."
    
    local elapsed=0
    local check_interval=15
    
    while [ $elapsed -lt $MAX_WAIT_TIME ]; do
        sleep $check_interval
        elapsed=$((elapsed + check_interval))
        
        running=$(get_running_count "$service")
        
        if [ "$running" = "$desired" ] && [ "$desired" != "0" ]; then
            log_success "$service is stable ($running/$desired tasks running)"
            return 0
        fi
        
        log_info "Check $(($elapsed / $check_interval)): $running/$desired tasks running..."
    done
    
    # Service didn't stabilize in time
    log_warning "$service did not stabilize within ${MAX_WAIT_TIME}s"
    log_warning "Current state: $running/$desired tasks"
    log_warning "Continuing anyway... Check service logs if needed"
    
    return 0
}

###############################################################################
# Main Script
###############################################################################

print_header "🚀 Safe Sequential Service Deployment"

log_info "Environment: $ENVIRONMENT"
log_info "Cluster: $CLUSTER"
log_info "Region: $REGION"
log_info "Services to deploy: ${#SERVICES[@]}"
log_info "Delay between deployments: ${DELAY_BETWEEN_DEPLOYMENTS}s"

echo ""
read -p "Continue with deployment? (y/n) " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_warning "Deployment cancelled by user"
    exit 0
fi

# Deploy each service sequentially
total_services=${#SERVICES[@]}
deployed_count=0
failed_services=()

for i in "${!SERVICES[@]}"; do
    service="${SERVICES[$i]}"
    service_num=$((i + 1))
    
    if deploy_service "$service" "$service_num" "$total_services"; then
        deployed_count=$((deployed_count + 1))
        
        # Add delay between deployments (except after last service)
        if [ $service_num -lt $total_services ]; then
            log_info "Waiting ${DELAY_BETWEEN_DEPLOYMENTS}s before next deployment to avoid AWS rate limits..."
            sleep $DELAY_BETWEEN_DEPLOYMENTS
        fi
    else
        failed_services+=("$service")
    fi
done

###############################################################################
# Summary
###############################################################################

print_header "📊 Deployment Summary"

log_info "Deployed: $deployed_count/$total_services services"

if [ ${#failed_services[@]} -eq 0 ]; then
    log_success "All services deployed successfully!"
else
    log_warning "Some services had issues:"
    for service in "${failed_services[@]}"; do
        echo "  • $service"
    done
fi

echo ""
log_info "Check service health:"
echo "  curl https://api.tklpm.com/api/health"
echo "  curl https://api.tklpm.com/api/auth/health"
echo ""

log_info "Monitor services:"
echo "  aws ecs describe-services --cluster $CLUSTER --services ${SERVICES[*]} --region $REGION"
echo ""

print_header "✅ Deployment Complete"

