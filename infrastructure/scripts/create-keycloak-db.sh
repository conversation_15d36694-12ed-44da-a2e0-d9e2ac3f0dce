#!/bin/bash
set -e

echo "🔍 Retrieving RDS credentials from Secrets Manager..."

# Get RDS credentials
RDS_SECRET=$(aws secretsmanager get-secret-value \
  --secret-id tk-lpm/rds/credentials \
  --region us-east-1 \
  --query SecretString \
  --output text)

RDS_HOST=$(echo $RDS_SECRET | jq -r '.host')
RDS_PORT=$(echo $RDS_SECRET | jq -r '.port')
RDS_USER=$(echo $RDS_SECRET | jq -r '.username')
RDS_PASSWORD=$(echo $RDS_SECRET | jq -r '.password')

# Get Keycloak DB credentials
KEYCLOAK_SECRET=$(aws secretsmanager get-secret-value \
  --secret-id tk-lpm/keycloak/db-credentials \
  --region us-east-1 \
  --query SecretString \
  --output text)

KEYCLOAK_DB_NAME=$(echo $KEYCLOAK_SECRET | jq -r '.dbname')
KEYCLOAK_DB_USER=$(echo $KEYCLOAK_SECRET | jq -r '.username')
KEYCLOAK_DB_PASSWORD=$(echo $KEYCLOAK_SECRET | jq -r '.password')

echo "📦 Installing PostgreSQL client (if not already installed)..."
if ! command -v psql &> /dev/null; then
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "Installing PostgreSQL via Homebrew..."
        brew install postgresql
    else
        echo "Please install PostgreSQL client manually"
        exit 1
    fi
fi

echo "🔗 Connecting to RDS PostgreSQL..."
echo "Host: $RDS_HOST"
echo "Port: $RDS_PORT"
echo ""

# Create Keycloak database and user
PGPASSWORD=$RDS_PASSWORD psql -h $RDS_HOST -p $RDS_PORT -U $RDS_USER -d postgres <<EOF
-- Create Keycloak database
CREATE DATABASE ${KEYCLOAK_DB_NAME};

-- Create Keycloak user
CREATE USER ${KEYCLOAK_DB_USER} WITH PASSWORD '${KEYCLOAK_DB_PASSWORD}';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE ${KEYCLOAK_DB_NAME} TO ${KEYCLOAK_DB_USER};

-- Connect to Keycloak database and grant schema privileges
\c ${KEYCLOAK_DB_NAME}
GRANT ALL ON SCHEMA public TO ${KEYCLOAK_DB_USER};
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO ${KEYCLOAK_DB_USER};
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO ${KEYCLOAK_DB_USER};

-- Verify
\l
\du
EOF

echo ""
echo "✅ Keycloak database created successfully!"
echo "   Database: $KEYCLOAK_DB_NAME"
echo "   User: $KEYCLOAK_DB_USER"
echo "   Host: $RDS_HOST:$RDS_PORT"

