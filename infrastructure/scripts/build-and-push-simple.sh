#!/usr/bin/env bash
set -e

echo "🐳 Building and pushing Docker images to ECR (using pre-built artifacts)..."
echo ""

# Configuration
REGION="us-east-1"
ACCOUNT_ID="************"
ECR_BASE="${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com"
TAG="${1:-latest}"

# Services (service:port format)
SERVICES=(
  "core:3000"
  "auth:3001"
  "case-management:3002"
  "communication:3003"
  "document-engine:3004"
  "quote-engine:3005"
  "task-management:3006"
)

echo "📋 Services to build and push:"
for entry in "${SERVICES[@]}"; do
    service="${entry%%:*}"
    port="${entry##*:}"
    echo "  - $service (port $port)"
done
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if dist folder exists
if [ ! -d "dist" ]; then
    echo "❌ dist folder not found. Please build the services first:"
    echo "   yarn build:all"
    exit 1
fi

# Login to ECR
echo "🔐 Logging into ECR..."
aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ECR_BASE
echo ""

# Build and push each service
for entry in "${SERVICES[@]}"; do
    service="${entry%%:*}"
    port="${entry##*:}"
    
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "📦 Building: tk-lpm/${service} (port ${port})"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # Check if service was built
    if [ ! -d "dist/apps/${service}" ]; then
        echo "⚠️  Service ${service} not built. Skipping..."
        continue
    fi
    
    # Build the image
    echo "⚙️  Building Docker image..."
    docker build \
        --build-arg SERVICE=${service} \
        --build-arg PORT=${port} \
        -f Dockerfile.production \
        -t tk-lpm/${service}:${TAG} \
        -t ${ECR_BASE}/tk-lpm/${service}:${TAG} \
        -t ${ECR_BASE}/tk-lpm/${service}:latest \
        .
    
    if [ $? -eq 0 ]; then
        echo "✅ Build successful"
    else
        echo "❌ Build failed for ${service}"
        exit 1
    fi
    
    # Push the image
    echo "📤 Pushing to ECR..."
    docker push ${ECR_BASE}/tk-lpm/${service}:${TAG}
    docker push ${ECR_BASE}/tk-lpm/${service}:latest
    
    if [ $? -eq 0 ]; then
        echo "✅ Push successful"
    else
        echo "❌ Push failed for ${service}"
        exit 1
    fi
    
    echo ""
done

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎉 All images built and pushed successfully!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Verify images in ECR
echo "📊 Verifying images in ECR..."
echo ""
for entry in "${SERVICES[@]}"; do
    service="${entry%%:*}"
    echo "🔍 tk-lpm/${service}:"
    aws ecr describe-images \
        --repository-name tk-lpm/${service} \
        --region $REGION \
        --query 'sort_by(imageDetails,& imagePushedAt)[-1:].{Tag:imageTags[0],Pushed:imagePushedAt,Size:imageSizeInBytes}' \
        --output table 2>/dev/null || echo "  No images found"
done

echo ""
echo "✅ Build and push process completed!"

