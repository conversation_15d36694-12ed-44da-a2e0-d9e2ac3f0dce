#!/usr/bin/env bash
set -e

echo "🔄 Running Tenant Migrations on AWS ECS"
echo "======================================="
echo ""

REGION="us-east-1"
CLUSTER="tk-lpm-staging-cluster"

# Get subnet from running service
echo "📋 Getting network configuration..."
AUTH_TASK=$(aws ecs list-tasks --cluster $CLUSTER --service-name auth --region $REGION --output json | jq -r '.taskArns[0]')
SUBNET_ID=$(aws ecs describe-tasks --cluster $CLUSTER --tasks $AUTH_TASK --region $REGION --output json | jq -r '.tasks[0].attachments[0].details[] | select(.name=="subnetId") | .value')
SG_ID=$(aws ecs describe-services --cluster $CLUSTER --services auth --region $REGION --output json | jq -r '.services[0].networkConfiguration.awsvpcConfiguration.securityGroups[0]')

echo "  Subnet: $SUBNET_ID"
echo "  Security Group: $SG_ID"
echo ""

echo "🚀 Running tenant migrations on AWS ECS..."
TASK_ARN=$(aws ecs run-task \
  --cluster $CLUSTER \
  --task-definition tk-lpm-core-staging \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_ID],securityGroups=[$SG_ID],assignPublicIp=ENABLED}" \
  --overrides "{
    \"memory\": \"2048\",
    \"cpu\": \"1024\",
    \"containerOverrides\": [{
      \"name\": \"core\",
      \"memory\": 2048,
      \"environment\": [
        {\"name\": \"NODE_ENV\", \"value\": \"production\"},
        {\"name\": \"NODE_OPTIONS\", \"value\": \"--max-old-space-size=1536\"},
        {\"name\": \"DB_HOST\", \"value\": \"tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com\"},
        {\"name\": \"POSTGRES_HOST\", \"value\": \"tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com\"},
        {\"name\": \"POSTGRES_PORT\", \"value\": \"5432\"},
        {\"name\": \"POSTGRES_USER\", \"value\": \"postgres\"},
        {\"name\": \"POSTGRES_PASSWORD\", \"value\": \"TKLPMPassword2025!\"},
        {\"name\": \"POSTGRES_DB\", \"value\": \"tklpm\"},
        {\"name\": \"POSTGRES_SSL\", \"value\": \"false\"}
      ],
      \"command\": [\"sh\", \"-c\", \"cd /app && echo 'Starting tenant migrations...' && npx ts-node -r tsconfig-paths/register scripts/enhanced-tenant-migration.ts run && echo 'Migrations completed!' && sleep 5\"]
    }]
  }" \
  --region $REGION \
  --output json | jq -r '.tasks[0].taskArn')

TASK_ID=$(echo $TASK_ARN | rev | cut -d'/' -f1 | rev)
echo "✅ Task started: $TASK_ID"
echo ""
echo "⏳ Waiting for tenant migrations to complete (max 10 minutes)..."

# Wait for task
aws ecs wait tasks-stopped \
  --cluster $CLUSTER \
  --tasks $TASK_ARN \
  --region $REGION

# Check result
EXIT_CODE=$(aws ecs describe-tasks --cluster $CLUSTER --tasks $TASK_ARN --region $REGION --output json | jq -r '.tasks[0].containers[0].exitCode')

echo ""
if [ "$EXIT_CODE" = "0" ]; then
  echo "✅ Tenant migrations completed successfully!"
  echo ""
  echo "🧪 Testing quote communications endpoint..."
  curl -X GET "https://api.tklpm.com/api/quote-engine/quotes/test-quote-id/communications/paginated?page=1&limit=20" \
    -H "Authorization: Bearer test-token" \
    -H "x-tenant-id: 647b8cd5-c59a-4f86-aad8-4f1f83044089" \
    -H "x-realm: decepticon" \
    --max-time 30 || echo "Endpoint test completed"
else
  echo "⚠️  Task exit code: $EXIT_CODE"
fi

echo ""
echo "📜 Migration output:"
aws logs tail /ecs/tk-lpm/core --region $REGION --since 5m | grep -E "migration|Migration|CREATE TABLE|ALTER TABLE|Starting tenant|Migrations completed" | tail -50

echo ""
echo "=" | head -c 80
echo ""
echo "🎉 Tenant migration process complete!"
echo ""

