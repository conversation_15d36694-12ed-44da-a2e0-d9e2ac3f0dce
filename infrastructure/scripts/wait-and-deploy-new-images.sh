#!/usr/bin/env bash
set -e

echo "⏳ Waiting for new images in ECR..."
echo ""

REGION="us-east-1"
CLUSTER="tk-lpm-staging-cluster"
TARGET_TAG="staging-09fa785"  # The commit SHA from the SSL fix

# Services to check
SERVICES=("core" "auth" "case-management" "communication" "document-engine" "quote-engine" "task-management")

echo "Looking for images with tag: ${TARGET_TAG}"
echo ""

# Wait for all images to be available
for service in "${SERVICES[@]}"; do
    echo "Checking tk-lpm/${service}..."
    
    MAX_ATTEMPTS=60  # 10 minutes (60 * 10 seconds)
    ATTEMPT=0
    
    while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
        IMAGE_EXISTS=$(aws ecr describe-images \
            --repository-name tk-lpm/${service} \
            --region $REGION \
            --image-ids imageTag=${TARGET_TAG} \
            --query 'imageDetails[0].imageTags[0]' \
            --output text 2>/dev/null || echo "None")
        
        if [ "$IMAGE_EXISTS" != "None" ] && [ "$IMAGE_EXISTS" != "" ]; then
            echo "✅ Found: tk-lpm/${service}:${TARGET_TAG}"
            break
        fi
        
        ATTEMPT=$((ATTEMPT + 1))
        if [ $((ATTEMPT % 6)) -eq 0 ]; then
            echo "   Still waiting... (${ATTEMPT}/60 attempts)"
        fi
        sleep 10
    done
    
    if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
        echo "❌ Timeout waiting for ${service}"
        exit 1
    fi
done

echo ""
echo "🎉 All images are ready in ECR!"
echo ""
echo "🚀 Updating ECS services to use new images..."
echo ""

# Update all services to use the new images
for service in "${SERVICES[@]}"; do
    echo "Updating: $service"
    aws ecs update-service \
        --cluster $CLUSTER \
        --service $service \
        --force-new-deployment \
        --region $REGION \
        --query 'service.serviceName' \
        --output text
done

echo ""
echo "✅ All services updated!"
echo ""
echo "📊 Services will restart with new images in ~2-3 minutes"
echo ""
echo "Monitor with:"
echo "aws ecs describe-services --cluster $CLUSTER --services core --region $REGION --query 'services[0].[serviceName,runningCount,desiredCount]'"

