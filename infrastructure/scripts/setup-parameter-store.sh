#!/bin/bash
set -e

echo "🔧 Setting up AWS Systems Manager Parameter Store for TK-LPM..."

REGION="us-east-1"
ENV="tk-lpm"

# Get values from CloudFormation outputs and Secrets Manager
echo "📊 Retrieving configuration values..."

# VPC and Networking
VPC_ID=$(aws cloudformation describe-stacks --stack-name ${ENV}-vpc --region $REGION --query 'Stacks[0].Outputs[?OutputKey==`VPCId`].OutputValue' --output text)
PRIVATE_SUBNET_1=$(aws cloudformation describe-stacks --stack-name ${ENV}-vpc --region $REGION --query 'Stacks[0].Outputs[?OutputKey==`PrivateSubnet1`].OutputValue' --output text)
PRIVATE_SUBNET_2=$(aws cloudformation describe-stacks --stack-name ${ENV}-vpc --region $REGION --query 'Stacks[0].Outputs[?OutputKey==`PrivateSubnet2`].OutputValue' --output text)
ECS_SG=$(aws cloudformation describe-stacks --stack-name ${ENV}-vpc --region $REGION --query 'Stacks[0].Outputs[?OutputKey==`ECSSecurityGroup`].OutputValue' --output text)

# Database
RDS_SECRET=$(aws secretsmanager get-secret-value --secret-id ${ENV}/rds/credentials --region $REGION --query SecretString --output text)
RDS_HOST=$(echo $RDS_SECRET | jq -r '.host')
RDS_PORT=$(echo $RDS_SECRET | jq -r '.port')
RDS_USER=$(echo $RDS_SECRET | jq -r '.username')
RDS_PASSWORD=$(echo $RDS_SECRET | jq -r '.password')
RDS_DBNAME=$(echo $RDS_SECRET | jq -r '.dbname')
RDS_URL=$(echo $RDS_SECRET | jq -r '.url')

# Redis
REDIS_SECRET=$(aws secretsmanager get-secret-value --secret-id ${ENV}/redis/credentials --region $REGION --query SecretString --output text)
REDIS_HOST=$(echo $REDIS_SECRET | jq -r '.host')
REDIS_PORT=$(echo $REDIS_SECRET | jq -r '.port')
REDIS_URL=$(echo $REDIS_SECRET | jq -r '.url')

# S3
S3_SECRET=$(aws secretsmanager get-secret-value --secret-id ${ENV}/s3/config --region $REGION --query SecretString --output text)
S3_BUCKET=$(echo $S3_SECRET | jq -r '.bucketName')
S3_REGION=$(echo $S3_SECRET | jq -r '.region')

# Keycloak
KEYCLOAK_DB_SECRET=$(aws secretsmanager get-secret-value --secret-id ${ENV}/keycloak/db-credentials --region $REGION --query SecretString --output text)
KEYCLOAK_DB_URL=$(echo $KEYCLOAK_DB_SECRET | jq -r '.url')

KEYCLOAK_ADMIN_SECRET=$(aws secretsmanager get-secret-value --secret-id ${ENV}/keycloak/admin-credentials --region $REGION --query SecretString --output text)
KEYCLOAK_ADMIN_USER=$(echo $KEYCLOAK_ADMIN_SECRET | jq -r '.username')
KEYCLOAK_ADMIN_PASSWORD=$(echo $KEYCLOAK_ADMIN_SECRET | jq -r '.password')

echo "💾 Creating Parameter Store entries..."

# Common parameters
aws ssm put-parameter --name "/${ENV}/common/NODE_ENV" --value "production" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/common/LOG_LEVEL" --value "info" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/common/TZ" --value "UTC" --type String --overwrite --region $REGION

# Database parameters
aws ssm put-parameter --name "/${ENV}/database/DB_HOST" --value "$RDS_HOST" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/database/DB_PORT" --value "$RDS_PORT" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/database/DB_NAME" --value "$RDS_DBNAME" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/database/DB_USER" --value "$RDS_USER" --type SecureString --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/database/DB_PASSWORD" --value "$RDS_PASSWORD" --type SecureString --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/database/DATABASE_URL" --value "$RDS_URL" --type SecureString --overwrite --region $REGION

# Redis parameters
aws ssm put-parameter --name "/${ENV}/redis/REDIS_HOST" --value "$REDIS_HOST" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/redis/REDIS_PORT" --value "$REDIS_PORT" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/redis/REDIS_URL" --value "$REDIS_URL" --type SecureString --overwrite --region $REGION

# S3 parameters
aws ssm put-parameter --name "/${ENV}/s3/S3_BUCKET_NAME" --value "$S3_BUCKET" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/s3/S3_REGION" --value "$S3_REGION" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/s3/AWS_REGION" --value "$S3_REGION" --type String --overwrite --region $REGION

# Keycloak parameters
aws ssm put-parameter --name "/${ENV}/keycloak/KEYCLOAK_ADMIN" --value "$KEYCLOAK_ADMIN_USER" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/keycloak/KEYCLOAK_ADMIN_PASSWORD" --value "$KEYCLOAK_ADMIN_PASSWORD" --type SecureString --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/keycloak/KC_DB" --value "postgres" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/keycloak/KC_DB_URL_HOST" --value "$RDS_HOST" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/keycloak/KC_DB_URL_PORT" --value "$RDS_PORT" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/keycloak/KC_DB_URL_DATABASE" --value "keycloak" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/keycloak/KC_DB_USERNAME" --value "keycloak" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/keycloak/KC_DB_PASSWORD" --value "KeycloakPassword2025!" --type SecureString --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/keycloak/KC_HOSTNAME" --value "keycloak.${ENV}.internal" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/keycloak/KC_HTTP_ENABLED" --value "true" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/keycloak/KC_PROXY" --value "edge" --type String --overwrite --region $REGION

# Service-specific ports
aws ssm put-parameter --name "/${ENV}/ports/CORE_PORT" --value "3000" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/ports/AUTH_PORT" --value "3001" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/ports/CASE_MANAGEMENT_PORT" --value "3002" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/ports/COMMUNICATION_PORT" --value "3003" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/ports/DOCUMENT_ENGINE_PORT" --value "3004" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/ports/QUOTE_ENGINE_PORT" --value "3005" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/ports/TASK_MANAGEMENT_PORT" --value "3006" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/ports/KEYCLOAK_PORT" --value "8080" --type String --overwrite --region $REGION

# JWT secrets (generate random secrets)
JWT_SECRET=$(openssl rand -base64 32)
REFRESH_TOKEN_SECRET=$(openssl rand -base64 32)
aws ssm put-parameter --name "/${ENV}/auth/JWT_SECRET" --value "$JWT_SECRET" --type SecureString --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/auth/REFRESH_TOKEN_SECRET" --value "$REFRESH_TOKEN_SECRET" --type SecureString --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/auth/JWT_EXPIRATION" --value "1h" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/auth/REFRESH_TOKEN_EXPIRATION" --value "7d" --type String --overwrite --region $REGION

# Internal service URLs (will be set once we have Cloud Map)
aws ssm put-parameter --name "/${ENV}/services/AUTH_SERVICE_URL" --value "http://auth.tk-lpm.internal:3001" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/services/CASE_MANAGEMENT_URL" --value "http://case-management.tk-lpm.internal:3002" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/services/COMMUNICATION_URL" --value "http://communication.tk-lpm.internal:3003" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/services/DOCUMENT_ENGINE_URL" --value "http://document-engine.tk-lpm.internal:3004" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/services/QUOTE_ENGINE_URL" --value "http://quote-engine.tk-lpm.internal:3005" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/services/TASK_MANAGEMENT_URL" --value "http://task-management.tk-lpm.internal:3006" --type String --overwrite --region $REGION
aws ssm put-parameter --name "/${ENV}/services/KEYCLOAK_URL" --value "http://keycloak.tk-lpm.internal:8080" --type String --overwrite --region $REGION

echo ""
echo "✅ Parameter Store setup completed!"
echo ""
echo "📋 Created parameters in: /${ENV}/*"
echo ""
echo "To view all parameters:"
echo "aws ssm get-parameters-by-path --path /${ENV} --recursive --region $REGION"

