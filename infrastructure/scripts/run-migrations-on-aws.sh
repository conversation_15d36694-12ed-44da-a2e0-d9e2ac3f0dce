#!/usr/bin/env bash
set -e

echo "🔄 Running Database Migrations on AWS RDS via ECS Task"
echo "======================================================"
echo ""

REGION="us-east-1"
CLUSTER="tk-lpm-staging-cluster"
SUBNET_ID="subnet-00c1390e5b2292c22"  # Private subnet ID (from Auth service)
SG_ID="sg-04998ba0fcf905335"  # ECS security group

echo "This will run migrations using a one-off ECS task..."
echo ""

# Use the Auth service task definition as base (it has all the DB env vars)
echo "📋 Creating migration task definition based on Auth service..."

cat > /tmp/migration-task.json <<'EOF'
{
  "family": "tk-lpm-migrations",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-execution-role",
  "taskRoleArn": "arn:aws:iam::039612857103:role/tk-lpm-ecs-task-role",
  "containerDefinitions": [
    {
      "name": "migrations",
      "image": "039612857103.dkr.ecr.us-east-1.amazonaws.com/tk-lpm/auth:staging",
      "essential": true,
      "command": ["/bin/sh", "-c", "yarn migration:run:all"],
      "environment": [
        {"name": "NODE_ENV", "value": "production"},
        {"name": "POSTGRES_HOST", "value": "tk-lpm-postgres.cneyigycexca.us-east-1.rds.amazonaws.com"},
        {"name": "POSTGRES_PORT", "value": "5432"},
        {"name": "POSTGRES_USER", "value": "postgres"},
        {"name": "POSTGRES_PASSWORD", "value": "TKLPMPassword2025!"},
        {"name": "POSTGRES_DB", "value": "tklpm"},
        {"name": "POSTGRES_SSL", "value": "false"}
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/tk-lpm/migrations",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "migrations"
        }
      }
    }
  ]
}
EOF

# Create log group if it doesn't exist
echo "📝 Creating CloudWatch log group..."
aws logs create-log-group --log-group-name /ecs/tk-lpm/migrations --region $REGION 2>/dev/null || echo "  Log group already exists"

# Register the task definition
echo "📋 Registering migration task definition..."
aws ecs register-task-definition \
  --cli-input-json file:///tmp/migration-task.json \
  --region $REGION > /dev/null

echo "✅ Task definition registered"
echo ""

# Run the task
echo "🚀 Running migration task..."
TASK_ARN=$(aws ecs run-task \
  --cluster $CLUSTER \
  --task-definition tk-lpm-migrations \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_ID],securityGroups=[$SG_ID],assignPublicIp=DISABLED}" \
  --region $REGION \
  --output json | jq -r '.tasks[0].taskArn')

echo "✅ Migration task started: $TASK_ARN"
echo ""

# Wait for task to complete
echo "⏳ Waiting for migrations to complete (this may take 1-2 minutes)..."
echo ""

aws ecs wait tasks-stopped \
  --cluster $CLUSTER \
  --tasks $TASK_ARN \
  --region $REGION

echo ""
echo "📊 Checking migration result..."

# Get task exit code
EXIT_CODE=$(aws ecs describe-tasks \
  --cluster $CLUSTER \
  --tasks $TASK_ARN \
  --region $REGION \
  --output json | jq -r '.tasks[0].containers[0].exitCode')

if [ "$EXIT_CODE" = "0" ]; then
  echo "✅ Migrations completed successfully!"
else
  echo "❌ Migrations failed with exit code: $EXIT_CODE"
fi

echo ""
echo "📜 Migration logs:"
aws logs tail /ecs/tk-lpm/migrations --region $REGION --since 5m --format short | tail -50

echo ""
echo "=" | head -c 80
echo ""
echo "🎉 Database migration process complete!"
echo ""
echo "Now test: POST /api/auth/create-tenant"
echo ""

