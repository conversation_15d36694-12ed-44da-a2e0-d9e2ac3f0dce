#!/usr/bin/env bash
set -e

echo "🚀 TK-LPM Core Gateway Fix & Deployment Script"
echo "=============================================="
echo ""

REGION="us-east-1"
CLUSTER="tk-lpm-staging-cluster"
ENV="staging"

# Step 1: Register services with Cloud Map
echo "📍 Step 1: Registering services with AWS Cloud Map..."
./infrastructure/scripts/register-services-cloud-map.sh

echo ""
read -p "✋ Services registered. Press ENTER to continue with deployment..."
echo ""

# Step 2: Commit and push code changes
echo "📝 Step 2: Committing Core gateway fixes..."
git add apps/core/src/proxy/proxy.service.ts
git add infrastructure/scripts/
git add CORE-GATEWAY-FIX.md
git commit -m "TKJ-490: Fix Core gateway service discovery for AWS ECS

- Updated proxy.service.ts to use environment variables for service hosts
- Added fallback logic: ENV VAR > Docker service name > localhost
- Created Cloud Map registration script for all microservices
- This enables Core to route to services via DNS in AWS ECS

Fixes: Core hanging during startup, unable to proxy requests" || echo "No changes to commit"

echo ""
echo "📤 Pushing to GitHub..."
git push origin dev

echo ""
echo "⏳ Waiting for GitHub Actions to build Docker image (this takes ~5-10 minutes)..."
echo "   You can monitor progress at: https://github.com/YOUR-ORG/tk-lpm-backend/actions"
echo ""
read -p "✋ Once the build is complete, press ENTER to continue..."
echo ""

# Step 3: Update ECS services to register with Cloud Map
echo "🔗 Step 3: Updating ECS services to use Cloud Map service discovery..."

# Get Cloud Map service IDs
NAMESPACE_ID="ns-gxjxpwdhgitfmpf3"

SERVICES=(
  "auth"
  "case-management"
  "communication"
  "document-engine"
  "quote-engine"
  "task-management"
)

for svc in "${SERVICES[@]}"; do
  echo "   Updating service: $svc"
  
  # Get Cloud Map service ARN
  SERVICE_ARN=$(aws servicediscovery list-services --region "$REGION" --output json | \
    jq -r ".Services[] | select(.Name==\"$svc\") | .Arn")
  
  if [ -z "$SERVICE_ARN" ]; then
    echo "   ❌ Cloud Map service not found for $svc"
    continue
  fi
  
  # Update ECS service to register with Cloud Map
  aws ecs update-service \
    --cluster "$CLUSTER" \
    --service "$svc" \
    --service-registries "registryArn=$SERVICE_ARN" \
    --region "$REGION" \
    --output json > /dev/null
  
  echo "   ✅ Updated $svc to register with Cloud Map"
done

echo ""
echo "✅ All services updated!"
echo ""

# Step 4: Update Core task definition with service host environment variables
echo "📝 Step 4: Creating new Core task definition with service discovery..."

./infrastructure/scripts/create-task-definitions.sh

# Modify Core task definition to add service host environment variables
CORE_TASK_DEF="infrastructure/ecs/core-task-definition.json"

# Use jq to add environment variables
jq '.containerDefinitions[0].environment += [
  {"name": "AUTH_HOST", "value": "auth.tk-lpm.staging.local"},
  {"name": "CASE_MANAGEMENT_HOST", "value": "case-management.tk-lpm.staging.local"},
  {"name": "COMMUNICATION_HOST", "value": "communication.tk-lpm.staging.local"},
  {"name": "DOCUMENT_ENGINE_HOST", "value": "document-engine.tk-lpm.staging.local"},
  {"name": "QUOTE_ENGINE_HOST", "value": "quote-engine.tk-lpm.staging.local"},
  {"name": "TASK_MANAGEMENT_HOST", "value": "task-management.tk-lpm.staging.local"}
]' "$CORE_TASK_DEF" > "${CORE_TASK_DEF}.tmp" && mv "${CORE_TASK_DEF}.tmp" "$CORE_TASK_DEF"

echo "✅ Core task definition updated with service hosts"
echo ""

# Step 5: Register new task definition
echo "📋 Step 5: Registering new Core task definition..."

TASK_DEF_REVISION=$(aws ecs register-task-definition \
  --cli-input-json file://"$CORE_TASK_DEF" \
  --region "$REGION" \
  --output json | jq -r '.taskDefinition.revision')

echo "✅ Registered task definition: tk-lpm-core-staging:$TASK_DEF_REVISION"
echo ""

# Step 6: Update Core service
echo "🔄 Step 6: Updating Core service with new task definition..."

aws ecs update-service \
  --cluster "$CLUSTER" \
  --service core \
  --task-definition "tk-lpm-core-staging:$TASK_DEF_REVISION" \
  --force-new-deployment \
  --region "$REGION" \
  --output json > /dev/null

echo "✅ Core service updated!"
echo ""

# Step 7: Monitor deployment
echo "👀 Step 7: Monitoring Core service deployment..."
echo ""
echo "📊 Watching ECS service status..."
echo "   (Ctrl+C to stop monitoring)"
echo ""

aws ecs wait services-stable \
  --cluster "$CLUSTER" \
  --services core \
  --region "$REGION" && echo "✅ Core service is stable!" || echo "⚠️  Service stability check timed out (this is normal for first deployment)"

echo ""
echo "📜 Recent Core logs:"
aws logs tail /ecs/tk-lpm/core --region "$REGION" --since 2m --format short

echo ""
echo "=" | head -c 80
echo ""
echo "🎉 DEPLOYMENT COMPLETE!"
echo "=" | head -c 80
echo ""
echo "✅ Next Steps:"
echo "1. Test Core health:"
echo "   curl http://tk-lpm-staging-alb-*********.us-east-1.elb.amazonaws.com/health"
echo ""
echo "2. Test service routing:"
echo "   curl http://tk-lpm-staging-alb-*********.us-east-1.elb.amazonaws.com/api/auth/health"
echo "   curl http://tk-lpm-staging-alb-*********.us-east-1.elb.amazonaws.com/api/quote-engine/health"
echo ""
echo "3. Monitor Core logs:"
echo "   aws logs tail /ecs/tk-lpm/core --region us-east-1 --follow"
echo ""
echo "4. Check all service statuses:"
echo "   aws ecs describe-services --cluster tk-lpm-staging-cluster --services core auth quote-engine case-management --region us-east-1 | jq '.services[] | {name: .serviceName, status: .status, running: .runningCount, desired: .desiredCount}'"
echo ""

