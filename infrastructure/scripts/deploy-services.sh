#!/usr/bin/env bash
set -e

echo "🚀 Deploying all microservices to ECS..."

# Configuration
REGION="us-east-1"
CLUSTER="tk-lpm-staging-cluster"
ENV="staging"
SUBNETS="subnet-0cf222a17cc24eb10,subnet-0b108dfe04071cf6d"
SECURITY_GROUP="sg-04998ba0fcf905335"

# Services to deploy
SERVICES=(
  "core"
  "auth"
  "case-management"
  "communication"
  "document-engine"
  "quote-engine"
  "task-management"
)

echo ""
echo "📋 Services to deploy:"
for service in "${SERVICES[@]}"; do
    echo "  - $service"
done
echo ""

# Deploy each service
for service in "${SERVICES[@]}"; do
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "🚀 Deploying: $service"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # Check if service already exists
    SERVICE_EXISTS=$(aws ecs describe-services \
        --cluster $CLUSTER \
        --services $service \
        --region $REGION \
        --query 'services[0].serviceName' \
        --output text 2>/dev/null || echo "None")
    
    if [ "$SERVICE_EXISTS" != "None" ] && [ "$SERVICE_EXISTS" != "" ]; then
        echo "⚠️  Service $service already exists. Updating..."
        aws ecs update-service \
            --cluster $CLUSTER \
            --service $service \
            --task-definition tk-lpm-${service}-${ENV}:1 \
            --force-new-deployment \
            --region $REGION \
            --query 'service.serviceName' \
            --output text
        echo "✅ Updated: $service"
    else
        echo "Creating new service: $service"
        aws ecs create-service \
            --cluster $CLUSTER \
            --service-name $service \
            --task-definition tk-lpm-${service}-${ENV}:1 \
            --desired-count 1 \
            --launch-type FARGATE \
            --network-configuration "awsvpcConfiguration={subnets=[$SUBNETS],securityGroups=[$SECURITY_GROUP],assignPublicIp=DISABLED}" \
            --region $REGION \
            --query 'service.serviceName' \
            --output text
        echo "✅ Created: $service"
    fi
    
    echo ""
done

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎉 All services deployed successfully!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Show cluster status
echo "📊 Cluster Status:"
aws ecs describe-clusters \
    --clusters $CLUSTER \
    --region $REGION \
    --query 'clusters[0].[clusterName,status,activeServicesCount,runningTasksCount,pendingTasksCount]' \
    --output table

echo ""
echo "📋 Service Status:"
aws ecs list-services \
    --cluster $CLUSTER \
    --region $REGION \
    --query 'serviceArns' \
    --output table

echo ""
echo "✅ Deployment complete!"
echo ""
echo "Monitor services with:"
echo "aws ecs describe-services --cluster $CLUSTER --services ${SERVICES[*]} --region $REGION"

