#!/usr/bin/env bash
set -e

echo "🔍 Registering all microservices with AWS Cloud Map..."

NAMESPACE_ID="ns-ggthemi7upjhaokm"
REGION="us-east-1"
ENV="staging"

SERVICES=(
  "auth"
  "case-management"
  "communication"
  "document-engine"
  "quote-engine"
  "task-management"
)

echo "📍 Using namespace: tk-lpm.staging.local (ID: $NAMESPACE_ID)"
echo ""

for svc in "${SERVICES[@]}"; do
  echo "📝 Registering service: $svc"
  
  # Check if service already exists
  EXISTING=$(aws servicediscovery list-services \
    --region "$REGION" \
    --output json | jq -r ".Services[] | select(.Name==\"$svc\") | .Id" || echo "")
  
  if [ -n "$EXISTING" ]; then
    echo "   ✅ Service '$svc' already registered (ID: $EXISTING)"
  else
    # Create the service
    SERVICE_ID=$(aws servicediscovery create-service \
      --name "$svc" \
      --namespace-id "$NAMESPACE_ID" \
      --dns-config "NamespaceId=$NAMESPACE_ID,DnsRecords=[{Type=A,TTL=10}]" \
      --health-check-custom-config FailureThreshold=1 \
      --region "$REGION" \
      --output json | jq -r '.Service.Id')
    
    echo "   ✅ Created service '$svc' (ID: $SERVICE_ID)"
    echo "   🌐 DNS: $svc.tk-lpm.staging.local"
  fi
  
  echo ""
done

echo "✅ All services registered successfully!"
echo ""
echo "📋 Service DNS names:"
for svc in "${SERVICES[@]}"; do
  echo "   - $svc.tk-lpm.staging.local"
done
echo ""
echo "🔍 Next steps:"
echo "1. Update ECS services to register with these Cloud Map services"
echo "2. Update Core task definition with service host environment variables"
echo "3. Rebuild and deploy Core service"

