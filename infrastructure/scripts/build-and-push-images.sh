#!/bin/bash
set -e

echo "🐳 Building and pushing Docker images to ECR..."
echo ""

# Configuration
REGION="us-east-1"
ACCOUNT_ID="************"
ECR_BASE="${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com"
TAG="${1:-latest}"

# Color codes for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Services to build
SERVICES=("core" "auth" "case-management" "communication" "document-engine" "quote-engine")

echo "📋 Services to build and push:"
for service in "${SERVICES[@]}"; do
    echo "  - $service"
done
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Login to ECR
echo "🔐 Logging into ECR..."
aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ECR_BASE
echo ""

# Build and push each service
for service in "${SERVICES[@]}"; do
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${BLUE}📦 Building: tk-lpm/${service}${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # Build the image
    echo -e "${YELLOW}⚙️  Building Docker image...${NC}"
    docker build \
        -f apps/${service}/Dockerfile \
        -t tk-lpm/${service}:${TAG} \
        -t ${ECR_BASE}/tk-lpm/${service}:${TAG} \
        -t ${ECR_BASE}/tk-lpm/${service}:latest \
        --build-arg NODE_ENV=production \
        .
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Build successful${NC}"
    else
        echo -e "${RED}❌ Build failed for ${service}${NC}"
        exit 1
    fi
    
    # Push the image
    echo -e "${YELLOW}📤 Pushing to ECR...${NC}"
    docker push ${ECR_BASE}/tk-lpm/${service}:${TAG}
    docker push ${ECR_BASE}/tk-lpm/${service}:latest
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Push successful${NC}"
    else
        echo -e "${RED}❌ Push failed for ${service}${NC}"
        exit 1
    fi
    
    echo ""
done

# Build task-management (if it has a Dockerfile)
if [ -f "apps/task-management/Dockerfile" ]; then
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${BLUE}📦 Building: tk-lpm/task-management${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    docker build \
        -f apps/task-management/Dockerfile \
        -t tk-lpm/task-management:${TAG} \
        -t ${ECR_BASE}/tk-lpm/task-management:${TAG} \
        -t ${ECR_BASE}/tk-lpm/task-management:latest \
        --build-arg NODE_ENV=production \
        .
    
    docker push ${ECR_BASE}/tk-lpm/task-management:${TAG}
    docker push ${ECR_BASE}/tk-lpm/task-management:latest
    
    echo -e "${GREEN}✅ Task Management built and pushed${NC}"
    echo ""
fi

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo -e "${GREEN}🎉 All images built and pushed successfully!${NC}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# Verify images in ECR
echo "📊 Verifying images in ECR..."
echo ""
for service in "${SERVICES[@]}"; do
    echo "🔍 tk-lpm/${service}:"
    aws ecr describe-images \
        --repository-name tk-lpm/${service} \
        --region $REGION \
        --query 'imageDetails[*].[imageTags[0],imagePushedAt,imageSizeInBytes]' \
        --output table 2>/dev/null || echo "  No images found"
    echo ""
done

echo ""
echo "✅ Build and push process completed!"
echo ""
echo "📋 Next steps:"
echo "  1. Create ECS cluster"
echo "  2. Create task definitions"
echo "  3. Deploy services to ECS"

