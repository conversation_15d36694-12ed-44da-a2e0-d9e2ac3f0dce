{"Version": "2012-10-17", "Statement": [{"Sid": "Route53DomainManagement", "Effect": "Allow", "Action": ["route53domains:RegisterDomain", "route53domains:CheckDomainAvailability", "route53domains:GetDomainDetail", "route53domains:ListDomains", "route53domains:GetOperationDetail", "route53domains:UpdateDomainContact", "route53domains:EnableDomainAutoRenew", "route53domains:DisableDomainAutoRenew"], "Resource": "*"}, {"Sid": "Route53DNSManagement", "Effect": "Allow", "Action": ["route53:CreateHostedZone", "route53:ListHostedZones", "route53:GetHostedZone", "route53:DeleteHostedZone", "route53:ChangeResourceRecordSets", "route53:GetChange", "route53:ListResourceRecordSets"], "Resource": "*"}, {"Sid": "ACMCertificateManagement", "Effect": "Allow", "Action": ["acm:RequestCertificate", "acm:DescribeCertificate", "acm:ListCertificates", "acm:GetCertificate", "acm:DeleteCertificate", "acm:AddTagsToCertificate", "acm:ListTagsForCertificate", "acm:RemoveTagsFromCertificate"], "Resource": "*"}, {"Sid": "LoadBalancerManagement", "Effect": "Allow", "Action": ["elasticloadbalancing:CreateListener", "elasticloadbalancing:ModifyListener", "elasticloadbalancing:DeleteListener", "elasticloadbalancing:DescribeListeners", "elasticloadbalancing:DescribeLoadBalancers", "elasticloadbalancing:DescribeTargetGroups", "elasticloadbalancing:DescribeSSLPolicies", "elasticloadbalancing:ModifyLoadBalancerAttributes"], "Resource": "*"}, {"Sid": "BillingAccess", "Effect": "Allow", "Action": ["aws-portal:ViewBilling", "aws-portal:ViewPaymentMethods", "aws-portal:ModifyBilling"], "Resource": "*"}]}