# Email Templates

This directory contains email templates for both SendGrid and Mailgun email providers.

## Template Structure

```
templates/
├── sendgrid/          # SendGrid templates using Handlebars syntax
│   ├── case-update.html
│   └── welcome.html
├── mailgun/           # Mailgun templates using Jinja2-like syntax
│   ├── case-update.html
│   └── welcome.html
└── README.md
```

## Template Variables

### Case Update Template (`case-update`)

**Required Variables:**
- `tenantName` - Name of the legal organization
- `recipientName` - Name of the client/recipient
- `caseNumber` - Case identifier
- `status` - Current case status

**Optional Variables:**
- `caseSummary` - Detailed case summary/description
- `nextSteps` - Next steps in the case
- `handlerName` - Name of the case handler/lawyer
- `handlerTitle` - Title of the case handler
- `handlerEmail` - Email of the case handler
- `handlerPhone` - Phone number of the case handler
- `urgency` - Urgency level ("high" shows red indicator)
- `deadline` - Important deadline information
- `caseType` - Type of case
- `caseOpenDate` - Date the case was opened
- `tenantAddress` - Address of the legal organization
- `tenantPhone` - Main phone number

### Welcome Template (`welcome`)

**Required Variables:**
- `tenantName` - Name of the legal organization
- `recipientName` - Name of the new user

**Optional Variables:**
- `role` - User's role in the organization
- `inviterName` - Name of the person who invited the user
- `assignedLawyer` - Name of assigned lawyer
- `loginUrl` - URL to access the dashboard
- `currentDate` - Account creation date
- `tenantEmail` - Organization's main email
- `tenantPhone` - Organization's main phone
- `tenantDomain` - Organization's domain for support email
- `tenantAddress` - Organization's address

## Syntax Differences

### SendGrid Templates
- Use Handlebars syntax: `{{variable}}`
- Conditionals: `{{#if condition}}...{{/if}}`
- Equality check: `{{#eq var "value"}}...{{/eq}}`

### Mailgun Templates
- Use Handlebars syntax: `{{variable}}`
- Conditionals: `{{#if condition}}...{{/if}}`
- Equality check: `{{#eq var "value"}}...{{/eq}}`
- **Note**: Both SendGrid and Mailgun use similar Handlebars syntax

## Template Features

### Case Update Template
- **Urgent Banner**: Shows red banner when `urgency == "high"`
- **Status Indicator**: Color-coded status indicator (red for urgent, green for normal)
- **Responsive Design**: Mobile-friendly layout
- **Professional Styling**: Clean, legal industry-appropriate design
- **Contact Information**: Dynamic handler and organization contact details
- **Conditional Sections**: Only shows sections when data is available

### Welcome Template
- **Gradient Header**: Professional blue gradient header
- **Account Information**: Displays user account details
- **Call-to-Action**: Prominent button to access dashboard
- **Getting Started**: Step-by-step onboarding guide
- **Contact Support**: Support information and contact details

## Usage in Code

The templates are referenced in the `TemplateService` configuration:

```typescript
'case-update': {
    sendGridTemplateId: 'd-case-update-template-id',
    mailgunTemplateName: 'case-update-template',
    defaultSubject: 'Case Update - {{caseNumber}}',
    categories: ['case', 'update', 'legal'],
    requiredVariables: ['tenantName', 'recipientName', 'caseNumber', 'status'],
    optionalVariables: ['caseSummary', 'nextSteps', 'handlerName', 'handlerTitle', 'urgency', 'deadline']
}
```

## Customization

To customize the templates:

1. **Colors**: Modify the CSS color variables in the `<style>` section
2. **Fonts**: Update the `font-family` declarations
3. **Layout**: Adjust the grid structure and spacing
4. **Branding**: Update logo, company name, and styling

## Best Practices

1. **Test Variables**: Always test with both filled and empty optional variables
2. **Mobile Responsive**: Verify templates render correctly on mobile devices
3. **Email Clients**: Test across different email clients (Gmail, Outlook, etc.)
4. **Accessibility**: Ensure proper contrast ratios and semantic HTML
5. **Performance**: Optimize images and minimize inline CSS where possible