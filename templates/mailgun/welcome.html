<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{tenantName}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .email-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
            padding: 40px;
            text-align: center;
            color: white;
        }
        
        .logo {
            font-size: 32px;
            font-weight: 300;
            margin-bottom: 10px;
        }
        
        .welcome-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .welcome-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .greeting {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
        }
        
        .intro-text {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
            font-size: 16px;
        }
        
        .info-section {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .info-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        
        .info-item {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .info-label {
            font-weight: 500;
            color: #555;
            min-width: 140px;
        }
        
        .info-value {
            color: #333;
        }
        
        .cta-section {
            text-align: center;
            margin: 40px 0;
        }
        
        .cta-button {
            display: inline-block;
            background-color: #4a90e2;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
        }
        
        .getting-started {
            margin-bottom: 30px;
        }
        
        .getting-started h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .steps-list {
            list-style: none;
            padding-left: 0;
            counter-reset: step-counter;
        }
        
        .steps-list li {
            position: relative;
            padding-left: 30px;
            margin-bottom: 15px;
            color: #555;
            font-size: 15px;
            line-height: 1.5;
            counter-increment: step-counter;
        }
        
        .steps-list li:before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            background-color: #4a90e2;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }
        
        .contact-info {
            border-top: 1px solid #e9ecef;
            padding-top: 25px;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .signature {
            margin-bottom: 15px;
        }
        
        .name {
            font-weight: 600;
            color: #333;
        }
        
        .title {
            color: #666;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 20px 40px;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #888;
            line-height: 1.4;
            text-align: center;
        }
        
        .copyright {
            margin-bottom: 5px;
        }
        
        .address {
            margin-bottom: 10px;
        }
        
        @media (max-width: 600px) {
            .content {
                padding: 30px 20px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .footer {
                padding: 20px;
            }
            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .info-label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">{{tenantName}}</div>
            <div class="welcome-title">Welcome to our Legal Services</div>
            <div class="welcome-subtitle">We're excited to have you on board</div>
        </div>
        
        <div class="content">
            <div class="greeting">Hello {{recipientName}},</div>
            
            <div class="intro-text">
                Welcome to {{tenantName}}! We're delighted to have you join our legal services platform. 
                {{#if inviterName}}
                {{inviterName}} has invited you to collaborate with our team.
                {{/if}}
                Your account has been successfully created and you can now access all the features and services we provide.
            </div>
            
            <div class="info-section">
                <div class="info-title">Your Account Information</div>
                <div class="info-item">
                    <span class="info-label">Organization:</span>
                    <span class="info-value">{{tenantName}}</span>
                </div>
                {{#if role}}
                <div class="info-item">
                    <span class="info-label">Your Role:</span>
                    <span class="info-value">{{role}}</span>
                </div>
                {{/if}}
                {{#if assignedLawyer}}
                <div class="info-item">
                    <span class="info-label">Assigned Lawyer:</span>
                    <span class="info-value">{{assignedLawyer}}</span>
                </div>
                {{/if}}
                <div class="info-item">
                    <span class="info-label">Account Created:</span>
                    <span class="info-value">{{currentDate}}</span>
                </div>
            </div>
            
            {{#if loginUrl}}
            <div class="cta-section">
                <a href="{{loginUrl}}" class="cta-button">Access Your Dashboard</a>
            </div>
            {{/if}}
            
            <div class="getting-started">
                <h3>Getting Started</h3>
                <ol class="steps-list">
                    <li>Log in to your dashboard using the button above</li>
                    <li>Complete your profile information and preferences</li>
                    <li>Review your assigned cases and upcoming deadlines</li>
                    <li>Explore our document management and communication tools</li>
                    <li>Schedule an introductory meeting with your legal team</li>
                </ol>
            </div>
            
            <div class="contact-info">
                <div>If you have any questions or need assistance getting started, please don't hesitate to contact our support team.</div>
                <br>
                <div class="signature">
                    <div>Best regards,</div>
                    <br>
                    {{#if inviterName}}
                    <div class="name">{{inviterName}}</div>
                    <div class="title">{{tenantName}} Team</div>
                    {{else}}
                    <div class="name">{{tenantName}} Legal Team</div>
                    {{/if}}
                </div>
                
                <div style="margin-top: 15px;">
                    {{#if tenantEmail}}
                    Email: {{tenantEmail}}<br>
                    {{/if}}
                    {{#if tenantPhone}}
                    Phone: {{tenantPhone}}<br>
                    {{/if}}
                    Support: support@{{tenantDomain}}
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="copyright">© 2025 {{tenantName}}. All rights reserved.</div>
            {{#if tenantAddress}}
            <div class="address">{{tenantAddress}}</div>
            {{/if}}
        </div>
    </div>
</body>
</html>