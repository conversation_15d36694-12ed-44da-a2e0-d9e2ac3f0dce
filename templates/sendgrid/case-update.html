<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Case Update - {{tenantName}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .email-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .header {
            padding: 30px 40px 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .logo {
            font-size: 28px;
            color: #4a90e2;
            font-weight: 300;
            margin-bottom: 5px;
        }
        
        .company-name {
            color: #666;
            font-size: 14px;
            font-weight: 400;
        }
        
        .content {
            padding: 40px;
        }
        
        .main-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .greeting {
            color: #666;
            margin-bottom: 20px;
        }
        
        .intro-text {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .case-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .case-item {
            margin-bottom: 20px;
        }
        
        .case-label {
            font-size: 12px;
            color: #888;
            margin-bottom: 5px;
            text-transform: uppercase;
            font-weight: 500;
        }
        
        .case-value {
            font-size: 16px;
            color: #333;
            font-weight: 500;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            background-color: {{#if urgency}}{{#eq urgency "high"}}#dc3545{{else}}#28a745{{/eq}}{{else}}#28a745{{/if}};
            border-radius: 50%;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .case-summary {
            background-color: #f8f9fa;
            border-left: 4px solid #4a90e2;
            padding: 20px;
            margin-bottom: 30px;
            font-size: 14px;
            line-height: 1.6;
            color: #555;
        }
        
        .next-steps {
            margin-bottom: 30px;
        }
        
        .next-steps ul {
            list-style: none;
            padding-left: 0;
        }
        
        .next-steps li {
            position: relative;
            padding-left: 20px;
            margin-bottom: 10px;
            color: #555;
            font-size: 14px;
        }
        
        .next-steps li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: #4a90e2;
            font-weight: bold;
        }
        
        .contact-info {
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .signature {
            margin-bottom: 15px;
        }
        
        .name {
            font-weight: 600;
            color: #333;
        }
        
        .title {
            color: #666;
        }
        
        .contact-details {
            margin-bottom: 20px;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 20px 40px;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #888;
            line-height: 1.4;
        }
        
        .copyright {
            margin-bottom: 5px;
        }
        
        .address {
            margin-bottom: 10px;
        }
        
        .disclaimer {
            font-style: italic;
        }
        
        .urgent-banner {
            background-color: #dc3545;
            color: white;
            padding: 15px 40px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
        }
        
        @media (max-width: 600px) {
            .case-details {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .footer {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        {{#if urgency}}
        {{#eq urgency "high"}}
        <div class="urgent-banner">
            URGENT CASE UPDATE - IMMEDIATE ATTENTION REQUIRED
        </div>
        {{/eq}}
        {{/if}}
        
        <div class="header">
            <div class="logo">{{tenantName}}</div>
            <div class="company-name">Legal Services</div>
        </div>
        
        <div class="content">
            <h1 class="main-title">Case Update</h1>
            
            <div class="greeting">Dear {{recipientName}},</div>
            
            <div class="intro-text">
                Please find below the latest update regarding your case:
            </div>
            
            <div class="case-details">
                <div class="left-column">
                    <div class="case-item">
                        <div class="case-label">Case Number</div>
                        <div class="case-value">{{caseNumber}}</div>
                    </div>
                    
                    <div class="case-item">
                        <div class="case-label">Client Name</div>
                        <div class="case-value">{{recipientName}}</div>
                    </div>
                    
                    {{#if caseOpenDate}}
                    <div class="case-item">
                        <div class="case-label">Case Opening Date</div>
                        <div class="case-value">{{caseOpenDate}}</div>
                    </div>
                    {{/if}}
                </div>
                
                <div class="right-column">
                    <div class="case-item">
                        <div class="case-label">Current Status</div>
                        <div class="case-value status">
                            <span class="status-indicator"></span>
                            {{status}}
                        </div>
                    </div>
                    
                    {{#if handlerName}}
                    <div class="case-item">
                        <div class="case-label">Case Handler</div>
                        <div class="case-value">{{handlerName}}</div>
                    </div>
                    {{/if}}
                    
                    {{#if caseType}}
                    <div class="case-item">
                        <div class="case-label">Case Type</div>
                        <div class="case-value">{{caseType}}</div>
                    </div>
                    {{/if}}
                </div>
            </div>
            
            {{#if caseSummary}}
            <h2 class="section-title">Case Summary</h2>
            
            <div class="case-summary">
                {{caseSummary}}
            </div>
            {{/if}}
            
            {{#if nextSteps}}
            <h2 class="section-title">Next Steps</h2>
            
            <div class="next-steps">
                <div>{{nextSteps}}</div>
            </div>
            {{/if}}
            
            {{#if deadline}}
            <div style="background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin-bottom: 30px;">
                <strong>Important Deadline:</strong> {{deadline}}
            </div>
            {{/if}}
            
            <div class="contact-info">
                <div>If you have any questions or require further information, please do not hesitate to contact me directly.</div>
                <br>
                <div class="signature">
                    <div>Best regards,</div>
                    <br>
                    {{#if handlerName}}
                    <div class="name">{{handlerName}}</div>
                    {{#if handlerTitle}}
                    <div class="title">{{handlerTitle}}</div>
                    {{/if}}
                    {{else}}
                    <div class="name">{{tenantName}} Legal Team</div>
                    {{/if}}
                </div>
                
                <div class="contact-details">
                    {{#if handlerEmail}}
                    Email: {{handlerEmail}}<br>
                    {{/if}}
                    {{#if handlerPhone}}
                    Direct: {{handlerPhone}}<br>
                    {{/if}}
                    {{#if tenantPhone}}
                    Main: {{tenantPhone}}
                    {{/if}}
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="copyright">© 2025 {{tenantName}}. All rights reserved.</div>
            {{#if tenantAddress}}
            <div class="address">{{tenantAddress}}</div>
            {{/if}}
            <div class="disclaimer">
                This email contains confidential information and is intended solely for the addressee. If you have received this email in error, please notify the sender immediately and delete it from your system.
            </div>
        </div>
    </div>
</body>
</html>