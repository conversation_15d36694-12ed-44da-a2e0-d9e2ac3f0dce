#### =================================================>
## Environment Production

# NODE_ENV=production

# # # Service Ports
# CORE_PORT=3000
# COMMUNICATION_PORT=3001
# DOCUMENT_ENGINE_PORT=3002
# AUTH_PORT=3003
# CASE_MANAGEMENT_PORT=3004

# # Database Conf
# POSTGRES_HOST=postgres
# POSTGRES_PORT=5432
# POSTGRES_USER=postgres
# POSTGRES_PASSWORD=postgres
# POSTGRES_DB=tk_lpm
# POSTGRES_SSL=false
# POSTGRES_MAX_CONNECTIONS=100
# POSTGRES_IDLE_TIMEOUT_MS=30000
# POSTGRES_CONNECTION_TIMEOUT_MS=2000

# # Used by NestJS API service
# KEYCLOAK_SERVER_URL=http://keycloak:8080
# KEYCLOAK_REALM=REALM_NAME
# KEYCLOAK_CLIENT_ID=admin-cli
# KEYCLOAK_CLIENT_SECRET=ulpN9SOvItx4ncOOUvR0CmIpza4khgMo

# # Used by Docker Keycloak container
# KEYCLOAK_HOST=keycloak
# KEYCLOAK_ADMIN=admin
# KEYCLOAK_ADMIN_PASSWORD=admin
# KEYCLOAK_HOSTNAME_PORT=8080

# #### New
# KEYCLOAK_HOST=keycloak
# KEYCLOAK_PORT=8080
# PG_HOST=postgres
# PG_USER=postgres
# PG_PASSWORD=postgres
# PG_SSL_MODE=false
# KEYCLOAK_URL=http://keycloak:8080

# # API Configuration
# API_GLOBAL_PREFIX=api
# API_RATE_LIMIT_WINDOW_MS=60000
# API_RATE_LIMIT_MAX=100

# # Logging
# LOG_LEVEL=info
# # Logging
# LOG_LEVEL=info
# # Logging
# LOG_LEVEL=info

# # CORS Configuration
# CORS_ENABLED=true
# CORS_ORIGIN=*
# NABLED=true
# CORS_ORIGIN=*
# NABLED=true
# CORS_ORIGIN=*



# ### =================================================>

# # ## Environment dev
NODE_ENV=development

# Service Ports
CORE_PORT=3000
COMMUNICATION_PORT=3001
DOCUMENT_ENGINE_PORT=3002
AUTH_PORT=3003
CASE_MANAGEMENT_PORT=3004
TASK_MANAGEMENT_PORT=3005

# Database Conf
# POSTGRES_HOST=localhost
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=tk_lpm
POSTGRES_SSL=false
POSTGRES_MAX_CONNECTIONS=100
POSTGRES_IDLE_TIMEOUT_MS=30000
POSTGRES_CONNECTION_TIMEOUT_MS=2000

# Used by NestJS API service
KEYCLOAK_SERVER_URL=http://localhost:8080
KEYCLOAK_REALM=REALM_NAME
KEYCLOAK_CLIENT_ID=admin-cli
KEYCLOAK_CLIENT_SECRET=ulpN9SOvItx4ncOOUvR0CmIpza4khgMo

# Used by Docker Keycloak container
KEYCLOAK_HOST=postgres
KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_HOSTNAME_PORT=8080

# API Configuration
API_GLOBAL_PREFIX=api
API_RATE_LIMIT_WINDOW_MS=60000
API_RATE_LIMIT_MAX=100

# Logging
LOG_LEVEL=info
# Logging
LOG_LEVEL=info
# Logging
LOG_LEVEL=info

# Logging
LOG_LEVEL=info

# CORS Configuration
CORS_ENABLED=true
CORS_ORIGIN=*
ogging
LOG_LEVEL=info

# CORS Configuration
CORS_ENABLED=true
CORS_ORIGIN=*
NABLED=true
CORS_ORIGIN=*
NABLED=true
CORS_ORIGIN=*

## Testing db 
# DB_HOST=localhost
# DB_PORT=5433
# DB_NAME=test_db
# DB_USER=postgres
# DB_PASS=postgres

#  AWS miniIO configurations 
# AWS_S3_ENDPOINT=http://localhost:9000
# AWS_ACCESS_KEY_ID=minioadmin
# AWS_SECRET_ACCESS_KEY=minioadmin
# AWS_DOCUMENT_BUCKET_NAME=tk-lpm-documents
# AWS_S3_FORCE_PATH_STYLE=true
# AWS_S3_USE_SSL=false

# AWS Testing 
AWS_REGION=eu-north-
AWS_ACCESS_KEY_ID=aws-access_key
AWS_SECRET_ACCESS_KEY=aws-secrete
AWS_DOCUMENT_BUCKET_NAME=tk-lpm-
AWS_S3_PRESIGNED_URL_EXPIRATION=expiry

# TEST_DB_HOST=localhost
# TEST_DB_PORT=5434
# TEST_DB_USERNAME=postgres
# TEST_DB_PASSWORD=postgres
# TEST_DB_DATABASE=tk_lpm_test
# TEST_KEYCLOAK_HOST=localhost
# TEST_KEYCLOAK_PORT=8090
# TEST_KEYCLOAK_USER=admin
# TEST_KEYCLOAK_PASSWORD=admin

# # Used by NestJS API service
# KEYCLOAK_SERVER_URL=http://localhost:8090
# KEYCLOAK_REALM=REALM_NAME
# KEYCLOAK_CLIENT_ID=admin-cli
# KEYCLOAK_CLIENT_SECRET=ulpN9SOvItx4ncOOUvR0CmIpza4khgMo
# #  Testing variables 
# NODE_ENV=production

# # # Service Ports
# CORE_PORT=3000
# COMMUNICATION_PORT=3001
# DOCUMENT_ENGINE_PORT=3002
# AUTH_PORT=3003
# CASE_MANAGEMENT_PORT=3004

# # Database Conf
# POSTGRES_HOST=localhost
# POSTGRES_PORT=5434 ## updated 
# POSTGRES_USER=postgres
# POSTGRES_PASSWORD=postgres
# POSTGRES_DB=tk_lpm_test
# POSTGRES_SSL=false
# POSTGRES_MAX_CONNECTIONS=100
# POSTGRES_IDLE_TIMEOUT_MS=30000
# POSTGRES_CONNECTION_TIMEOUT_MS=2000 