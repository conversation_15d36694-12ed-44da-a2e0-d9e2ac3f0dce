# Production Dockerfile for Docker Compose local testing
# This installs dependencies inside the container for the correct architecture

FROM node:20-alpine

ARG SERVICE=core
ARG PORT=3000

WORKDIR /app

# Install dependencies required for native modules
RUN apk add --no-cache python3 make g++

# Enable Corepack and set Yarn version
RUN corepack enable && corepack prepare yarn@4.9.2 --activate

# Copy package files
COPY package.json yarn.lock .yarnrc.yml* ./
COPY .yarn ./.yarn

# Install dependencies (this will compile native modules for the container's architecture)
# Keep ts-node and typescript for running migrations
RUN yarn install --immutable

# Copy source and built application
COPY dist ./dist
COPY libs ./libs
COPY scripts ./scripts
COPY nest-cli.json ./
COPY tsconfig.json ./

# Set environment
ENV NODE_ENV=production
ENV PORT=${PORT}
ENV SERVICE=${SERVICE}

# Expose port
EXPOSE ${PORT}

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=40s --retries=3 \
  CMD node -e "require('http').get('http://localhost:${PORT}/health', (r) => {process.exit(r.statusCode === 200 ? 0 : 1)})" || exit 1

# Start the service
CMD ["sh", "-c", "node dist/apps/${SERVICE}/main.js"]

