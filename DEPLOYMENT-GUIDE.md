# Deployment Guide

## Safe Sequential Deployment

When deploying services to AWS ECS, use the safe sequential deployment script to avoid AWS rate limits and capacity issues.

### Script Location
```bash
infrastructure/scripts/deploy-all-services-safe.sh
```

### Why Use This Script?

Deploying all microservices simultaneously can cause:
- ❌ AWS rate limits on network interface creation
- ❌ Capacity unavailable errors in availability zones
- ❌ Network timeouts when pulling images from ECR
- ❌ Services failing to start

This script deploys services **one at a time** with proper delays, ensuring stable deployments.

### Usage

#### Basic Usage
```bash
./infrastructure/scripts/deploy-all-services-safe.sh staging
```

#### For Different Environments
```bash
# Staging environment
./infrastructure/scripts/deploy-all-services-safe.sh staging

# Development environment
./infrastructure/scripts/deploy-all-services-safe.sh dev

# Production environment
./infrastructure/scripts/deploy-all-services-safe.sh prod
```

### What the Script Does

1. ✅ Deploys services sequentially (Core → Auth → Quote-engine → Communication)
2. ✅ Waits 90 seconds between each deployment to avoid rate limits
3. ✅ Monitors each service for up to 5 minutes to ensure stability
4. ✅ Provides detailed progress updates
5. ✅ Shows a summary report at the end

### When to Run This Script

Run this script **after GitHub Actions successfully builds and pushes new images to ECR**:

1. **Check GitHub Actions** - Ensure build workflow completed successfully
2. **Verify ECR Images** - Confirm new images are pushed (look for latest commit hash tag)
3. **Run Deployment Script** - Execute the safe deployment script
4. **Monitor Services** - The script will monitor automatically
5. **Test Endpoints** - Verify services are responding correctly

### Example Workflow

```bash
# 1. After merging PR and GitHub Actions build completes (~10 minutes)

# 2. Verify latest images in ECR (optional)
aws ecr describe-images --repository-name tk-lpm/auth --region us-east-1 \
  --query 'imageDetails[0].imageTags[0]' --output text

# 3. Run safe deployment
./infrastructure/scripts/deploy-all-services-safe.sh staging

# 4. Test services
curl https://api.tklpm.com/api/health
curl https://api.tklpm.com/api/auth/health
```

### Troubleshooting

#### Service Doesn't Stabilize
If a service doesn't reach "running" state within 5 minutes:
```bash
# Check service events
aws ecs describe-services --cluster tk-lpm-staging-cluster \
  --services auth --region us-east-1 \
  --query 'services[0].events[0:5]'

# Check service logs
aws logs tail /ecs/tk-lpm/auth --region us-east-1 --since 10m
```

#### Capacity Unavailable Error
This is an AWS infrastructure issue. Options:
1. Wait 30-60 minutes and try again
2. AWS capacity limits usually resolve automatically
3. Long-term: Consider migrating to private subnets with NAT Gateway

### Manual Deployment (Not Recommended)

If you need to deploy a single service manually:
```bash
aws ecs update-service --cluster tk-lpm-staging-cluster \
  --service auth --force-new-deployment --region us-east-1
```

**⚠️ Warning:** Do not deploy multiple services manually at once - use the script instead!

### Script Features

- **Interactive Confirmation** - Prompts before starting deployment
- **Progress Monitoring** - Shows real-time status updates
- **Failure Handling** - Continues even if one service has issues
- **Summary Report** - Shows final status of all deployments
- **Color-Coded Output** - Easy to read status indicators

### Environment Variables

No special environment variables needed. The script uses:
- AWS CLI credentials from your environment
- Cluster name: `tk-lpm-{environment}-cluster`
- Region: `us-east-1`

### Support

For issues or questions:
1. Check service logs: `aws logs tail /ecs/tk-lpm/{service-name} --region us-east-1 --since 30m`
2. Check service status: `aws ecs describe-services --cluster tk-lpm-staging-cluster --services {service-name}`
3. Review GitHub Actions build logs
4. Contact the platform team

---

**Last Updated:** October 20, 2025
**Script Version:** 1.0.0
