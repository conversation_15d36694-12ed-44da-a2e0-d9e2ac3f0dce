# Document Generation System

## Overview

The Document Generation System provides automated document creation capabilities by combining templates with dynamic data through token resolution. It integrates with both the Token Management and Template Management systems to produce professional legal documents with strict validation and error handling.

## Table of Contents

- [Architecture](#architecture)
- [Generation Process](#generation-process)
- [API Endpoints](#api-endpoints)
- [Generation Modes](#generation-modes)
- [Token Resolution](#token-resolution)
- [Error Handling](#error-handling)
- [Integration Points](#integration-points)
- [Performance](#performance)
- [Best Practices](#best-practices)
- [Examples](#examples)

## Architecture

### Core Components

```
┌─────────────────────────┐
│ Document Generation     │ ← API Layer & Orchestration
│ Service                 │
├─────────────────────────┤
│ Template Validation     │ ← Template Processing
│ Service                 │
├─────────────────────────┤
│ Token Management        │ ← Token Resolution
│ Service                 │
├─────────────────────────┤
│ Docxtemplater          │ ← Document Engine
│ (Template Engine)       │
├─────────────────────────┤
│ S3 Storage Service      │ ← File Storage
└─────────────────────────┘
```

### Integration Architecture

```
Case Data ──┐
            ├── Token Resolution Context
Client Data ─┤
            ├── Document Generation ──► Generated Document
Property ────┤
            └── Template + System Tokens
User Context ┘
```

### Key Features

- **Strict Token Validation**: All tokens must resolve successfully
- **Rich Context Building**: Comprehensive data integration
- **Professional Document Engine**: Industry-standard docxtemplater
- **Comprehensive Error Reporting**: Detailed failure analysis
- **Usage Analytics**: Generation tracking and statistics
- **S3 Integration**: Secure document storage
- **Audit Trail**: Complete generation history

## Generation Process

### Document Generation Pipeline

```
1. Request Validation
   ├── Template Existence
   ├── Case Data Availability
   └── User Authorization

2. Template Processing
   ├── Download Template from S3
   ├── Extract Tokens from Template
   └── Pre-validate Token Availability

3. Context Building
   ├── Gather Case Data
   ├── Build User Context
   └── Prepare Custom Data

4. Token Resolution
   ├── Resolve System Tokens
   ├── Resolve Custom Tokens
   └── Apply Transformations

5. Document Generation
   ├── Process Template with Data
   ├── Generate DOCX Document
   └── Validate Output

6. Storage & Response
   ├── Upload to S3
   ├── Create Document Record
   └── Return Generation Result
```

### Validation Stages

#### 1. **Pre-Generation Validation**
- Template exists and is active
- Case data is available and complete
- User has appropriate permissions
- Template is compatible with case type

#### 2. **Token Pre-Validation** 
- All tokens in template are known (system or custom)
- Custom tokens exist and are active
- No unknown or deprecated tokens

#### 3. **Token Resolution Validation**
- All tokens resolve to non-null values
- Transformed values pass validation rules
- Required data is available in context

#### 4. **Post-Generation Validation**
- Document generation completed successfully
- Output file is valid DOCX format
- File upload to S3 succeeded

## API Endpoints

### Core Generation

```http
# Generate document (main endpoint)
POST /api/document-engine/documents/generate
Content-Type: application/json

{
  "templateId": "uuid",
  "caseId": "uuid", 
  "customData": {
    "specialInstructions": "Rush completion required"
  },
  "userId": "uuid",
  "userName": "Sarah Johnson",
  "userEmail": "<EMAIL>",
  "outputFileName": "Contract_Request_CAS2025001.docx",
  "autoAttachToCase": true,
  "autoEmailToClient": false
}

# Response
{
  "success": true,
  "filename": "Contract_Request_CAS2025001.docx",
  "documentUrl": "https://s3.../document.docx",
  "attachmentId": "uuid",
  "errors": [],
  "warnings": [],
  "tokenResolutionDetails": [
    {
      "tokenName": "clientName",
      "resolved": true,
      "value": "John Smith",
      "source": "custom"
    }
  ],
  "metadata": {
    "templateId": "uuid",
    "templateName": "Contract Request",
    "caseId": "uuid", 
    "caseNumber": "CAS-2025-001",
    "generationTimeMs": 1250,
    "tokensResolved": 15,
    "totalTokens": 15
  }
}
```

### Testing & Validation

```http
# Test token resolution without generating document
POST /api/document-engine/documents/test-resolution
{
  "tokenNames": ["clientName", "propertyAddress", "currentDate"],
  "context": {
    "case": { "caseNumber": "CAS-2025-001" },
    "client": { "name": "John Smith" },
    "property": { "fullAddress": "123 Main St" }
  }
}

# Preview document generation (validation only)
POST /api/document-engine/documents/preview
{
  "templateId": "uuid",
  "caseId": "uuid"
}

# Validate template for specific case
POST /api/document-engine/templates/{templateId}/validate-case/{caseId}
```

### Analytics & Monitoring

```http
# Get generation statistics
GET /api/document-engine/documents/generation-stats?period=month

# Get generation history for case
GET /api/document-engine/documents/history/{caseId}

# Get template usage analytics
GET /api/document-engine/templates/{templateId}/usage-stats
```

## Generation Modes

### Strict Mode (Default)

**Behavior**: All tokens must resolve successfully or generation fails

```typescript
interface StrictModeConfig {
  failOnUnresolvedTokens: true;     // Default behavior
  requireAllTokens: true;           // All tokens must have values
  strictValidation: true;           // Apply all validation rules
}

// Example result when token fails
{
  "success": false,
  "errors": [
    "Document generation failed: 1 unresolved token(s) found: clientEmail",
    "All tokens must be resolved before document generation can proceed.",
    "- {clientEmail}: Custom token resolution failed"
  ]
}
```

### Graceful Mode (Future Enhancement)

**Behavior**: Continue generation with placeholder values for failed tokens

```typescript
interface GracefulModeConfig {
  failOnUnresolvedTokens: false;
  continueWithDefaults: true;
  placeholderFormat: "[MISSING: {tokenName}]";
}
```

## Token Resolution

### Resolution Context Structure

```typescript
interface TokenResolutionContext {
  // Primary entities
  case?: Case;                    // Complete case data with relationships
  client?: Client;                // Client information
  property?: Property;            // Property details
  
  // User context
  user?: {
    name?: string;                // Current user display name
    email?: string;               // User email address
    id?: string;                  // User system ID
  };
  
  // Additional data
  customEntities?: Record<string, any>; // Custom/additional data
  timestamp?: Date;               // Generation timestamp
}
```

### System Token Resolution

System tokens are resolved directly by the generation service:

```typescript
// System token examples with resolution
const systemTokens = {
  // Date/time tokens
  currentDate: '15/01/2025',                    // GB date format
  currentDateTime: '15/01/2025 14:30',          // GB datetime format
  currentYear: 2025,                            // Current year
  currentMonth: 'January',                      // Month name
  currentDay: 15,                               // Day of month
  
  // User context tokens  
  'currentUser.name': 'Sarah Johnson',          // From user context
  'currentUser.email': '<EMAIL>',        // From user context
  'currentUser.id': 'usr_123',                  // From user context
  
  // System metadata
  generationTimestamp: '15/01/2025 14:30',      // When generated
  generatedBy: 'Sarah Johnson',                 // Who generated
  documentId: 'DOC-1706185800123',              // Unique doc ID
  templateVersion: '1.0'                        // Template version
};
```

### Custom Token Resolution

Custom tokens are resolved through the Token Management Service:

```typescript
// Custom token resolution process
1. Look up token definition in database
2. Extract field path and entity name
3. Navigate context to find data: context[entityName][fieldPath]
4. Apply transformations if configured
5. Validate result against validation rules
6. Return resolved value or error

// Example: resolving {clientName}
Token Definition: {
  tokenName: "clientName",
  entityName: "client", 
  fieldPath: "name"
}

Context Navigation: context.client.name
Result: "John Smith"
```

### Token Resolution Priority

```
1. System Tokens (highest priority)
   └── Resolved directly by generation service
   
2. Custom Tokens (secondary priority)  
   └── Resolved via Token Management Service
   
3. Fallback Handling
   └── Generate error for unknown tokens
```

### Advanced Token Features

#### Nested Field Resolution

```typescript
// Complex field paths supported
{
  tokenName: "vendorSolicitor",
  fieldPath: "property.vendorSolicitorName"
}

// Resolution: context.property.vendorSolicitorName
```

#### Transformation Application

```typescript
// Token with transformation
{
  tokenName: "purchasePrice",
  dataType: "CURRENCY",
  transformationConfig: {
    currencyCode: "GBP",
    locale: "en-GB", 
    showSymbol: true
  }
}

// Input: 450000
// Output: "£450,000.00"
```

## Error Handling

### Error Categories

#### 1. **Pre-Generation Errors**
```typescript
// Template not found
{
  "error": "Template with ID abc-123 not found",
  "code": "TEMPLATE_NOT_FOUND",
  "severity": "CRITICAL"
}

// Case not found  
{
  "error": "Case with ID xyz-789 not found",
  "code": "CASE_NOT_FOUND", 
  "severity": "CRITICAL"
}

// Template validation failed
{
  "error": "Template contains unknown tokens",
  "unknownTokens": ["invalidToken1", "invalidToken2"],
  "code": "INVALID_TOKENS",
  "severity": "CRITICAL"
}
```

#### 2. **Token Resolution Errors**
```typescript
// Token resolution failed
{
  "tokenName": "clientEmail",
  "error": "Custom token resolution failed",
  "details": "Field 'email' not found in client data",
  "code": "TOKEN_RESOLUTION_FAILED",
  "severity": "CRITICAL"
}

// Validation failed
{
  "tokenName": "purchasePrice", 
  "error": "Token value failed validation",
  "details": "Value 5000 below minimum required 10000",
  "code": "VALIDATION_FAILED",
  "severity": "ERROR"
}
```

#### 3. **Generation Errors**
```typescript
// Template processing failed
{
  "error": "Template rendering failed: Invalid tag name",
  "code": "TEMPLATE_PROCESSING_ERROR",
  "severity": "CRITICAL"
}

// File operations failed
{
  "error": "Failed to upload generated document to S3",
  "code": "STORAGE_ERROR",
  "severity": "CRITICAL"
}
```

### Error Response Structure

```typescript
interface GenerationResult {
  success: boolean;
  filename?: string;
  documentUrl?: string;
  attachmentId?: string;
  
  // Error information
  errors: string[];                    // Critical errors that stopped generation
  warnings: string[];                  // Non-critical issues
  
  // Detailed token information
  tokenResolutionDetails: Array<{
    tokenName: string;
    resolved: boolean;
    value: any;
    source: 'system' | 'custom' | 'fallback';
    error?: string;                    // Error details if resolution failed
  }>;
  
  // Generation metadata
  metadata: {
    templateId?: string;
    templateName?: string;
    caseId?: string;
    caseNumber?: string;
    generationTimeMs?: number;
    tokensResolved?: number;
    totalTokens?: number;
    s3Key?: string;
    s3Bucket?: string;
  };
}
```

### Error Recovery Strategies

#### 1. **Data Completeness Issues**
```typescript
// Missing required case data
"Solution": "Ensure case has complete property and client information"

// Missing vendor solicitor details  
"Solution": "Add vendor solicitor information to property record"

// Invalid date ranges
"Solution": "Set completion date after exchange date"
```

#### 2. **Token Issues**
```typescript
// Unknown token in template
"Solution": "Create missing token or update template to use existing token"

// Inactive token
"Solution": "Reactivate token or replace with active alternative"

// Invalid token syntax
"Solution": "Fix token syntax in template (use {tokenName} format)"
```

#### 3. **Template Issues**
```typescript
// Corrupted template file
"Solution": "Re-upload template in valid DOCX format"

// Template not compatible with case type
"Solution": "Use appropriate template for case type or update template compatibility"
```

## Integration Points

### Case Management Integration

```typescript
// Document generation triggers from case events
interface CaseEventTrigger {
  event: 'STATUS_CHANGE' | 'DATE_REACHED' | 'MANUAL';
  templateId: string;
  autoGenerate: boolean;
  conditions?: {
    fromStatus?: CaseStatus;
    toStatus?: CaseStatus;
    dateField?: string;
    daysOffset?: number;
  };
}

// Example: Auto-generate exchange confirmation
{
  event: 'STATUS_CHANGE',
  templateId: 'exchange-confirmation-template',
  autoGenerate: true,
  conditions: {
    toStatus: 'CONTRACTS_EXCHANGED'
  }
}
```

### Document Management Integration

```typescript
// Generated documents automatically attached to cases
interface DocumentAttachment {
  documentId: string;
  caseId: string;
  templateId: string;
  generatedAt: Date;
  generatedBy: string;
  category: DocumentCategory;
  s3Key: string;
  originalFilename: string;
}
```

### Communication Integration

```typescript
// Auto-email generated documents
interface EmailIntegration {
  autoEmailToClient: boolean;
  emailTemplate?: string;
  additionalRecipients?: string[];
  attachDocument: boolean;
  subject?: string;
}
```

## Performance

### Optimization Strategies

#### 1. **Template Caching**
```typescript
// Templates cached after first download
interface TemplateCache {
  templateId: string;
  buffer: Buffer;
  lastModified: Date;
  expiresAt: Date;
}
```

#### 2. **Token Resolution Optimization**
```typescript
// Batch token resolution where possible
interface BatchResolution {
  systemTokens: Record<string, any>;     // Resolved once per generation
  customTokens: Record<string, any>;     // Batch database queries
}
```

#### 3. **Parallel Processing**
```typescript
// Concurrent operations where safe
const [template, caseData, systemTokens] = await Promise.all([
  downloadTemplate(templateId),
  getCaseData(caseId), 
  getSystemTokens()
]);
```

### Performance Metrics

```typescript
interface PerformanceMetrics {
  totalGenerationTime: number;          // Total time in milliseconds
  templateDownloadTime: number;         // S3 download time
  tokenResolutionTime: number;          // Token processing time
  documentGenerationTime: number;       // Template processing time
  uploadTime: number;                   // S3 upload time
  
  tokenCounts: {
    total: number;                      // Total tokens in template
    system: number;                     // System tokens
    custom: number;                     // Custom tokens
    resolved: number;                   // Successfully resolved
  };
}
```

### Scalability Considerations

1. **Concurrent Generations**: Handle multiple simultaneous requests
2. **Resource Management**: Monitor memory usage during generation
3. **Queue Management**: Queue large generation requests
4. **Cache Strategy**: Intelligent template and token caching
5. **Database Optimization**: Efficient token lookup queries

## Best Practices

### Request Design

#### ✅ Optimal Requests
```typescript
{
  templateId: "uuid",                   // Use specific template IDs
  caseId: "uuid",                      // Provide complete case ID
  outputFileName: "Contract_CAS001.docx", // Descriptive filenames
  customData: {                        // Minimal additional data
    urgentRequest: true
  }
}
```

#### ❌ Avoid
```typescript
{
  templateId: "uuid",
  caseId: "uuid",
  customData: {                        // Excessive custom data
    hugeDataSet: [...largeArray],      // Large data objects
    unnecessaryInfo: "..."             // Unused information
  }
}
```

### Error Handling Best Practices

1. **Comprehensive Validation**: Validate all inputs before generation
2. **Graceful Degradation**: Handle partial failures appropriately
3. **Detailed Logging**: Log all generation attempts and failures
4. **User-Friendly Messages**: Provide actionable error messages
5. **Retry Logic**: Implement retry for transient failures

### Security Best Practices

1. **Input Validation**: Validate all request parameters
2. **Access Control**: Verify user permissions for case and template
3. **Data Sanitization**: Ensure no sensitive data in generated documents
4. **Audit Logging**: Log all generation requests and results
5. **File Security**: Secure S3 storage with appropriate permissions

## Examples

### Example 1: Basic Contract Request Generation

```typescript
// Generation request
const request = {
  templateId: "contract-request-template-uuid",
  caseId: "case-uuid-123",
  userId: "user-uuid-456", 
  userName: "Sarah Johnson",
  userEmail: "<EMAIL>",
  outputFileName: "Contract_Request_CAS2025001.docx"
};

// Expected context resolution
const context = {
  case: {
    caseNumber: "CAS-2025-001",
    type: "PURCHASE",
    property: {
      fullAddress: "123 Main Street, London, SW1A 1AA",
      purchasePrice: 450000,
      vendorSolicitorName: "Jane Smith",
      vendorSolicitorFirm: "Smith & Associates"
    }
  },
  client: {
    name: "John Smith",
    email: "<EMAIL>"
  },
  user: {
    name: "Sarah Johnson",
    email: "<EMAIL>",
    id: "user-uuid-456"
  },
  timestamp: new Date()
};

// Token resolution results
[
  { tokenName: "caseNumber", resolved: true, value: "CAS-2025-001", source: "custom" },
  { tokenName: "clientName", resolved: true, value: "John Smith", source: "custom" },
  { tokenName: "propertyAddress", resolved: true, value: "123 Main Street, London, SW1A 1AA", source: "custom" },
  { tokenName: "purchasePrice", resolved: true, value: "£450,000.00", source: "custom" },
  { tokenName: "currentDate", resolved: true, value: "15/01/2025", source: "system" },
  { tokenName: "currentUser.name", resolved: true, value: "Sarah Johnson", source: "system" }
]
```

### Example 2: Exchange Confirmation with Validation

```typescript
// Request with business rule validation
const request = {
  templateId: "exchange-confirmation-template-uuid", 
  caseId: "case-uuid-789"
};

// Case data validation
const caseValidation = {
  exchangeDate: "2025-02-15",          // Required
  completionDate: "2025-03-15",       // Must be after exchange
  propertyAddress: "456 Oak Avenue",   // Required
  clientName: "Mary Johnson",          // Required
  purchasePrice: 320000               // Required
};

// Validation passes - all required data present
// Generation proceeds with validated context
```

### Example 3: Error Handling Scenario

```typescript
// Request with missing data
const request = {
  templateId: "contract-request-template-uuid",
  caseId: "case-with-incomplete-data"
};

// Context with missing required data
const incompleteContext = {
  case: {
    caseNumber: "CAS-2025-002",
    property: {
      fullAddress: "789 Pine Street"
      // Missing: purchasePrice, vendorSolicitorName
    }
  },
  client: {
    name: "Bob Wilson"
    // Missing: email
  }
};

// Generation result with detailed errors
{
  "success": false,
  "errors": [
    "Document generation failed: 3 unresolved token(s) found: purchasePrice, vendorSolicitorName, clientEmail",
    "All tokens must be resolved before document generation can proceed.",
    "- {purchasePrice}: Field 'purchasePrice' not found in property data",
    "- {vendorSolicitorName}: Field 'vendorSolicitorName' not found in property data", 
    "- {clientEmail}: Field 'email' not found in client data"
  ],
  "tokenResolutionDetails": [
    { "tokenName": "caseNumber", "resolved": true, "value": "CAS-2025-002", "source": "custom" },
    { "tokenName": "clientName", "resolved": true, "value": "Bob Wilson", "source": "custom" },
    { "tokenName": "propertyAddress", "resolved": true, "value": "789 Pine Street", "source": "custom" },
    { "tokenName": "purchasePrice", "resolved": false, "error": "Field 'purchasePrice' not found in property data", "source": "fallback" },
    { "tokenName": "vendorSolicitorName", "resolved": false, "error": "Field 'vendorSolicitorName' not found in property data", "source": "fallback" },
    { "tokenName": "clientEmail", "resolved": false, "error": "Field 'email' not found in client data", "source": "fallback" }
  ]
}
```

## Troubleshooting

### Common Issues & Solutions

| Issue | Symptoms | Root Cause | Solution |
|-------|----------|------------|----------|
| Generation fails with token errors | `Unknown token: {tokenName}` | Token doesn't exist in system | Create missing token or update template |
| Empty document generated | Document has no content | Template file corrupted or empty | Re-upload valid template file |
| Formatting issues | Generated document looks wrong | Template design issues | Review and fix template layout |
| Slow generation | Long response times | Large templates or complex data | Optimize template size and structure |
| S3 upload failures | Generation succeeds but no file | S3 configuration issues | Check S3 permissions and configuration |

### Debug Checklist

1. **Verify Template**: Ensure template exists and is active
2. **Check Case Data**: Confirm all required case data is present
3. **Validate Tokens**: Use token testing endpoints to verify resolution
4. **Review Logs**: Check generation service logs for detailed errors
5. **Test Template**: Use template validation endpoints
6. **Check Permissions**: Verify user has access to case and template
7. **Monitor Performance**: Check generation times and resource usage

### Support Information

For additional support:
- Review API documentation for latest endpoint details
- Check system logs for detailed error information
- Use testing endpoints to isolate issues
- Contact system administrators for infrastructure issues