# Authorization Decorators - Quick Reference

## Role Group Decorators

### Basic Access Control

| Decorator | Access Level | Description |
|-----------|--------------|-------------|
| `@RequireSuperAdmin()` | Super Admin only | Highest privilege level |
| `@RequireRoleGroup('group')` | Role group (admin + user) | Any member of role group |
| `@RequireRoleGroupAdmin('group')` | Role group admin only | Admin within role group |
| `@RequireRoleGroupUser('group')` | Role group user (includes admin) | User level within role group |
| `@RequireAnyRoleGroupAdmin()` | Any role group admin | Admin of any role group |

### Combined Access Control

| Decorator | Access Level | Description |
|-----------|--------------|-------------|
| `@SuperAdminOrRoleGroup('group')` | Super Admin OR role group | Most common pattern |
| `@SuperAdminOrRoleGroupAdmin('group')` | Super Admin OR role group admin | Administrative operations |

### Multiple Role Groups

| Decorator | Access Level | Description |
|-----------|--------------|-------------|
| `@AllowRoleGroups(['group1', 'group2'])` | Multiple role groups | Any member of listed groups |
| `@AllowRoleGroupAdmins(['group1', 'group2'])` | Multiple role group admins | Admin of any listed group |

### Convenience Decorators

| Decorator | Equivalent To | Use Case |
|-----------|---------------|----------|
| `@ConveyancersOnly()` | `@SuperAdminOrRoleGroup('conveyancers')` | Conveyancing operations |
| `@ConveyancersAdminOnly()` | `@SuperAdminOrRoleGroupAdmin('conveyancers')` | Conveyancing admin |
| `@FinanceOnly()` | `@SuperAdminOrRoleGroup('finance')` | Finance operations |
| `@FinanceAdminOnly()` | `@SuperAdminOrRoleGroupAdmin('finance')` | Finance admin |

## Permission Decorators

### Resource Permissions

| Decorator | Description | Example |
|-----------|-------------|---------|
| `@HasPermission(resource, action)` | Requires specific permission | `@HasPermission(ResourceType.CASE, Permission.READ)` |

### Resource Types

```typescript
ResourceType.CASE          // Case management
ResourceType.DOCUMENT      // Document management
ResourceType.TASK          // Task management
ResourceType.CLIENT        // Client management
ResourceType.USER          // User management
ResourceType.REPORT        // Reporting
ResourceType.SYSTEM        // System configuration
```

### Permission Actions

```typescript
Permission.CREATE          // Create new resources
Permission.READ            // View/read resources
Permission.UPDATE          // Modify existing resources
Permission.DELETE          // Remove resources
Permission.ASSIGN          // Assign resources to users
Permission.APPROVE         // Approve/reject resources
Permission.EXPORT          // Export data
Permission.IMPORT          // Import data
```

## Common Usage Patterns

### CRUD Operations

```typescript
// CREATE
@Post()
@HasPermission(ResourceType.CASE, Permission.CREATE)
@ConveyancersOnly()

// READ
@Get()
@HasPermission(ResourceType.CASE, Permission.READ)
@SuperAdminOrRoleGroup('conveyancers')

// UPDATE
@Patch(':id')
@HasPermission(ResourceType.CASE, Permission.UPDATE)
@SuperAdminOrRoleGroup('conveyancers')

// DELETE
@Delete(':id')
@RequireSuperAdmin()
```

### Administrative Operations

```typescript
// User Management
@Post('users')
@HasPermission(ResourceType.USER, Permission.CREATE)
@SuperAdminOrRoleGroupAdmin('conveyancers')

// Assignment
@Patch(':id/assign')
@HasPermission(ResourceType.CASE, Permission.ASSIGN)
@SuperAdminOrRoleGroupAdmin('conveyancers')

// Statistics
@Get('statistics')
@HasPermission(ResourceType.REPORT, Permission.READ)
@RequireAnyRoleGroupAdmin()
```

### Cross-Role Group Access

```typescript
// Multiple role groups
@Get('shared-reports')
@HasPermission(ResourceType.REPORT, Permission.READ)
@AllowRoleGroups(['conveyancers', 'finance'])

// Multiple admins
@Put('system-config')
@HasPermission(ResourceType.SYSTEM, Permission.UPDATE)
@AllowRoleGroupAdmins(['conveyancers', 'finance'])
```

## Guard Configuration

### Required Guards (in order)

```typescript
@UseGuards(JwtGuard, TenantGuard, RolesGuard, RoleGroupGuard, PermissionGuard)
```

### Guard Responsibilities

1. **JwtGuard** - Authentication
2. **TenantGuard** - Tenant context
3. **RolesGuard** - Legacy roles (backward compatibility)
4. **RoleGroupGuard** - Role group access
5. **PermissionGuard** - Resource permissions

## Role Hierarchy

```
SUPER_ADMIN
├── conveyancers_admin → conveyancers_user
├── finance_admin → finance_user
└── [rolegroup]_admin → [rolegroup]_user
```

## Best Practices Checklist

- ✅ Always use both role group AND permission decorators
- ✅ Use appropriate access level (user vs admin vs super admin)
- ✅ Use convenience decorators for readability
- ✅ Include all guards in correct order
- ✅ Use Super Admin only for destructive operations
- ✅ Use role group admins for administrative operations
- ✅ Use role groups for regular operations

## Quick Decision Tree

**Need to restrict access?**
1. **Authentication only?** → Use `@UseGuards(JwtGuard)`
2. **Super Admin only?** → Add `@RequireSuperAdmin()`
3. **Specific role group?** → Add `@SuperAdminOrRoleGroup('group')`
4. **Admin operations?** → Add `@SuperAdminOrRoleGroupAdmin('group')`
5. **Resource permissions?** → Add `@HasPermission(resource, action)`
6. **Multiple role groups?** → Add `@AllowRoleGroups(['group1', 'group2'])`

**Always remember:**
- Include all required guards
- Combine role groups with permissions
- Use descriptive decorator names
- Test with different user roles
