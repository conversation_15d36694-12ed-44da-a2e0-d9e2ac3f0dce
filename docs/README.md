# TK-LPM Backend

## Overview

TK-LPM Backend is a multi-tenant platform built with NestJS, TypeORM, and Keycloak. The application is designed around a microservices architecture with separate modules for authentication, case management, communication, document management, and core services.

## Key Features

- **Multi-tenancy**: Schema-based isolation for tenant-specific data
- **Single Login**: Universal login system across all tenants
- **Role-based Access Control**: Granular permission management
- **Microservice Architecture**: Modular design with specialized services

## Documentation

- [Authentication System](./AUTHENTICATION.md) - Details on the auth service, JWT tokens, and the new single login system
- [Database Management](./DATABASE.md) - Information about the multi-tenant database structure

## Getting Started

### Prerequisites

- Node.js >= 18
- PostgreSQL 14+
- Docker (optional)
- Keycloak 20+

### Installation

```bash
# Install dependencies
yarn install

# Copy and configure environment variables
cp .env.example .env

# Start the development server
yarn start:dev
```

### Running with <PERSON><PERSON>

```bash
# Build and start all services with Docker Compose
docker-compose up -d
```

## Architecture

The application is structured into several microservices:

- **Auth**: User authentication, tenant management, and authorization
- **Case Management**: Case handling and workflow
- **Communication**: Messaging and notifications
- **Document Engine**: Document generation and management
- **Core**: Shared services and utilities

## Authentication

The authentication system has been significantly improved to support a single login experience:

- Users can now log in without specifying a tenant (realm)
- System automatically identifies available tenants for each user
- Passwords are securely stored and verified using bcrypt
- User-tenant relationships are properly separated from user-role relationships

See the [Authentication Documentation](./AUTHENTICATION.md) for more details.

## Database Structure

The application uses a multi-tenant database architecture:

- Public schema for system-wide tables and tenant metadata
- Separate schema for each tenant's isolated data
- Migration tools for managing schema evolution

See the [Database Documentation](./DATABASE.md) for more details.

<<<<<<< HEAD
## Unified Configuration

All database and service configuration is now managed in a single config file/module. Update all references to use this unified config.

## Project Structure

```
tk-lpm-backend/
├── apps/                # Microservice applications
│   ├── auth/            # Authentication service
│   ├── case-management/ # Case management service 
│   ├── communication/   # Communication service
│   ├── core/            # Core utilities
│   └── document-engine/ # Document management
├── libs/                # Shared libraries
│   └── common/          # Common utilities and modules
│       ├── repositories/  # Shared repositories (now here)
│       ├── guards/        # Shared guards (now here)
│       └── ...
└── docs/                # Documentation
```

## Debugging

- If you see database connection errors (e.g., `ECONNREFUSED`), check that your environment variables are set correctly and that the database is reachable from the container.
- Use `docker compose exec <service> env | grep POSTGRES` to verify env vars inside the container.
- Print TypeORM config at startup to verify the actual values being used.
- For tenant creation, verify that tables are created in the new schema by querying `information_schema.tables`.

=======
>>>>>>> 3ace0a4 (TKJ-183: Ensure segregated schema connections and shared login endpoint)
## API Endpoints

The main API endpoints are organized by microservice:

### Auth Service

- `POST /login` - Authenticate a user (realm optional)
- `POST /create-tenant` - Create a new tenant
- `POST /users` - Create a new user
- `GET /me` - Get current user profile

## Development

### Code Structure

```
tk-lpm-backend/
├── apps/                # Microservice applications
│   ├── auth/            # Authentication service
│   ├── case-management/ # Case management service 
│   ├── communication/   # Communication service
│   ├── core/            # Core utilities
│   └── document-engine/ # Document management
├── libs/                # Shared libraries
│   └── common/          # Common utilities and modules
└── docs/                # Documentation
```

### Testing

```bash
# Run all tests
yarn test

# Run tests for a specific microservice
yarn test:auth
```

## Contributing

Please follow the established coding conventions and commit message format. All new features should include appropriate tests and documentation. 