# Communication Service Integration Guide

This guide helps developers integrate with the TK-LPM Backend communication service for sending emails, notifications, and other communications using AWS SES as the primary provider with SendGrid and Mailgun as fallbacks.

## Table of Contents

- [Quick Start](#quick-start)
- [Email Templates](#email-templates)
- [Template Management](#template-management)
- [Integration Examples](#integration-examples)
- [Error Handling](#error-handling)
- [Environment Configuration](#environment-configuration)
- [Testing & Monitoring](#testing--monitoring)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Quick Start

### Basic Email Sending

```typescript
import { CommunicationProducer } from '@app/common/communication';

@Injectable()
export class NotificationService {
    constructor(
        private readonly communicationProducer: CommunicationProducer
    ) {}

    async sendCaseUpdate(caseId: string, recipientEmail: string, tenantId: string, userId: string) {
        await this.communicationProducer.sendEmail({
            tenantId,
            userId,
            recipient: recipientEmail,
            variables: {
                type: 'case-update',
                tenantName: 'Smith & Associates Law Firm',
                recipientName: '<PERSON>',
                caseNumber: 'LCS-2025-001',
                status: 'In Progress',
                caseSummary: 'Latest case developments...',
                handlerName: 'Sarah Richardson'
            }
        });
    }
}
```

### Module Setup

```typescript
import { 
    CommunicationProducer,
    MessageProducerService 
} from '@app/common/communication';
import { BullProviderModule } from '@app/common/bull/bull.module';

@Module({
    imports: [
        BullProviderModule, // Required for queue operations
    ],
    providers: [
        CommunicationProducer,
        MessageProducerService,
        NotificationService
    ],
    exports: [NotificationService]
})
export class NotificationModule {}
```

## Email Templates

The communication service supports two main SES templates that cover all communication needs:

### 1. Case Update Template (`case-update`)

Professional template for legal case communications with comprehensive case details.

**Required Variables:**
- `tenantName` - Law firm name
- `recipientName` - Client or recipient name  
- `caseNumber` - Case identifier
- `status` - Current case status

**Optional Variables:**
- `tenantLogo` - Firm logo text
- `tenantAddress` - Firm address
- `clientName` - Client company name
- `caseType` - Type of legal matter
- `urgency` - 'normal', 'high', 'critical'
- `caseSummary` - Case update details
- `additionalDetails` - Extra information
- `nextSteps` - Array of next steps or string
- `handlerName` - Lawyer/handler name
- `handlerTitle` - Handler's title
- `handlerEmail` - Handler's email
- `handlerPhone` - Handler's phone
- `caseUrl` - Link to case details
- `formattedOpeningDate` - Case opening date
- `supportEmail` - Support contact
- `currentYear` - Current year

### 2. Generic Notification Template (`generic-notification`)

Flexible template for general business communications, announcements, and system notifications.

**Required Variables:**
- `tenantName` - Organization name
- `recipientName` - Recipient name

**Optional Variables:**
- `tenantLogo` - Organization logo text
- `tenantAddress` - Organization address  
- `title` - Email title/heading
- `subject` - Custom subject (overrides default)
- `message` - Main message content
- `ctaText` - Call-to-action button text
- `ctaUrl` - Call-to-action link
- `supportEmail` - Support email
- `supportPhone` - Support phone
- `currentYear` - Current year

## Template Management

### Initialize Default Templates

Create both templates in AWS SES:

```bash
curl -X POST http://localhost:3001/api/communication/templates/initialize/default \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "totalTemplates": 2,
  "successful": 2,
  "failed": 0,
  "results": [
    {
      "templateName": "case-update-template",
      "status": "created",
      "message": "Template 'case-update-template' created successfully"
    },
    {
      "templateName": "generic-notification-template", 
      "status": "created",
      "message": "Template 'generic-notification-template' created successfully"
    }
  ]
}
```

### Template CRUD Operations

#### List Templates
```bash
curl -X GET http://localhost:3001/api/communication/templates
```

#### Get Template
```bash
curl -X GET http://localhost:3001/api/communication/templates/case-update-template
```

#### Create Custom Template
```bash
curl -X POST http://localhost:3001/api/communication/templates \
  -H "Content-Type: application/json" \
  -d '{
    "templateName": "custom-template",
    "subject": "{{title}} - {{tenantName}}",
    "htmlPart": "<html><body><h1>{{title}}</h1><p>{{message}}</p></body></html>",
    "textPart": "{{title}}\n\n{{message}}"
  }'
```

#### Update Template
```bash
curl -X PUT http://localhost:3001/api/communication/templates/custom-template \
  -H "Content-Type: application/json" \
  -d '{
    "subject": "Updated {{title}} - {{tenantName}}",
    "htmlPart": "<updated html>",
    "textPart": "updated text"
  }'
```

#### Delete Template
```bash
curl -X DELETE http://localhost:3001/api/communication/templates/custom-template
```

## Integration Examples

### Case Management Integration

```typescript
@Injectable()
export class CaseNotificationService {
    private readonly logger = new Logger(CaseNotificationService.name);

    constructor(
        private readonly communicationProducer: CommunicationProducer
    ) {}

    async notifyCaseStatusChange(
        caseEntity: CaseEntity,
        newStatus: string,
        recipientEmail: string
    ): Promise<void> {
        try {
            const variables = {
                type: 'case-update',
                tenantName: caseEntity.tenant.displayName,
                recipientName: caseEntity.client.fullName,
                caseNumber: caseEntity.caseNumber,
                status: newStatus,
                caseType: caseEntity.caseType,
                caseSummary: `Case status updated to: ${newStatus}`,
                handlerName: caseEntity.assignedLawyer?.fullName,
                handlerEmail: caseEntity.assignedLawyer?.email,
                caseUrl: `${process.env.CLIENT_PORTAL_URL}/cases/${caseEntity.id}`,
                currentYear: new Date().getFullYear().toString()
            };

            await this.communicationProducer.sendEmail({
                tenantId: caseEntity.tenantId,
                userId: caseEntity.assignedLawyer?.id || 'system',
                recipient: recipientEmail,
                variables
            });

            this.logger.log(`Case update notification sent for ${caseEntity.caseNumber}`);
        } catch (error) {
            this.logger.error(`Failed to send case notification: ${error.message}`);
            throw error;
        }
    }

    async sendUrgentAlert(
        caseEntity: CaseEntity,
        alertMessage: string,
        recipients: string[]
    ): Promise<void> {
        const variables = {
            type: 'case-update',
            tenantName: caseEntity.tenant.displayName,
            recipientName: '', // Set per recipient
            caseNumber: caseEntity.caseNumber,
            status: caseEntity.status,
            urgency: 'critical',
            caseSummary: alertMessage,
            handlerName: caseEntity.assignedLawyer?.fullName,
            currentYear: new Date().getFullYear().toString()
        };

        const emailPromises = recipients.map(email => 
            this.communicationProducer.sendEmail({
                tenantId: caseEntity.tenantId,
                userId: caseEntity.assignedLawyer?.id || 'system',
                recipient: email,
                variables: { ...variables, recipientName: email.split('@')[0] }
            })
        );

        await Promise.allSettled(emailPromises);
    }
}
```

### System Notifications

```typescript
@Injectable()
export class SystemNotificationService {
    constructor(
        private readonly communicationProducer: CommunicationProducer
    ) {}

    async sendMaintenanceNotification(
        tenantId: string,
        recipients: string[],
        maintenanceDetails: {
            startTime: string;
            duration: string;
            affectedServices: string[];
        }
    ): Promise<void> {
        const variables = {
            type: 'generic-notification',
            tenantName: 'TK-LPM System',
            recipientName: '', // Set per recipient
            title: 'Scheduled Maintenance Notice',
            message: `System maintenance is scheduled for ${maintenanceDetails.startTime}. Expected duration: ${maintenanceDetails.duration}. Affected services: ${maintenanceDetails.affectedServices.join(', ')}.`,
            supportEmail: '<EMAIL>',
            currentYear: new Date().getFullYear().toString()
        };

        const emailPromises = recipients.map(email =>
            this.communicationProducer.sendEmail({
                tenantId,
                userId: 'system',
                recipient: email,
                variables: { ...variables, recipientName: email.split('@')[0] }
            })
        );

        await Promise.allSettled(emailPromises);
    }

    async sendWelcomeEmail(
        newUser: UserEntity,
        inviterName: string,
        loginUrl: string
    ): Promise<void> {
        const variables = {
            type: 'generic-notification',
            tenantName: newUser.tenant.displayName,
            recipientName: newUser.fullName,
            title: 'Welcome to TK-LPM',
            message: `Welcome to TK-LPM! ${inviterName} has invited you to join ${newUser.tenant.displayName}. Click the button below to access your account.`,
            ctaText: 'Access Portal',
            ctaUrl: loginUrl,
            supportEmail: '<EMAIL>',
            currentYear: new Date().getFullYear().toString()
        };

        await this.communicationProducer.sendEmail({
            tenantId: newUser.tenantId,
            userId: 'system',
            recipient: newUser.email,
            variables
        });
    }
}
```

## Error Handling

### Communication Exceptions

```typescript
import {
    TemplateValidationException,
    RecipientValidationException,
    EmailProviderException,
    ConfigurationException
} from '@app/common/communication/exceptions';

@Injectable()
export class ReliableEmailService {
    private readonly logger = new Logger(ReliableEmailService.name);

    async sendEmailSafely(emailData: any): Promise<boolean> {
        try {
            await this.communicationProducer.sendEmail(emailData);
            return true;
        } catch (error) {
            if (error instanceof TemplateValidationException) {
                this.logger.error(`Template validation failed: ${error.message}`);
                // Don't retry - fix the template variables
                return false;
            } else if (error instanceof RecipientValidationException) {
                this.logger.error(`Invalid recipient: ${error.message}`);
                // Don't retry - fix the email address
                return false;
            } else if (error instanceof EmailProviderException) {
                this.logger.error(`All email providers failed: ${error.message}`);
                // Could retry later or alert administrators
                return false;
            } else if (error instanceof ConfigurationException) {
                this.logger.error(`Email service misconfigured: ${error.message}`);
                // Fix configuration before retrying
                return false;
            } else {
                this.logger.error(`Unexpected email error: ${error.message}`);
                return false;
            }
        }
    }
}
```

## Environment Configuration

### Required Environment Variables

```bash
# AWS SES (Primary Provider) - REQUIRED
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_SESSION_TOKEN=your_session_token  # For temporary credentials
AWS_SES_REGION=us-east-1

# Email Configuration
FROM_EMAIL=<EMAIL>

# SendGrid (Optional Fallback)
SENDGRID_API_KEY=your_sendgrid_api_key

# Mailgun (Optional Fallback)
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_DOMAIN=your_mailgun_domain

# Redis (Required for Queue)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
```

### Configuration Notes

- **SES Region**: Use region code (e.g., `us-east-1`) not descriptive name
- **FROM_EMAIL**: Must be verified in AWS SES
- **Session Token**: Required when using temporary AWS credentials
- **Fallback Providers**: At least SES must be configured; others are optional

## Testing & Monitoring

### Test Email Endpoints

```bash
# Test case update template
curl -X POST http://localhost:3001/api/communication/test/send-email \
  -H "Content-Type: application/json" \
  -d '{"to": "<EMAIL>"}'

# Test generic notification template
curl -X POST http://localhost:3001/api/communication/test/send-simple \
  -H "Content-Type: application/json" \
  -d '{"to": "<EMAIL>"}'
```

### Health Monitoring

```typescript
@Injectable()
export class EmailHealthService {
    constructor(
        private readonly emailConsumer: EmailConsumer
    ) {}

    async getProviderStatus() {
        return this.emailConsumer.getProviderStatus();
        // Returns status of SES (primary), SendGrid, and Mailgun
    }

    async isHealthy(): Promise<boolean> {
        const status = await this.getProviderStatus();
        return status.overall.healthy;
    }
}
```

## Best Practices

### 1. Template Variable Validation

```typescript
import { validateTemplateVariables } from '@app/common/communication';

// Always validate before sending
try {
    validateTemplateVariables('case-update', variables);
    await this.communicationProducer.sendEmail(emailData);
} catch (error) {
    this.logger.error(`Template validation failed: ${error.message}`);
    throw error;
}
```

### 2. Bulk Email Handling

```typescript
async sendBulkNotifications(recipients: string[], variables: any): Promise<void> {
    const BATCH_SIZE = 10;
    const batches = this.chunkArray(recipients, BATCH_SIZE);

    for (const batch of batches) {
        const promises = batch.map(recipient =>
            this.communicationProducer.sendEmail({
                ...emailData,
                recipient,
                variables: { ...variables, recipientName: recipient.split('@')[0] }
            })
        );

        await Promise.allSettled(promises);
        
        // Add delay between batches to avoid rate limits
        await this.delay(1000);
    }
}

private chunkArray<T>(array: T[], size: number): T[][] {
    return Array.from({ length: Math.ceil(array.length / size) }, (_, i) =>
        array.slice(i * size, i * size + size)
    );
}

private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}
```

### 3. Logging and Observability

```typescript
@Injectable()
export class ObservableEmailService {
    private readonly logger = new Logger(ObservableEmailService.name);

    async sendEmail(emailData: any): Promise<void> {
        const startTime = Date.now();
        
        try {
            this.logger.log(`Sending email to ${emailData.recipient} with template ${emailData.variables.type}`);
            
            await this.communicationProducer.sendEmail(emailData);
            
            const duration = Date.now() - startTime;
            this.logger.log(`Email sent successfully in ${duration}ms`);
            
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`Email failed after ${duration}ms: ${error.message}`);
            throw error;
        }
    }
}
```

## Troubleshooting

### Common Issues

1. **Template Not Found**
   - Run template initialization: `POST /api/communication/templates/initialize/default`
   - Verify templates exist in AWS SES Console

2. **Invalid AWS Credentials**
   - Check `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, `AWS_SESSION_TOKEN`
   - Ensure credentials have SES permissions
   - Verify region configuration

3. **Email Not Delivered**
   - Check if `FROM_EMAIL` is verified in SES
   - Verify recipient email is not in SES sandbox mode
   - Check application logs for provider errors

4. **Template Validation Errors**
   - Ensure all required variables are provided
   - Check variable names match template requirements
   - Use `validateTemplateVariables()` for testing

5. **Queue Connection Issues**
   - Verify Redis is running and accessible
   - Check Redis connection parameters
   - Ensure BullProviderModule is properly imported

### Debug Commands

```bash
# Check template status
curl -X GET http://localhost:3001/api/communication/templates/status/providers

# Test SES connectivity
curl -X POST http://localhost:3001/api/communication/test/send-simple \
  -d '{"to": "<EMAIL>"}'

# Monitor queue status via BullBoard
open http://localhost:3001/admin/queues
```

### Logging

Enable debug logging for detailed troubleshooting:

```bash
LOG_LEVEL=debug
```

This shows provider fallback attempts, template validation details, and queue processing information.

---

For additional support, check the [Communication Testing Plan](./COMMUNICATION_TESTING_PLAN.md) or contact the development team.