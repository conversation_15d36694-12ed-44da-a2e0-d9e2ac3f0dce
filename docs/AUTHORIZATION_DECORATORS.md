# Authorization Decorators Documentation

This document provides comprehensive documentation for the authorization system decorators used in the Legal Practice Management System. The system uses a dual-layer approach combining **Role Groups** and **Resource Permissions** for fine-grained access control.

## Table of Contents

1. [Overview](#overview)
2. [Role Group System](#role-group-system)
3. [Role Group Decorators](#role-group-decorators)
4. [Permission Decorators](#permission-decorators)
5. [Guard Chain](#guard-chain)
6. [Usage Examples](#usage-examples)
7. [Best Practices](#best-practices)

## Overview

The authorization system consists of three main layers:

1. **Authentication** (`JwtGuard`) - Verifies user identity
2. **Role-Based Access** (`RolesGuard`) - Controls access based on user roles
3. **Resource Permissions** (`PermissionGuard`) - Controls specific actions on resources

## Role Group System

### Hierarchy Structure

```
SUPER_ADMIN
├── conveyancers_admin
│   └── conveyancers_user
├── finance_admin
│   └── finance_user
└── [other_rolegroup]_admin
    └── [other_rolegroup]_user
```

### Role Types

- **SUPER_ADMIN**: Global administrator with access to all resources and operations
- **{roleGroup}_admin**: Administrator within a specific role group (e.g., `conveyancers_admin`)
- **{roleGroup}_user**: Regular user within a specific role group (e.g., `conveyancers_user`)

## Role Group Decorators

### Basic Role Group Decorators

#### `@RequireSuperAdmin()`
Restricts access to Super Admin only.

```typescript
@Delete(':id')
@RequireSuperAdmin()
async deleteCase(@Param('id') id: string) {
    // Only SUPER_ADMIN can access this endpoint
}
```

#### `@RequireRoleGroup(roleGroupKey: string)`
Requires access to a specific role group (admin or user level).

```typescript
@Get('reports')
@RequireRoleGroup('finance')
async getFinanceReports() {
    // finance_admin OR finance_user can access
}
```

#### `@RequireRoleGroupAdmin(roleGroupKey: string)`
Requires admin access to a specific role group.

```typescript
@Post('users')
@RequireRoleGroupAdmin('conveyancers')
async createUser() {
    // Only conveyancers_admin can access
}
```

#### `@RequireRoleGroupUser(roleGroupKey: string)`
Requires user access to a specific role group (includes admin).

```typescript
@Get('tasks')
@RequireRoleGroupUser('conveyancers')
async getTasks() {
    // conveyancers_user OR conveyancers_admin can access
}
```

#### `@RequireAnyRoleGroupAdmin()`
Requires admin access to any role group.

```typescript
@Get('statistics')
@RequireAnyRoleGroupAdmin()
async getStatistics() {
    // Any role group admin can access
}
```

### Combined Role Group Decorators

#### `@SuperAdminOrRoleGroup(roleGroupKey: string)`
Allows Super Admin OR any member of the specified role group.

```typescript
@Get('cases')
@SuperAdminOrRoleGroup('conveyancers')
async getCases() {
    // SUPER_ADMIN OR conveyancers_admin OR conveyancers_user
}
```

#### `@SuperAdminOrRoleGroupAdmin(roleGroupKey: string)`
Allows Super Admin OR admin of the specified role group.

```typescript
@Patch(':id/assign')
@SuperAdminOrRoleGroupAdmin('conveyancers')
async assignCase() {
    // SUPER_ADMIN OR conveyancers_admin
}
```

### Multiple Role Group Decorators

#### `@AllowRoleGroups(roleGroupKeys: string[])`
Allows access to multiple role groups.

```typescript
@Get('shared-reports')
@AllowRoleGroups(['conveyancers', 'finance'])
async getSharedReports() {
    // Members of conveyancers OR finance role groups
}
```

#### `@AllowRoleGroupAdmins(roleGroupKeys: string[])`
Allows admin access to multiple role groups.

```typescript
@Post('system-config')
@AllowRoleGroupAdmins(['conveyancers', 'finance'])
async updateSystemConfig() {
    // conveyancers_admin OR finance_admin
}
```

### Convenience Decorators

Pre-configured decorators for common role groups:

```typescript
// Role group access
@RequireConveyancers()     // Same as @RequireRoleGroup('conveyancers')
@RequireFinance()          // Same as @RequireRoleGroup('finance')

// Admin access
@RequireConveyancersAdmin() // Same as @RequireRoleGroupAdmin('conveyancers')
@RequireFinanceAdmin()      // Same as @RequireRoleGroupAdmin('finance')

// Combined access
@ConveyancersOnly()        // Same as @SuperAdminOrRoleGroup('conveyancers')
@FinanceOnly()             // Same as @SuperAdminOrRoleGroup('finance')
@ConveyancersAdminOnly()   // Same as @SuperAdminOrRoleGroupAdmin('conveyancers')
@FinanceAdminOnly()        // Same as @SuperAdminOrRoleGroupAdmin('finance')
```

## Permission Decorators

### Resource-Based Permission Control

#### `@HasPermission(resource: ResourceType, action: Permission)`
Requires specific permission on a resource type.

```typescript
@Post('cases')
@HasPermission(ResourceType.CASE, Permission.CREATE)
async createCase() {
    // User must have CREATE permission on CASE resource
}

@Get('documents')
@HasPermission(ResourceType.DOCUMENT, Permission.READ)
async getDocuments() {
    // User must have READ permission on DOCUMENT resource
}
```

### Resource Types

Available resource types (from `ResourceType` enum):

```typescript
enum ResourceType {
    CASE = 'case',
    DOCUMENT = 'document',
    TASK = 'task',
    CLIENT = 'client',
    USER = 'user',
    REPORT = 'report',
    SYSTEM = 'system',
    WORKFLOW = 'workflow',
    TEMPLATE = 'template',
    NOTIFICATION = 'notification',
    AUDIT = 'audit',
    SETTING = 'setting',
    INTEGRATION = 'integration',
    DASHBOARD = 'dashboard',
    ANALYTICS = 'analytics',
    CUSTOM = 'custom'
}
```

### Permission Actions

Available permission actions (from `Permission` enum):

```typescript
enum Permission {
    // Basic CRUD operations
    CREATE = 'CREATE',
    READ = 'READ',
    UPDATE = 'UPDATE',
    DELETE = 'DELETE',
    
    // Extended operations
    EDIT_BASIC = 'EDIT_BASIC',
    EDIT_ADVANCED = 'EDIT_ADVANCED',
    ASSIGN = 'ASSIGN',
    APPROVE = 'APPROVE',
    COMMENT = 'COMMENT',
    SHARE = 'SHARE',
    EXPORT = 'EXPORT',
    IMPORT = 'IMPORT',
    
    // View permissions
    VIEW_LIST = 'VIEW_LIST',
    VIEW_DETAILS = 'VIEW_DETAILS',
    VIEW_HISTORY = 'VIEW_HISTORY',
    VIEW_AUDIT = 'VIEW_AUDIT',
    
    // Administrative permissions
    MANAGE_PERMISSIONS = 'MANAGE_PERMISSIONS',
    SYSTEM_ADMIN = 'SYSTEM_ADMIN',
    TENANT_ADMIN = 'TENANT_ADMIN'
}
```

### Legacy Permission Decorators

#### `@IsSuperAdmin()`
**DEPRECATED** - Use `@RequireSuperAdmin()` instead.

```typescript
// OLD (deprecated)
@IsSuperAdmin()

// NEW (recommended)
@RequireSuperAdmin()
```

## Guard Chain

Controllers must use the complete guard chain for proper authorization:

```typescript
@Controller('cases')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, RoleGroupGuard, PermissionGuard)
export class CaseController {
    // Controller methods...
}
```

### Guard Execution Order

1. **JwtGuard** - Validates JWT token and extracts user information
2. **TenantGuard** - Validates tenant context
3. **RolesGuard** - Checks legacy system roles (for backward compatibility)
4. **RoleGroupGuard** - Checks role group access
5. **PermissionGuard** - Validates resource permissions

## Usage Examples

### Complete Endpoint Examples

```typescript
@Controller('cases')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, RoleGroupGuard, PermissionGuard)
export class CaseController {
    
    // Create case: Requires CREATE permission AND conveyancers access
    @Post()
    @HasPermission(ResourceType.CASE, Permission.CREATE)
    @ConveyancersOnly()
    async createCase(@Body() dto: CreateCaseDto) { }
    
    // Get cases: Requires READ permission AND conveyancers access
    @Get()
    @HasPermission(ResourceType.CASE, Permission.READ)
    @SuperAdminOrRoleGroup('conveyancers')
    async getCases() { }
    
    // Update case: Requires UPDATE permission AND conveyancers access
    @Patch(':id')
    @HasPermission(ResourceType.CASE, Permission.UPDATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async updateCase(@Param('id') id: string) { }
    
    // Delete case: Super Admin only (no role group restriction)
    @Delete(':id')
    @RequireSuperAdmin()
    async deleteCase(@Param('id') id: string) { }
    
    // Assign case: Requires UPDATE permission AND admin access
    @Patch(':id/assign')
    @HasPermission(ResourceType.CASE, Permission.ASSIGN)
    @SuperAdminOrRoleGroupAdmin('conveyancers')
    async assignCase(@Param('id') id: string) { }
    
    // Statistics: Admin of any role group
    @Get('statistics')
    @HasPermission(ResourceType.CASE, Permission.READ)
    @RequireAnyRoleGroupAdmin()
    async getStatistics() { }
}
```

### Multi-Role Group Access

```typescript
@Controller('reports')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, RoleGroupGuard, PermissionGuard)
export class ReportsController {

    // Accessible by multiple role groups
    @Get('shared')
    @HasPermission(ResourceType.REPORT, Permission.READ)
    @AllowRoleGroups(['conveyancers', 'finance'])
    async getSharedReports() { }

    // Admin access to multiple role groups
    @Post('system-reports')
    @HasPermission(ResourceType.REPORT, Permission.CREATE)
    @AllowRoleGroupAdmins(['conveyancers', 'finance'])
    async createSystemReport() { }
}
```

### Finance-Specific Examples

```typescript
@Controller('finance')
@UseGuards(JwtGuard, TenantGuard, RolesGuard, RoleGroupGuard, PermissionGuard)
export class FinanceController {

    // Finance role group access
    @Get('invoices')
    @HasPermission(ResourceType.DOCUMENT, Permission.READ)
    @FinanceOnly()
    async getInvoices() { }

    // Finance admin only
    @Post('payments')
    @HasPermission(ResourceType.DOCUMENT, Permission.CREATE)
    @FinanceAdminOnly()
    async processPayment() { }
}
```

## Best Practices

### 1. Always Combine Role Groups with Permissions

```typescript
// ✅ GOOD: Both role group and permission checks
@Get('cases')
@HasPermission(ResourceType.CASE, Permission.READ)
@ConveyancersOnly()
async getCases() { }

// ❌ BAD: Only role group check (no resource permission)
@Get('cases')
@ConveyancersOnly()
async getCases() { }
```

### 2. Use Appropriate Access Levels

```typescript
// ✅ GOOD: Regular operations use role group access
@Get('cases')
@SuperAdminOrRoleGroup('conveyancers')
async getCases() { }

// ✅ GOOD: Administrative operations use admin access
@Post('users')
@SuperAdminOrRoleGroupAdmin('conveyancers')
async createUser() { }

// ✅ GOOD: Destructive operations use Super Admin only
@Delete(':id')
@RequireSuperAdmin()
async deleteCase() { }
```

### 3. Use Convenience Decorators

```typescript
// ✅ GOOD: Use convenience decorators for readability
@ConveyancersOnly()
async getCases() { }

// ❌ LESS READABLE: Explicit decorator
@SuperAdminOrRoleGroup('conveyancers')
async getCases() { }
```

### 4. Consistent Guard Chain

Always use the complete guard chain in the same order:

```typescript
@UseGuards(JwtGuard, TenantGuard, RolesGuard,PermissionGuard)
```

### 5. Meaningful Error Messages

The guards provide descriptive error messages:

```typescript
// Role group access denied
"Access denied: requires conveyancers role group access"

// Admin access denied
"Access denied: requires conveyancers admin access"

// Permission denied
"Access denied: missing CREATE permission on CASE resource"
```

## Common Patterns

### 1. CRUD Operations

```typescript
// Create: Role group access + CREATE permission
@Post()
@HasPermission(ResourceType.CASE, Permission.CREATE)
@ConveyancersOnly()

// Read: Role group access + READ permission
@Get()
@HasPermission(ResourceType.CASE, Permission.READ)
@SuperAdminOrRoleGroup('conveyancers')

// Update: Role group access + UPDATE permission
@Patch(':id')
@HasPermission(ResourceType.CASE, Permission.UPDATE)
@SuperAdminOrRoleGroup('conveyancers')

// Delete: Super Admin only (most restrictive)
@Delete(':id')
@RequireSuperAdmin()
```

### 2. Administrative Operations

```typescript
// User management: Admin access required
@Post('users')
@HasPermission(ResourceType.USER, Permission.CREATE)
@SuperAdminOrRoleGroupAdmin('conveyancers')

// Assignment operations: Admin access required
@Patch(':id/assign')
@HasPermission(ResourceType.CASE, Permission.ASSIGN)
@SuperAdminOrRoleGroupAdmin('conveyancers')

// Statistics/Reports: Any admin can view
@Get('statistics')
@HasPermission(ResourceType.REPORT, Permission.READ)
@RequireAnyRoleGroupAdmin()
```

### 3. Cross-Role Group Operations

```typescript
// Shared resources: Multiple role groups
@Get('shared-documents')
@HasPermission(ResourceType.DOCUMENT, Permission.READ)
@AllowRoleGroups(['conveyancers', 'finance'])

// System configuration: Multiple admins
@Put('system-settings')
@HasPermission(ResourceType.SYSTEM, Permission.UPDATE)
@AllowRoleGroupAdmins(['conveyancers', 'finance'])
```

## Migration from Legacy System

### Old System (Deprecated)

```typescript
// OLD: Legacy role-based access
@Roles(AppRole.ADMIN, AppRole.LAWYER)
@IsSuperAdmin()
```

### New System (Current)

```typescript
// NEW: Role group-based access
@SuperAdminOrRoleGroup('conveyancers')
@RequireSuperAdmin()
```

## Troubleshooting

### Common Issues

1. **403 Forbidden**: Check if user has the required role group access
2. **Permission Denied**: Verify the user has the required resource permission
3. **Guard Order**: Ensure guards are in the correct order
4. **Missing Decorators**: Both role group and permission decorators are usually required

### Debugging

Enable debug logging to see role expansion and permission checks:

```typescript
// In your environment configuration
LOG_LEVEL=debug
```

This will show detailed logs of:
- Role expansion (which roles the user inherits)
- Permission checks (which permissions are being validated)
- Guard execution flow

---

For more information about role group management and permission configuration, see:
- [Role Group Management API Documentation](./ROLE_GROUP_API.md)
- [Permission System Architecture](./PERMISSION_ARCHITECTURE.md)
