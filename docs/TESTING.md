# Testing Infrastructure

This document describes the testing infrastructure for the TK-LPM backend.

## Types of Tests

The project has two main types of tests:

1. **Unit Tests** - Tests individual functions, classes, or components in isolation without external dependencies.
2. **Integration Tests** - Tests the interaction between different parts of the application, including database and Keycloak connections.

## Test Environment

### Unit Tests

Unit tests run without actual database or external service connections. They use mocked dependencies and can be run locally without any setup:

```bash
yarn test:unit
```

### Integration Tests

Integration tests require a running PostgreSQL and Keycloak environment. We provide Docker containers for these dependencies:

```bash
# Run integration tests with Docker (recommended)
yarn test:docker integration

# Run just a simple database integration test
yarn test:integration:simple-db
```

Integration tests use the following environment:

- PostgreSQL running on port 5434 (to avoid conflicts with other PostgreSQL instances)
- Keycloak running in dev mode on port 8090
- Test databases are isolated from development/production databases

## Docker Test Environment

Integration tests run in a dedicated Docker environment defined in `docker-compose.test.yml`:

- A dedicated PostgreSQL container with test data
- A Keycloak container using an in-memory H2 database for faster startup
- Network isolation for test components

### Database Configuration

Integration tests use the `TEST_DB_*` environment variables to connect to the database:

- `TEST_DB_HOST`: Host of test database (default: 'localhost')
- `TEST_DB_PORT`: Port of test database (default: 5434)
- `TEST_DB_USERNAME`: Username for test database (default: 'postgres')
- `TEST_DB_PASSWORD`: Password for test database (default: 'postgres')
- `TEST_DB_DATABASE`: Database name (default: 'tk_lpm_test')

### Test Configuration

Test configuration is centralized in `test/test-config.ts`, which provides:

1. Database connection settings
2. Keycloak connection settings
3. Other test-specific configuration

## Running Tests

### Unit Tests

```bash
# Run all unit tests
yarn test:unit

# Run unit tests in watch mode
yarn test:unit --watch

# Run specific unit tests
yarn test:unit path/to/test
```

### Integration Tests

```bash
# Run all integration tests with Docker
yarn test:docker integration

# Run integration tests for specific module
yarn test:integration:auth
yarn test:integration:case-management

# Run simple DB connectivity test
yarn test:integration:simple-db
```

## Test Setup Files

- `test/jest-setup.ts`: Runs before each test file, configures mock data or real connections
- `test/jest-global-setup.js`: Runs once before all tests, initializes the test database
- `test/integration-test.module.ts`: NestJS module for integration tests with proper DB config
- `test/test-config.ts`: Centralized test configuration for database and services

## Troubleshooting

If integration tests fail with database connection errors:

1. Check if the Docker containers are running: `docker ps`
2. Verify the PostgreSQL container is healthy: `docker logs tk-lpm-postgres-test`
3. Try resetting the test environment: `yarn test:docker integration`
4. Make sure no other services are using the test ports (5434, 8090)

If unit tests fail with import errors or missing mocks:

1. Check if the mock definitions in `test/jest-setup.ts` are correct
2. Verify that your test is correctly categorized (unit vs integration)

## Overview

The testing infrastructure is designed to:
1. Work consistently across all environments (development, CI, production)
2. Isolate tests from any external dependencies for reliable results
3. Support both unit tests and integration tests
4. Minimize configuration needed to run tests

## Test Environments

### Local Development

For daily development, tests can run directly against local services:

```bash
# Run unit tests
yarn test:unit

# Run integration tests 
yarn test:integration

# Run all tests
yarn test
```

These rely on locally running services matching the expected configuration.

### Docker-Based Environment

A more reliable approach is to use the Docker-based test environment:

```bash
# Run all tests with dedicated Docker containers
yarn test:docker

# Run only unit tests with Docker
yarn test:unit:docker

# Run only integration tests with Docker
yarn test:integration:docker
```

This approach:
- Spins up isolated PostgreSQL and Keycloak containers
- Configures them identically to production
- Runs tests against these containers
- Tears down the containers after tests complete

## How It Works

1. The `docker-compose.test.yml` file defines the test environment containers:
   - PostgreSQL on port 5434
   - Keycloak on port 8090

2. The `scripts/run-tests.sh` script:
   - Starts the containers
   - Waits for them to be ready
   - Sets the proper environment variables
   - Runs the tests
   - Tears down the containers

3. The `test-config.ts` file:
   - Reads environment variables to determine test configuration
   - Provides consistent configuration to tests

## Database Setup

The test database is initialized with a minimal schema:
- The public schema with tenant and user tables
- Empty tenant schemas that tests can populate

## Keycloak Setup

The test Keycloak instance:
- Runs in development mode for faster startup
- Uses the test PostgreSQL instance
- Is configured with admin credentials matching the tests

## Resolving Test Failures

If tests fail with connection errors, check:
1. Are the test containers running? (`docker ps`)
2. Are they using the expected ports? (5434 for PostgreSQL, 8090 for Keycloak)
3. Are there any errors in the container logs? (`docker logs tk-lpm-postgres-test`)

To manually clean up:
```bash
docker compose -f docker-compose.test.yml down -v
```

## Best Practices

1. **Write environment-agnostic tests**: Tests should work in any environment by using environment variables for configuration
2. **Use mocks for unit tests**: Unit tests should mock all external dependencies
3. **Reset between tests**: Each test should start with a clean state
4. **Use the Docker environment**: For consistent results, prefer the Docker-based test environment
5. **Fast feedback**: Keep unit tests fast for quick feedback during development 

## Database Setup for Integration Tests

Integration tests require a properly set up database with the correct schema. Our approach is to:

1. Use a dedicated PostgreSQL database for tests running on port 5434
2. Initialize the database with SQL scripts in the `initdb/` directory:
   - `test-setup.sql` - Basic schema setup (tenant and user tables)
   - `test-migration.sql` - Additional tables required for specific tests
3. Reset the database between test runs to ensure test isolation

### Schema Initialization

The database initialization happens in two ways:

1. **Docker initialization scripts**: When the Docker container starts, it automatically runs SQL scripts in the `/docker-entrypoint-initdb.d/` directory.
2. **Jest global setup**: For each test run, `jest-global-setup.js` ensures the schema is properly set up.

This dual approach ensures that:
- Even if a Docker volume persists, tests will always have the required schema
- Tests can be run against a fresh database or an existing one

### Resolving Schema Issues

If your tests are failing with database schema issues (tables don't exist), check that:

1. The Docker container was started with `scripts/run-tests.sh`
2. The correct SQL initialization files are mounted in `docker-compose.test.yml`
3. The `jest-global-setup.js` file is properly creating the necessary tables

### Managing Test Data

The `integration-db-reset.ts` file provides a function to reset the database between test runs. It:

1. Truncates all tables to remove existing data
2. Re-inserts any required default data (like system roles)
3. Ensures test isolation while preserving the schema

To use it in your tests:

```typescript
import { resetTestDatabase } from './integration-db-reset';

describe('Your Integration Test', () => {
  beforeEach(async () => {
    await resetTestDatabase();
  });
  
  // Your tests...
});
``` 