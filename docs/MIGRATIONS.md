# Database Migrations Guide

This guide explains how to manage database migrations in the TK-LPM-Backend application.

## Overview

Our application uses TypeORM for database management and has a multi-tenant architecture with two separate schemas:

1. **Public Schema**: Contains global application data, tenant information, etc.
2. **Tenant Schema**: Contains tenant-specific data

Each schema has its own set of entities and migrations.

## Directory Structure

- Entities:
  - Public schema entities: `libs/common/src/typeorm/entities/public/`
  - Tenant schema entities: `libs/common/src/typeorm/entities/tenant/`

- Migrations:
  - Public schema migrations: `libs/common/src/typeorm/migrations/public/`
  - Tenant schema migrations: `libs/common/src/typeorm/migrations/tenant/`

## Generating Migrations

We have separate commands for generating migrations for each schema:

### For Public Schema Migrations

```bash
yarn migration:generate:public
```

This will generate a new migration file in `libs/common/src/typeorm/migrations/public/` based on changes detected in public schema entities. The migration will be named with a timestamp and "public-migration" prefix.

### For Tenant Schema Migrations

```bash
yarn migration:generate:tenant MigrationName
```

This will create an empty migration file in `libs/common/src/typeorm/migrations/tenant/` with a name prefixed with "tenant-migration-". Unlike public schema migrations, you'll need to manually edit this file to add your schema changes.

The reason for this is that TypeORM CLI has difficulty resolving path aliases when loading tenant-specific entity files. By creating an empty migration, you can manually add the necessary SQL or TypeORM query builder code.

Example of a manually edited tenant migration:

```typescript
import { MigrationInterface, QueryRunner } from 'typeorm';

export class TenantMigrationExample1234567890123 implements MigrationInterface {
    name = 'TenantMigrationExample1234567890123';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add your schema changes here
        await queryRunner.query(`
            CREATE TABLE "documents" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying NOT NULL,
                "content" text,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_documents" PRIMARY KEY ("id")
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add code to revert your schema changes here
        await queryRunner.query(`DROP TABLE "documents"`);
    }
}
```

## Running Migrations

To run public schema migrations:

```bash
yarn migration:run:public
```

To run tenant schema migrations:

```bash
yarn migration:run:tenant
```

To run all migrations (both public and tenant):

```bash
yarn migration:run
```

## Reverting Migrations

To revert the most recent public schema migration:

```bash
yarn migration:revert:public
```

To revert the most recent tenant schema migration:

```bash
yarn migration:revert:tenant
```

To revert both public and tenant migrations:

```bash
yarn migration:revert
```

## Best Practices

1. **Descriptive Names**: Use descriptive names for migrations, e.g., `AddUserEmailColumn` instead of `Update1`.

2. **Review Generated Migrations**: Always review generated migrations before running them to ensure they match your expectations.

3. **Test Migrations**: Test migrations in a development environment before applying them to production.

4. **Backup Database**: Always backup your database before running migrations in production.

5. **Separate Concerns**: Keep public and tenant schema migrations separate to maintain a clean architecture.

## Troubleshooting

If you encounter issues with migrations:

1. Make sure your database connection is properly configured in `.env`.
2. Check that entity paths in `typeorm.config.ts` are correct.
3. Ensure that you've committed any pending changes to entities before generating migrations.
4. If a migration fails, you can revert it and try again with corrections. 