# Database Management Documentation

## Overview

This application uses a multi-tenant database architecture with PostgreSQL. The database is structured with two types of schemas:

1. **Public Schema**: Contains system-wide tables and tenant metadata
2. **Tenant Schemas**: Individual schemas for each tenant's data (dynamically created)

## Directory Structure

```
libs/common/src/typeorm/
├── entities/
│   ├── public/           # System-wide entities
│   │   ├── tenant.entity.ts
│   │   ├── system-user.entity.ts
│   │   └── system-role.entity.ts
│   └── tenant/          # Tenant-specific entities
│       ├── user-profile.entity.ts
│       └── tenant-role.entity.ts
└── migrations/
    ├── public/          # Public schema migrations
    │   └── *************-InitialPublicSchema.ts
    ├── tenant/          # Tenant schema migrations
    │   └── *************-InitialTenantSchema.ts
    └── index.ts         # Migration exports
```

## Schema Management

### Public Schema

- Contains tenant metadata and system-wide tables
- Created during initial database setup
- Shared across all tenants
- Contains tables like:
  - `tenants`: Tenant metadata
  - `system_users`: System-wide user accounts
  - `system_roles`: System-level roles

### Tenant Schemas

- Dynamically created for each tenant
- Named using pattern: `tenant_x{uuid}`
- Isolated data per tenant
- Contains tables like:
  - `user_profiles`: Tenant-specific user profiles
  - `tenant_roles`: Tenant-specific roles
  - `user_roles`: User-role associations

## Migrations

### Running Migrations

1. **Public Schema Migrations**:
   ```bash
   # Run public schema migrations
   yarn migration:run:public
   
   # Generate new public schema migration
   yarn migration:generate:public
   ```

2. **Tenant Schema Migrations**:
   ```bash
   # Run migrations for all tenant schemas
   yarn migration:run:tenant
   
   # Run migrations for specific tenant
   yarn migration:run:tenant --tenant=<tenant-id>
   
   # Generate new tenant schema migration
   yarn migration:generate:tenant
   ```

### Migration Files

- **Naming Convention**: `{timestamp}-{description}.ts`
- **Location**: 
  - Public: `libs/common/src/typeorm/migrations/public/`
  - Tenant: `libs/common/src/typeorm/migrations/tenant/`

### Creating New Migrations

1. Make changes to entity files
2. Generate migration:
   ```bash
   yarn migration:generate:public src/migrations/public/my-migration
   # or
   yarn migration:generate:tenant src/migrations/tenant/my-migration
   ```
3. Review and adjust the generated migration file
4. Run the migration

## Entity Management

### Public Entities

- Located in `libs/common/src/typeorm/entities/public/`
- Represent system-wide data structures
- Shared across all tenants

### Tenant Entities

- Located in `libs/common/src/typeorm/entities/tenant/`
- Represent tenant-specific data structures
- Automatically created in each tenant schema

## Multi-tenancy Implementation

### Tenant Creation Process

1. Create tenant record in public schema
2. Create tenant-specific schema
3. Run tenant migrations
4. Initialize default data (roles, etc.)

### Schema Isolation

- Each tenant has its own schema
- Cross-schema queries are prevented
- Tenant context is required for all operations

## Configuration

### TypeORM Configuration

The database configuration is managed in `typeorm.config.ts`:

```typescript
// Base configuration shared between schemas
const baseConfig = {
  type: 'postgres',
  host: process.env.POSTGRES_HOST,
  // ... other common settings
};

// Public schema config
const publicConfig = {
  ...baseConfig,
  schema: 'public',
  entities: ['./libs/common/src/typeorm/entities/public/**/*.entity.{js,ts}'],
  migrations: ['./libs/common/src/typeorm/migrations/public/**/*.ts'],
};

// Tenant schema config
const tenantConfig = {
  ...baseConfig,
  entities: ['./libs/common/src/typeorm/entities/tenant/**/*.entity.{js,ts}'],
  migrations: ['./libs/common/src/typeorm/migrations/tenant/**/*.ts'],
};
```

## Best Practices

1. **Migration Generation**:
   - Always generate migrations for entity changes
   - Review generated migrations before applying
   - Test migrations in development environment

2. **Entity Updates**:
   - Keep public and tenant entities separate
   - Use appropriate decorators (@Entity, @Column, etc.)
   - Maintain proper relationships between entities

3. **Schema Management**:
   - Never modify tenant schemas directly
   - Use migration scripts for all schema changes
   - Maintain backward compatibility

4. **Data Isolation**:
   - Always use tenant context in services
   - Verify schema isolation in queries
   - Use transaction where appropriate

## Troubleshooting

### Common Issues

1. **Migration Failures**:
   ```bash
   # Reset failed migration
   yarn migration:revert:public
   # or
   yarn migration:revert:tenant --tenant=<tenant-id>
   ```

2. **Schema Sync Issues**:
   ```bash
   # Check schema status
   yarn schema:check
   
   # Force sync (development only)
   yarn schema:sync
   ```

3. **Tenant Creation Issues**:
   ```bash
   # Verify tenant schema
   yarn tenant:verify <tenant-id>
   
   # Repair tenant schema
   yarn tenant:repair <tenant-id>
   ```

## Tenant Schema Creation & Migration

When a new tenant is created:
1. The system creates a new schema for the tenant.
2. TypeORM migrations are run to create all required tables in the new schema.
3. You can verify table creation by querying `information_schema.tables` for the new schema.

## Debugging Migration Issues

- Log the migrations being run and the schema context.
- Check the `typeorm_migrations` table in the new schema for executed migrations.
- If tables are missing, ensure your migration files are included in the DataSource config and actually create tables.
- Repositories and guards are now located in their respective app folders, not in `libs/common`.

## Unified Configuration

All database and service configuration is now managed in a single config file/module. Update all references to use this unified config. 
