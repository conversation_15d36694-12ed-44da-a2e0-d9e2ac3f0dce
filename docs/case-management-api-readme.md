# Case Management API Documentation

This document provides comprehensive documentation for the Case Management API, including all endpoints, request/response formats, and usage examples.

## Table of Contents

1. [Introduction](#introduction)
2. [Authentication](#authentication)
3. [Postman Collection](#postman-collection)
4. [API Endpoints](#api-endpoints)
   - [Case Management](#case-management)
   - [Search & Filtering](#search--filtering)
   - [Client Management](#client-management)
   - [Case Assignments](#case-assignments)
   - [Case Notes](#case-notes)
   - [Case Attachments](#case-attachments)
   - [Notifications & Deadlines](#notifications--deadlines)
5. [Error Handling](#error-handling)
6. [Pagination](#pagination)

## Introduction

The Case Management API provides a comprehensive set of endpoints for managing legal cases, clients, assignments, notes, attachments, and deadlines. It supports multi-tenancy, role-based access control, and comprehensive audit logging.

## Authentication

All API endpoints require authentication using a JWT token. The token should be included in the `Authorization` header as a Bearer token.

Example:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

Additionally, all requests must include a tenant ID in the `x-tenant-id` header.

Example:
```
x-tenant-id: your-tenant-id
```

## Postman Collection

A comprehensive Postman collection is provided in the `case-management-api.postman_collection.json` file. This collection includes all API endpoints with example requests and responses.

### Importing the Collection

1. Open Postman
2. Click on "Import" in the top left corner
3. Select the `case-management-api.postman_collection.json` file
4. The collection will be imported with all endpoints and example requests

### Setting Up Environment Variables

The collection uses the following environment variables:

- `base_url`: The base URL for the API (e.g., `http://localhost:3000/api/case-management`)
- `auth_url`: The authentication URL (e.g., `http://localhost:3000/api/auth`)
- `tenant_id`: Your tenant ID
- `auth_token`: Your JWT authentication token
- `username`: Your username for authentication
- `password`: Your password for authentication
- `case_id`: ID of a case for testing
- `client_id`: ID of a client for testing
- `user_id`: ID of a user for testing
- `note_id`: ID of a note for testing
- `attachment_id`: ID of an attachment for testing
- `old_user_id`: ID of a user to reassign from
- `new_user_id`: ID of a user to reassign to

Create a Postman environment and set these variables before using the collection.

## API Endpoints

### Case Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/cases` | Create a new case |
| GET | `/cases/:id` | Get a case by ID |
| GET | `/cases/number/:caseNumber` | Get a case by case number |
| GET | `/cases/:id/details` | Get comprehensive case details |
| PATCH | `/cases/:id` | Update a case |
| PATCH | `/cases/:id/status/:status` | Change case status |
| PATCH | `/cases/:id/close` | Close a case |
| GET | `/cases` | Get all cases with pagination and filtering |

### Search & Filtering

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/cases/search/quick` | Quick search for cases |
| GET | `/cases/search/global` | Global search across cases, clients, and documents |
| GET | `/cases/search/id/:caseNumber` | Search for a case by ID |
| GET | `/clients/search/quick` | Quick search for clients |
| GET | `/clients/search/:name` | Search clients by name |
| GET | `/cases/filter/type/:type` | Filter cases by type |
| GET | `/cases/filter/status/:status` | Filter cases by status |
| GET | `/cases/filter/date-range` | Filter cases by date range |
| GET | `/cases/filter/priority/:priority` | Filter cases by priority |

### Client Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/clients` | Create a new client |
| GET | `/clients/:id` | Get a client by ID |
| PATCH | `/clients/:id` | Update a client |
| GET | `/clients` | Get all clients with pagination |

### Case Assignments

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/cases/:caseId/assignments` | Assign a case to a user |
| DELETE | `/cases/:caseId/assignments/users/:userId` | Unassign a user from a case |
| GET | `/cases/:caseId/assignments` | Get all assignments for a case |
| POST | `/cases/:caseId/assignments/reassign/:oldUserId` | Reassign a case from one user to another |

### Case Notes

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/cases/:caseId/notes` | Create a new note for a case |
| GET | `/cases/:caseId/notes` | Get all notes for a case |
| GET | `/cases/:caseId/notes/:noteId` | Get a note by ID |
| PATCH | `/cases/:caseId/notes/:noteId/pin` | Toggle the pin status of a note |
| GET | `/cases/:caseId/notes/pinned` | Get all pinned notes for a case |
| GET | `/cases/:caseId/notes/recent` | Get recent notes for a case |

### Case Attachments

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/cases/:caseId/attachments` | Create a new attachment for a case |
| GET | `/cases/:caseId/attachments` | Get all attachments for a case |
| GET | `/cases/:caseId/attachments/:attachmentId` | Get an attachment by ID |
| DELETE | `/cases/:caseId/attachments/:attachmentId` | Delete an attachment |
| GET | `/cases/:caseId/attachments/search` | Search for attachments |

### Notifications & Deadlines

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/cases/notifications/upcoming-deadlines` | Get cases with upcoming deadlines |
| GET | `/cases/notifications/missed-deadlines` | Get cases with missed deadlines |
| POST | `/cases/:caseId/reminders` | Set a custom reminder for a case deadline |

## Error Handling

All API endpoints return standardized error responses with the following format:

```json
{
  "code": 400,
  "status": "Bad Request",
  "message": "Error message",
  "data": null,
  "meta": {
    "errorCode": "VALIDATION_ERROR",
    "fields": {
      "email": "Invalid email format"
    }
  }
}
```

## Pagination

Endpoints that return multiple items support pagination with the following query parameters:

- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `sortBy`: Field to sort by
- `order`: Sort order (ASC or DESC)

Example:
```
GET /cases?page=2&limit=20&sortBy=createdAt&order=DESC
```

Response includes pagination metadata:

```json
{
  "code": 200,
  "status": "OK",
  "message": "Cases retrieved successfully",
  "data": [...],
  "meta": {
    "pagination": {
      "total": 100,
      "page": 2,
      "limit": 20,
      "pages": 5
    }
  }
}
```
