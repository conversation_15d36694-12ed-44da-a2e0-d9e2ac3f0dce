# SES Template Setup Guide

## Overview

The system now uses **AWS SES as the primary email provider** with comprehensive template management capabilities.

## Key Changes Made

### 1. Email Provider Priority Updated
- **Primary**: AWS SES
- **Secondary**: SendGrid  
- **Fallback**: Mailgun

### 2. New Services Added
- `SESTemplateManagerService`: Complete CRUD operations for SES templates
- `TemplateManagementController`: RESTful API for template management

### 3. Available Endpoints

#### Template Initialization
```bash
# Initialize default case-update template
POST /api/communication/templates/initialize/default

# Initialize custom templates
POST /api/communication/templates/initialize
{
  "templates": [
    {
      "templateName": "case-update-template",
      "subject": "Case Update - {{caseNumber}}",
      "htmlPart": "<html>...</html>",
      "textPart": "text version..."
    }
  ]
}

# Sync all unified templates with SES
POST /api/communication/templates/sync/unified
```

#### Template CRUD Operations
```bash
# Create template
POST /api/communication/templates
{
  "templateName": "my-template",
  "subject": "Subject {{variable}}",
  "htmlPart": "<html>...</html>",
  "textPart": "text version"
}

# Update template
PUT /api/communication/templates/{templateName}
{
  "subject": "Updated Subject {{variable}}",
  "htmlPart": "<html>...</html>"
}

# Get template
GET /api/communication/templates/{templateName}

# List all templates
GET /api/communication/templates

# Delete template
DELETE /api/communication/templates/{templateName}
```

#### Status and Configuration
```bash
# Get provider status
GET /api/communication/templates/status/providers

# Get unified template configurations
GET /api/communication/templates/unified/configurations
```

## Setting Up Your Case Update Template

### Step 1: Prepare Your HTML Template

Replace the default HTML in the controller with your custom template. The template should use **Handlebars syntax** for variables:

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Case Update - {{caseNumber}}</title>
</head>
<body>
    <!-- Your custom HTML here -->
    <h1>Case Update: {{caseNumber}}</h1>
    <p>Dear {{recipientName}},</p>
    
    <p>Status: {{status}}</p>
    {{#if caseSummary}}<p>Summary: {{caseSummary}}</p>{{/if}}
    
    <!-- Conditional content -->
    {{#if urgency}}
        {{#eq urgency "high"}}
            <div style="background: red; color: white; padding: 10px;">
                URGENT: This case requires immediate attention!
            </div>
        {{/eq}}
    {{/if}}
</body>
</html>
```

### Step 2: Available Template Variables

For **case-update** template, these variables are available:

#### Required Variables
- `tenantName`: Law firm name
- `recipientName`: Client name
- `caseNumber`: Case identifier
- `status`: Current case status

#### Optional Variables
- `caseSummary`: Case summary text
- `nextSteps`: Next action items
- `handlerName`: Lawyer/handler name
- `handlerTitle`: Handler title
- `urgency`: Priority level (low, normal, high, critical)
- `deadline`: Important deadline
- `caseUrl`: Link to case details
- `supportEmail`: Support contact
- `tenantLogo`: Logo URL
- `courtDate`: Court appearance date
- `courtTime`: Court time

### Step 3: Initialize the Template

Use the initialization endpoint to create your template in SES:

```bash
curl -X POST http://localhost:3000/api/communication/templates/initialize \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "templates": [
      {
        "templateName": "case-update-template",
        "subject": "Case Update - {{caseNumber}}",
        "htmlPart": "YOUR_HTML_TEMPLATE_HERE",
        "textPart": "YOUR_TEXT_VERSION_HERE"
      }
    ]
  }'
```

### Step 4: Test the Template

Send a test email using the communication system:

```javascript
// Example usage in your application
const emailJob = {
  tenantId: "your-tenant-id",
  userId: "user-id",
  channels: ["email"],
  recipients: {
    email: ["<EMAIL>"]
  },
  variables: {
    type: "case-update",
    tenantName: "Smith & Associates Law Firm",
    recipientName: "John Doe",
    caseNumber: "CASE-2024-001",
    status: "In Progress",
    caseSummary: "Discovery phase completed, preparing for depositions",
    nextSteps: "Schedule depositions with key witnesses",
    handlerName: "Jane Smith",
    urgency: "normal",
    caseUrl: "https://portal.lawfirm.com/cases/CASE-2024-001"
  }
};
```

## Environment Variables Required

Make sure these are set in your environment:

```env
# AWS SES Configuration (Primary)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_SES_REGION=us-east-1
FROM_EMAIL=<EMAIL>

# Optional: SendGrid (Fallback)
SENDGRID_API_KEY=your_sendgrid_key

# Optional: Mailgun (Final Fallback)
MAILGUN_API_KEY=your_mailgun_key
MAILGUN_DOMAIN=your_domain
```

## Security & Permissions

The template endpoints require appropriate roles:
- `system_admin`: Full access to all operations
- `tenant_admin`: Create/update/delete templates for their tenant
- `case_manager`, `lawyer`: Read-only access to templates

## Next Steps

1. **Share your HTML template** - Provide the HTML version of your case-update template
2. **Test the initialization** - Use the `/initialize` endpoint to create the template
3. **Verify functionality** - Send test emails to ensure everything works
4. **Scale to other templates** - Add more templates (welcome, billing, etc.) as needed

The system is now ready for SES-first email delivery with your custom templates!