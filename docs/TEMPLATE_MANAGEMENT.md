# Template Management System

## Overview

The Template Management System provides comprehensive functionality for creating, validating, and managing document templates in the legal practice management system. It integrates tightly with the Token Management System to ensure templates can be populated with dynamic data during document generation.

## Table of Contents

- [Architecture](#architecture)
- [Template Types](#template-types)
- [Template Validation](#template-validation)
- [API Endpoints](#api-endpoints)
- [Template Creation](#template-creation)
- [Token Integration](#token-integration)
- [Validation Rules](#validation-rules)
- [Best Practices](#best-practices)
- [Examples](#examples)

## Architecture

### Core Components

```
┌─────────────────────────┐
│ Document Template       │ ← API Layer
│ Controller              │
├─────────────────────────┤
│ Document Template       │ ← Business Logic
│ Service                 │
├─────────────────────────┤
│ Template Validation     │ ← Validation Engine
│ Service                 │
├─────────────────────────┤
│ Document Template       │ ← Data Model
│ Entity                  │
└─────────────────────────┘
```

### Integration Points

- **Token Management**: Validates tokens used in templates
- **Case Management**: Ensures template compatibility with case types
- **Document Generation**: Provides templates for document creation
- **S3 Storage**: Stores template files securely
- **Audit System**: Tracks template changes and usage

## Template Types

### Template Categories

| Category | Description | Use Case |
|----------|-------------|----------|
| `CONTRACT_REQUEST` | Contract and agreement templates | Purchase contracts, sale agreements |
| `EXCHANGE_CONFIRMATION` | Exchange confirmation documents | Exchange of contracts notifications |
| `COMPLETION_CONFIRMATION` | Completion notices | Completion confirmations |
| `SOLICITOR_CORRESPONDENCE` | Inter-solicitor communications | Vendor solicitor letters |
| `CLIENT_COMMUNICATION` | Client-facing documents | Status updates, requests |
| `COURT_DOCUMENTS` | Legal court filings | Court applications, responses |
| `COMPLIANCE_DOCUMENTS` | Regulatory compliance | Money laundering checks |

### Template Status

- `DRAFT` - Template under development
- `ACTIVE` - Ready for use in document generation
- `INACTIVE` - Temporarily disabled
- `ARCHIVED` - Archived but preserved for audit

### Generation Triggers

Templates can be triggered:
- `MANUAL` - User-initiated generation
- `CASE_STATUS_CHANGE` - Automatic on case status change
- `DATE_BASED` - Scheduled generation
- `EVENT_BASED` - Triggered by specific events

## Template Validation

### Validation Pipeline

```
Template Upload
       ↓
Parse Template File
       ↓
Extract Tokens
       ↓
Validate Token Syntax
       ↓
Check Token Availability
       ↓
Validate Business Rules
       ↓
Store Template
```

### Validation Types

#### 1. **Syntax Validation**
- Ensures proper template file format (DOCX)
- Validates token syntax `{tokenName}`
- Checks for malformed tokens

#### 2. **Token Validation**
- Verifies all tokens exist (system or custom)
- Checks token compatibility with template category
- Validates required vs optional tokens

#### 3. **Business Rule Validation**
- Ensures required tokens for specific categories
- Validates case type compatibility
- Checks template-specific requirements

#### 4. **Data Completeness**
- Verifies templates have necessary metadata
- Ensures proper categorization
- Validates trigger configurations

## API Endpoints

### Template CRUD Operations

```http
# Create a new template
POST /api/document-engine/templates
Content-Type: multipart/form-data

{
  "name": "Contract Request Template",
  "description": "Standard contract request for property purchases",
  "category": "CONTRACT_REQUEST",
  "allowedCaseTypes": ["PURCHASE"],
  "file": <template.docx>
}

# Get all templates
GET /api/document-engine/templates?category=CONTRACT_REQUEST&status=ACTIVE

# Get template by ID
GET /api/document-engine/templates/{id}

# Update template
PUT /api/document-engine/templates/{id}

# Delete template
DELETE /api/document-engine/templates/{id}
```

### Template Validation

```http
# Validate template file
POST /api/document-engine/templates/validate
Content-Type: multipart/form-data

{
  "file": <template.docx>,
  "category": "CONTRACT_REQUEST",
  "allowedCaseTypes": ["PURCHASE"]
}

# Validate template for specific case
POST /api/document-engine/templates/{id}/validate-for-case
{
  "caseId": "uuid",
  "userId": "uuid"
}

# Test template tokens
POST /api/document-engine/templates/{id}/test-tokens
{
  "context": {
    "case": { "caseNumber": "CAS-2025-001" },
    "client": { "name": "John Smith" }
  }
}
```

### Template Discovery

```http
# Get templates by category
GET /api/document-engine/templates/by-category/{category}

# Search templates
GET /api/document-engine/templates/search?q=contract&category=CONTRACT_REQUEST

# Get template usage statistics
GET /api/document-engine/templates/{id}/usage-stats

# Get compatible templates for case
GET /api/document-engine/templates/compatible/{caseId}
```

## Template Creation

### Basic Template Creation

```typescript
interface CreateTemplateRequest {
  name: string;                           // Template display name
  description?: string;                   // Template description
  category: DocumentTemplateCategory;     // Template category
  allowedCaseTypes?: CaseType[];         // Compatible case types
  requiredTokens?: string[];             // Tokens that must resolve
  optionalTokens?: string[];             // Tokens that may be missing
  generationTriggers?: DocumentGenerationTrigger[]; // Auto-generation rules
  isActive?: boolean;                    // Active status
  metadata?: Record<string, any>;        // Additional metadata
}
```

### Example Template Creation

```typescript
const templateRequest = {
  name: "Property Purchase Contract Request",
  description: "Standard template for requesting purchase contracts from vendor solicitors",
  category: "CONTRACT_REQUEST",
  allowedCaseTypes: ["PURCHASE"],
  requiredTokens: [
    "caseNumber",
    "clientName", 
    "propertyAddress",
    "purchasePrice",
    "vendorSolicitorName"
  ],
  optionalTokens: [
    "completionDate",
    "specialConditions"
  ],
  generationTriggers: ["MANUAL"],
  metadata: {
    version: "1.0",
    author: "Legal Team",
    lastModified: new Date()
  }
};
```

### Template File Requirements

#### DOCX Format
- Use Microsoft Word DOCX format
- Ensure compatibility with docxtemplater library
- Test template with sample data before upload

#### Token Syntax
```
Correct:   {clientName}
Correct:   {propertyAddress}
Correct:   {currentDate}

Incorrect: {{clientName}}     // Double braces
Incorrect: {client.name}      // Dots in token names  
Incorrect: { clientName }     // Spaces in braces
```

#### Layout Considerations
- Use proper paragraph formatting
- Ensure tokens are in appropriate text contexts
- Test table and list formatting with dynamic content
- Consider page breaks and document structure

## Token Integration

### Required Tokens by Category

#### CONTRACT_REQUEST Templates
```typescript
requiredTokens: [
  "caseNumber",           // Case identification
  "propertyAddress",      // Property being purchased
  "purchasePrice",        // Agreed purchase price
  "clientName",           // Purchaser name
  "vendorSolicitorName",  // Vendor's solicitor
  "vendorSolicitorFirm"   // Vendor solicitor firm
]
```

#### EXCHANGE_CONFIRMATION Templates
```typescript
requiredTokens: [
  "caseNumber",           // Case identification
  "propertyAddress",      // Property address
  "exchangeDate",         // Exchange date
  "completionDate",       // Completion date
  "clientName",           // Client name
  "purchasePrice"         // Purchase price
]
```

#### CLIENT_COMMUNICATION Templates
```typescript
requiredTokens: [
  "clientName",           // Client name
  "caseNumber",           // Case reference
  "currentDate",          // Letter date
  "currentUser.name"      // Sender name
]
```

### Token Compatibility Validation

Templates are validated against:

1. **System Token Availability**: Ensures system tokens are correctly referenced
2. **Custom Token Existence**: Verifies custom tokens exist and are active
3. **Category Compatibility**: Checks tokens are appropriate for template category
4. **Case Type Compatibility**: Ensures tokens work with specified case types

### Missing Token Handling

```typescript
interface ValidationResult {
  isValid: boolean;
  foundTokens: string[];        // All tokens found in template
  validTokens: string[];        // Tokens that can be resolved
  invalidTokens: string[];      // Unknown or inactive tokens
  missingTokens: string[];      // Required but not found
  warnings: string[];           // Non-critical issues
  errors: string[];             // Critical validation failures
}
```

## Validation Rules

### Category-Specific Rules

#### CONTRACT_REQUEST Validation
```typescript
// Required case data
- property.fullAddress must be present
- property.purchasePrice or property.agreedPrice required
- client information must be complete
- vendor solicitor details recommended

// Template requirements
- Must include property address token
- Must include purchase price token
- Should include completion timeline
```

#### EXCHANGE_CONFIRMATION Validation
```typescript
// Required case data  
- exchangeDate must be set
- completionDate must be set
- completionDate > exchangeDate
- property and client data complete

// Template requirements
- Must include both exchange and completion dates
- Should include insurance requirements
- Must include case reference
```

#### SOLICITOR_CORRESPONDENCE Validation
```typescript
// Required case data
- vendor solicitor details must be complete
- vendor solicitor contact information preferred

// Template requirements
- Should include vendor solicitor name and firm
- Must include case reference
- Should include sender details
```

### Business Logic Validation

#### Date Validation
```typescript
// Exchange/completion date logic
if (exchangeDate >= completionDate) {
  errors.push("Completion date must be after exchange date");
}

// Future date validation
if (exchangeDate < currentDate) {
  warnings.push("Exchange date is in the past");
}
```

#### Financial Validation
```typescript
// Purchase price validation
if (!purchasePrice && templateCategory === "CONTRACT_REQUEST") {
  errors.push("Purchase price required for contract requests");
}

if (purchasePrice < 10000) {
  warnings.push("Purchase price seems unusually low");
}
```

#### Case Type Validation
```typescript
// Template-case compatibility
if (templateCategory === "CONTRACT_REQUEST" && caseType !== "PURCHASE") {
  errors.push("Contract request templates only compatible with purchase cases");
}
```

## Best Practices

### Template Design

#### ✅ Good Practices
- **Clear Structure**: Use headings, paragraphs, and proper formatting
- **Consistent Styling**: Apply consistent fonts, colors, and spacing
- **Token Placement**: Place tokens in logical, readable contexts
- **Conditional Sections**: Design for optional content gracefully
- **Professional Layout**: Follow legal document conventions

#### ❌ Avoid
- **Complex Nested Tables**: Can break with dynamic content
- **Hard-coded Data**: Use tokens instead of fixed text
- **Poor Formatting**: Inconsistent styles and layouts
- **Missing Context**: Tokens without clear meaning
- **Overly Complex Logic**: Keep templates simple and readable

### Performance Optimization

1. **Template Size**: Keep templates reasonably sized (< 5MB)
2. **Token Count**: Limit excessive token usage (< 100 per template)
3. **Image Optimization**: Compress images and graphics
4. **Font Usage**: Stick to standard fonts
5. **Test Regularly**: Validate templates with real data

### Version Control

1. **Template Versioning**: Maintain version history
2. **Change Documentation**: Document template modifications
3. **Backup Strategy**: Regular template backups
4. **Testing Protocol**: Test all changes before deployment
5. **Rollback Plan**: Ability to revert problematic templates

### Security Considerations

1. **Content Review**: Review all template content for sensitive data
2. **Access Control**: Restrict template editing to authorized users
3. **Audit Trail**: Log all template changes and usage
4. **Data Sanitization**: Ensure tokens don't expose sensitive information
5. **Virus Scanning**: Scan uploaded template files

## Examples

### Example 1: Contract Request Template

```typescript
// Template creation
const contractTemplate = {
  name: "Standard Contract Request",
  description: "Template for requesting purchase contracts",
  category: "CONTRACT_REQUEST",
  allowedCaseTypes: ["PURCHASE"],
  requiredTokens: [
    "caseNumber",
    "propertyAddress", 
    "purchasePrice",
    "clientName",
    "vendorSolicitorName",
    "vendorSolicitorFirm"
  ],
  optionalTokens: [
    "completionDate",
    "specialConditions",
    "mortgageProvider"
  ]
};

// Template content (DOCX):
/*
Contract Request

Case Reference: {caseNumber}
Date: {currentDate}

Dear {vendorSolicitorName},

We act for {clientName} in connection with the proposed purchase of {propertyAddress} 
for the sum of {purchasePrice}.

We should be grateful if you would let us have the contract documents at your 
earliest convenience.

{completionDate ? `The proposed completion date is {completionDate}.` : ''}

Yours faithfully,
{currentUser.name}
*/
```

### Example 2: Exchange Confirmation Template

```typescript
// Template with validation
const exchangeTemplate = {
  name: "Exchange Confirmation Notice",
  description: "Confirms exchange of contracts", 
  category: "EXCHANGE_CONFIRMATION",
  allowedCaseTypes: ["PURCHASE", "SALE"],
  requiredTokens: [
    "caseNumber",
    "clientName",
    "propertyAddress",
    "exchangeDate", 
    "completionDate",
    "purchasePrice"
  ],
  generationTriggers: ["CASE_STATUS_CHANGE"]
};

// Validation will ensure:
// - exchangeDate is set
// - completionDate > exchangeDate  
// - All required case data present
```

### Example 3: Client Communication Template

```typescript
// Multi-purpose client template
const clientTemplate = {
  name: "General Client Update",
  description: "Template for client status updates",
  category: "CLIENT_COMMUNICATION", 
  allowedCaseTypes: ["PURCHASE", "SALE", "REMORTGAGE"],
  requiredTokens: [
    "clientName",
    "caseNumber",
    "currentDate"
  ],
  optionalTokens: [
    "nextSteps",
    "documentsRequired",
    "completionDate"
  ],
  metadata: {
    flexible: true,
    multiPurpose: true
  }
};
```

## Error Handling

### Common Validation Errors

```typescript
// Token not found
{
  "error": "Unknown token: {invalidToken}",
  "solution": "Create the token or use an existing token name",
  "availableTokens": ["clientName", "caseNumber", "propertyAddress"]
}

// Template file format error  
{
  "error": "Invalid template format. Expected DOCX file",
  "solution": "Upload a valid Microsoft Word DOCX file"
}

// Category mismatch
{
  "error": "Template category CONTRACT_REQUEST incompatible with case type SALE",
  "solution": "Choose appropriate template category or modify allowed case types"
}

// Missing required tokens
{
  "error": "Template missing required tokens for category CONTRACT_REQUEST",
  "missingTokens": ["purchasePrice", "vendorSolicitorName"],
  "solution": "Add required tokens to template or mark as optional"
}
```

### Resolution Strategies

1. **Token Issues**: Use token discovery endpoints to find correct token names
2. **Format Issues**: Ensure proper DOCX format and token syntax
3. **Compatibility Issues**: Review category and case type requirements
4. **Validation Failures**: Check business rule requirements for template category

## Migration Guide

### Upgrading Templates

When updating the template system:

1. **Backup Existing Templates**: Export all current templates
2. **Validate Against New Rules**: Check templates against updated validation
3. **Update Token References**: Modify any changed token names
4. **Test Generation**: Verify document generation works correctly
5. **Gradual Rollout**: Deploy templates incrementally

### Legacy Template Support

- **Format Conversion**: Convert older template formats to DOCX
- **Token Migration**: Map legacy placeholders to new token system  
- **Validation Updates**: Apply new validation rules gradually
- **Documentation**: Update template documentation and training

## Troubleshooting

### Common Issues

| Issue | Symptom | Solution |
|-------|----------|----------|
| Template won't validate | Validation errors on upload | Check token syntax and availability |
| Document generation fails | Tokens not resolving | Verify case data completeness |
| Formatting issues | Generated documents look wrong | Review template layout and styling |
| Performance problems | Slow generation | Optimize template size and complexity |
| Token errors | Unknown token warnings | Update tokens or template references |

### Debug Tools

1. **Template Validation Endpoint**: Test templates before deployment
2. **Token Test Endpoint**: Verify token resolution with sample data
3. **Generation Logs**: Review detailed generation error logs
4. **Usage Analytics**: Monitor template usage and performance
5. **Validation Reports**: Detailed validation result analysis