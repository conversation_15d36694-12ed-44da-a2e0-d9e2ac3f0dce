{"info": {"name": "Case Management API", "description": "A comprehensive collection of endpoints for the Case Management system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12345678"}, "item": [{"name": "0. Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"{{username}}\",\n    \"password\": \"{{password}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{auth_url}}/auth/login", "host": ["{{auth_url}}"], "path": ["auth", "login"]}, "description": "Authenticates a user and returns an access token"}, "response": []}], "description": "Authentication endpoints"}, {"name": "1. Case Management", "item": [{"name": "Create Case", "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Contract Dispute - ABC Corp\",\n    \"description\": \"Breach of contract dispute with ABC Corporation\",\n    \"priority\": \"HIGH\",\n    \"type\": \"LITIGATION\",\n    \"status\": \"DRAFT\",\n    \"clientId\": \"{{client_id}}\",\n    \"deadline\": \"2023-12-31T23:59:59Z\",\n    \"initialNote\": {\n        \"content\": \"Initial consultation completed on 2023-10-15\",\n        \"isPrivate\": false,\n        \"isPinned\": true\n    },\n    \"initialAttachment\": {\n        \"filename\": \"contract.pdf\",\n        \"url\": \"https://example.com/files/contract.pdf\",\n        \"fileSize\": 1024000,\n        \"mimeType\": \"application/pdf\",\n        \"description\": \"Original contract document\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/cases", "host": ["{{base_url}}"], "path": ["cases"]}, "description": "Creates a new case with optional client, notes, and attachments"}, "response": []}, {"name": "Create Case with New Client", "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Property Dispute - New Client\",\n    \"description\": \"Property boundary dispute\",\n    \"priority\": \"MEDIUM\",\n    \"type\": \"REAL_ESTATE\",\n    \"status\": \"DRAFT\",\n    \"client\": {\n        \"name\": \"<PERSON>\",\n        \"email\": \"<EMAIL>\",\n        \"phone\": \"+1234567890\",\n        \"address\": \"123 Main St, Anytown, USA\"\n    },\n    \"deadline\": \"2023-12-15T23:59:59Z\",\n    \"notes\": [\n        {\n            \"content\": \"Initial consultation scheduled\",\n            \"isPrivate\": false\n        },\n        {\n            \"content\": \"Client provided property documents\",\n            \"isPrivate\": false\n        }\n    ],\n    \"attachments\": [\n        {\n            \"filename\": \"property_deed.pdf\",\n            \"url\": \"https://example.com/files/property_deed.pdf\",\n            \"fileSize\": 2048000,\n            \"mimeType\": \"application/pdf\",\n            \"description\": \"Property deed\"\n        },\n        {\n            \"filename\": \"survey_map.jpg\",\n            \"url\": \"https://example.com/files/survey_map.jpg\",\n            \"fileSize\": 1536000,\n            \"mimeType\": \"image/jpeg\",\n            \"description\": \"Property survey map\"\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/cases", "host": ["{{base_url}}"], "path": ["cases"]}, "description": "Creates a new case with a new client, multiple notes, and multiple attachments"}, "response": []}, {"name": "Get Case by ID", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}"]}, "description": "Retrieves a case by its ID"}, "response": []}, {"name": "Get Case by Case Number", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/number/CASE-2023-00001", "host": ["{{base_url}}"], "path": ["cases", "number", "CASE-2023-00001"]}, "description": "Retrieves a case by its case number"}, "response": []}, {"name": "Get Case Details", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/details", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "details"]}, "description": "Retrieves comprehensive details for a case including client, assignments, notes, attachments, and audit trail"}, "response": []}, {"name": "Update Case", "request": {"method": "PATCH", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Case Title\",\n    \"description\": \"Updated case description\",\n    \"priority\": \"HIGH\",\n    \"deadline\": \"2024-01-15T23:59:59Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/cases/{{case_id}}", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}"]}, "description": "Updates a case's details"}, "response": []}, {"name": "Change Case Status", "request": {"method": "PATCH", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/status/SUBMITTED", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "status", "SUBMITTED"]}, "description": "Changes the status of a case (DRAFT, SUBMITTED, APPROVED, DECLINED, IN_PROGRESS, ON_HOLD, CLOSED, ARCHIVED)"}, "response": []}, {"name": "Close Case", "request": {"method": "PATCH", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"closureReason\": \"Case settled out of court\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/cases/{{case_id}}/close", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "close"]}, "description": "Closes a case with validation and required closure reason"}, "response": []}, {"name": "Get All Cases", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases?page=1&limit=10&sortBy=createdAt&order=DESC", "host": ["{{base_url}}"], "path": ["cases"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "sortBy", "value": "createdAt"}, {"key": "order", "value": "DESC"}]}, "description": "Retrieves all cases with pagination, sorting, and optional filtering"}, "response": []}], "description": "Core case management endpoints"}, {"name": "2. Search & Filtering", "item": [{"name": "Quick Search Cases", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/search/quick?term=contract&limit=5", "host": ["{{base_url}}"], "path": ["cases", "search", "quick"], "query": [{"key": "term", "value": "contract"}, {"key": "limit", "value": "5"}]}, "description": "Fast search for cases by title, case number, or client name (<1ms response time)"}, "response": []}, {"name": "Global Search", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/search/global?term=contract&limit=10", "host": ["{{base_url}}"], "path": ["cases", "search", "global"], "query": [{"key": "term", "value": "contract"}, {"key": "limit", "value": "10"}]}, "description": "Global search across cases, clients, and documents"}, "response": []}, {"name": "Search Case by ID", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/search/id/CASE-2023-00001", "host": ["{{base_url}}"], "path": ["cases", "search", "id", "CASE-2023-00001"]}, "description": "Search for a case by its unique ID/case number"}, "response": []}, {"name": "Quick Search Clients", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/clients/search/quick?term=john", "host": ["{{base_url}}"], "path": ["clients", "search", "quick"], "query": [{"key": "term", "value": "john"}]}, "description": "Fast search for clients by name or email"}, "response": []}, {"name": "Search Clients by Name", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/clients/search/smith", "host": ["{{base_url}}"], "path": ["clients", "search", "smith"]}, "description": "Search for clients by name"}, "response": []}, {"name": "Filter Cases by Type", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/filter/type/LITIGATION?page=1&limit=10", "host": ["{{base_url}}"], "path": ["cases", "filter", "type", "LITIGATION"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Filter cases by their type (<PERSON><PERSON><PERSON><PERSON><PERSON>, CORPORAT<PERSON>, REAL_ESTATE, INTELLECTUAL_PROPERTY, FAMILY, CRIMINAL, OTHER)"}, "response": []}, {"name": "Filter Cases by Status", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/filter/status/DRAFT?page=1&limit=10", "host": ["{{base_url}}"], "path": ["cases", "filter", "status", "DRAFT"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Filter cases by their status (DRAFT, SU<PERSON>ITTED, APPROVED, DECLINED, IN_PROGRESS, ON_HOLD, CLOSED, ARCHIVED)"}, "response": []}, {"name": "Filter Cases by Date Range", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/filter/date-range?startDate=2023-01-01T00:00:00Z&endDate=2023-12-31T23:59:59Z&page=1&limit=10", "host": ["{{base_url}}"], "path": ["cases", "filter", "date-range"], "query": [{"key": "startDate", "value": "2023-01-01T00:00:00Z"}, {"key": "endDate", "value": "2023-12-31T23:59:59Z"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Filter cases by their creation date range"}, "response": []}, {"name": "Filter Cases by Priority", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/filter/priority/HIGH?page=1&limit=10", "host": ["{{base_url}}"], "path": ["cases", "filter", "priority", "HIGH"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Filter cases by their priority (LOW, MEDIUM, HIGH, URGENT)"}, "response": []}], "description": "Search and filtering endpoints"}, {"name": "3. Client Management", "item": [{"name": "Create Client", "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+1987654321\",\n    \"address\": \"456 Oak St, Anytown, USA\",\n    \"additionalInfo\": {\n        \"referredBy\": \"<PERSON>\",\n        \"preferredContactMethod\": \"email\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/clients", "host": ["{{base_url}}"], "path": ["clients"]}, "description": "Creates a new client"}, "response": []}, {"name": "Get Client by ID", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/clients/{{client_id}}", "host": ["{{base_url}}"], "path": ["clients", "{{client_id}}"]}, "description": "Retrieves a client by ID"}, "response": []}, {"name": "Update Client", "request": {"method": "PATCH", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"phone\": \"+1987654322\",\n    \"address\": \"789 Pine St, Anytown, USA\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/clients/{{client_id}}", "host": ["{{base_url}}"], "path": ["clients", "{{client_id}}"]}, "description": "Updates a client's information"}, "response": []}, {"name": "Get All Clients", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/clients?page=1&limit=10&search=smith", "host": ["{{base_url}}"], "path": ["clients"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": "smith"}]}, "description": "Retrieves all clients with pagination and optional search"}, "response": []}], "description": "Client management endpoints"}, {"name": "4. Case Assignments", "item": [{"name": "Assign Case to User", "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"userId\": \"{{user_id}}\",\n    \"userName\": \"<PERSON>\",\n    \"notes\": \"Primary case handler\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/cases/{{case_id}}/assignments", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "assignments"]}, "description": "Assigns a case to a user (requires Super Admin role)"}, "response": []}, {"name": "Unassign User from Case", "request": {"method": "DELETE", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/assignments/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "assignments", "users", "{{user_id}}"]}, "description": "Unassigns a user from a case (requires Super Admin role)"}, "response": []}, {"name": "Get Case Assignments", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/assignments", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "assignments"]}, "description": "Gets all assignments for a case"}, "response": []}, {"name": "Reassign Case", "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"userId\": \"{{new_user_id}}\",\n    \"userName\": \"<PERSON>\",\n    \"notes\": \"Taking over from previous handler\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/cases/{{case_id}}/assignments/reassign/{{old_user_id}}", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "assignments", "reassign", "{{old_user_id}}"]}, "description": "Reassigns a case from one user to another (requires Super Admin role)"}, "response": []}], "description": "Case assignment endpoints"}, {"name": "5. Case Notes", "item": [{"name": "Create Note", "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"content\": \"Client meeting scheduled for next week\",\n    \"isPinned\": false,\n    \"isPrivate\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/cases/{{case_id}}/notes", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "notes"]}, "description": "Creates a new note for a case"}, "response": []}, {"name": "Get All Notes", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/notes?includePrivate=false", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "notes"], "query": [{"key": "includePrivate", "value": "false"}]}, "description": "Gets all notes for a case"}, "response": []}, {"name": "Get Note by ID", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/notes/{{note_id}}", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "notes", "{{note_id}}"]}, "description": "Gets a specific note by ID"}, "response": []}, {"name": "Toggle Pin Status", "request": {"method": "PATCH", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"isPinned\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/cases/{{case_id}}/notes/{{note_id}}/pin", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "notes", "{{note_id}}", "pin"]}, "description": "Toggles the pin status of a note"}, "response": []}, {"name": "Get Pinned Notes", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/notes/pinned", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "notes", "pinned"]}, "description": "Gets all pinned notes for a case"}, "response": []}, {"name": "Get Recent Notes", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/notes/recent", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "notes", "recent"]}, "description": "Gets the 3 most recent notes for a case"}, "response": []}], "description": "Case notes endpoints"}, {"name": "6. <PERSON>", "item": [{"name": "Create Attachment", "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"filename\": \"evidence.pdf\",\n    \"url\": \"https://example.com/files/evidence.pdf\",\n    \"fileSize\": 3072000,\n    \"mimeType\": \"application/pdf\",\n    \"description\": \"Evidence document for the case\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/cases/{{case_id}}/attachments", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "attachments"]}, "description": "Creates a new attachment for a case"}, "response": []}, {"name": "Get All Attachments", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/attachments", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "attachments"]}, "description": "Gets all attachments for a case"}, "response": []}, {"name": "Get Attachment by ID", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/attachments/{{attachment_id}}", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "attachments", "{{attachment_id}}"]}, "description": "Gets a specific attachment by ID"}, "response": []}, {"name": "Delete Attachment", "request": {"method": "DELETE", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/attachments/{{attachment_id}}", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "attachments", "{{attachment_id}}"]}, "description": "Deletes an attachment"}, "response": []}, {"name": "Search Attachments", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/{{case_id}}/attachments/search?filename=evidence", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "attachments", "search"], "query": [{"key": "filename", "value": "evidence"}]}, "description": "Searches for attachments by filename"}, "response": []}], "description": "Case attachments endpoints"}, {"name": "7. Notifications & Deadlines", "item": [{"name": "Get Upcoming Deadlines", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/notifications/upcoming-deadlines?hours=24", "host": ["{{base_url}}"], "path": ["cases", "notifications", "upcoming-deadlines"], "query": [{"key": "hours", "value": "24"}]}, "description": "Gets cases with deadlines in the next 24 hours"}, "response": []}, {"name": "Get Missed Deadlines", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/cases/notifications/missed-deadlines", "host": ["{{base_url}}"], "path": ["cases", "notifications", "missed-deadlines"]}, "description": "Gets cases with missed deadlines"}, "response": []}, {"name": "Set Custom Reminder", "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}", "type": "text"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"reminderTime\": \"2023-12-30T10:00:00Z\",\n    \"reminderType\": \"email\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/cases/{{case_id}}/reminders", "host": ["{{base_url}}"], "path": ["cases", "{{case_id}}", "reminders"]}, "description": "Sets a custom reminder for a case deadline"}, "response": []}], "description": "Notification and deadline endpoints"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:3000/api/case-management", "type": "string"}, {"key": "auth_url", "value": "http://localhost:3000/api/auth", "type": "string"}, {"key": "tenant_id", "value": "your-tenant-id", "type": "string"}, {"key": "auth_token", "value": "your-auth-token", "type": "string"}, {"key": "username", "value": "your-username", "type": "string"}, {"key": "password", "value": "your-password", "type": "string"}, {"key": "case_id", "value": "your-case-id", "type": "string"}, {"key": "client_id", "value": "your-client-id", "type": "string"}, {"key": "user_id", "value": "your-user-id", "type": "string"}, {"key": "note_id", "value": "your-note-id", "type": "string"}, {"key": "attachment_id", "value": "your-attachment-id", "type": "string"}, {"key": "old_user_id", "value": "old-user-id", "type": "string"}, {"key": "new_user_id", "value": "new-user-id", "type": "string"}]}