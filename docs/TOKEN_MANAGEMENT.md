# Token Management System

## Overview

The Token Management System provides a sophisticated framework for creating, managing, and resolving dynamic tokens used in document generation. It supports both system-generated tokens (dates, user context) and custom user-defined tokens that map to entity data.

## Table of Contents

- [Architecture](#architecture)
- [Token Types](#token-types)
- [API Endpoints](#api-endpoints)
- [Token Creation](#token-creation)
- [Token Resolution](#token-resolution)
- [Validation & Transformations](#validation--transformations)
- [Best Practices](#best-practices)
- [Examples](#examples)

## Architecture

### Core Components

```
┌─────────────────────┐
│ Token Controller    │ ← API Layer
├─────────────────────┤
│ Token Service       │ ← Business Logic
├─────────────────────┤
│ Token Repository    │ ← Data Access
├─────────────────────┤
│ CustomToken Entity  │ ← Data Model
└─────────────────────┘
```

### Key Features

- **Dual Token System**: System tokens (auto-generated) + Custom tokens (user-defined)
- **Field Uniqueness**: Each entity field can only be tokenized once
- **Rich Validation**: Comprehensive validation rules per data type
- **Advanced Transformations**: Formatting, prefixes, case changes, etc.
- **Usage Analytics**: Track token usage patterns and statistics
- **Template Compatibility**: Integration with template and case type systems

## Token Types

### System Tokens

Auto-generated tokens providing system context:

| Token Name | Description | Example Value |
|------------|-------------|---------------|
| `currentDate` | Current date (GB format) | `15/01/2025` |
| `currentDateTime` | Current date & time | `15/01/2025 14:30` |
| `currentYear` | Current year | `2025` |
| `currentUser.name` | Current user name | `Sarah Johnson` |
| `currentUser.email` | Current user email | `<EMAIL>` |
| `generationTimestamp` | Document generation time | `15/01/2025 14:30` |
| `documentId` | Unique document ID | `DOC-1706185800123` |

### Custom Tokens

User-defined tokens that map to entity data:

**Supported Data Types:**
- `STRING` - Text values
- `NUMBER` - Numeric values  
- `DATE` - Date values
- `BOOLEAN` - True/False values
- `CURRENCY` - Monetary values (auto-formatted)
- `EMAIL` - Email addresses
- `PHONE` - Phone numbers
- `ADDRESS` - Address fields

**Available Entities:**
- `case` - Case information (caseNumber, type, dates, etc.)
- `client` - Client details (name, email, phone, address)
- `property` - Property information (address, price, type, etc.)
- `lawyer` - Lawyer details (name, email, department)

## API Endpoints

### Core Token Operations

```http
# Create a custom token
POST /api/document-engine/tokens
Content-Type: application/json

{
  "tokenName": "clientName",
  "description": "Client full name",
  "dataType": "STRING",
  "entityName": "client",
  "fieldPath": "name",
  "category": "client"
}

# Get all tokens (with filtering)
GET /api/document-engine/tokens?search=client&category=client&page=1&limit=10

# Get system tokens only
GET /api/document-engine/tokens/system

# Get custom tokens only  
GET /api/document-engine/tokens/custom

# Update a token
PUT /api/document-engine/tokens/{id}

# Delete a token
DELETE /api/document-engine/tokens/{id}
```

### Discovery & Validation

```http
# Get available entities and fields for tokenization
GET /api/document-engine/tokens/tokenizable-fields

# Validate token name availability
POST /api/document-engine/tokens/validate-name
{
  "tokenName": "myCustomToken"
}

# Get tokens by entity
GET /api/document-engine/tokens/by-entity/client

# Test token resolution
POST /api/document-engine/tokens/test-resolution
{
  "tokenName": "clientName",
  "context": {
    "client": { "name": "John Smith" }
  }
}
```

### Analytics & Management

```http
# Get token usage statistics
GET /api/document-engine/tokens/usage-stats

# Search tokens
GET /api/document-engine/tokens/search?query=client

# Bulk create tokens
POST /api/document-engine/tokens/bulk
{
  "tokens": [
    { "tokenName": "token1", "entityName": "client", "fieldPath": "name", "dataType": "STRING" },
    { "tokenName": "token2", "entityName": "case", "fieldPath": "caseNumber", "dataType": "STRING" }
  ]
}
```

## Token Creation

### Basic Token Creation

```typescript
const tokenRequest = {
  tokenName: "clientEmail",           // Unique name for the token
  description: "Client email address", // Human-readable description
  dataType: "EMAIL",                 // Data type for validation/formatting
  entityName: "client",              // Source entity
  fieldPath: "email",                // Field within entity
  category: "contact"                // Optional categorization
};
```

### Advanced Token Configuration

```typescript
const advancedToken = {
  tokenName: "propertyPrice",
  description: "Formatted property purchase price",
  dataType: "CURRENCY",
  entityName: "property",
  fieldPath: "purchasePrice",
  category: "financial",
  
  // Transformation rules
  transformationConfig: {
    currencyCode: "GBP",
    locale: "en-GB",
    showSymbol: true,
    decimalPlaces: 2
  },
  
  // Validation rules
  validationConfig: {
    required: true,
    minValue: 10000,
    maxValue: 10000000
  },
  
  // Template compatibility
  compatibleTemplateTypes: ["CONTRACT_REQUEST", "PURCHASE_COMMUNICATION"],
  compatibleCaseTypes: ["PURCHASE", "SALE"]
};
```

### Field Uniqueness Rule

**Important**: Each entity field can only be tokenized once. For example:

```typescript
// ✅ First token creation - succeeds
{
  "tokenName": "clientName",
  "entityName": "client", 
  "fieldPath": "name"
}

// ❌ Second token creation - fails
{
  "tokenName": "clientFullName",  // Different name
  "entityName": "client",
  "fieldPath": "name"             // Same field - ERROR!
}
```

## Token Resolution

### Resolution Context

Tokens are resolved using a rich context object:

```typescript
interface TokenResolutionContext {
  case?: Case;                    // Case data
  client?: Client;                // Client data  
  property?: Property;            // Property data
  user?: {                        // Current user context
    name?: string;
    email?: string;
    id?: string;
  };
  customEntities?: Record<string, any>; // Additional data
  timestamp?: Date;               // Generation timestamp
}
```

### Resolution Process

1. **Token Discovery**: Extract tokens from template (`{tokenName}`)
2. **Token Classification**: Identify as system or custom token
3. **Context Resolution**: Resolve value from appropriate data source
4. **Transformation**: Apply formatting rules
5. **Validation**: Ensure value meets validation criteria
6. **Usage Tracking**: Increment usage statistics

### Example Resolution

```typescript
// Token: {clientName}
// Context: { client: { name: "John Smith" } }
// Result: "John Smith"

// Token: {currentDate}  
// Context: { timestamp: new Date() }
// Result: "15/01/2025"

// Token: {propertyPrice} with transformation
// Context: { property: { purchasePrice: 500000 } }
// Result: "£500,000.00"
```

## Validation & Transformations

### Validation Rules by Data Type

#### STRING
```typescript
validationConfig: {
  required: true,
  minLength: 2,
  maxLength: 100,
  pattern: "^[A-Za-z\\s]+$",
  allowedValues: ["PURCHASE", "SALE", "REMORTGAGE"],
  forbiddenValues: ["TEST", "INVALID"]
}
```

#### NUMBER
```typescript
validationConfig: {
  required: true,
  minValue: 1,
  maxValue: 1000000,
  integerOnly: true,
  positiveOnly: true,
  excludeZero: true
}
```

#### CURRENCY  
```typescript
validationConfig: {
  required: true,
  minValue: 10000,
  maxValue: 10000000,
  multipleOf: 1000
}
```

#### EMAIL
```typescript
validationConfig: {
  required: true,
  allowedDomains: ["gmail.com", "company.com"],
  blockedDomains: ["tempmail.com"],
  strictValidation: true
}
```

### Transformation Options

#### String Transformations
```typescript
transformationConfig: {
  toUpperCase: true,
  trim: true,
  prefix: "REF: ",
  suffix: " (FINAL)",
  replacePatterns: [
    { find: "\\s+", replace: "_", flags: "g" }
  ]
}
```

#### Currency Transformations
```typescript
transformationConfig: {
  currencyCode: "GBP",
  locale: "en-GB", 
  showSymbol: true,
  showCode: false,
  decimalPlaces: 2,
  thousandsSeparator: ",",
  roundingMode: "ROUND_HALF_UP"
}
```

#### Date Transformations
```typescript
transformationConfig: {
  inputFormat: "YYYY-MM-DD",
  outputFormat: "DD MMMM YYYY",
  locale: "en-GB",
  timezone: "Europe/London",
  addDays: 5
}
```

## Best Practices

### Token Naming

✅ **Good Token Names:**
- `clientName` - Clear and descriptive
- `propertyAddress` - Indicates purpose
- `caseCompletionDate` - Specific and unambiguous

❌ **Avoid:**
- `data` - Too generic
- `client.name` - Dots not allowed in token names
- `x1` - Non-descriptive

### Performance Considerations

1. **Use Appropriate Data Types**: Choose the most specific data type for better validation and formatting
2. **Limit Transformations**: Complex transformations can impact performance
3. **Cache Frequently Used Tokens**: System handles caching automatically
4. **Monitor Usage**: Use analytics to identify unused tokens

### Security Guidelines

1. **Validate Input**: Always validate token data before document generation
2. **Sanitize Output**: Transformations help sanitize data for documents
3. **Access Control**: Tokens inherit entity-level access controls
4. **Audit Trail**: All token operations are logged and tracked

## Examples

### Example 1: Basic Client Information Token

```typescript
// Create client name token
const clientNameToken = {
  tokenName: "clientName",
  description: "Client full name for documents",
  dataType: "STRING",
  entityName: "client",
  fieldPath: "name",
  category: "client",
  validationConfig: {
    required: true,
    minLength: 2,
    maxLength: 100
  }
};

// Usage in template: "Dear {clientName},"
// Result: "Dear John Smith,"
```

### Example 2: Formatted Currency Token

```typescript
// Create property price token with formatting
const priceToken = {
  tokenName: "propertyPrice", 
  description: "Formatted property purchase price",
  dataType: "CURRENCY",
  entityName: "property",
  fieldPath: "purchasePrice",
  category: "financial",
  transformationConfig: {
    currencyCode: "GBP",
    locale: "en-GB",
    showSymbol: true,
    decimalPlaces: 2
  },
  validationConfig: {
    required: true,
    minValue: 10000,
    maxValue: 5000000
  }
};

// Usage in template: "Purchase price: {propertyPrice}"
// Result: "Purchase price: £450,000.00"
```

### Example 3: Complex Case Information

```typescript
// Create case reference with transformation
const caseRefToken = {
  tokenName: "caseReference",
  description: "Formatted case reference number", 
  dataType: "STRING",
  entityName: "case",
  fieldPath: "caseNumber",
  category: "case",
  transformationConfig: {
    toUpperCase: true,
    prefix: "REF: ",
    suffix: " (CONV)"
  },
  validationConfig: {
    required: true,
    pattern: "^[A-Z0-9\\-]+$"
  }
};

// Usage in template: "Case Reference: {caseReference}"
// Result: "Case Reference: REF: CAS-2025-001 (CONV)"
```

### Example 4: System Token Usage

```typescript
// System tokens are predefined - no creation needed
// Available system tokens:

// Usage in template:
"Generated on {currentDate} by {currentUser.name}"
"Document ID: {documentId}"
"Template Version: {templateVersion}"

// Results:
"Generated on 15/01/2025 by Sarah Johnson"
"Document ID: DOC-1706185800123" 
"Template Version: 1.0"
```

## Error Handling

Common error scenarios and solutions:

```typescript
// Error: Token name already exists
{
  "error": "Token with name 'clientName' already exists",
  "solution": "Choose a different token name or update existing token"
}

// Error: Field already tokenized
{
  "error": "Field 'name' in entity 'client' has already been tokenized as 'clientName'",
  "solution": "Each field can only be tokenized once - use existing token or choose different field"
}

// Error: Invalid entity/field combination
{
  "error": "Field path 'invalidField' is not available for entity 'client'",
  "solution": "Use GET /tokens/tokenizable-fields to see available fields"
}

// Error: Token resolution failed
{
  "error": "Token 'clientName' resolved to null/undefined",
  "solution": "Ensure required data is available in resolution context"
}
```

## Migration Guide

### Upgrading from Legacy Systems

If migrating from a legacy token system:

1. **Audit Existing Tokens**: List all current tokens and their usage
2. **Map to New System**: Create equivalent tokens using new API
3. **Test Resolution**: Verify all tokens resolve correctly with sample data
4. **Update Templates**: Ensure template syntax matches new format
5. **Gradual Migration**: Migrate templates incrementally to minimize disruption

### Version History

- **v1.0**: Initial token management system
- **v1.1**: Added advanced transformations and validation
- **v1.2**: System token integration
- **v1.3**: Usage analytics and bulk operations