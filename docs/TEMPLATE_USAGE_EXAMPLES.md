# SES Template Usage Examples

## Your Beautiful Template is Now Ready! 🎉

I've converted your professional HTML template into a dynamic SES template with **Handlebars variables**. Here's how to use it:

## Quick Setup

### 1. Initialize the Template
```bash
# Start your communication service
yarn start:communication:dev

# Initialize the default case-update template (uses your design)
curl -X POST http://localhost:3001/api/communication/templates/initialize/default \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. Test Template Variables

The template now supports all these dynamic variables:

#### **Core Case Data**
```javascript
{
  // Required
  tenantName: "Smith & Associates Law Firm",
  recipientName: "John <PERSON>e", 
  caseNumber: "LCS-2025-06789",
  status: "In Progress",
  
  // Optional but recommended
  clientName: "Westfield Holdings Ltd.",
  caseType: "Corporate Acquisition",
  handlerName: "<PERSON>",
  handlerTitle: "Senior Partner, Corporate Acquisitions",
  handlerEmail: "s.rich<PERSON><PERSON>@lawfirm.com",
  handlerPhone: "+44 20 7123 4567"
}
```

#### **Rich Content**
```javascript
{
  caseSummary: "We have completed the initial due diligence on the target company's financial records and have identified several areas that require further investigation...",
  
  nextSteps: [
    "Meeting with target company's financial team - scheduled for June 10th, 2025",
    "Completion of regulatory compliance review - expected by June 15th, 2025", 
    "Draft acquisition agreement review - to be sent by June 20th, 2025"
  ],
  
  additionalDetails: "The regulatory compliance review is proceeding as planned, with no major issues identified thus far."
}
```

#### **Visual Indicators**
```javascript
{
  urgency: "high",        // Shows red urgent banner
  urgency: "critical",    // Shows critical banner with siren
  urgency: "normal",      // Default green status indicator
  
  courtDate: "June 15th, 2025",
  courtTime: "10:00 AM",
  
  caseUrl: "https://portal.lawfirm.com/cases/LCS-2025-06789"
}
```

## Complete Example Usage

### Send via Communication Producer
```javascript
import { MessageProducerService } from '@app/common/communication';

const emailJob = {
  tenantId: "firm-123",
  userId: "user-456", 
  channels: ["email"],
  recipients: {
    email: ["<EMAIL>"]
  },
  variables: {
    type: "case-update",
    
    // Your firm details
    tenantName: "Smith & Associates Law Firm",
    tenantLogo: "S&A Legal", // Will show in header
    tenantAddress: "123 Chancery Lane, London, EC4A 1NF",
    supportEmail: "<EMAIL>",
    
    // Client details
    recipientName: "John Smith",
    clientName: "Westfield Holdings Ltd.",
    
    // Case information
    caseNumber: "LCS-2025-06789",
    caseType: "Corporate Acquisition", 
    status: "In Progress",
    urgency: "normal", // or "high" for urgent banner
    
    // Case content
    caseSummary: "We have completed the initial due diligence on the target company's financial records and have identified several areas that require further investigation. Our team has scheduled meetings with the target's financial team for next week to clarify certain discrepancies in their Q1 2025 reports.",
    
    additionalDetails: "The regulatory compliance review is proceeding as planned, with no major issues identified thus far. We anticipate completing this phase by June 15th.",
    
    nextSteps: [
      "Meeting with target company's financial team - scheduled for June 10th, 2025",
      "Completion of regulatory compliance review - expected by June 15th, 2025", 
      "Draft acquisition agreement review - to be sent by June 20th, 2025"
    ],
    
    // Handler information  
    handlerName: "Sarah Richardson",
    handlerTitle: "Senior Partner, Corporate Acquisitions",
    handlerEmail: "<EMAIL>",
    handlerPhone: "+44 20 7123 4567",
    
    // Links and dates
    caseUrl: "https://portal.smithassociates.com/cases/LCS-2025-06789",
    formattedOpeningDate: "15 May 2025",
    
    // Auto-populated
    currentYear: "2025"
  }
};

// Send the email
await messageProducer.enqueueMessage(emailJob);
```

### REST API Usage
```bash
# Send case update email via REST API
curl -X POST http://localhost:3001/api/communication/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "tenantId": "firm-123",
    "userId": "user-456", 
    "channels": ["email"],
    "recipients": {
      "email": ["<EMAIL>"]
    },
    "variables": {
      "type": "case-update",
      "tenantName": "Smith & Associates Law Firm",
      "recipientName": "John Smith",
      "caseNumber": "LCS-2025-06789",
      "status": "In Progress",
      "caseSummary": "Your case summary here...",
      "handlerName": "Sarah Richardson"
    }
  }'
```

## Template Features Implemented

### ✅ **Design Elements**
- **Responsive grid layout** for case details
- **Professional color scheme** (#4a90e2 primary, clean grays)
- **Status indicators** with color-coded dots
- **Mobile-responsive** design
- **Clean typography** with proper hierarchy

### ✅ **Dynamic Content**
- **Conditional urgent banners** for high/critical cases
- **Flexible next steps** (array or string)
- **Optional sections** (only show if data provided)
- **Smart fallbacks** (defaults when optional data missing)
- **Court date highlighting** with warning styling

### ✅ **Variable Substitution** 
- All your static content now uses **Handlebars variables**
- **Logo/Company name** from tenant data
- **Handler information** or fallback to "Legal Team"
- **Client-specific** details throughout
- **Dynamic year** and **formatted dates**

## Ready for Production

The template is now:
- ✅ **Stored in AWS SES** as 'case-update-template'
- ✅ **Integrated** with your communication system
- ✅ **Type-safe** with full variable validation
- ✅ **Tested** with fallback providers
- ✅ **Professional** design matching your requirements

Just initialize it with the endpoint and start sending beautiful case update emails! 

**Next:** Test with your actual case data and customize any styling as needed.