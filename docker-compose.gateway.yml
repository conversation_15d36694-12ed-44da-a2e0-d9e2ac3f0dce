services:
  # 1) Postgres with init‑script to create both the app DB and the keycloak DB
  postgres:
    image: postgres:15-alpine
    container_name: tk-lpm-postgres
    restart: unless-stopped
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      # Additional PostgreSQL configurations
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      # any *.sh in this folder runs once at first init
      - ./initdb:/docker-entrypoint-initdb.d
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Keycloak for authentication and authorization
  keycloak:
    image: quay.io/keycloak/keycloak:22.0.1
    container_name: tk-lpm-keycloak
    restart: unless-stopped
    env_file:
      - .env
    environment:
      KC_DB: postgres
      KC_DB_URL_HOST: postgres
      KC_DB_URL_PORT: 5432
      KC_DB_USERNAME: ${POSTGRES_USER}
      KC_DB_PASSWORD: ${POSTGRES_PASSWORD}
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
      KC_HOSTNAME_PORT: ${KEYCLOAK_HOSTNAME_PORT}
      KC_HTTP_ENABLED: "true"
      KC_PROXY: edge
      KC_HEALTH_ENABLED: "true"
    ports:
      - "${KEYCLOAK_HOSTNAME_PORT}:8080"
    volumes:
      - keycloak_data:/opt/keycloak/data
    depends_on:
      postgres:
        condition: service_healthy
    command:
      - start-dev
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # CORE SERVICE - API GATEWAY (ONLY PUBLIC FACING SERVICE)
  core:
    build:
      context: .
      dockerfile: ./apps/core/Dockerfile
    container_name: tk-lpm-core
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV:            ${NODE_ENV}
      DOCKER_ENV:          "true"  # Enable Docker mode for service discovery
      PORT:                ${CORE_PORT}
      POSTGRES_HOST:       postgres
      KEYCLOAK_SERVER_URL: ${KEYCLOAK_SERVER_URL}
    ports:
      # ONLY Core service is exposed externally
      - "${CORE_PORT}:${CORE_PORT}"
    depends_on:
      - postgres
      - keycloak
      - auth
      - communication
      - document-engine
      - case-management
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${CORE_PORT}/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 15s

  # INTERNAL SERVICES (NO EXTERNAL PORTS EXPOSED)
  communication:
    build:
      context: .
      dockerfile: ./apps/communication/Dockerfile
    container_name: tk-lpm-communication
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV:            ${NODE_ENV}
      PORT:                ${COMMUNICATION_PORT}
      POSTGRES_HOST:       postgres
      KEYCLOAK_SERVER_URL: ${KEYCLOAK_SERVER_URL}
    # NO EXTERNAL PORTS - Only accessible via Docker network
    expose:
      - "${COMMUNICATION_PORT}"
    depends_on:
      - postgres
      - keycloak
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${COMMUNICATION_PORT}/api/communication/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 15s

  document-engine:
    build:
      context: .
      dockerfile: ./apps/document-engine/Dockerfile
    container_name: tk-lpm-document-engine
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV:            ${NODE_ENV}
      PORT:                ${DOCUMENT_ENGINE_PORT}
      POSTGRES_HOST:       postgres
      KEYCLOAK_SERVER_URL: ${KEYCLOAK_SERVER_URL}
    # NO EXTERNAL PORTS - Only accessible via Docker network
    expose:
      - "${DOCUMENT_ENGINE_PORT}"
    depends_on:
      - postgres
      - keycloak
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${DOCUMENT_ENGINE_PORT}/api/document-engine/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 15s

  auth:
    build:
      context: .
      dockerfile: ./apps/auth/Dockerfile
    container_name: tk-lpm-auth
    restart: on-failure:5
    env_file:
      - .env
    environment:
      NODE_ENV:                   ${NODE_ENV}
      PORT:                       ${AUTH_PORT}
      POSTGRES_HOST:              postgres
      POSTGRES_USER:              ${POSTGRES_USER}
      POSTGRES_PASSWORD:          ${POSTGRES_PASSWORD}
      POSTGRES_DB:                ${POSTGRES_DB}
      KEYCLOAK_SERVER_URL:        ${KEYCLOAK_SERVER_URL}
      KEYCLOAK_ADMIN:             ${KEYCLOAK_ADMIN}
      KEYCLOAK_ADMIN_PASSWORD:    ${KEYCLOAK_ADMIN_PASSWORD}
      KEYCLOAK_CONNECTION_TIMEOUT: 60000
      KEYCLOAK_CONNECTION_RETRIES: 10
      KEYCLOAK_RETRY_DELAY:       5000
      LOG_LEVEL:                  debug
    # NO EXTERNAL PORTS - Only accessible via Docker network
    expose:
      - "${AUTH_PORT}"
    volumes:
      - ./dist:/app/dist
      - ./apps/auth/docker-entrypoint.sh:/app/docker-entrypoint.sh
    depends_on:
      - postgres
      - keycloak
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${AUTH_PORT}/api/auth/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  case-management:
    build:
      context: .
      dockerfile: ./apps/case-management/Dockerfile
    container_name: tk-lpm-case-management
    restart: unless-stopped
    env_file:
      - .env
    environment:
      NODE_ENV:            ${NODE_ENV:-development}
      PORT:                ${CASE_MANAGEMENT_PORT}
      POSTGRES_HOST:       postgres
      KEYCLOAK_SERVER_URL: ${KEYCLOAK_SERVER_URL}
    # NO EXTERNAL PORTS - Only accessible via Docker network
    expose:
      - "${CASE_MANAGEMENT_PORT}"
    depends_on:
      - postgres
      - keycloak
    networks:
      - tk-lpm-network
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:${CASE_MANAGEMENT_PORT}/api/case-management/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 15s

volumes:
  postgres_data:
    name: tk-lpm-postgres-data
  keycloak_data:
    name: tk-lpm-keycloak-data

networks:
  tk-lpm-network:
    name: tk-lpm-network
    external: true 