import { DataSource } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Register ts-node so TypeScript migrations work locally and in prod
// Some local runs failed to import .ts migration files when NODE_ENV!=='production'
try {
    // Allow opt-out by setting TS_NODE_REGISTER=false
    if (process.env.TS_NODE_REGISTER !== 'false') {
        require('ts-node').register({
            transpileOnly: true,
            compilerOptions: { module: 'commonjs' }
        });
    }
    // ts-node registered (or intentionally skipped)
} catch {
    // Could not register ts-node - migrations may fail if only .ts files are present
}

// Load environment variables
dotenv.config({
    path: path.resolve(process.cwd(), '.env')
});

// Base configuration shared between public and tenant schemas
export const baseConfig: Partial<PostgresConnectionOptions> = {
    type: 'postgres',
    host: process.env.POSTGRES_HOST,
    port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
    synchronize: false,
    ssl: process.env.POSTGRES_SSL === 'true'
};

// Public schema configuration
export const publicConfig: PostgresConnectionOptions = {
    ...baseConfig,
    type: 'postgres',
    schema: 'public',
    entities: [
        // Support both dev (from root) and production (from dist) paths
        path.join(process.cwd(), 'libs/common/src/typeorm/entities/public/**/*.entity.{js,ts}'),
        path.join(process.cwd(), 'dist/libs/common/src/typeorm/entities/public/**/*.entity.js')
    ],
    migrations: [
        // Keep as .ts - will use ts-node in production
        path.join(process.cwd(), 'libs/common/src/typeorm/migrations/public/**/*.ts')
    ],
    migrationsTableName: 'typeorm_migrations'
};

// Tenant schema configuration (used by the TenantSchemaMigrationService)
export const tenantConfig: PostgresConnectionOptions = {
    ...baseConfig,
    type: 'postgres',
    entities: [
        // Support both dev (from root) and production (from dist) paths
        path.join(process.cwd(), 'libs/common/src/typeorm/entities/tenant/**/*.entity.{js,ts}'),
        path.join(process.cwd(), 'dist/libs/common/src/typeorm/entities/tenant/**/*.entity.js')
    ],
    migrations: [
        // Keep as .ts - will use ts-node in production
        path.join(process.cwd(), 'libs/common/src/typeorm/migrations/tenant/**/*.ts')
    ],
    migrationsTableName: 'tenant_typeorm_migrations'
};

// Create and export the DataSource for public schema
export default new DataSource(publicConfig);
