module.exports = {
    rules: {
      "type-case": [0],  // Turn off case checking
      "type-enum": [0],  // Turn off type enumeration checking
      "subject-case": [2, "always", "sentence-case"],
      "header-max-length": [2, "always", 100]
    },
    parserPreset: {
      parserOpts: {
        headerPattern: /^TKJ-(\d+): (.*)$/,
        headerCorrespondence: ["ticket", "subject"]
      }
    }
  };
