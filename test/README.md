# Testing Strategy Guide

This project uses a standardized approach to testing with a clear directory structure and pattern for both unit and integration tests.

## Directory Structure

```
test/
├── helpers/             # Shared testing utilities
│   ├── integration-db-reset.ts
│   ├── integration-migrations-setup.js
│   ├── integration-jest-setup.js
│   ├── setup-test-db.js
│   ├── test-app-factory.ts
│   ├── test-config.ts
│   ├── testing-helpers.ts
│   ├── jest-setup.ts
│   ├── jest-global-setup.js
│   └── wait-for-schema.js
├── integration/         # Integration tests
│   ├── auth.integration.spec.ts
│   ├── case-management.integration.spec.ts
│   ├── axios-interceptors.integration.spec.ts
│   ├── keycloak-mock.integration.spec.ts
│   ├── role-hierarchy-mock.integration.spec.ts
│   └── simple-db.integration.spec.ts
├── mocks/               # Mock services
│   ├── mock-keycloak-http.service.ts
│   └── mock-role-hierarchy.service.ts
├── templates/           # Test templates
│   ├── module.integration.spec.ts
│   ├── service.unit.spec.ts
│   └── controller.unit.spec.ts
├── unit/                # Unit tests
│   └── ...unit tests
└── README.md            # This file
```

## Test Types

### Unit Tests

Unit tests focus on testing individual components in isolation with all dependencies mocked. They are fast and don't require external services.

- **File Pattern**: `*.spec.ts` (excluding `*.integration.spec.ts`)
- **Run Command**: `yarn test:unit`
- **Directory**: `test/unit/`
- **Create From Template**: 
  - `yarn test:create:unit:service apps/module-name/service-name`
  - `yarn test:create:unit:controller apps/module-name/controller-name`

### Integration Tests

Integration tests verify interactions between components or with external services. They require a test database.

- **File Pattern**: `*.integration.spec.ts`
- **Run Command**: `yarn test:integration`
- **Directory**: `test/integration/`
- **Create From Template**: `yarn test:create:integration apps/module-name/component-name`

## Inter-Service Integration Tests

The project now supports true integration tests that verify the actual interactions between microservices. These tests simulate a real-world scenario where multiple services are running and communicating with each other.

### Key Features of Inter-Service Tests:

1. **Real Service Communication** - Tests actual API calls between services without mocking
2. **Authentication Flow** - Tests complete auth flow from registration to using tokens
3. **Cross-Service Operations** - Tests operations that span multiple services
4. **Database Persistence** - Tests data persistence across service boundaries

### Running Inter-Service Tests:

```bash
# Run all integration tests including inter-service tests
yarn test:integration

# Run only the inter-service integration tests
yarn test:integration:inter-service
```

### How Inter-Service Tests Work:

1. The test starts all required microservices (auth, case-management, core)
2. Each service connects to the same test database
3. The database is reset before tests begin
4. Tests authenticate with the auth service to get a token
5. The token is used to make requests to other services
6. Data created in one service is accessed by other services
7. All services are shut down after tests complete

### Example Inter-Service Test Flow:

1. Register user in auth service
2. Obtain authentication token
3. Create client in core service
4. Create case in case-management service using client ID
5. Add attachments, events, and assignments to the case
6. Verify all data is accessible across service boundaries
7. Verify the auth token works with all services

## Test Commands

```bash
# Run all tests (unit + integration)
yarn test

# Run all tests with Docker containers (recommended for CI/CD)
yarn test:all

# Run unit tests only
yarn test:unit

# Run all integration tests
yarn test:integration

# Run integration tests in sequential order (recommended)
yarn test:integration:sequential

# Run integration tests with Docker setup
yarn test:docker

# Run specific test categories
yarn test:integration:auth             # Run auth module tests
yarn test:integration:case-management  # Run case management tests
yarn test:integration:mocks            # Run mock service tests
yarn test:integration:simple-db        # Run simple DB tests

# Database utilities
yarn test:db:health                    # Check database connection health
yarn test:db:setup                     # Set up test database schema

# Create tests from templates
yarn test:create:integration apps/module-name/component-name
yarn test:create:unit:service apps/module-name/service-name  
yarn test:create:unit:controller apps/module-name/controller-name
```

## CI/CD Testing Workflow

This project includes a GitHub Actions workflow for automated testing on push and pull requests. The workflow:

1. Runs unit tests first (faster and don't require Docker)
2. Sets up Docker containers for integration tests
3. Runs integration tests using the configured containers
4. Uploads test results as artifacts

The workflow configuration is located at `.github/workflows/run-tests.yml`.

For local testing with the same setup that CI/CD uses, run:

```bash
yarn test:all
```

This script will:
1. Run unit tests
2. Start Docker containers for PostgreSQL and Keycloak
3. Wait for the services to be ready
4. Set up the test database
5. Run integration tests
6. Clean up all resources

## Testing Patterns

### Unit Testing Pattern

When writing unit tests, follow this structure:

1. **Mock all dependencies** using Jest mock functions
2. **Test each method individually** in separate describe blocks
3. **Follow the AAA pattern** (Arrange-Act-Assert)
4. **Test edge cases and errors**

Example (see `test/templates/service.unit.spec.ts` for full template):

```typescript
describe('method: findById', () => {
  it('should find and return an item by id', async () => {
    // Arrange
    const id = 1;
    const mockItem = { id, name: 'Test item' };
    mockRepository.findOne.mockResolvedValue(mockItem);
    
    // Act
    const result = await service.findById(id);
    
    // Assert
    expect(mockRepository.findOne).toHaveBeenCalledWith({ where: { id } });
    expect(result).toEqual(mockItem);
  });
});
```

### Integration Testing Pattern

When writing integration tests, follow this structure:

1. **Prepare the database** using resetTestDatabase()
2. **Create a test application** using TestAppFactory or createSimpleTestApp()
3. **Run tests in a logical sequence** (e.g., create, then read, then update)
4. **Clean up resources** after tests complete

Example (see `test/templates/module.integration.spec.ts` for full template):

```typescript
describe('Module Integration', () => {
  let app: INestApplication;
  
  beforeAll(async () => {
    app = await createSimpleTestApp();
  });
  
  beforeEach(async () => {
    await resetTestDatabase();
  });
  
  afterAll(async () => {
    if (app) {
      await app.close();
    }
  });
  
  it('should have a working health check endpoint', async () => {
    const res = await request(app.getHttpServer()).get('/api/health');
    expect(res.status).toBe(200);
    expect(res.body.status).toBe('ok');
  });
  
  // More tests...
});
```

## Troubleshooting

### Schema Not Ready

If you encounter "Schema not ready" errors:

1. Run the database health check: `yarn test:db:health`
2. Make sure environment variables are properly set
3. Try setting up the database manually: `yarn test:db:setup`
4. Run tests in sequential order: `yarn test:integration:sequential`

### Circular Dependencies or Module Loading Issues

If you encounter "metatype is not a constructor" errors:

1. Use the simplified test app: `createSimpleTestApp()`
2. Check for circular dependencies in your imports
3. Use the mock services from `test/mocks/`

## Best Practices

1. **Always create new tests from templates**
   - Use `yarn test:create:integration` for integration tests
   - Use `yarn test:create:unit:service` or `yarn test:create:unit:controller` for unit tests

2. **Run tests in sequential order for integration tests**
   - Use `yarn test:integration:sequential` to ensure proper test execution order

3. **Reset the database between tests**
   - Use `resetTestDatabase()` in beforeEach hooks

4. **Use the TestAppFactory for NestJS app creation**
   - Each module has its own factory method with proper prefixes

5. **Follow naming conventions**
   - Unit tests: `[name].spec.ts`
   - Integration tests: `[name].integration.spec.ts`
   - Place in the correct directory (`test/unit/` or `test/integration/`)

6. **Isolate tests properly**
   - Don't share state between tests
   - Use beforeEach to set up clean state 