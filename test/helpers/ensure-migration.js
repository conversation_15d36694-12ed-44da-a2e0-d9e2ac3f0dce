/**
 * This script ensures that database migrations are run before tests.
 * It should be run as part of the test setup process.
 */
const { Logger } = require('@nestjs/common');
const { runIntegrationTestMigrations } = require('./integration-migrations-setup');

async function ensureMigrations() {
  const logger = new Logger('EnsureMigrations');
  logger.log('Ensuring database migrations are applied before tests...');

  try {
    // Run migrations
    const success = await runIntegrationTestMigrations();
    
    if (success) {
      logger.log('✅ Database migrations verified and applied successfully');
      return true;
    } else {
      logger.error('❌ Failed to apply database migrations');
      throw new Error('Database migration failed');
    }
  } catch (error) {
    logger.error(`❌ Error ensuring migrations: ${error.message}`, error.stack);
    throw error;
  }
}

// Allow script to be run directly
if (require.main === module) {
  ensureMigrations()
    .then(() => {
      process.exit(0);
    })
    .catch(err => {
      console.error('Migration error:', err);
      process.exit(1);
    });
}

module.exports = { ensureMigrations }; 