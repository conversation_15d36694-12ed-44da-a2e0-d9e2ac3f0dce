// Pre-compiled JavaScript version of wait-for-schema.ts
const { Logger } = require('@nestjs/common');
const { DataSource } = require('typeorm');

/**
 * Check PostgreSQL health connection
 * Runs basic diagnostics to see if we can connect to the database and run queries
 */
async function checkDatabaseHealth() {
  const logger = new Logger('DatabaseHealthCheck');
  logger.log('Running database health check...');
  
  // Load the integrations setup module directly
  const { createPublicDataSource } = require('./integration-migrations-setup');

  let dataSource = null;
  
  try {
    // Try to connect to the database
    dataSource = createPublicDataSource();
    if (!dataSource.isInitialized) {
      await dataSource.initialize();
    }
    
    // Run a simple query to verify connection
    const result = await dataSource.query('SELECT NOW() as current_time');
    logger.log(`Database connection successful! Server time: ${result[0].current_time}`);
    
    // Check some environment variables
    logger.log(`Database environment variables:
      POSTGRES_HOST: ${process.env.POSTGRES_HOST || 'not set (default: localhost)'}
      POSTGRES_PORT: ${process.env.POSTGRES_PORT || 'not set (default: 5434)'}
      POSTGRES_DB: ${process.env.POSTGRES_DB || 'not set (default: tk_lpm_test)'}
      POSTGRES_USER: ${process.env.POSTGRES_USER ? '(set)' : 'not set (default: postgres)'}
      INTEGRATION_TEST: ${process.env.INTEGRATION_TEST || 'not set'}
      RUN_MIGRATIONS: ${process.env.RUN_MIGRATIONS || 'not set'}
    `);
    
    return {
      success: true,
      message: 'Database connection is healthy',
      details: result[0]
    };
  } catch (error) {
    logger.error(`Database health check failed: ${error.message}`, error.stack);
    return {
      success: false,
      message: `Database connection failed: ${error.message}`,
      error: error
    };
  } finally {
    // Always close the connection
    if (dataSource && dataSource.isInitialized) {
      try {
        await dataSource.destroy();
      } catch (err) {
        logger.error(`Error closing database connection: ${err.message}`);
      }
    }
  }
}

/**
 * Helper utility to wait for database schema to be ready
 * Used in integration tests to ensure database is initialized before tests run
 */
/**
 * Check if the database schema is ready by attempting a connection
 * and querying for expected tables
 */
async function isSchemaReady() {
    // Get environment variables with defaults that match Docker setup
    const host = process.env.POSTGRES_HOST || 'localhost';
    const port = parseInt(process.env.POSTGRES_PORT || '5434', 10);
    const username = process.env.POSTGRES_USER || 'postgres';
    const password = process.env.POSTGRES_PASSWORD || 'postgres';
    const database = process.env.POSTGRES_DB || 'tk_lpm_test';
    
    let dataSource = null;
    
    try {
        // Create a connection
        dataSource = new DataSource({
            type: 'postgres',
            host,
            port,
            username,
            password,
            database,
            schema: 'public',
            synchronize: false
        });
        
        await dataSource.initialize();
        
        // Check if tenant table exists - this is a key table for our app
        const result = await dataSource.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'tenant'
            );
        `);
        
        const exists = result[0]?.exists === true;
        
        // Close the connection
        await dataSource.destroy();
        
        return exists;
    } catch (error) {
        console.error('Error checking schema readiness:', error.message);
        // Clean up connection if it was created
        if (dataSource && dataSource.isInitialized) {
            await dataSource.destroy();
        }
        return false;
    }
}

/**
 * Wait for the database schema to be ready
 * @param {number} maxRetries Maximum number of retry attempts
 * @param {number} interval Time between retries in milliseconds
 * @returns {Promise<boolean>} True if schema is ready, false otherwise
 */
async function waitForSchemaReady(maxRetries = 5, interval = 2000) {
    for (let i = 0; i < maxRetries; i++) {
        console.log(`Checking database schema readiness (attempt ${i + 1}/${maxRetries})...`);
        
        const ready = await isSchemaReady();
        if (ready) {
            return true;
        }
        
        if (i < maxRetries - 1) {
            console.log(`Schema not ready, waiting ${interval / 1000}s before retry...`);
            await new Promise(resolve => setTimeout(resolve, interval));
        }
    }
    
    return false;
}

/**
 * Simple health check function to verify the database is accessible
 * Used for running outside of tests
 */
async function healthCheck() {
    const ready = await isSchemaReady();
    console.log(ready ? '✅ Database schema is ready' : '❌ Database schema is NOT ready');
    return ready;
}

// Allow this utility to be run directly for database health checks
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'health-check') {
    healthCheck()
      .then(success => process.exit(success ? 0 : 1))
      .catch(err => {
        console.error('Health check error:', err);
        process.exit(1);
      });
  } else {
    waitForSchemaReady()
      .then(isReady => {
        if (!isReady) {
          process.exit(1);
        }
      })
      .catch(err => {
        console.error('Error checking schema readiness:', err);
        process.exit(1);
      });
  }
}

module.exports = {
  waitForSchemaReady,
  checkDatabaseHealth,
  isSchemaReady,
  healthCheck
}; 