/**
 * Global setup for Jest tests.
 * This file runs once before all tests.
 */

const { DataSource } = require('typeorm');
require('dotenv').config();

// Whether running in integration test mode
const isIntegrationTest = !!process.env.INTEGRATION_TEST;

// Whether to run migrations before tests
const shouldRunMigrations = process.env.RUN_MIGRATIONS === 'true';

/**
 * Global setup function for Jest
 */
module.exports = async () => {
    // Only perform database setup in integration test mode
    if (!isIntegrationTest) {
        console.log('Running in UNIT TEST mode - skipping database setup.');
    return;
  }
  
  console.log('Running in INTEGRATION mode - initializing test database...');
  
    // Database connection details
    const host = process.env.TEST_DB_HOST || process.env.POSTGRES_HOST || 'localhost';
    const port = parseInt(process.env.TEST_DB_PORT || process.env.POSTGRES_PORT || '5434', 10);
    const username = process.env.TEST_DB_USERNAME || process.env.POSTGRES_USER || 'postgres';
    const password = process.env.TEST_DB_PASSWORD || process.env.POSTGRES_PASSWORD || 'postgres';
    const database = process.env.TEST_DB_DATABASE || process.env.POSTGRES_DB || 'tk_lpm_test';
  
    console.log(`Connecting to PostgreSQL at ${host}:${port} as ${username}...`);
  
  try {
        // Create database connection
        const dataSource = new DataSource({
      type: 'postgres',
            host,
            port,
            username,
            password,
            database,
      synchronize: false, 
            logging: false
    });
    
        // Initialize the connection
        await dataSource.initialize();
    console.log('Database connection established successfully.');
    
        // Create basic schema structure
        await dataSource.query(`
      CREATE SCHEMA IF NOT EXISTS public;
      
      -- Create tenant table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.tenant (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        realm VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
      
      -- Create user table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.user (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        keycloak_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- Create typeorm_migrations table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.typeorm_migrations (
        id SERIAL PRIMARY KEY,
        timestamp BIGINT NOT NULL,
        name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- Create system_roles table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.system_roles (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        permissions JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- Insert default roles if needed
      INSERT INTO public.system_roles (name, description, permissions)
      VALUES 
        ('admin', 'System administrator with all permissions', '{"*": true}'),
        ('user', 'Regular user with limited permissions', '{"read": true}')
      ON CONFLICT (name) DO NOTHING;

      -- Create tenant_migrations table if it doesn't exist
      CREATE TABLE IF NOT EXISTS public.tenant_typeorm_migrations (
        id SERIAL PRIMARY KEY,
        timestamp BIGINT NOT NULL,
        name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    console.log('Basic schema tables created.');

        // Close the connection
        await dataSource.destroy();
    console.log('Test database schema initialized.');

        // Run migrations if required
        if (shouldRunMigrations) {
        console.log('Running migrations for integration tests...');
            const { runIntegrationTestMigrations } = require('./integration-migrations-setup');
            const success = await runIntegrationTestMigrations();
            
            if (success) {
          console.log('Migrations completed successfully.');
        } else {
                console.error('Failed to run migrations.');
      }
    }
  } catch (error) {
        console.error('Error setting up test database:', error);
        process.exit(1);
  }
}; 