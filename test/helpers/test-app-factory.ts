import { INestApplication, Controller, Get, Module, Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Type } from '@nestjs/common';
import { testConfig, getTestDatabaseConfig, getTestKeycloakConfig } from './test-config';
import { ConfigService } from '@nestjs/config';
import { MockKeycloakHttpService } from '../mocks/mock-keycloak-http.service';
import { MockRoleHierarchyService } from '../mocks/mock-role-hierarchy.service';
import { MockKeycloakService } from '../mocks/mock-keycloak.service';

// Add a global pending function if it doesn't exist
// This is needed for case-management.integration.spec.ts which uses pending()
if (typeof global.pending === 'undefined') {
    global.pending = () => {
        // No-op implementation for Jest
    };
}

/**
 * Simple health controller that always returns OK status
 */
@Controller('health')
export class HealthController {
    @Get()
    check() {
        return { status: 'ok' };
    }
}

/**
 * Simple module for testing with minimal dependencies
 */
@Module({
    controllers: [HealthController],
    providers: [
        {
            provide: 'KeycloakHttpService',
            useValue: new MockKeycloakHttpService()
        },
        {
            provide: 'RoleHierarchyService',
            useValue: new MockRoleHierarchyService()
        },
        {
            provide: 'KeycloakService',
            useValue: new MockKeycloakService()
        }
    ]
})
export class TestModule {}

/**
 * Factory to create NestJS applications for testing with proper
 * configuration including global prefixes matching production.
 */
export class TestAppFactory {
    /**
     * Check if we're running integration tests
     */
    private static isIntegrationTest(): boolean {
        return process.env.INTEGRATION_TEST === 'true' || testConfig.testMode;
    }

    /**
     * Create an application for testing
     * This is a generic method that handles common setup for all app types
     */
    static async create<T>(module: Type<T>, apiPrefix: string): Promise<INestApplication> {
        Logger.log(
            `Running in ${this.isIntegrationTest() ? 'INTEGRATION TEST' : 'UNIT TEST'} mode`
        );

        const moduleBuilder = Test.createTestingModule({
            imports: [module]
        });

        // Always provide mocks for services with complex initialization
        moduleBuilder
            .overrideProvider('KeycloakHttpService')
            .useValue(new MockKeycloakHttpService());
        moduleBuilder
            .overrideProvider('RoleHierarchyService')
            .useValue(new MockRoleHierarchyService());
        moduleBuilder.overrideProvider('KeycloakService').useValue(new MockKeycloakService());

        if (!this.isIntegrationTest()) {
            // For unit tests only, use additional mocks
        } else {
            // For integration tests, override the config service with test settings
            moduleBuilder.overrideProvider(ConfigService).useFactory({
                factory: () => {
                    return {
                        get: (key: string) => {
                            if (key === 'database') {
                                return getTestDatabaseConfig();
                            }
                            if (key === 'auth') {
                                return getTestKeycloakConfig();
                            }
                            if (key === 'auth.serverUrl') {
                                return getTestKeycloakConfig().serverUrl;
                            }
                            if (key === 'auth.admin') {
                                return getTestKeycloakConfig().admin;
                            }
                            if (key === 'auth.adminPassword') {
                                return getTestKeycloakConfig().adminPassword;
                            }
                            if (key === 'auth.clientId') {
                                return getTestKeycloakConfig().clientId;
                            }
                            // Add roles configuration for RoleHierarchyService
                            if (key === 'roles.hierarchy') {
                                return {
                                    super_admin: ['admin'],
                                    admin: ['manager', 'lawyer', 'finance', 'partner'],
                                    manager: ['user'],
                                    lawyer: ['user'],
                                    finance: ['user'],
                                    partner: ['user']
                                };
                            }

                            return undefined;
                        }
                    };
                }
            });
        }

        const moduleRef: TestingModule = await moduleBuilder.compile();
        const app = moduleRef.createNestApplication();
        app.setGlobalPrefix(apiPrefix);
        await app.init();
        return app;
    }

    /**
     * Creates a test application for the auth module
     */
    static async createAuthApp(module: Type<any>): Promise<INestApplication> {
        return this.create(module, 'api/auth');
    }

    /**
     * Creates a test application for the case-management module
     */
    static async createCaseManagementApp(module: Type<any>): Promise<INestApplication> {
        return this.create(module, 'api/case-management');
    }

    /**
     * Creates a test application for the document-engine module
     */
    static async createDocumentEngineApp(module: Type<any>): Promise<INestApplication> {
        return this.create(module, 'api/document-engine');
    }

    /**
     * Creates a test application for the core module
     */
    static async createCoreApp(module: Type<any>): Promise<INestApplication> {
        return this.create(module, 'api');
    }

    /**
     * Creates a test application for the communication module
     */
    static async createCommunicationApp(module: Type<any>): Promise<INestApplication> {
        return this.create(module, 'api/communication');
    }
}

/**
 * Create a simplified test app for integration testing without dependencies
 * This is used for basic health checks and when we don't need the full app functionality
 */
export async function createSimpleTestApp(): Promise<INestApplication> {
    Logger.log('Creating simplified test app for basic health checks');

    // Create a minimal module with just the health controller
    const moduleRef = await Test.createTestingModule({
        imports: [TestModule]
    }).compile();

    const app = moduleRef.createNestApplication();
    app.setGlobalPrefix('api');
    await app.init();

    return app;
}
