import { createMockDataSource } from '@app/common/testing';
import { testPublicConfig, testTenantConfig } from './test-config';

/**
 * This file runs before each test file is executed.
 * Environment flag INTEGRATION_TEST determines whether tests are integration or unit tests.
 */

// For unit tests only: Mock axios to prevent real HTTP calls
const isIntegrationTest = !!process.env.INTEGRATION_TEST;

if (!isIntegrationTest) {
    // Unit tests: Mock external dependencies
    jest.mock('axios', () => {
        const originalModule = jest.requireActual('axios');

        // Create a mock axios implementation that properly handles interceptors
        const mockAxios = {
            ...originalModule,
            create: jest.fn(() => {
                const instance = originalModule.create();

                // Make sure interceptors are properly initialized with working methods
                instance.interceptors = {
                    request: {
                        use: jest.fn(() => {
                            // Add to a mock handlers array if needed in the future
                            return 0; // Interceptor ID
                        }),
                        eject: jest.fn(),
                        clear: jest.fn()
                    },
                    response: {
                        use: jest.fn(() => {
                            // Add to a mock handlers array if needed in the future
                            return 0; // Interceptor ID
                        }),
                        eject: jest.fn(),
                        clear: jest.fn()
                    }
                };

                return instance;
            }),
            defaults: originalModule.defaults,
            interceptors: {
                request: {
                    use: jest.fn(),
                    eject: jest.fn(),
                    clear: jest.fn()
                },
                response: {
                    use: jest.fn(),
                    eject: jest.fn(),
                    clear: jest.fn()
                }
            },
            isAxiosError: originalModule.isAxiosError,
            isCancel: originalModule.isCancel,
            CancelToken: originalModule.CancelToken,
            VERSION: originalModule.VERSION
        };

        return mockAxios;
    });

    // Mock publicDataSource - using the correct path that matches moduleNameMapper
    jest.mock('@app/common/config/typeorm/typeorm.config.public', () => ({
        publicDataSource: createMockDataSource()
    }));

    // Mock typeorm.config
    jest.mock(
        'typeorm.config',
        () => ({
            publicConfig: {
                type: 'postgres',
                host: 'localhost',
                port: 5433,
                username: 'postgres',
                password: 'postgres',
                database: 'test_db',
                synchronize: false,
                ssl: false,
                schema: 'public',
                entities: [],
                migrations: [],
                migrationsTableName: 'typeorm_migrations'
            },
            tenantConfig: {
                type: 'postgres',
                host: 'localhost',
                port: 5433,
                username: 'postgres',
                password: 'postgres',
                database: 'test_db',
                synchronize: false,
                ssl: false,
                entities: [],
                migrations: [],
                migrationsTableName: 'tenant_typeorm_migrations'
            }
        }),
        { virtual: true }
    );
} else {
    // Integration tests: Configure real database connection
    // Override typeorm.config to use the test database configuration
    jest.mock(
        'typeorm.config',
        () => ({
            publicConfig: testPublicConfig,
            tenantConfig: testTenantConfig,
            baseConfig: {
                ...testPublicConfig,
                schema: undefined
            }
        }),
        { virtual: true }
    );

    // Set environment variables for NestJS app modules that use them directly
    process.env.POSTGRES_HOST = process.env.TEST_DB_HOST || 'localhost';
    process.env.POSTGRES_PORT = process.env.TEST_DB_PORT || '5434';
    process.env.POSTGRES_USER = process.env.TEST_DB_USERNAME || 'postgres';
    process.env.POSTGRES_PASSWORD = process.env.TEST_DB_PASSWORD || 'postgres';
    process.env.POSTGRES_DB = process.env.TEST_DB_DATABASE || 'tk_lpm_test';
}

// Configure Jest to clear all mocks automatically between tests
beforeEach(() => {
    jest.clearAllMocks();
});

// Clean up any remaining mocks after all tests
afterAll(() => {
    jest.restoreAllMocks();
});
