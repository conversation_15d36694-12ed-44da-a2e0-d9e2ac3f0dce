/**
 * Simple script to prepare the database for tests
 * Can be run directly from Node.js
 */
const { Logger } = require('@nestjs/common');
const { runIntegrationTestMigrations } = require('./integration-migrations-setup');

async function setupTestDb() {
  const logger = new Logger('SetupTestDb');
  logger.log('Setting up test database...');

  try {
    const success = await runIntegrationTestMigrations();
    
    if (success) {
      logger.log('✅ Test database setup completed successfully');
      return true;
    } else {
      logger.error('❌ Failed to set up test database');
      return false;
    }
  } catch (error) {
    logger.error(`❌ Error setting up test database: ${error.message}`, error.stack);
    return false;
  }
}

// Allow script to be run directly
if (require.main === module) {
  setupTestDb()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(err => {
      console.error('Unhandled error:', err);
      process.exit(1);
    });
}

module.exports = { setupTestDb }; 