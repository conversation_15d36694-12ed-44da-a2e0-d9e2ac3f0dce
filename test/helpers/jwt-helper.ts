import * as jwt from 'jsonwebtoken';

/**
 * Helper utility for working with JWT tokens in integration tests
 */
export class JwtHelper {
    /**
     * Decode a JWT token without verifying the signature
     *
     * @param token The JWT token string
     * @returns The decoded token payload
     */
    static decodeToken(token: string): any {
        try {
            // Decode without verification - this is safe for testing purposes
            const decoded = jwt.decode(token);
            return decoded;
        } catch (error) {
            throw new Error(`Failed to decode JWT token: ${error.message}`);
        }
    }

    /**
     * Extract user ID from a JWT token
     *
     * @param token The JWT token string
     * @returns The user ID from the subject claim
     */
    static getUserIdFromToken(token: string): string {
        const decoded = this.decodeToken(token);
        return decoded?.sub || null;
    }

    /**
     * Extract user roles from a JWT token
     *
     * @param token The JWT token string
     * @returns Array of user roles
     */
    static getRolesFromToken(token: string): string[] {
        const decoded = this.decodeToken(token);
        return decoded?.roles || [];
    }
}
