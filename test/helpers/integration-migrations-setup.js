// This is a pre-compiled JS version of integration-migrations-setup.ts
// Used for integration tests to setup the database
const { Logger } = require('@nestjs/common');
const path = require('path');
const { DataSource } = require('typeorm');
const fs = require('fs');

/**
 * Create a DataSource for the public schema
 * This is a simplified version that doesn't depend on NestJS modules
 */
const createPublicDataSource = () => {
  const logger = new Logger('DatabaseSetup');
  
  // Get environment variables with defaults that match Docker setup
  const host = process.env.TEST_DB_HOST || 'localhost';
  const port = parseInt(process.env.TEST_DB_PORT || '5434', 10);
  const username = process.env.TEST_DB_USER || 'postgres';
  const password = process.env.TEST_DB_PASSWORD || 'postgres';
  const database = process.env.TEST_DB_NAME || 'tk_lpm_test';
  // Log connection details (without sensitive info)
  logger.log(`Connecting to PostgreSQL at ${host}:${port}/${database}`);
  
  return new DataSource({
    type: 'postgres',
    host,
    port,
    username,
    password,
    database,
    schema: 'public',
    synchronize: false,
    logging: ['query', 'error']
  });
};

/**
 * Check if public schema is ready
 * Used before running tests to ensure database is set up
 */
const isPublicSchemaReady = async () => {
  let dataSource = null;
  try {
    // Create a connection
    dataSource = createPublicDataSource();
    await dataSource.initialize();
  
    // Check for basic tables - system_users is created by our public schema migration
    const systemUsersTable = await dataSource.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public'
        AND table_name = 'system_users'
      );
    `);
    
    const isReady = systemUsersTable && systemUsersTable[0] && systemUsersTable[0].exists;
    
    // Clean up
    await dataSource.destroy();
    
    return isReady;
  } catch (error) {
    // Handle connection errors
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    return false;
  }
};

/**
 * Run public schema migrations for integration tests
 * This manually creates the tables we need for testing
 */
const runIntegrationTestMigrations = async () => {
  const logger = new Logger('IntegrationTestMigrations');
  logger.log('Setting up integration test database with basic migrations...');
  
  let dataSource = null;
  try {
    // Create a connection
    dataSource = createPublicDataSource();
    await dataSource.initialize();
    
    // Run migrations
    logger.log('Running public schema migrations...');
    
    // First, drop any existing tables to avoid conflicts
    await dataSource.query(`
      DROP TABLE IF EXISTS "public"."user_tenants" CASCADE;
      DROP TABLE IF EXISTS "public"."user_system_roles" CASCADE; 
      DROP TABLE IF EXISTS "public"."tenants" CASCADE;
      DROP TABLE IF EXISTS "public"."system_users" CASCADE;
      DROP TABLE IF EXISTS "public"."system_roles" CASCADE;
    `);
    
    // Create the tables directly based on our schema migration, without foreign key constraints for testing
    await dataSource.query(`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    
      CREATE TABLE IF NOT EXISTS "public"."typeorm_migrations" (
        id SERIAL PRIMARY KEY,
        timestamp BIGINT NOT NULL,
        name VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
      
      -- Create system_users table
      CREATE TABLE IF NOT EXISTS "public"."system_users" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "email" character varying NOT NULL,
        "password" character varying NOT NULL,
        "first_name" character varying,
        "last_name" character varying,
        "is_active" boolean NOT NULL DEFAULT true,
        "email_verified" boolean NOT NULL DEFAULT false,
        "remember_me" boolean NOT NULL DEFAULT false,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_system_users" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_system_users_email" UNIQUE ("email")
      );
    
      -- Create system_roles table
      CREATE TABLE IF NOT EXISTS "public"."system_roles" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "name" character varying NOT NULL,
        "description" character varying,
        "permissions" jsonb,
        "enabled" boolean NOT NULL DEFAULT true,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_system_roles" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_system_roles_name" UNIQUE ("name")
      );
    
      -- Create tenants table
      CREATE TABLE IF NOT EXISTS "public"."tenants" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "realm" character varying NOT NULL,
        "display_name" character varying NOT NULL,
        "admin_username" character varying,
        "admin_email" character varying,
        "admin_first_name" character varying,
        "admin_last_name" character varying,
        "registration_allowed" boolean NOT NULL DEFAULT false,
        "verify_email" boolean NOT NULL DEFAULT true,
        "remember_me" boolean NOT NULL DEFAULT true,
        "dedicated_realm_admin" boolean NOT NULL DEFAULT true,
        "client_id" character varying,
        "client_secret" character varying,
        "enabled" boolean NOT NULL DEFAULT true,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_tenants" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_tenants_realm" UNIQUE ("realm")
      );
      
      -- Create user_system_roles junction table (simplified without FKs for testing)
      CREATE TABLE IF NOT EXISTS "public"."user_system_roles" (
        "role_id" uuid NOT NULL,
        "user_id" uuid NOT NULL,
        CONSTRAINT "PK_user_system_roles" PRIMARY KEY ("role_id", "user_id")
      );
      
      -- Create user_tenants junction table (simplified without FKs for testing)
      CREATE TABLE IF NOT EXISTS "public"."user_tenants" (
        "user_id" uuid NOT NULL,
        "tenant_id" uuid NOT NULL,
        CONSTRAINT "PK_user_tenants" PRIMARY KEY ("user_id", "tenant_id")
      );
      
      -- Insert default system roles if they don't exist
      INSERT INTO "public"."system_roles" (name, description, permissions) VALUES
      ('SUPER_ADMIN', 'Super administrator role with full access', '{"*": ["*"]}'),
      ('TENANT_ADMIN', 'Tenant administrator role with full access', '{"tenant": ["*"]}'),
      ('USER', 'Default user role with limited access', '{"user": ["read"]}')
      ON CONFLICT (name) DO NOTHING;
    `);
    
    logger.log('Migrations completed successfully');
    
    // Clean up
    await dataSource.destroy();
    
    return true;
  } catch (error) {
    logger.error(`Failed to set up integration test database: ${error.message}`, error.stack);
    
    // Clean up
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    
    return false;
  }
};

module.exports = {
  createPublicDataSource,
  isPublicSchemaReady,
  runIntegrationTestMigrations
}; 