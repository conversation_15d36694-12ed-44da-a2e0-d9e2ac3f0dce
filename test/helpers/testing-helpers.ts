import { Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';

const logger = new Logger('TestingHelpers');

/**
 * Helper function to check if a database is reachable.
 * Returns true if connected successfully, false otherwise.
 */
export async function isDatabaseReachable(
    host: string = 'localhost',
    port: number = 5433,
    user: string = 'postgres',
    password: string = 'postgres',
    database: string = 'test_db'
): Promise<boolean> {
    const dataSource = new DataSource({
        type: 'postgres',
        host,
        port,
        username: user,
        password,
        database,
        entities: []
    });

    try {
        await dataSource.initialize();
        logger.log('Successfully connected to database');

        // Run simple query to verify connection fully works
        const result = await dataSource.query('SELECT 1 as number');
        if (result[0].number !== 1) {
            throw new Error('Database query returned unexpected result');
        }

        await dataSource.destroy();
        return true;
    } catch (error) {
        logger.error(`Failed to connect to database: ${error.message}`);
        if (dataSource.isInitialized) {
            await dataSource.destroy();
        }
        return false;
    }
}

/**
 * Helper function to wait for a condition to be true
 * @param conditionFn Function that returns a promise resolving to a boolean
 * @param timeout Timeout in milliseconds
 * @param interval Interval in milliseconds between checks
 */
export async function waitForCondition(
    conditionFn: () => Promise<boolean>,
    timeout: number = 30000,
    interval: number = 1000
): Promise<boolean> {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
        if (await conditionFn()) {
            return true;
        }
        await new Promise((resolve) => setTimeout(resolve, interval));
    }

    return false;
}

/**
 * Helper function to wait for the test database to be ready
 */
export async function waitForTestDatabase(timeout: number = 30000): Promise<boolean> {
    logger.log('Waiting for test database to be ready...');
    const result = await waitForCondition(() => isDatabaseReachable(), timeout);

    if (result) {
        logger.log('Test database is ready');
    } else {
        logger.error(`Test database not ready after ${timeout}ms timeout`);
    }

    return result;
}

/**
 * Common helper functions for testing
 */

/**
 * Gets an available unique ID, useful for avoiding test conflicts
 */
export function getUniqueId(): string {
    return Date.now().toString() + Math.floor(Math.random() * 1000).toString();
}

/**
 * Sleep for a specified number of milliseconds
 * @param ms Milliseconds to sleep
 */
export function sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Generate a random email address for testing
 */
export function getRandomEmail(): string {
    return `test-${getUniqueId()}@example.com`;
}

/**
 * Generate a random username for testing
 */
export function getRandomUsername(): string {
    return `testuser-${getUniqueId()}`;
}
