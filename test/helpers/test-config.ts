/**
 * Test configuration with environment-based settings
 */
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import * as path from 'path';

// Base configuration for tests
export const testBaseConfig: PostgresConnectionOptions = {
    type: 'postgres',
    host: process.env.TEST_DB_HOST || 'localhost',
    port: process.env.TEST_DB_PORT ? parseInt(process.env.TEST_DB_PORT, 10) : 5434,
    username: process.env.TEST_DB_USERNAME || 'postgres',
    password: process.env.TEST_DB_PASSWORD || 'postgres',
    database: process.env.TEST_DB_DATABASE || 'tk_lpm_test',
    synchronize: false,
    logging: false,
    ssl: false
};

// Public schema configuration for tests
export const testPublicConfig: PostgresConnectionOptions = {
    ...testBaseConfig,
    schema: 'public',
    entities: [
        path.join(__dirname, '../libs/common/src/typeorm/entities/public/**/*.entity.{js,ts}')
    ],
    migrations: [path.join(__dirname, '../libs/common/src/typeorm/migrations/public/**/*.ts')],
    migrationsTableName: 'typeorm_migrations'
};

// Tenant schema configuration for tests
export const testTenantConfig: PostgresConnectionOptions = {
    ...testBaseConfig,
    entities: [
        path.join(__dirname, '../libs/common/src/typeorm/entities/tenant/**/*.entity.{js,ts}')
    ],
    migrations: [path.join(__dirname, '../libs/common/src/typeorm/migrations/tenant/**/*.ts')],
    migrationsTableName: 'tenant_typeorm_migrations'
};

// Configuration for tests including keycloak settings
export const testConfig = {
    database: testBaseConfig,
    keycloak: {
        host: process.env.TEST_KEYCLOAK_HOST || 'localhost',
        port: process.env.TEST_KEYCLOAK_PORT ? parseInt(process.env.TEST_KEYCLOAK_PORT, 10) : 8090,
        adminUser: process.env.TEST_KEYCLOAK_USER || 'admin',
        adminPassword: process.env.TEST_KEYCLOAK_PASSWORD || 'admin',
        clientId: 'admin-cli',
        baseUrl: () =>
            `http://${process.env.TEST_KEYCLOAK_HOST || 'localhost'}:${process.env.TEST_KEYCLOAK_PORT || 8090}`
    },
    testMode: true
};

/**
 * Get the database connection options for testing
 */
export function getTestDatabaseConfig() {
    return {
        type: 'postgres',
        host: testConfig.database.host,
        port: testConfig.database.port,
        username: testConfig.database.username,
        password: testConfig.database.password,
        database: testConfig.database.database,
        synchronize: true, // Only true for tests
        dropSchema: true, // Only true for tests
        entities: [
            'libs/common/src/typeorm/entities/**/*.entity.ts',
            'libs/common/src/typeorm/entities/**/*.entity.js'
        ],
        migrations: [
            'libs/common/src/typeorm/migrations/**/*.migration.ts',
            'libs/common/src/typeorm/migrations/**/*.migration.js'
        ],
        logging: false
    };
}

/**
 * Get the Keycloak configuration for testing
 */
export function getTestKeycloakConfig() {
    return {
        serverUrl: testConfig.keycloak.baseUrl(),
        admin: testConfig.keycloak.adminUser,
        adminPassword: testConfig.keycloak.adminPassword,
        clientId: testConfig.keycloak.clientId
    };
}
