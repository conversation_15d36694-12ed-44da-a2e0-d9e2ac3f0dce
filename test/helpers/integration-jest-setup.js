/**
 * This setup file is used specifically for integration tests
 * It ensures the database schema is ready before any tests run
 * and sets up proper mocks for external dependencies
 */

const { waitForSchemaReady } = require('./wait-for-schema');

// Global before all hook
beforeAll(async () => {
  if (process.env.INTEGRATION_TEST !== 'true') {
    return; // Skip setup if not in integration test mode
  }

  console.log('Integration test setup: Checking if database schema is ready...');
  
  try {
    const isReady = await waitForSchemaReady(5, 2000);
    if (!isReady) {
      console.warn('⚠️ Database schema may not be ready. Tests might fail.');
    } else {
      console.log('✅ Database schema is ready for testing.');
    }
  } catch (error) {
    console.error('Failed to check schema readiness:', error);
  }
}, 30000); // 30 second timeout

// Ensure axios mock is properly setup
beforeEach(() => {
  // This test setup file has been moved to the helpers folder
  // and ensures axios mock is properly setup
  jest.mock('axios', () => {
    // Create a mock axios instance with interceptors
    const mockAxios = {
      defaults: { 
        baseURL: '', 
        headers: { common: {} } 
      },
      interceptors: {
        request: {
          use: jest.fn(),
          eject: jest.fn(),
          handlers: []
        },
        response: {
          use: jest.fn(),
          eject: jest.fn(),
          handlers: []
        }
      },
      get: jest.fn().mockResolvedValue({}),
      post: jest.fn().mockResolvedValue({}),
      put: jest.fn().mockResolvedValue({}),
      delete: jest.fn().mockResolvedValue({}),
      patch: jest.fn().mockResolvedValue({})
    };
    
    return mockAxios;
  });
});

// Global after all hook
afterAll(async () => {
  console.log('Integration test teardown: Cleaning up resources...');
}); 