import { DataSource } from 'typeorm';
import { testPublicConfig } from './test-config';
import { Logger } from '@nestjs/common';

/**
 * Helper function to reset the test database to a clean state between test runs
 */
export async function resetTestDatabase() {
    let connection: DataSource | null = null;

    try {
        // Create a connection to the test database
        const dataSource = new DataSource({
            ...testPublicConfig,
            // Allow database to exist already
            synchronize: false,
            dropSchema: false
        });

        connection = await dataSource.initialize();
        Logger.log('Test database connection established for reset operation.');

        // Truncate tables in the right order to avoid foreign key constraint issues
        await connection.query(`
            -- Disable triggers temporarily to avoid issues with foreign keys
            SET session_replication_role = 'replica';

            -- Clear data from tables but keep the structure
            TRUNCATE public.user_tenants CASCADE;
            TRUNCATE public.user_system_roles CASCADE;
            TRUNCATE public.tenants CASCADE;
            TRUNCATE public.system_users CASCADE;
            TRUNCATE public.system_roles CASCADE;
            
            -- Insert default system roles
            INSERT INTO public.system_roles (name, description, permissions) VALUES
            ('SUPER_ADMIN', 'Super administrator role with full access', '{"*": ["*"]}'),
            ('TENANT_ADMIN', 'Tenant administrator role with full access', '{"tenant": ["*"]}'),
            ('USER', 'Default user role with limited access', '{"user": ["read"]}')
            ON CONFLICT (name) DO NOTHING;

            -- Re-enable triggers
            SET session_replication_role = 'origin';
        `);

        Logger.log('Test database reset completed successfully.');
        return true;
    } catch (error) {
        Logger.error('Failed to reset test database:', error);
        return false;
    } finally {
        // Close the connection if it was opened
        if (connection && connection.isInitialized) {
            await connection.destroy();
        }
    }
}
