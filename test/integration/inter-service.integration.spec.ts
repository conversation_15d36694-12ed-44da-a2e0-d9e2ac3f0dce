import { INestApplication, Logger } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule as AuthAppModule } from '../../apps/auth/src/app.module';
import { AppModule as CaseManagementAppModule } from '../../apps/case-management/src/app.module';
import { AppModule as CoreAppModule } from '../../apps/core/src/app.module';
import { TestAppFactory } from '../helpers/test-app-factory';
import { getUniqueId } from '../helpers/testing-helpers';
import { JwtHelper } from 'test/helpers/jwt-helper';

/**
 * Integration test for testing interactions between multiple microservices
 *
 * This tests actual API calls between services without mocking the responses
 */
describe('Inter-Service Communication Integration Tests', () => {
    let authApp: INestApplication;
    let caseManagementApp: INestApplication;
    let coreApp: INestApplication;
    let authToken: string;

    // Test data
    const testUser = {
        username: `test-user-${getUniqueId()}`,
        email: `test-${getUniqueId()}@example.com`,
        password: 'Password123!',
        firstName: 'Test',
        lastName: 'User'
    };

    const testCase = {
        title: `Test Case ${getUniqueId()}`,
        description: 'This is a test case created by integration tests',
        clientId: null, // Will be populated after client creation
        priority: 'MEDIUM',
        type: 'LITIGATION',
        status: 'DRAFT'
    };

    // Increase timeout for slower test environment
    jest.setTimeout(60000);

    beforeAll(async () => {
        // Start all required microservices
        authApp = await TestAppFactory.create(AuthAppModule, 'api/auth');
        caseManagementApp = await TestAppFactory.create(
            CaseManagementAppModule,
            'api/case-management'
        );
        coreApp = await TestAppFactory.create(CoreAppModule, 'api');
    });

    afterAll(async () => {
        if (authApp) await authApp.close();
        if (caseManagementApp) await caseManagementApp.close();
        if (coreApp) await coreApp.close();
    });

    describe('Service Health Checks', () => {
        it('should verify auth service is healthy', async () => {
            const response = await request(authApp.getHttpServer())
                .get('/api/auth/health')
                .expect(200);

            expect(response.body.status).toBe('ok');
        });

        it('should verify case-management service is healthy', async () => {
            const response = await request(caseManagementApp.getHttpServer())
                .get('/api/case-management/health')
                .expect(200);

            expect(response.body.status).toBe('ok');
        });

        it('should verify core service is healthy', async () => {
            const response = await request(coreApp.getHttpServer()).get('/api/health').expect(200);

            expect(response.body.status).toBe('ok');
        });
    });

    describe('Authentication Flow', () => {
        it('should register a new test user', async () => {
            const response = await request(authApp.getHttpServer())
                .post('/api/auth/register')
                .send(testUser)
                .expect(201);

            expect(response.body.data).toHaveProperty('id');
            expect(response.body.data.username).toBe(testUser.username);
        });

        it('should authenticate and get access token', async () => {
            const response = await request(authApp.getHttpServer())
                .post('/api/auth/login')
                .send({
                    username: testUser.username,
                    password: testUser.password
                })
                .expect(200);

            expect(response.body.data).toHaveProperty('accessToken');
            authToken = response.body.data.accessToken;
        });
    });

    describe('Create Case & Manage Through Services', () => {
        it('should create a client in core service using auth token', async () => {
            const clientData = {
                name: `Test Client ${getUniqueId()}`,
                email: `client-${getUniqueId()}@example.com`,
                phone: '************',
                address: '123 Test St, Testville'
            };

            const response = await request(coreApp.getHttpServer())
                .post('/api/clients')
                .set('Authorization', `Bearer ${authToken}`)
                .send(clientData)
                .expect(201);

            expect(response.body.data).toHaveProperty('id');
            testCase.clientId = response.body.data.id;
        });

        it('should create a case in case-management service', async () => {
            // Skip if we don't have a client ID
            if (!testCase.clientId) {
                Logger.warn('Skipping case creation test because client ID is missing');
                return;
            }

            const response = await request(caseManagementApp.getHttpServer())
                .post('/api/case-management/cases')
                .set('Authorization', `Bearer ${authToken}`)
                .send(testCase)
                .expect(201);

            expect(response.body.data).toHaveProperty('id');
            expect(response.body.data).toHaveProperty('caseNumber');
            expect(response.body.data.title).toBe(testCase.title);
            testCase['id'] = response.body.data.id;
        });

        it('should assign the case to the user', async () => {
            // Skip if we don't have a case ID
            if (!testCase['id']) {
                Logger.warn('Skipping case assignment test because case ID is missing');
                return;
            }

            // Get user info from token
            const decodedToken = JwtHelper.decodeToken(authToken);
            const userId = decodedToken.sub;

            const assignmentData = {
                userId,
                userName: `${testUser.firstName} ${testUser.lastName}`,
                notes: 'Assigned during integration testing'
            };

            const response = await request(caseManagementApp.getHttpServer())
                .post(`/api/case-management/cases/${testCase['id']}/assignments`)
                .set('Authorization', `Bearer ${authToken}`)
                .send(assignmentData)
                .expect(201);

            expect(response.body.data).toHaveProperty('id');
            expect(response.body.data.userId).toBe(userId);
            expect(response.body.data.isActive).toBe(true);
        });

        it('should add an attachment to the case', async () => {
            // Skip if we don't have a case ID
            if (!testCase['id']) {
                Logger.warn('Skipping attachment test because case ID is missing');
                return;
            }

            const attachmentData = {
                filename: 'test-document.pdf',
                url: 'https://example.com/test-document.pdf',
                fileSize: 1024,
                mimeType: 'application/pdf',
                description: 'Test document for integration tests',
                documentType: 'EVIDENCE'
            };

            const response = await request(caseManagementApp.getHttpServer())
                .post(`/api/case-management/cases/${testCase['id']}/attachments`)
                .set('Authorization', `Bearer ${authToken}`)
                .send(attachmentData)
                .expect(201);

            expect(response.body.data).toHaveProperty('id');
            expect(response.body.data.filename).toBe(attachmentData.filename);
            expect(response.body.data.documentType).toBe(attachmentData.documentType);
        });

        it('should create a case event', async () => {
            // Skip if we don't have a case ID
            if (!testCase['id']) {
                Logger.warn('Skipping event test because case ID is missing');
                return;
            }

            const eventData = {
                title: 'Test Event',
                description: 'This is a test event created during integration testing',
                category: 'OTHER',
                type: 'OTHER',
                eventDate: new Date().toISOString(),
                metadata: { source: 'integration_test' }
            };

            const response = await request(caseManagementApp.getHttpServer())
                .post(`/api/case-management/cases/${testCase['id']}/events`)
                .set('Authorization', `Bearer ${authToken}`)
                .send(eventData)
                .expect(201);

            expect(response.body.data).toHaveProperty('id');
            expect(response.body.data.title).toBe(eventData.title);
            expect(response.body.data.category).toBe(eventData.category);
        });

        it('should get the user profile from auth service with the same token', async () => {
            const response = await request(authApp.getHttpServer())
                .get('/api/auth/profile')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body.data).toHaveProperty('username');
            expect(response.body.data.username).toBe(testUser.username);
        });

        it('should retrieve case details, assignments, attachments, and events', async () => {
            // Skip if we don't have a case ID
            if (!testCase['id']) {
                Logger.warn('Skipping case retrieval test because case ID is missing');
                return;
            }

            // Get case details
            const caseResponse = await request(caseManagementApp.getHttpServer())
                .get(`/api/case-management/cases/${testCase['id']}`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(caseResponse.body.data).toHaveProperty('id');
            expect(caseResponse.body.data.id).toBe(testCase['id']);
            expect(caseResponse.body.data.title).toBe(testCase.title);

            // Get case assignments
            const assignmentsResponse = await request(caseManagementApp.getHttpServer())
                .get(`/api/case-management/cases/${testCase['id']}/assignments`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(assignmentsResponse.body.data).toBeInstanceOf(Array);
            expect(assignmentsResponse.body.data.length).toBeGreaterThan(0);

            // Get case attachments
            const attachmentsResponse = await request(caseManagementApp.getHttpServer())
                .get(`/api/case-management/cases/${testCase['id']}/attachments`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(attachmentsResponse.body.data).toBeInstanceOf(Array);
            expect(attachmentsResponse.body.data.length).toBeGreaterThan(0);

            // Get case events
            const eventsResponse = await request(caseManagementApp.getHttpServer())
                .get(`/api/case-management/cases/${testCase['id']}/events`)
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(eventsResponse.body.data).toBeInstanceOf(Array);
            expect(eventsResponse.body.data.length).toBeGreaterThan(0);
        });
    });
});
