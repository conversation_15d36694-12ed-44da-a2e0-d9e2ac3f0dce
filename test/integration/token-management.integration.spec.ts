import { Test, TestingModule } from '@nestjs/testing';
import { CustomTokenRepository } from '@app/common/repositories/custom-token.repository';
import { TokenType, TokenDataType } from '@app/common/typeorm/entities/tenant/custom-token.entity';
import { TokenManagementService } from 'apps/document-engine/src/document/services';

describe('TokenManagementService Integration', () => {
    let service: TokenManagementService;

    beforeEach(async () => {
        const mockCustomTokenRepository = {
            findSystemTokens: jest.fn().mockResolvedValue([]),
            findWithFilters: jest.fn().mockResolvedValue([[], 0])
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TokenManagementService,
                { provide: CustomTokenRepository, useValue: mockCustomTokenRepository }
            ]
        }).compile();

        service = module.get<TokenManagementService>(TokenManagementService);
        customTokenRepository = module.get(CustomTokenRepository);
    });

    describe('System Token Functionality', () => {
        it('should return hardcoded system tokens with correct metadata', async () => {
            const systemTokens = await service.getSystemTokens();

            // Check for specific system tokens
            const currentDateToken = systemTokens.find((t) => t.tokenName === 'currentDate');
            expect(currentDateToken).toBeDefined();
            expect(currentDateToken?.tokenType).toBe(TokenType.SYSTEM);
            expect(currentDateToken?.dataType).toBe(TokenDataType.DATE);
            expect(currentDateToken?.metadata?.source).toBe('hardcoded');
            expect(currentDateToken?.metadata?.location).toBe('system');
            expect(currentDateToken?.category).toBe('Date/Time');

            const currentUserNameToken = systemTokens.find(
                (t) => t.tokenName === 'currentUser.name'
            );
            expect(currentUserNameToken).toBeDefined();
            expect(currentUserNameToken?.category).toBe('User Context');
            expect(currentUserNameToken?.dataType).toBe(TokenDataType.STRING);

            const generationTimestampToken = systemTokens.find(
                (t) => t.tokenName === 'generationTimestamp'
            );
            expect(generationTimestampToken).toBeDefined();
            expect(generationTimestampToken?.category).toBe('System Metadata');
            expect(generationTimestampToken?.dataType).toBe(TokenDataType.DATE);
        });

        it('should include system tokens in search results when no filter applied', async () => {
            const searchResult = await service.searchTokens({});

            expect(searchResult.tokens.length).toBeGreaterThan(0);

            // Should include hardcoded system tokens (default pagination may limit results)
            const systemTokens = searchResult.tokens.filter(
                (t) => t.tokenType === TokenType.SYSTEM
            );
            expect(systemTokens.length).toBeGreaterThan(0);

            // Verify system tokens have correct metadata
            systemTokens.forEach((token) => {
                expect(token.metadata?.source).toBe('hardcoded');
                expect(token.metadata?.location).toBe('system');
                expect(token.tags).toContain('system');
                expect(token.tags).toContain('hardcoded');
            });
        });

        it('should filter only system tokens when TokenType.SYSTEM filter applied', async () => {
            const searchResult = await service.searchTokens({
                tokenType: TokenType.SYSTEM,
                limit: 20 // Ensure we get all system tokens
            });

            expect(searchResult.tokens.length).toBe(12);

            // All tokens should be system tokens
            searchResult.tokens.forEach((token) => {
                expect(token.tokenType).toBe(TokenType.SYSTEM);
                expect(token.metadata?.source).toBe('hardcoded');
                expect(token.metadata?.location).toBe('system');
            });
        });

        it('should apply search filter to system tokens', async () => {
            const searchResult = await service.searchTokens({
                tokenType: TokenType.SYSTEM,
                search: 'current'
            });

            expect(searchResult.tokens.length).toBeGreaterThan(0);

            // All tokens should contain 'current' in name or description
            searchResult.tokens.forEach((token) => {
                const containsCurrent =
                    token.tokenName.toLowerCase().includes('current') ||
                    token.description.toLowerCase().includes('current') ||
                    (token.category && token.category.toLowerCase().includes('current'));
                expect(containsCurrent).toBe(true);
            });
        });

        it('should apply category filter to system tokens', async () => {
            const searchResult = await service.searchTokens({
                tokenType: TokenType.SYSTEM,
                category: 'Date/Time'
            });

            expect(searchResult.tokens.length).toBeGreaterThan(0);

            // All tokens should be in Date/Time category
            searchResult.tokens.forEach((token) => {
                expect(token.category).toBe('Date/Time');
            });
        });

        it('should correctly infer data types for system tokens', async () => {
            const systemTokens = await service.getSystemTokens();

            // Test email inference
            const userEmailToken = systemTokens.find((t) => t.tokenName === 'currentUser.email');
            expect(userEmailToken?.dataType).toBe(TokenDataType.EMAIL);

            // Test date inference
            const dateTokens = systemTokens.filter(
                (t) =>
                    t.tokenName.includes('Date') ||
                    t.tokenName.includes('DateTime') ||
                    t.tokenName.includes('Timestamp')
            );
            dateTokens.forEach((token) => {
                expect(token.dataType).toBe(TokenDataType.DATE);
            });

            // Test number inference
            const yearToken = systemTokens.find((t) => t.tokenName === 'currentYear');
            expect(yearToken?.dataType).toBe(TokenDataType.NUMBER);

            const dayToken = systemTokens.find((t) => t.tokenName === 'currentDay');
            expect(dayToken?.dataType).toBe(TokenDataType.NUMBER);
        });

        it('should handle pagination for system tokens', async () => {
            const page1 = await service.searchTokens({
                tokenType: TokenType.SYSTEM,
                page: 1,
                limit: 5
            });

            const page2 = await service.searchTokens({
                tokenType: TokenType.SYSTEM,
                page: 2,
                limit: 5
            });

            expect(page1.tokens.length).toBe(5);
            expect(page2.tokens.length).toBeGreaterThan(0);

            // Ensure no overlap between pages
            const page1TokenNames = page1.tokens.map((t) => t.tokenName);
            const page2TokenNames = page2.tokens.map((t) => t.tokenName);
            const overlap = page1TokenNames.filter((name) => page2TokenNames.includes(name));
            expect(overlap.length).toBe(0);
        });

        it('should handle sorting for system tokens', async () => {
            const ascResult = await service.searchTokens({
                tokenType: TokenType.SYSTEM,
                sortBy: 'tokenName',
                order: 'ASC'
            });

            const descResult = await service.searchTokens({
                tokenType: TokenType.SYSTEM,
                sortBy: 'tokenName',
                order: 'DESC'
            });

            expect(ascResult.tokens.length).toBeGreaterThan(1);
            expect(descResult.tokens.length).toBeGreaterThan(1);

            // Verify sorting
            const ascNames = ascResult.tokens.map((t) => t.tokenName);
            const descNames = descResult.tokens.map((t) => t.tokenName);

            // For ascending order, first should be lexicographically smaller than last
            expect(ascNames[0].localeCompare(ascNames[ascNames.length - 1])).toBeLessThan(0);
            // For descending order, first should be lexicographically greater than last
            expect(descNames[0].localeCompare(descNames[descNames.length - 1])).toBeGreaterThan(0);
        });
    });

    describe('Available Entities System Integration', () => {
        it('should include system entity in available entities', () => {
            const entities = service.getAvailableEntities();

            const systemEntity = entities.find((e) => e.name === 'system');
            expect(systemEntity).toBeDefined();
            expect(systemEntity?.displayName).toBe('System');
            expect(systemEntity?.description).toBe('System information (auto-populated)');
            expect(systemEntity?.fields.length).toBeGreaterThan(0);

            // Check for specific system fields
            const currentDateField = systemEntity?.fields.find((f) => f.name === 'currentDate');
            expect(currentDateField).toBeDefined();
            expect(currentDateField?.dataType).toBe('date');

            const currentUserNameField = systemEntity?.fields.find(
                (f) => f.name === 'currentUserName'
            );
            expect(currentUserNameField).toBeDefined();
            expect(currentUserNameField?.dataType).toBe('string');
        });

        it('should provide comprehensive entity field mappings', () => {
            const entities = service.getAvailableEntities();

            // Verify all major entities exist
            const expectedEntities = ['case', 'client', 'property', 'lawyer', 'system'];
            expectedEntities.forEach((entityName) => {
                const entity = entities.find((e) => e.name === entityName);
                expect(entity).toBeDefined();
                expect(entity?.fields.length).toBeGreaterThan(0);
            });

            // Verify case entity has property relationship fields
            const caseEntity = entities.find((e) => e.name === 'case');
            const propertyAddressField = caseEntity?.fields.find(
                (f) => f.path === 'property.fullAddress'
            );
            expect(propertyAddressField).toBeDefined();

            const purchasePriceField = caseEntity?.fields.find(
                (f) => f.path === 'property.purchasePrice'
            );
            expect(purchasePriceField).toBeDefined();
            expect(purchasePriceField?.dataType).toBe('currency');
        });
    });
});
