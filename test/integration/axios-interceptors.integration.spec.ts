import axios, { AxiosError } from 'axios';

/**
 * Tests for axios interceptors
 * Verifies our fixes for axios interceptors in KeycloakHttpService
 */
describe('Axios Interceptors Integration', () => {
    // Test Axios directly
    it('should have working interceptors on axios instance', () => {
        expect(axios.interceptors).toBeDefined();
        expect(axios.interceptors.request).toBeDefined();
        expect(axios.interceptors.response).toBeDefined();
        expect(typeof axios.interceptors.request.use).toBe('function');
        expect(typeof axios.interceptors.response.use).toBe('function');
    });

    // Test axios.create()
    it('should have working interceptors on created axios instance', () => {
        const instance = axios.create();
        expect(instance.interceptors).toBeDefined();
        expect(instance.interceptors.request).toBeDefined();
        expect(instance.interceptors.response).toBeDefined();
        expect(typeof instance.interceptors.request.use).toBe('function');
        expect(typeof instance.interceptors.response.use).toBe('function');
    });

    // Test adding interceptors
    it('should successfully add interceptors', () => {
        const instance = axios.create();

        // Add request interceptor
        const requestInterceptor = instance.interceptors.request.use(
            (config) => {
                return config;
            },
            (error: AxiosError | Error) => {
                return Promise.reject(error);
            }
        );

        // Add response interceptor
        const responseInterceptor = instance.interceptors.response.use(
            (response) => {
                return response;
            },
            (error: AxiosError | Error) => {
                return Promise.reject(error);
            }
        );

        expect(requestInterceptor).toBeDefined();
        expect(responseInterceptor).toBeDefined();
    });
});
