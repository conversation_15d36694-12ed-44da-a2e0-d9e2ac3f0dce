import { MockKeycloakHttpService } from '../mocks/mock-keycloak-http.service';

/**
 * Tests for MockKeycloakHttpService
 */
describe('MockKeycloakHttpService', () => {
    let service: MockKeycloakHttpService;

    beforeEach(() => {
        service = new MockKeycloakHttpService();
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    it('should have axios interceptors setup properly', () => {
        const axiosInstance = service.getInstance();
        expect(axiosInstance).toBeDefined();
        expect(axiosInstance.interceptors).toBeDefined();
        expect(axiosInstance.interceptors.request).toBeDefined();
        expect(axiosInstance.interceptors.response).toBeDefined();
    });
});
