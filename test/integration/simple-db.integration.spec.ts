import { DataSource } from 'typeorm';
import { testPublicConfig } from '../helpers/test-config';

/**
 * Simple integration test to verify database connectivity
 * This is used to ensure the test database is properly set up
 */
describe('Database Integration Tests', () => {
    let dataSource: DataSource;

    beforeAll(async () => {
        // Create a connection to the test database
        dataSource = new DataSource({
            type: 'postgres',
            host: testPublicConfig.host,
            port: testPublicConfig.port,
            username: testPublicConfig.username,
            password: testPublicConfig.password,
            database: testPublicConfig.database,
            schema: 'public',
            logging: false
        });

        await dataSource.initialize();
    });

    afterAll(async () => {
        if (dataSource && dataSource.isInitialized) {
            await dataSource.destroy();
        }
    });

    it('should connect to the database', async () => {
        // Simple test to check if we can run a query
        const result = await dataSource.query('SELECT NOW() as now');
        expect(result).toBeDefined();
        expect(result.length).toBeGreaterThan(0);
        expect(result[0].now).toBeDefined();
    });

    it('should have the test configuration set correctly', () => {
        // Check if the test configuration is correct
        expect(testPublicConfig).toBeDefined();
        expect(testPublicConfig.type).toBe('postgres');
        expect(testPublicConfig.host).toBeDefined();
        expect(testPublicConfig.port).toBeDefined();
        expect(testPublicConfig.username).toBeDefined();
    });
});
