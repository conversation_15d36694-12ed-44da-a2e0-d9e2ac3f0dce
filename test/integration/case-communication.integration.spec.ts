import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CaseService } from '../../apps/case-management/src/services/case.service';
import { CaseNotificationService } from '../../apps/case-management/src/services/case-notification.service';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import { CaseStatus } from '@app/common/enums/case-status.enum';
import { CasePriority, CaseType } from '@app/common/typeorm/entities/tenant/case.entity';
import { CreateCaseDto } from '../../apps/case-management/src/dto/create-case.dto';
import { UpdateCaseDto } from '../../apps/case-management/src/dto/update-case.dto';
import { AssignCaseDto } from '../../apps/case-management/src/dto/assign-case.dto';

describe('Case Communication Integration', () => {
    let app: INestApplication;
    let caseService: CaseService;
    let caseNotificationService: CaseNotificationService;
    let messageProducerService: MessageProducerService;
    let testCaseId: string;

    beforeAll(async () => {
        const moduleFixture: TestingModule = await Test.createTestingModule({
            imports: [
                ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test'
                })
            ]
        }).compile();

        app = moduleFixture.createNestApplication();
        await app.init();

        caseService = moduleFixture.get<CaseService>(CaseService);
        caseNotificationService =
            moduleFixture.get<CaseNotificationService>(CaseNotificationService);
        messageProducerService = moduleFixture.get<MessageProducerService>(MessageProducerService);
    });

    afterAll(async () => {
        await app.close();
    });

    describe('Case Creation Notifications', () => {
        it('should send notification when case is created', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            const createCaseDto: CreateCaseDto = {
                title: 'Test Case for Notifications',
                description: 'Testing communication integration',
                priority: CasePriority.HIGH,
                type: CaseType.LITIGATION,
                status: CaseStatus.DRAFT,
                client: {
                    name: 'Test Client',
                    email: '<EMAIL>',
                    phone: '1234567890'
                }
            };

            const mockRequest = {
                headers: {},
                socket: { remoteAddress: '127.0.0.1' }
            } as any;

            const createdCase = await caseService.createCase(
                createCaseDto,
                'user-123',
                'Test User',
                mockRequest
            );

            testCaseId = createdCase.id;

            expect(enqueueSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    channels: [COMMUNICATION_CHANNELS.EMAIL],
                    recipients: {
                        email: ['<EMAIL>']
                    },
                    variables: expect.objectContaining({
                        type: 'case-created',
                        caseNumber: createdCase.caseNumber,
                        status: CaseStatus.DRAFT
                    }),
                    metadata: {
                        notificationType: 'case-created'
                    }
                })
            );
        });
    });

    describe('Case Status Change Notifications', () => {
        it('should send notification when case status changes', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            const updateCaseDto: UpdateCaseDto = {
                status: CaseStatus.IN_PROGRESS
            };

            const mockRequest = {
                headers: {},
                socket: { remoteAddress: '127.0.0.1' }
            } as any;

            await caseService.updateCase(
                testCaseId,
                updateCaseDto,
                'user-123',
                'Test User',
                mockRequest
            );

            expect(enqueueSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    channels: [COMMUNICATION_CHANNELS.EMAIL],
                    variables: expect.objectContaining({
                        type: 'case-update',
                        status: CaseStatus.IN_PROGRESS,
                        caseSummary: expect.stringContaining('status changed')
                    }),
                    metadata: {
                        notificationType: 'status-change',
                        oldStatus: CaseStatus.DRAFT,
                        newStatus: CaseStatus.IN_PROGRESS
                    }
                })
            );
        });
    });

    describe('Case Assignment Notifications', () => {
        it('should send notification when case is assigned', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            const assignCaseDto: AssignCaseDto = {
                userId: 'lawyer-456',
                userName: 'Jane Lawyer',
                userEmail: '<EMAIL>',
                notes: 'Assigning to experienced lawyer'
            };

            await caseNotificationService.sendCaseAssignmentNotification(
                { id: testCaseId, caseNumber: 'TEST-2024-001', status: CaseStatus.IN_PROGRESS },
                assignCaseDto.userId,
                assignCaseDto.userEmail,
                'Test Admin'
            );

            expect(enqueueSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    channels: expect.arrayContaining([
                        COMMUNICATION_CHANNELS.EMAIL,
                        COMMUNICATION_CHANNELS.NOTIFICATION
                    ]),
                    recipients: {
                        email: ['<EMAIL>'],
                        notification: ['lawyer-456']
                    },
                    variables: expect.objectContaining({
                        type: 'case-update',
                        caseSummary: expect.stringContaining('assigned')
                    }),
                    metadata: {
                        notificationType: 'case-assignment',
                        assignedBy: 'Test Admin'
                    }
                })
            );
        });
    });

    describe('Deadline Reminder Notifications', () => {
        it('should send deadline reminder notifications', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            const upcomingCases = [
                {
                    id: testCaseId,
                    caseNumber: 'TEST-2024-001',
                    status: CaseStatus.IN_PROGRESS,
                    deadline: new Date(Date.now() + 5 * 60 * 60 * 1000), // 5 hours from now
                    client: { name: 'Test Client' },
                    assignments: [
                        {
                            userId: 'lawyer-456',
                            userEmail: '<EMAIL>'
                        }
                    ]
                }
            ];

            jest.spyOn(caseNotificationService, 'checkUpcomingDeadlines').mockResolvedValue(
                upcomingCases
            );

            await caseNotificationService.sendDeadlineReminders();

            expect(enqueueSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    channels: expect.arrayContaining([
                        COMMUNICATION_CHANNELS.EMAIL,
                        COMMUNICATION_CHANNELS.NOTIFICATION
                    ]),
                    variables: expect.objectContaining({
                        type: 'case-urgent',
                        urgency: 'critical',
                        caseSummary: expect.stringContaining('Deadline approaching')
                    }),
                    metadata: {
                        notificationType: 'deadline-reminder',
                        hoursUntilDeadline: expect.any(Number)
                    }
                })
            );
        });
    });

    describe('Multi-channel Notifications', () => {
        it('should send notifications through multiple channels', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            await caseNotificationService.sendCaseAssignmentNotification(
                {
                    id: testCaseId,
                    caseNumber: 'TEST-2024-001',
                    status: CaseStatus.IN_PROGRESS,
                    priority: CasePriority.HIGH
                },
                'user-789',
                '<EMAIL>',
                'Admin User'
            );

            const callArgs = enqueueSpy.mock.calls[enqueueSpy.mock.calls.length - 1][0];

            expect(callArgs.channels).toEqual(
                expect.arrayContaining([
                    COMMUNICATION_CHANNELS.EMAIL,
                    COMMUNICATION_CHANNELS.NOTIFICATION
                ])
            );

            expect(callArgs.recipients).toEqual({
                email: ['<EMAIL>'],
                notification: ['user-789']
            });
        });
    });

    describe('Template Variable Validation', () => {
        it('should properly format case template variables', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            const caseEntity = {
                id: 'case-123',
                caseNumber: 'LCS-2024-001',
                title: 'Test Case',
                status: CaseStatus.IN_PROGRESS,
                type: CaseType.LITIGATION,
                priority: CasePriority.HIGH,
                deadline: new Date('2024-12-31'),
                client: {
                    name: 'Test Client Corp',
                    email: '<EMAIL>'
                }
            };

            await caseNotificationService.sendCaseStatusNotification(
                caseEntity,
                CaseStatus.DRAFT,
                CaseStatus.IN_PROGRESS,
                'user-123',
                ['<EMAIL>']
            );

            const callArgs = enqueueSpy.mock.calls[enqueueSpy.mock.calls.length - 1][0];

            expect(callArgs.variables).toMatchObject({
                type: 'case-update',
                caseId: 'case-123',
                caseNumber: 'LCS-2024-001',
                status: CaseStatus.IN_PROGRESS,
                caseType: CaseType.LITIGATION,
                deadline: caseEntity.deadline,
                urgency: 'high'
            });
        });
    });

    describe('Error Handling', () => {
        it('should handle communication service errors gracefully', async () => {
            const error = new Error('Queue service unavailable');
            jest.spyOn(messageProducerService, 'enqueueMessage').mockRejectedValueOnce(error);

            const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

            await caseNotificationService.sendCaseCreatedNotification(
                { id: testCaseId, caseNumber: 'TEST-2024-001' },
                'user-123',
                ['<EMAIL>']
            );

            expect(consoleErrorSpy).toHaveBeenCalledWith(
                expect.stringContaining('Failed to send case creation notification'),
                error.stack
            );

            consoleErrorSpy.mockRestore();
        });
    });
});
