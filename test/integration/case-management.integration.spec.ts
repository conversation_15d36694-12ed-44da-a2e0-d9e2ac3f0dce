import { INestApplication, Logger } from '@nestjs/common';
import * as request from 'supertest';
import { resetTestDatabase } from '../helpers/integration-db-reset';
import { TestAppFactory } from '../helpers/test-app-factory';
import { getUniqueId } from '../helpers/testing-helpers';
import { AppModule } from '../../apps/case-management/src/app.module';
const { ensureMigrations } = require('../helpers/ensure-migration');

describe('Case Management Integration', () => {
    let app: INestApplication;
    let authApp: INestApplication;

    // Test data storage
    const testData = {
        tenantName: `Test Tenant ${getUniqueId()}`,
        tenantRealm: `test-tenant-${getUniqueId()}`,
        adminUsername: 'admin',
        adminEmail: `admin-${getUniqueId()}@example.com`,
        adminPassword: 'Password123!',
        accessToken: null,
        tenantId: null,
        clientId: null,
        caseId: null,
        caseNumber: null,
        noteId: null
    };

    // Force jest to wait longer for this test
    jest.setTimeout(60000);

    beforeAll(async () => {
        try {
            // Ensure migrations are run before creating the app
            await ensureMigrations();

            // Initialize the auth service first
            authApp = await TestAppFactory.createAuthApp(
                require('../../apps/auth/src/app.module').AppModule
            );

            // Initialize the case management service
            app = await TestAppFactory.createCaseManagementApp(AppModule);

            // Reset the database to a clean state
            await resetTestDatabase().catch((err) => {
                Logger.warn('Error resetting database, tests may be unstable:', err.message);
            });

            // Create test tenant and user
            await setupTenantAndUser();
        } catch (err) {
            Logger.error('Failed to set up test environment:', err);
            throw err;
        }
    });

    // Helper to set up tenant and authenticate
    async function setupTenantAndUser() {
        // Create tenant
        const createTenantRes = await request(authApp.getHttpServer())
            .post('/api/auth/create-tenant')
            .send({
                name: testData.tenantName,
                realm: testData.tenantRealm,
                adminUsername: testData.adminUsername,
                adminEmail: testData.adminEmail,
                adminPassword: testData.adminPassword
            });

        if (createTenantRes.status !== 201) {
            throw new Error(`Failed to create tenant: ${JSON.stringify(createTenantRes.body)}`);
        }

        testData.tenantId = createTenantRes.body.data.tenantId;

        // Login to get access token
        const loginRes = await request(authApp.getHttpServer()).post('/api/auth/login').send({
            username: testData.adminUsername,
            password: testData.adminPassword,
            realm: testData.tenantRealm
        });

        if (loginRes.status !== 200) {
            throw new Error(`Failed to login: ${JSON.stringify(loginRes.body)}`);
        }

        testData.accessToken = loginRes.body.data.access_token;
    }

    afterAll(async () => {
        if (app) {
            await app.close();
        }
        if (authApp) {
            await authApp.close();
        }
    });

    it('should have a working health check endpoint', async () => {
        const response = await request(app.getHttpServer())
            .get('/api/case-management/health')
            .expect(200);
        expect(response.body.status).toBe('ok');
    });

    describe('Client and Case Management', () => {
        it('should create a client successfully', async () => {
            const clientData = {
                firstName: 'John',
                lastName: 'Smith',
                email: `client-${getUniqueId()}@example.com`,
                phone: '************',
                address: {
                    street: '123 Main St',
                    city: 'Anytown',
                    state: 'CA',
                    zip: '12345'
                }
            };

            const response = await request(app.getHttpServer())
                .post('/api/case-management/clients')
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .send(clientData)
                .expect(201);

            expect(response.body.data).toBeDefined();
            expect(response.body.data.id).toBeDefined();
            expect(response.body.data.firstName).toBe(clientData.firstName);
            expect(response.body.data.lastName).toBe(clientData.lastName);

            testData.clientId = response.body.data.id;
        });

        it('should retrieve clients with search functionality', async () => {
            // Skip if no client was created
            if (!testData.clientId) {
                pending('No client created in previous test');
                return;
            }

            const response = await request(app.getHttpServer())
                .get('/api/case-management/clients?search=Smith')
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .expect(200);

            expect(response.body.data).toBeInstanceOf(Array);
            expect(response.body.data.length).toBeGreaterThan(0);
            expect(response.body.data.some((client) => client.id === testData.clientId)).toBe(true);
        });

        it('should create a case for a client', async () => {
            // Skip if no client was created
            if (!testData.clientId) {
                pending('No client created in previous test');
                return;
            }

            const caseData = {
                clientId: testData.clientId,
                title: 'Test Case',
                description: 'This is a test case for integration testing',
                type: 'CIVIL',
                status: 'OPEN',
                priority: 'MEDIUM'
            };

            const response = await request(app.getHttpServer())
                .post('/api/case-management/cases')
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .send(caseData)
                .expect(201);

            expect(response.body.data).toBeDefined();
            expect(response.body.data.id).toBeDefined();
            expect(response.body.data.caseNumber).toBeDefined();
            expect(response.body.data.title).toBe(caseData.title);

            testData.caseId = response.body.data.id;
            testData.caseNumber = response.body.data.caseNumber;
        });

        it('should retrieve case details', async () => {
            // Skip if no case was created
            if (!testData.caseId) {
                pending('No case created in previous test');
                return;
            }

            const response = await request(app.getHttpServer())
                .get(`/api/case-management/cases/${testData.caseId}`)
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .expect(200);

            expect(response.body.data).toBeDefined();
            expect(response.body.data.id).toBe(testData.caseId);
            expect(response.body.data.clientId).toBe(testData.clientId);
        });

        it('should find a case by case number', async () => {
            // Skip if no case was created
            if (!testData.caseNumber) {
                pending('No case created with case number');
                return;
            }

            const response = await request(app.getHttpServer())
                .get(`/api/case-management/cases/by-number/${testData.caseNumber}`)
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .expect(200);

            expect(response.body.data).toBeDefined();
            expect(response.body.data.id).toBe(testData.caseId);
            expect(response.body.data.caseNumber).toBe(testData.caseNumber);
        });
    });

    describe('Case Notes', () => {
        it('should add a note to a case', async () => {
            // Skip if no case was created
            if (!testData.caseId) {
                pending('No case created in previous test');
                return;
            }

            const noteData = {
                content: 'This is a test note for the case',
                isPinned: false
            };

            const response = await request(app.getHttpServer())
                .post(`/api/case-management/cases/${testData.caseId}/notes`)
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .send(noteData)
                .expect(201);

            expect(response.body.data).toBeDefined();
            expect(response.body.data.id).toBeDefined();
            expect(response.body.data.content).toBe(noteData.content);
            expect(response.body.data.caseId).toBe(testData.caseId);

            testData.noteId = response.body.data.id;
        });

        it('should retrieve notes for a case', async () => {
            // Skip if no case was created
            if (!testData.caseId) {
                pending('No case created in previous test');
                return;
            }

            const response = await request(app.getHttpServer())
                .get(`/api/case-management/cases/${testData.caseId}/notes`)
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .expect(200);

            expect(response.body.data).toBeInstanceOf(Array);
            if (testData.noteId) {
                expect(response.body.data.some((note) => note.id === testData.noteId)).toBe(true);
            }
        });

        it('should pin a note', async () => {
            // Skip if no note was created
            if (!testData.noteId) {
                pending('No note created in previous test');
                return;
            }

            const response = await request(app.getHttpServer())
                .patch(`/api/case-management/cases/notes/${testData.noteId}/pin`)
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .expect(200);

            expect(response.body.data).toBeDefined();
            expect(response.body.data.isPinned).toBe(true);
        });

        it('should retrieve pinned notes', async () => {
            // Skip if no case was created
            if (!testData.caseId) {
                pending('No case created in previous test');
                return;
            }

            const response = await request(app.getHttpServer())
                .get(`/api/case-management/cases/${testData.caseId}/notes/pinned`)
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .expect(200);

            expect(response.body.data).toBeInstanceOf(Array);
            if (testData.noteId) {
                expect(response.body.data.some((note) => note.id === testData.noteId)).toBe(true);
            }
        });
    });

    describe('Case Filtering and Search', () => {
        it('should filter cases by status', async () => {
            const response = await request(app.getHttpServer())
                .get('/api/case-management/cases?status=OPEN')
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .expect(200);

            expect(response.body.data).toBeInstanceOf(Array);
            if (testData.caseId) {
                expect(response.body.data.some((caseItem) => caseItem.id === testData.caseId)).toBe(
                    true
                );
            }
        });

        it('should filter cases by priority', async () => {
            const response = await request(app.getHttpServer())
                .get('/api/case-management/cases?priority=MEDIUM')
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .expect(200);

            expect(response.body.data).toBeInstanceOf(Array);
        });

        it('should perform a quick search on cases', async () => {
            const response = await request(app.getHttpServer())
                .get('/api/case-management/cases?search=Test')
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .expect(200);

            expect(response.body.data).toBeInstanceOf(Array);
        });
    });

    describe('Case Updates', () => {
        it('should update a case', async () => {
            // Skip if no case was created
            if (!testData.caseId) {
                pending('No case created in previous test');
                return;
            }

            const updateData = {
                title: 'Updated Test Case',
                description: 'This case has been updated via integration test'
            };

            const response = await request(app.getHttpServer())
                .patch(`/api/case-management/cases/${testData.caseId}`)
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .send(updateData)
                .expect(200);

            expect(response.body.data).toBeDefined();
            expect(response.body.data.title).toBe(updateData.title);
            expect(response.body.data.description).toBe(updateData.description);
        });

        it('should change a case status', async () => {
            // Skip if no case was created
            if (!testData.caseId) {
                pending('No case created in previous test');
                return;
            }

            const response = await request(app.getHttpServer())
                .patch(`/api/case-management/cases/${testData.caseId}/status`)
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm)
                .send({ status: 'IN_PROGRESS' })
                .expect(200);

            expect(response.body.data).toBeDefined();
            expect(response.body.data.status).toBe('IN_PROGRESS');
        });
    });
});
