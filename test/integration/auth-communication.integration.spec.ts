import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AuthService } from '../../apps/auth/src/services/auth.service';
import { AuthNotificationService } from '../../apps/auth/src/services/auth-notification.service';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import { CreateTenantDto } from '../../apps/auth/src/dto/create-tenant.dto';

describe('Auth Communication Integration', () => {
    let app: INestApplication;
    let authService: AuthService;
    let authNotificationService: AuthNotificationService;
    let messageProducerService: MessageProducerService;

    beforeAll(async () => {
        const moduleFixture: TestingModule = await Test.createTestingModule({
            imports: [
                ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test'
                })
            ]
        }).compile();

        app = moduleFixture.createNestApplication();
        await app.init();

        authService = moduleFixture.get<AuthService>(AuthService);
        authNotificationService =
            moduleFixture.get<AuthNotificationService>(AuthNotificationService);
        messageProducerService = moduleFixture.get<MessageProducerService>(MessageProducerService);
    });

    afterAll(async () => {
        await app.close();
    });

    describe('Tenant Creation Notifications', () => {
        it('should send tenant creation notification when tenant is created', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            const createTenantDto: CreateTenantDto = {
                realm: 'test-law-firm',
                displayName: 'Test Law Firm',
                adminUsername: 'admin',
                adminPassword: 'testpassword123',
                adminEmail: '<EMAIL>',
                adminFirstName: 'John',
                adminLastName: 'Admin'
            };

            // Mock the complex tenant creation process
            jest.spyOn(authService, 'createTenant').mockResolvedValue({
                realm: createTenantDto.realm,
                displayName: createTenantDto.displayName,
                adminUsername: createTenantDto.adminUsername,
                adminPassword: createTenantDto.adminPassword,
                clientId: 'test-client-id'
            });

            await authNotificationService.sendTenantCreationNotification(
                createTenantDto.adminEmail,
                `${createTenantDto.adminFirstName} ${createTenantDto.adminLastName}`,
                createTenantDto.displayName,
                'tenant-123',
                'https://app.example.com/test-law-firm',
                'test-client-id',
                createTenantDto.adminPassword
            );

            expect(enqueueSpy).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'generic-notification',
                    tenantName: 'Test Law Firm',
                    recipientName: 'John Admin',
                    message: expect.stringContaining('Congratulations')
                }),
                metadata: {
                    notificationType: 'tenant-created',
                    clientId: 'test-client-id'
                }
            });
        });
    });

    describe('User Welcome Notifications', () => {
        it('should send welcome notification for new user creation', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            await authNotificationService.sendWelcomeNotification(
                '<EMAIL>',
                'Jane Lawyer',
                'Test Law Firm',
                'tenant-123',
                'https://app.example.com/test-law-firm',
                'John Admin',
                undefined // No temporary password
            );

            expect(enqueueSpy).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'welcome',
                    tenantName: 'Test Law Firm',
                    recipientName: 'Jane Lawyer',
                    inviterName: 'John Admin'
                }),
                metadata: {
                    notificationType: 'user-welcome',
                    hasTemporaryPassword: false
                }
            });
        });

        it('should send welcome notification with temporary password info', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            await authNotificationService.sendWelcomeNotification(
                '<EMAIL>',
                'Bob Paralegal',
                'Test Law Firm',
                'tenant-123',
                'https://app.example.com/test-law-firm',
                'John Admin',
                'temp123'
            );

            expect(enqueueSpy).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'welcome',
                    tenantName: 'Test Law Firm',
                    recipientName: 'Bob Paralegal',
                    welcomeMessage: expect.stringContaining('temporary password')
                }),
                metadata: {
                    notificationType: 'user-welcome',
                    hasTemporaryPassword: true
                }
            });
        });
    });

    describe('User Invitation Notifications', () => {
        it('should send user invitation notification', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            await authNotificationService.sendUserInvitationNotification(
                '<EMAIL>',
                'Alice Newlawyer',
                'Test Law Firm',
                'tenant-123',
                'https://app.example.com/invite?token=abc123',
                'John Admin',
                'lawyer'
            );

            expect(enqueueSpy).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'user-invitation',
                    tenantName: 'Test Law Firm',
                    recipientName: 'Alice Newlawyer',
                    inviterName: 'John Admin',
                    role: 'Lawyer'
                }),
                metadata: {
                    notificationType: 'user-invitation',
                    roleGroupKey: 'lawyer'
                }
            });
        });
    });

    describe('Password Reset Notifications', () => {
        it('should send password reset notification', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            await authNotificationService.sendPasswordResetNotification(
                '<EMAIL>',
                'Test User',
                'Test Law Firm',
                'tenant-123',
                'https://app.example.com/reset?token=reset123',
                '24 hours'
            );

            expect(enqueueSpy).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'password-reset',
                    tenantName: 'Test Law Firm',
                    recipientName: 'Test User',
                    resetUrl: 'https://app.example.com/reset?token=reset123',
                    expirationTime: '24 hours'
                }),
                metadata: {
                    notificationType: 'password-reset'
                }
            });
        });
    });

    describe('Role Promotion Notifications', () => {
        it('should send role promotion notification', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            await authNotificationService.sendRolePromotionNotification(
                '<EMAIL>',
                'Test User',
                'Test Law Firm',
                'tenant-123',
                'lawyer_admin',
                'John Admin'
            );

            expect(enqueueSpy).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: expect.arrayContaining([
                    COMMUNICATION_CHANNELS.EMAIL,
                    COMMUNICATION_CHANNELS.NOTIFICATION
                ]),
                recipients: {
                    email: ['<EMAIL>'],
                    notification: ['Test User']
                },
                variables: expect.objectContaining({
                    type: 'generic-notification',
                    tenantName: 'Test Law Firm',
                    recipientName: 'Test User',
                    message: expect.stringContaining('role')
                }),
                metadata: {
                    notificationType: 'role-promotion',
                    newRoleGroup: 'lawyer_admin',
                    promotedBy: 'John Admin'
                }
            });
        });
    });

    describe('Security Alert Notifications', () => {
        it('should send security alert notification', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            await authNotificationService.sendSecurityAlertNotification(
                '<EMAIL>',
                'Test User',
                'Test Law Firm',
                'tenant-123',
                'suspicious-login',
                'Unusual login activity detected',
                '*************'
            );

            expect(enqueueSpy).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: expect.arrayContaining([
                    COMMUNICATION_CHANNELS.EMAIL,
                    COMMUNICATION_CHANNELS.NOTIFICATION
                ]),
                recipients: {
                    email: ['<EMAIL>'],
                    notification: ['Test User']
                },
                variables: expect.objectContaining({
                    type: 'generic-notification',
                    tenantName: 'Test Law Firm',
                    recipientName: 'Test User',
                    message: expect.stringContaining('*************')
                }),
                metadata: {
                    notificationType: 'security-alert',
                    alertType: 'suspicious-login',
                    ipAddress: '*************'
                }
            });
        });
    });

    describe('Template Variable Validation', () => {
        it('should properly format role group names', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            await authNotificationService.sendUserInvitationNotification(
                '<EMAIL>',
                'Test User',
                'Test Tenant',
                'tenant-123',
                'https://example.com/invite',
                'Admin',
                'super_admin'
            );

            const callArgs = enqueueSpy.mock.calls[enqueueSpy.mock.calls.length - 1][0];
            expect(callArgs.variables.role).toBe('Super Administrator');
        });

        it('should include current year in all notifications', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');
            const currentYear = new Date().getFullYear().toString();

            await authNotificationService.sendWelcomeNotification(
                '<EMAIL>',
                'Test User',
                'Test Tenant',
                'tenant-123',
                'https://example.com'
            );

            const callArgs = enqueueSpy.mock.calls[enqueueSpy.mock.calls.length - 1][0];
            expect(callArgs.variables.currentYear).toBe(currentYear);
        });
    });

    describe('Error Handling', () => {
        it('should handle communication service errors gracefully', async () => {
            const error = new Error('Queue service unavailable');
            jest.spyOn(messageProducerService, 'enqueueMessage').mockRejectedValueOnce(error);

            const loggerErrorSpy = jest
                .spyOn((authNotificationService as any).logger, 'error')
                .mockImplementation();

            // Should not throw an error, but should log it
            await expect(
                authNotificationService.sendWelcomeNotification(
                    '<EMAIL>',
                    'Test User',
                    'Test Tenant',
                    'tenant-123',
                    'https://example.com'
                )
            ).resolves.not.toThrow();

            expect(loggerErrorSpy).toHaveBeenCalledWith(
                expect.stringContaining('Failed to send welcome notification'),
                error.stack
            );

            loggerErrorSpy.mockRestore();
        });
    });

    describe('Multi-channel Notifications', () => {
        it('should send notifications through multiple channels for important events', async () => {
            const enqueueSpy = jest.spyOn(messageProducerService, 'enqueueMessage');

            await authNotificationService.sendRolePromotionNotification(
                '<EMAIL>',
                'Test User',
                'Test Tenant',
                'tenant-123',
                'admin',
                'Super Admin'
            );

            const callArgs = enqueueSpy.mock.calls[enqueueSpy.mock.calls.length - 1][0];

            expect(callArgs.channels).toEqual(
                expect.arrayContaining([
                    COMMUNICATION_CHANNELS.EMAIL,
                    COMMUNICATION_CHANNELS.NOTIFICATION
                ])
            );

            expect(callArgs.recipients).toEqual({
                email: ['<EMAIL>'],
                notification: ['Test User']
            });
        });
    });
});
