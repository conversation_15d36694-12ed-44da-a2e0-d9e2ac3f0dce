import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { resetTestDatabase } from '../helpers/integration-db-reset';
import { TestAppFactory } from '../helpers/test-app-factory';
import { getUniqueId } from '../helpers/testing-helpers';
import { AppModule } from '../../apps/auth/src/app.module';
const { ensureMigrations } = require('../helpers/ensure-migration');

describe('Auth Integration', () => {
    let app: INestApplication;

    // Store test data that will be used across test cases
    const testData = {
        tenantName: `Test Tenant ${getUniqueId()}`,
        tenantRealm: `test-tenant-${getUniqueId()}`,
        adminUsername: 'admin',
        adminEmail: `admin-${getUniqueId()}@example.com`,
        adminPassword: 'Password123!',
        accessToken: null,
        refreshToken: null,
        tenantId: null
    };

    beforeAll(async () => {
        // Ensure migrations are run before creating the app
        await ensureMigrations();

        // Creating a full app instance that uses TestAppFactory to configure services
        // but maintains real connections to databases and other services
        app = await TestAppFactory.createAuthApp(AppModule);
    }, 30000);

    beforeEach(async () => {
        await resetTestDatabase();
    });

    afterAll(async () => {
        if (app) {
            await app.close();
        }
    });

    it('should have a working health check endpoint', async () => {
        const res = await request(app.getHttpServer()).get('/api/auth/health');

        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('status');
        expect(res.body.status.toLowerCase()).toBe('ok');
    });

    it('should create a tenant with initial admin user', async () => {
        const res = await request(app.getHttpServer()).post('/api/auth/create-tenant').send({
            name: testData.tenantName,
            realm: testData.tenantRealm,
            adminUsername: testData.adminUsername,
            adminEmail: testData.adminEmail,
            adminPassword: testData.adminPassword
        });

        expect(res.status).toBe(201);
        expect(res.body.data).toBeDefined();
        expect(res.body.data.realm).toBe(testData.tenantRealm);
        expect(res.body.data.tenantId).toBeDefined();

        // Store tenant ID for later tests
        testData.tenantId = res.body.data.tenantId;
    });

    it('should successfully login as admin user', async () => {
        // First ensure we have a tenant
        if (!testData.tenantId) {
            const createRes = await request(app.getHttpServer())
                .post('/api/auth/create-tenant')
                .send({
                    name: testData.tenantName,
                    realm: testData.tenantRealm,
                    adminUsername: testData.adminUsername,
                    adminEmail: testData.adminEmail,
                    adminPassword: testData.adminPassword
                });

            testData.tenantId = createRes.body.data.tenantId;
        }

        // Now attempt to login
        const res = await request(app.getHttpServer()).post('/api/auth/login').send({
            username: testData.adminUsername,
            password: testData.adminPassword,
            realm: testData.tenantRealm
        });

        expect(res.status).toBe(200);
        expect(res.body.data).toBeDefined();
        expect(res.body.data.access_token).toBeDefined();
        expect(res.body.data.refresh_token).toBeDefined();

        // Store tokens for later tests
        testData.accessToken = res.body.data.access_token;
        testData.refreshToken = res.body.data.refresh_token;
    });

    it('should get the user profile with a valid token', async () => {
        // First ensure we have valid tokens
        if (!testData.accessToken) {
            // Create tenant if needed
            if (!testData.tenantId) {
                const createRes = await request(app.getHttpServer())
                    .post('/api/auth/create-tenant')
                    .send({
                        name: testData.tenantName,
                        realm: testData.tenantRealm,
                        adminUsername: testData.adminUsername,
                        adminEmail: testData.adminEmail,
                        adminPassword: testData.adminPassword
                    });

                testData.tenantId = createRes.body.data.tenantId;
            }

            // Login to get tokens
            const loginRes = await request(app.getHttpServer()).post('/api/auth/login').send({
                username: testData.adminUsername,
                password: testData.adminPassword,
                realm: testData.tenantRealm
            });

            testData.accessToken = loginRes.body.data.access_token;
            testData.refreshToken = loginRes.body.data.refresh_token;
        }

        // Now get user profile
        const res = await request(app.getHttpServer())
            .get('/api/auth/me')
            .set('Authorization', `Bearer ${testData.accessToken}`)
            .set('x-realm', testData.tenantRealm);

        expect(res.status).toBe(200);
        expect(res.body.data).toBeDefined();
        expect(res.body.data.username).toBe(testData.adminUsername);
        expect(res.body.data.email).toBe(testData.adminEmail);
        expect(res.body.data.roles).toContain('admin');
    });

    it('should refresh the token with a valid refresh token', async () => {
        // First ensure we have valid tokens
        if (!testData.refreshToken) {
            // Create tenant if needed
            if (!testData.tenantId) {
                const createRes = await request(app.getHttpServer())
                    .post('/api/auth/create-tenant')
                    .send({
                        name: testData.tenantName,
                        realm: testData.tenantRealm,
                        adminUsername: testData.adminUsername,
                        adminEmail: testData.adminEmail,
                        adminPassword: testData.adminPassword
                    });

                testData.tenantId = createRes.body.data.tenantId;
            }

            // Login to get tokens
            const loginRes = await request(app.getHttpServer()).post('/api/auth/login').send({
                username: testData.adminUsername,
                password: testData.adminPassword,
                realm: testData.tenantRealm
            });

            testData.accessToken = loginRes.body.data.access_token;
            testData.refreshToken = loginRes.body.data.refresh_token;
        }

        // Now refresh token
        const res = await request(app.getHttpServer()).post('/api/auth/refresh').send({
            refreshToken: testData.refreshToken,
            realm: testData.tenantRealm
        });

        expect(res.status).toBe(200);
        expect(res.body.data).toBeDefined();
        expect(res.body.data.access_token).toBeDefined();
        expect(res.body.data.refresh_token).toBeDefined();

        // Update tokens for future tests
        testData.accessToken = res.body.data.access_token;
        testData.refreshToken = res.body.data.refresh_token;
    });

    it('should successfully log out', async () => {
        // Simply test the logout endpoint
        const res = await request(app.getHttpServer()).post('/api/auth/logout');

        expect(res.status).toBe(201);

        // Verify logout by trying to use the token (should fail)
        if (testData.accessToken) {
            const profileRes = await request(app.getHttpServer())
                .get('/api/auth/me')
                .set('Authorization', `Bearer ${testData.accessToken}`)
                .set('x-realm', testData.tenantRealm);

            expect(profileRes.status).not.toBe(200);
        }
    });
});
