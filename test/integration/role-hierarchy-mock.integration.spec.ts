import { Test } from '@nestjs/testing';
import { MockRoleHierarchyService } from '../mocks/mock-role-hierarchy.service';

/**
 * Simple tests for the MockRoleHierarchyService
 * To verify it works correctly in integration tests
 */
describe('RoleHierarchyMock', () => {
    let service: MockRoleHierarchyService;

    beforeEach(async () => {
        const moduleRef = await Test.createTestingModule({
            providers: [MockRoleHierarchyService]
        }).compile();

        service = moduleRef.get<MockRoleHierarchyService>(MockRoleHierarchyService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    it('should have the expected methods', () => {
        // Just test that the methods exist, not their implementation
        expect(typeof service.expandRoles).toBe('function');
        expect(typeof service.hasAllRoles).toBe('function');
        expect(typeof service.hasAnyRole).toBe('function');
    });
});
