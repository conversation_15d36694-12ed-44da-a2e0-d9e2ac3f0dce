import { <PERSON>, Post, Body, Get, Req, Res, Headers } from '@nestjs/common';
import { Response, Request } from 'express';

/**
 * Simplified controller for testing auth endpoints
 * This avoids dependencies on real databases and external services
 */
@Controller('auth')
export class TestAuthController {
    // Mock user and tenant data
    private users = new Map();
    private tenants = new Map();
    private tokens = new Map();

    constructor() {
        // Initialize with some test data
        this.users.set('admin', {
            id: '1234-5678',
            username: 'admin',
            email: '<EMAIL>',
            roles: ['admin']
        });
    }

    @Get('health')
    health() {
        return { status: 'ok' };
    }

    @Post('create-tenant')
    createTenant(@Body() data: any) {
        const { name, realm, adminUsername, adminEmail, adminPassword } = data;

        // Check if tenant already exists
        if (this.tenants.has(realm)) {
            return {
                statusCode: 409,
                message: `Tenant with realm '${realm}' already exists`
            };
        }

        // Create new tenant
        const tenantId = `tenant-${Date.now()}`;
        this.tenants.set(realm, { id: tenantId, name, realm });

        // Create admin user for tenant
        const userId = `user-${Date.now()}`;
        this.users.set(adminUsername, {
            id: userId,
            username: adminUsername,
            email: adminEmail,
            password: adminPassword,
            roles: ['admin'],
            tenantId
        });

        return {
            id: tenantId,
            name,
            realm,
            createdAt: new Date().toISOString()
        };
    }

    @Post('login')
    login(@Body() data: any, @Res({ passthrough: true }) response: Response) {
        const { username, password, realm } = data;

        // Check if tenant exists
        if (!this.tenants.has(realm)) {
            return {
                statusCode: 404,
                message: `Tenant with realm '${realm}' not found`
            };
        }

        // Check if user exists and password matches
        const user = this.users.get(username);
        if (!user || user.password !== password) {
            return {
                statusCode: 401,
                message: 'Invalid credentials'
            };
        }

        // Generate tokens
        const accessToken = `access-${Date.now()}`;
        const refreshToken = `refresh-${Date.now()}`;

        // Store tokens for verification later
        this.tokens.set(accessToken, {
            userId: user.id,
            username,
            exp: Date.now() + 3600000 // 1 hour expiry
        });

        this.tokens.set(refreshToken, {
            userId: user.id,
            username,
            exp: Date.now() + 86400000 // 24 hours expiry
        });

        // Set cookies
        response.cookie('accessToken', accessToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV !== 'development',
            maxAge: 3600000 // 1 hour
        });

        response.cookie('refreshToken', refreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV !== 'development',
            maxAge: 86400000 // 24 hours
        });

        response.cookie('realm', realm, {
            httpOnly: true,
            secure: process.env.NODE_ENV !== 'development',
            maxAge: 86400000 // 24 hours
        });

        return {
            accessToken,
            refreshToken,
            expiresIn: 3600,
            tokenType: 'Bearer'
        };
    }

    @Get('me')
    getProfile(@Req() request: Request, @Headers('authorization') auth: string) {
        if (!auth || !auth.startsWith('Bearer ')) {
            return {
                statusCode: 401,
                message: 'Unauthorized'
            };
        }

        const token = auth.substring(7); // Remove 'Bearer ' prefix
        const tokenData = this.tokens.get(token);

        if (!tokenData || tokenData.exp < Date.now()) {
            return {
                statusCode: 401,
                message: 'Token expired or invalid'
            };
        }

        const user = this.users.get(tokenData.username);

        return {
            id: user.id,
            username: user.username,
            email: user.email,
            roles: user.roles
        };
    }

    @Post('refresh')
    refreshToken(@Body() data: any, @Res({ passthrough: true }) response: Response) {
        const { refreshToken, realm } = data;

        // Check if tenant exists
        if (!this.tenants.has(realm)) {
            return {
                statusCode: 404,
                message: `Tenant with realm '${realm}' not found`
            };
        }

        // Check if refresh token is valid
        const tokenData = this.tokens.get(refreshToken);
        if (!tokenData || tokenData.exp < Date.now()) {
            return {
                statusCode: 401,
                message: 'Invalid or expired refresh token'
            };
        }

        // Generate new tokens
        const newAccessToken = `access-${Date.now()}`;
        const newRefreshToken = `refresh-${Date.now()}`;

        // Store new tokens
        this.tokens.set(newAccessToken, {
            userId: tokenData.userId,
            username: tokenData.username,
            exp: Date.now() + 3600000 // 1 hour expiry
        });

        this.tokens.set(newRefreshToken, {
            userId: tokenData.userId,
            username: tokenData.username,
            exp: Date.now() + 86400000 // 24 hours expiry
        });

        // Invalidate old refresh token
        this.tokens.delete(refreshToken);

        // Set cookies
        response.cookie('accessToken', newAccessToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV !== 'development',
            maxAge: 3600000 // 1 hour
        });

        response.cookie('refreshToken', newRefreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV !== 'development',
            maxAge: 86400000 // 24 hours
        });

        return {
            accessToken: newAccessToken,
            refreshToken: newRefreshToken,
            expiresIn: 3600,
            tokenType: 'Bearer'
        };
    }

    @Post('logout')
    logout(
        @Req() request: Request,
        @Res({ passthrough: true }) response: Response,
        @Headers('authorization') auth: string
    ) {
        if (auth && auth.startsWith('Bearer ')) {
            const token = auth.substring(7);
            this.tokens.delete(token);
        }

        // Clear cookies
        response.clearCookie('accessToken');
        response.clearCookie('refreshToken');
        response.clearCookie('realm');

        return {
            message: 'Logged out successfully'
        };
    }
}
