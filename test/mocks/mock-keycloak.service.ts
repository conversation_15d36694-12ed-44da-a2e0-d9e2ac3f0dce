import { Injectable } from '@nestjs/common';
// import { v4 as uuidv4 } from 'uuid';
// import * as jwt from 'jsonwebtoken';

/**
 * Mock implementation of the KeycloakService for integration tests
 * This avoids the need to connect to a real Keycloak instance during tests
 */
@Injectable()
export class MockKeycloakService {
    // private readonly logger = new Logger(MockKeycloakService.name);
    // private readonly SECRET_KEY = 'integration-test-secret-key';
    // private readonly tenants = new Map();
    // private readonly users = new Map();
    /**
     * Creates a realm in the mock Keycloak
     */
    // async createRealm(realmName: string, displayName: string): Promise<string> {
    //     this.logger.debug(`Creating mock realm: ${realmName}`);
    //     const realmId = uuidv4();
    //     this.tenants.set(realmName, {
    //         id: realmId,
    //         name: realmName,
    //         displayName: displayName,
    //         users: new Map(),
    //         roles: new Map([
    //             ['admin', { id: uuidv4(), name: 'admin', description: 'Administrator' }],
    //             ['user', { id: uuidv4(), name: 'user', description: 'Regular user' }]
    //         ])
    //     });
    //     return realmId;
    // }
    // /**
    //  * Creates a user in the specified realm
    //  */
    // async createUser(realm: string, userDetails: any): Promise<string> {
    //     this.logger.debug(`Creating mock user in realm ${realm}: ${userDetails.username}`);
    //     if (!this.tenants.has(realm)) {
    //         throw new Error(`Realm ${realm} does not exist`);
    //     }
    //     const userId = uuidv4();
    //     const user = {
    //         id: userId,
    //         username: userDetails.username,
    //         email: userDetails.email,
    //         firstName: userDetails.firstName || '',
    //         lastName: userDetails.lastName || '',
    //         enabled: true,
    //         roles: userDetails.roles || ['user'],
    //         credentials: [{
    //             type: 'password',
    //             value: userDetails.password || 'defaultPassword',
    //             temporary: false
    //         }]
    //     };
    //     this.tenants.get(realm).users.set(userDetails.username, user);
    //     this.users.set(userId, { ...user, realm });
    //     return userId;
    // }
    // /**
    //  * Authenticate a user and return tokens
    //  */
    // async authenticate(realm: string, username: string, password: string): Promise<any> {
    //     this.logger.debug(`Authenticating user ${username} in realm ${realm}`);
    //     if (!this.tenants.has(realm)) {
    //         throw new Error(`Realm ${realm} does not exist`);
    //     }
    //     const realmData = this.tenants.get(realm);
    //     if (!realmData.users.has(username)) {
    //         throw new Error(`User ${username} not found in realm ${realm}`);
    //     }
    //     const user = realmData.users.get(username);
    //     const validPassword = user.credentials.some(c => c.type === 'password' && c.value === password);
    //     if (!validPassword) {
    //         throw new Error('Invalid credentials');
    //     }
    //     // Generate tokens
    //     const accessToken = this.generateToken(user, realm);
    //     const refreshToken = this.generateRefreshToken(user, realm);
    //     return {
    //         access_token: accessToken,
    //         refresh_token: refreshToken,
    //         expires_in: 300,
    //         refresh_expires_in: 1800,
    //         token_type: 'Bearer'
    //     };
    // }
    // /**
    //  * Refresh an access token using a refresh token
    //  */
    // async refreshToken(realm: string, refreshToken: string): Promise<any> {
    //     this.logger.debug(`Refreshing token in realm ${realm}`);
    //     try {
    //         const decoded = jwt.verify(refreshToken, this.SECRET_KEY) as any;
    //         if (!decoded || decoded.type !== 'refresh') {
    //             throw new Error('Invalid refresh token');
    //         }
    //         const userId = decoded.sub;
    //         if (!this.users.has(userId)) {
    //             throw new Error('User not found');
    //         }
    //         const user = this.users.get(userId);
    //         const accessToken = this.generateToken(user, realm);
    //         const newRefreshToken = this.generateRefreshToken(user, realm);
    //         return {
    //             access_token: accessToken,
    //             refresh_token: newRefreshToken,
    //             expires_in: 300,
    //             refresh_expires_in: 1800,
    //             token_type: 'Bearer'
    //         };
    //     } catch (error) {
    //         this.logger.error(`Error refreshing token: ${error.message}`);
    //         throw new Error('Invalid refresh token');
    //     }
    // }
    // /**
    //  * Verify a token and return the decoded payload
    //  */
    // async verifyToken(token: string): Promise<any> {
    //     try {
    //         return jwt.verify(token, this.SECRET_KEY);
    //     } catch (error) {
    //         this.logger.error(`Error verifying token: ${error.message}`);
    //         throw new Error('Invalid token');
    //     }
    // }
    // /**
    //  * Get a user's profile from their ID
    //  */
    // async getUserById(realm: string, userId: string): Promise<any> {
    //     if (!this.users.has(userId)) {
    //         throw new Error('User not found');
    //     }
    //     const user = this.users.get(userId);
    //     if (user.realm !== realm) {
    //         throw new Error('User not in specified realm');
    //     }
    //     return {
    //         id: user.id,
    //         username: user.username,
    //         email: user.email,
    //         firstName: user.firstName,
    //         lastName: user.lastName,
    //         enabled: user.enabled,
    //         roles: user.roles
    //     };
    // }
    // /**
    //  * Get a user's profile from their username
    //  */
    // async getUserByUsername(realm: string, username: string): Promise<any> {
    //     if (!this.tenants.has(realm)) {
    //         throw new Error(`Realm ${realm} does not exist`);
    //     }
    //     const realmData = this.tenants.get(realm);
    //     if (!realmData.users.has(username)) {
    //         return null;
    //     }
    //     const user = realmData.users.get(username);
    //     return {
    //         id: user.id,
    //         username: user.username,
    //         email: user.email,
    //         firstName: user.firstName,
    //         lastName: user.lastName,
    //         enabled: user.enabled,
    //         roles: user.roles
    //     };
    // }
    // /**
    //  * Assign roles to a user
    //  */
    // async assignRolesToUser(realm: string, userId: string, roles: string[]): Promise<void> {
    //     if (!this.users.has(userId)) {
    //         throw new Error('User not found');
    //     }
    //     const user = this.users.get(userId);
    //     if (user.realm !== realm) {
    //         throw new Error('User not in specified realm');
    //     }
    //     // Add new roles while preserving existing ones
    //     user.roles = [...new Set([...user.roles, ...roles])];
    // }
    // /**
    //  * Generate a JWT access token
    //  */
    // private generateToken(user: any, realm: string): string {
    //     const payload = {
    //         sub: user.id,
    //         preferred_username: user.username,
    //         email: user.email,
    //         given_name: user.firstName,
    //         family_name: user.lastName,
    //         email_verified: true,
    //         realm_access: {
    //             realm: realm,
    //             roles: user.roles
    //         },
    //         resource_access: {
    //             account: {
    //                 roles: ['manage-account']
    //             }
    //         },
    //         systemUserId: user.id,
    //         type: 'access',
    //         exp: Math.floor(Date.now() / 1000) + 300 // 5 minutes
    //     };
    //     return jwt.sign(payload, this.SECRET_KEY);
    // }
    // /**
    //  * Generate a JWT refresh token
    //  */
    // private generateRefreshToken(user: any, realm: string): string {
    //     const payload = {
    //         sub: user.id,
    //         type: 'refresh',
    //         exp: Math.floor(Date.now() / 1000) + 1800 // 30 minutes
    //     };
    //     return jwt.sign(payload, this.SECRET_KEY);
    // }
}
