import axios, { AxiosError, AxiosInstance } from 'axios';
import { Injectable } from '@nestjs/common';

// Simplified error type enum
export enum KeycloakErrorType {
    NETWORK = 'NETWORK',
    CLIENT = 'CLIENT',
    SERVER = 'SERVER',
    UNAUTHORIZED = 'UNAUTHORIZED',
    NOT_FOUND = 'NOT_FOUND',
    TIMEOUT = 'TIMEOUT',
    UNKNOWN = 'UNKNOWN'
}

// Simple health check interface
export interface HealthCheckResult {
    status: 'UP' | 'DOWN';
    message?: string;
    details?: any;
}

/**
 * Mock implementation of KeycloakHttpService for integration tests
 * This avoids the dependency on the real service which has complex initialization
 */
@Injectable()
export class MockKeycloakHttpService {
    private readonly axiosInstance: AxiosInstance;

    constructor() {
        // Create a axios instance with proper interceptors
        this.axiosInstance = axios.create({
            baseURL: 'http://mock-keycloak:8080',
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        // Ensure interceptors are properly initialized
        if (!this.axiosInstance.interceptors) {
            this.axiosInstance.interceptors = {
                request: {
                    use: () => 0,
                    eject: () => {},
                    clear: () => {}
                },
                response: {
                    use: () => 0,
                    eject: () => {},
                    clear: () => {}
                }
            };
        }

        // Add basic interceptors
        this.axiosInstance.interceptors.request.use(
            (config) => {
                config.headers = config.headers || {};
                return config;
            },
            (error: Error | AxiosError) => Promise.reject(error)
        );

        this.axiosInstance.interceptors.response.use(
            (response) => response,
            (error: Error | AxiosError) => Promise.reject(error)
        );
    }

    // // Mock HTTP methods
    // async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    //     return {} as T;
    // }

    // async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    //     return {} as T;
    // }

    // async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    //     return {} as T;
    // }

    // async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    //     return {} as T;
    // }

    // async postWithFullResponse<T = any>(
    //     url: string,
    //     data?: any,
    //     config?: AxiosRequestConfig
    // ): Promise<AxiosResponse<T>> {
    //     return {
    //         data: {} as T,
    //         status: 200,
    //         statusText: 'OK',
    //         headers: {},
    //         config: {} as any,
    //         request: {}
    //     };
    // }

    // Mock circuit breaker methods
    resetCircuitBreaker = () => {};
    getCircuitState = () => ({ state: 'CLOSED', failureCount: 0 });

    // Mock metrics methods
    getMetrics = () => ({
        requestCount: 0,
        successRate: 100,
        avgLatency: 0,
        errorBreakdown: {},
        circuitState: 'CLOSED'
    });

    resetMetrics = () => {};

    // Mock health check methods
    checkHealth = async (): Promise<HealthCheckResult> => {
        return await Promise.resolve({
            status: 'UP',
            details: { circuitState: 'CLOSED' }
        });
    };

    getInstance = () => this.axiosInstance;

    // Mock additional utility methods that might be needed
    startPeriodicHealthCheck = () => {};
}
