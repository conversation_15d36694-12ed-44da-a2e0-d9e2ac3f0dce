import { Injectable, Logger } from '@nestjs/common';

/**
 * Define app roles locally to avoid import dependencies
 */
export enum AppRole {
    SUPER_ADMIN = 'super_admin',
    ADMIN = 'admin',
    MANAGER = 'manager',
    LAWYER = 'lawyer',
    FINANCE = 'finance',
    PARTNER = 'partner',
    USER = 'user'
}

/**
 * Mock implementation of the RoleHierarchyService for integration tests
 * This avoids the dependency on configuration which may not be available in the test environment
 */
@Injectable()
export class MockRoleHierarchyService {
    private readonly logger = new Logger(MockRoleHierarchyService.name);
    private readonly roleHierarchy: Map<string, string[]> = new Map();

    constructor() {
        this.setDefaultHierarchy();
    }

    /**
     * Set default role hierarchy
     */
    private setDefaultHierarchy(): void {
        // Setup hierarchy using the local AppRole enum
        this.roleHierarchy.set(AppRole.SUPER_ADMIN, [AppRole.ADMIN]);
        this.roleHierarchy.set(AppRole.ADMIN, [
            AppRole.MANAGER,
            AppRole.LAWYER,
            AppRole.FINANCE,
            AppRole.PARTNER
        ]);
        this.roleHierarchy.set(AppRole.MANAGER, [AppRole.USER]);
        this.roleHierarchy.set(AppRole.LAWYER, [AppRole.USER]);
        this.roleHierarchy.set(AppRole.FINANCE, [AppRole.USER]);
        this.roleHierarchy.set(AppRole.PARTNER, [AppRole.USER]);

        this.logger.log('Default role hierarchy set');
    }

    /**
     * Expand a set of roles to include all implied roles
     * @param roles Direct roles assigned to the user
     * @returns Expanded set of roles including all implied roles
     */
    expandRoles(roles: string[]): string[] {
        const result = new Set<string>(roles);
        let added = true;

        // Keep expanding roles until no new roles are added
        while (added) {
            added = false;

            // Create a copy of current result to avoid concurrent modification
            const currentRoles = Array.from(result);

            for (const role of currentRoles) {
                const impliedRoles = this.roleHierarchy.get(role) || [];

                for (const impliedRole of impliedRoles) {
                    if (!result.has(impliedRole)) {
                        result.add(impliedRole);
                        added = true;
                    }
                }
            }
        }

        return Array.from(result);
    }

    /**
     * Check if a user with the given roles has all required roles
     * @param userRoles User's roles including implied roles
     * @param requiredRoles Roles required for access
     * @returns Whether the user has all the required roles
     */
    hasAllRoles(userRoles: string[], requiredRoles: string[]): boolean {
        return requiredRoles.every((role) => userRoles.includes(role));
    }

    /**
     * Check if a user with the given roles has any of the required roles
     * @param userRoles User's roles including implied roles
     * @param requiredRoles Roles required for access
     * @returns Whether the user has any of the required roles
     */
    hasAnyRole(userRoles: string[], requiredRoles: string[]): boolean {
        return requiredRoles.some((role) => userRoles.includes(role));
    }
}
