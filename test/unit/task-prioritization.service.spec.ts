import { Test, TestingModule } from '@nestjs/testing';
import { TaskPrioritizationService } from '../../apps/task-management/src/services/task-prioritization.service';
import { TaskPriority } from '@app/common/typeorm/entities/tenant/task.entity';

describe('TaskPrioritizationService', () => {
    let service: TaskPrioritizationService;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [TaskPrioritizationService]
        }).compile();
        service = module.get<TaskPrioritizationService>(TaskPrioritizationService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('getPriorityValue', () => {
        it('should return correct numeric values for all priority levels', () => {
            // Arrange & Act & Assert
            expect(service.getPriorityValue(TaskPriority.HIGHEST)).toBe(5);
            expect(service.getPriorityValue(TaskPriority.HIGH)).toBe(4);
            expect(service.getPriorityValue(TaskPriority.MEDIUM)).toBe(3);
            expect(service.getPriorityValue(TaskPriority.LOW)).toBe(2);
            expect(service.getPriorityValue(TaskPriority.LOWEST)).toBe(1);
        });

        it('should return MEDIUM (3) for unknown priority values', () => {
            // Arrange
            const unknownPriority = 'UNKNOWN' as TaskPriority;

            // Act
            const result = service.getPriorityValue(unknownPriority);

            // Assert
            expect(result).toBe(3);
        });

        it('should return MEDIUM (3) for null/undefined priority', () => {
            // Arrange & Act & Assert
            expect(service.getPriorityValue(null as any)).toBe(3);
            expect(service.getPriorityValue(undefined as any)).toBe(3);
        });
    });

    describe('suggestPriority', () => {
        it('should return MEDIUM priority when no due date is provided', () => {
            // Arrange
            const task = { title: 'Test Task' };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.MEDIUM);
        });

        it('should return HIGHEST priority for tasks due within 1 day', () => {
            // Arrange
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const task = { title: 'Test Task', dueDate: tomorrow };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.HIGHEST);
        });

        it('should return HIGHEST priority for tasks due today', () => {
            // Arrange
            const today = new Date();
            const task = { title: 'Test Task', dueDate: today };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.HIGHEST);
        });

        it('should return HIGHEST priority for overdue tasks', () => {
            // Arrange
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            const task = { title: 'Test Task', dueDate: yesterday };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.HIGHEST);
        });

        it('should return HIGH priority for tasks due within 3 days', () => {
            // Arrange
            const inThreeDays = new Date();
            inThreeDays.setDate(inThreeDays.getDate() + 3);
            const task = { title: 'Test Task', dueDate: inThreeDays };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.HIGH);
        });

        it('should return HIGH priority for tasks due in 2 days', () => {
            // Arrange
            const inTwoDays = new Date();
            inTwoDays.setDate(inTwoDays.getDate() + 2);
            const task = { title: 'Test Task', dueDate: inTwoDays };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.HIGH);
        });

        it('should return MEDIUM priority for tasks due within 14 days', () => {
            // Arrange
            const inTenDays = new Date();
            inTenDays.setDate(inTenDays.getDate() + 10);
            const task = { title: 'Test Task', dueDate: inTenDays };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.MEDIUM);
        });

        it('should return MEDIUM priority for tasks due in exactly 14 days', () => {
            // Arrange
            const inFourteenDays = new Date();
            inFourteenDays.setDate(inFourteenDays.getDate() + 14);
            const task = { title: 'Test Task', dueDate: inFourteenDays };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.MEDIUM);
        });

        it('should return LOW priority for tasks due within 30 days', () => {
            // Arrange
            const inTwentyDays = new Date();
            inTwentyDays.setDate(inTwentyDays.getDate() + 20);
            const task = { title: 'Test Task', dueDate: inTwentyDays };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.LOW);
        });

        it('should return LOW priority for tasks due in exactly 30 days', () => {
            // Arrange
            const inThirtyDays = new Date();
            inThirtyDays.setDate(inThirtyDays.getDate() + 30);
            const task = { title: 'Test Task', dueDate: inThirtyDays };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.LOW);
        });

        it('should return LOWEST priority for tasks due after 30 days', () => {
            // Arrange
            const inFortyDays = new Date();
            inFortyDays.setDate(inFortyDays.getDate() + 40);
            const task = { title: 'Test Task', dueDate: inFortyDays };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.LOWEST);
        });

        it('should return LOWEST priority for tasks due in 60 days', () => {
            // Arrange
            const inSixtyDays = new Date();
            inSixtyDays.setDate(inSixtyDays.getDate() + 60);
            const task = { title: 'Test Task', dueDate: inSixtyDays };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.LOWEST);
        });

        it('should handle due date as string and return correct priority', () => {
            // Arrange
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const task = { title: 'Test Task', dueDate: tomorrow.toISOString() };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.HIGHEST);
        });

        it('should handle due date as timestamp and return correct priority', () => {
            // Arrange
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const task = { title: 'Test Task', dueDate: tomorrow.getTime() };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.HIGHEST);
        });

        it('should return LOWEST priority when date parsing fails', () => {
            // Arrange
            const task = { title: 'Test Task', dueDate: 'invalid-date' };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.LOWEST);
        });

        it('should return MEDIUM priority when due date is null', () => {
            // Arrange
            const task = { title: 'Test Task', dueDate: null };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.MEDIUM);
        });

        it('should return MEDIUM priority when due date is undefined', () => {
            // Arrange
            const task = { title: 'Test Task', dueDate: undefined };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.MEDIUM);
        });

        it('should handle edge case of very large due date', () => {
            // Arrange
            const farFuture = new Date();
            farFuture.setFullYear(farFuture.getFullYear() + 10); // 10 years from now
            const task = { title: 'Test Task', dueDate: farFuture };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.LOWEST);
        });

        it('should handle edge case of very old due date', () => {
            // Arrange
            const farPast = new Date();
            farPast.setFullYear(farPast.getFullYear() - 10); // 10 years ago
            const task = { title: 'Test Task', dueDate: farPast };

            // Act
            const result = service.suggestPriority(task as any);

            // Assert
            expect(result).toBe(TaskPriority.HIGHEST);
        });
    });

    describe('priority calculation accuracy', () => {
        it('should calculate days correctly for various scenarios', () => {
            // Mock the current date to ensure consistent results
            const mockNow = new Date('2024-01-01T12:00:00Z');
            jest.useFakeTimers();
            jest.setSystemTime(mockNow);

            try {
                // 1 day from now (2024-01-02)
                const oneDay = new Date('2024-01-02T12:00:00Z');
                expect(service.suggestPriority({ dueDate: oneDay } as any)).toBe(
                    TaskPriority.HIGHEST
                );

                // 4 days from now (2024-01-05) - should be MEDIUM, not HIGH, since 4 > 3
                const fourDays = new Date('2024-01-05T12:00:00Z');
                expect(service.suggestPriority({ dueDate: fourDays } as any)).toBe(
                    TaskPriority.MEDIUM
                );

                // 15 days from now (2024-01-16) - should be LOW, since 15 > 14
                const fifteenDays = new Date('2024-01-16T12:00:00Z');
                expect(service.suggestPriority({ dueDate: fifteenDays } as any)).toBe(
                    TaskPriority.LOW
                );

                // 31 days from now (2024-02-01) - should be LOWEST, since 31 > 30
                const thirtyOneDays = new Date('2024-02-01T12:00:00Z');
                expect(service.suggestPriority({ dueDate: thirtyOneDays } as any)).toBe(
                    TaskPriority.LOWEST
                );

                // 100 days from now (2024-04-10)
                const hundredDays = new Date('2024-04-10T12:00:00Z');
                expect(service.suggestPriority({ dueDate: hundredDays } as any)).toBe(
                    TaskPriority.LOWEST
                );
            } finally {
                // Restore original timers
                jest.useRealTimers();
            }
        });
    });
});
