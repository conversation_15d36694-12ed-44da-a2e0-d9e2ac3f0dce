import { Test, TestingModule } from '@nestjs/testing';
import { AuthNotificationService } from '../../apps/auth/src/services/auth-notification.service';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';

describe('AuthNotificationService', () => {
    let service: AuthNotificationService;
    let messageProducer: MessageProducerService;

    beforeEach(async () => {
        const mockMessageProducer = {
            enqueueMessage: jest.fn().mockResolvedValue({ jobId: 'test-job', status: 'queued' })
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                AuthNotificationService,
                {
                    provide: MessageProducerService,
                    useValue: mockMessageProducer
                }
            ]
        }).compile();

        service = module.get<AuthNotificationService>(AuthNotificationService);
        messageProducer = module.get<MessageProducerService>(MessageProducerService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('sendWelcomeNotification', () => {
        it('should send welcome notification with correct parameters', async () => {
            await service.sendWelcomeNotification(
                '<EMAIL>',
                'John Doe',
                'Test Law Firm',
                'tenant-123',
                'https://app.example.com/login',
                'Admin User',
                undefined
            );

            expect(messageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'welcome',
                    tenantName: 'Test Law Firm',
                    recipientName: 'John Doe',
                    loginUrl: 'https://app.example.com/login',
                    inviterName: 'Admin User'
                }),
                metadata: {
                    notificationType: 'user-welcome',
                    hasTemporaryPassword: false
                }
            });
        });

        it('should handle temporary password in welcome notification', async () => {
            await service.sendWelcomeNotification(
                '<EMAIL>',
                'John Doe',
                'Test Law Firm',
                'tenant-123',
                'https://app.example.com/login',
                undefined,
                'temp123'
            );

            const call = (messageProducer.enqueueMessage as jest.Mock).mock.calls[0][0];
            expect(call.variables.welcomeMessage).toContain('temporary password');
            expect(call.metadata.hasTemporaryPassword).toBe(true);
        });
    });

    describe('sendPasswordResetNotification', () => {
        it('should send password reset notification', async () => {
            await service.sendPasswordResetNotification(
                '<EMAIL>',
                'John Doe',
                'Test Law Firm',
                'tenant-123',
                'https://app.example.com/reset?token=abc123',
                '24 hours'
            );

            expect(messageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'password-reset',
                    tenantName: 'Test Law Firm',
                    recipientName: 'John Doe',
                    resetUrl: 'https://app.example.com/reset?token=abc123',
                    expirationTime: '24 hours'
                }),
                metadata: {
                    notificationType: 'password-reset'
                }
            });
        });
    });

    describe('sendUserInvitationNotification', () => {
        it('should send user invitation notification', async () => {
            await service.sendUserInvitationNotification(
                '<EMAIL>',
                'Jane Smith',
                'Test Law Firm',
                'tenant-123',
                'https://app.example.com/invite?token=xyz789',
                'John Admin',
                'lawyer'
            );

            expect(messageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'user-invitation',
                    tenantName: 'Test Law Firm',
                    recipientName: 'Jane Smith',
                    inviterName: 'John Admin',
                    role: 'Lawyer'
                }),
                metadata: {
                    notificationType: 'user-invitation',
                    roleGroupKey: 'lawyer'
                }
            });
        });
    });

    describe('sendTenantCreationNotification', () => {
        it('should send tenant creation notification', async () => {
            await service.sendTenantCreationNotification(
                '<EMAIL>',
                'Admin User',
                'New Law Firm',
                'tenant-456',
                'https://app.example.com/new-law-firm',
                'client-id-123',
                'admin123'
            );

            expect(messageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'tenant-456',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'generic-notification',
                    tenantName: 'New Law Firm',
                    recipientName: 'Admin User',
                    message: expect.stringContaining('Congratulations')
                }),
                metadata: {
                    notificationType: 'tenant-created',
                    clientId: 'client-id-123'
                }
            });
        });
    });

    describe('role group name formatting', () => {
        it('should format standard role group names correctly', () => {
            const formatMethod = (service as any).formatRoleGroupName;

            expect(formatMethod('super_admin')).toBe('Super Administrator');
            expect(formatMethod('lawyer')).toBe('Lawyer');
            expect(formatMethod('paralegal')).toBe('Paralegal');
            expect(formatMethod('custom_role')).toBe('Custom Role');
        });
    });

    describe('error handling', () => {
        it('should handle messageProducer errors gracefully', async () => {
            const error = new Error('Queue service error');
            (messageProducer.enqueueMessage as jest.Mock).mockRejectedValueOnce(error);

            const loggerErrorSpy = jest
                .spyOn((service as any).logger, 'error')
                .mockImplementation();

            await service.sendWelcomeNotification(
                '<EMAIL>',
                'John Doe',
                'Test Tenant',
                'tenant-123',
                'https://example.com'
            );

            expect(loggerErrorSpy).toHaveBeenCalledWith(
                expect.stringContaining('Failed to send welcome notification'),
                error.stack
            );

            loggerErrorSpy.mockRestore();
        });
    });
});
