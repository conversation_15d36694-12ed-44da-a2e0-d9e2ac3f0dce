import { Test, TestingModule } from '@nestjs/testing';
import { CaseAssignmentService } from '../../apps/case-management/src/services/case-assignment.service';
import { CaseAssignmentRepository } from '../../apps/case-management/src/repositories/case-assignment.repository';
import { CaseRepository } from '../../apps/case-management/src/repositories/case.repository';
import { CaseAuditService } from '../../apps/case-management/src/services/case-audit.service';
import { CaseNotificationService } from '../../apps/case-management/src/services/case-notification.service';
import { NotFoundException } from '@nestjs/common';
import { AssignCaseDto } from '../../apps/case-management/src/dto/assign-case.dto';
import { Request } from 'express';

describe('CaseAssignmentService', () => {
    let service: CaseAssignmentService;

    // Mock repositories
    const mockCaseRepository = {
        findOne: jest.fn(),
        findByAssignedUser: jest.fn()
    };

    const mockCaseAssignmentRepository = {
        findOne: jest.fn(),
        find: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        delete: jest.fn(),
        findByCaseId: jest.fn(),
        findActiveAssignment: jest.fn()
    };

    // Mock audit service
    const mockCaseAuditService = {
        logAssignment: jest.fn(),
        logUnassignment: jest.fn(),
        logAction: jest.fn()
    };

    // Mock notification service
    const mockCaseNotificationService = {
        sendAssignmentNotification: jest.fn(),
        sendUnassignmentNotification: jest.fn()
    };

    // Mock request
    const mockRequest = {
        ip: '127.0.0.1',
        headers: {
            'x-forwarded-for': '********'
        }
    } as unknown as Request;

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                CaseAssignmentService,
                { provide: CaseRepository, useValue: mockCaseRepository },
                { provide: CaseAssignmentRepository, useValue: mockCaseAssignmentRepository },
                { provide: CaseAuditService, useValue: mockCaseAuditService },
                { provide: CaseNotificationService, useValue: mockCaseNotificationService }
            ]
        }).compile();

        service = module.get<CaseAssignmentService>(CaseAssignmentService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('assignCase', () => {
        it('should assign a case to a user', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const userId = 'user-id-123';
            const userName = 'Test User';

            const assignCaseDto: AssignCaseDto = {
                userId,
                userName,
                notes: 'Test assignment'
            };

            const mockCase = { id: caseId, title: 'Test Case' };
            const mockAssignment = {
                id: 'assignment-id-123',
                caseId,
                userId,
                userName,
                assignedBy: userId,
                notes: assignCaseDto.notes,
                isActive: true
            };

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseAssignmentRepository.findActiveAssignment.mockResolvedValue(null);
            mockCaseAssignmentRepository.create.mockReturnValue(mockAssignment);
            mockCaseAssignmentRepository.save.mockResolvedValue(mockAssignment);

            // Act
            const result = await service.assignCase(
                caseId,
                assignCaseDto,
                userId,
                userName,
                mockRequest
            );

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAssignmentRepository.findActiveAssignment).toHaveBeenCalledWith(
                caseId,
                userId
            );
            expect(mockCaseAssignmentRepository.create).toHaveBeenCalledWith({
                caseId,
                userId,
                userName,
                assignedBy: userId,
                notes: assignCaseDto.notes,
                isActive: true
            });
            expect(mockCaseAssignmentRepository.save).toHaveBeenCalledWith(mockAssignment);
            expect(mockCaseAuditService.logAssignment).toHaveBeenCalledWith(
                caseId,
                userId,
                userName,
                mockRequest,
                userId,
                userName
            );
            expect(result).toEqual(mockAssignment);
        });

        it('should return existing assignment if already assigned', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const userId = 'user-id-123';
            const userName = 'Test User';

            const assignCaseDto: AssignCaseDto = {
                userId,
                userName,
                notes: 'Test assignment'
            };

            const mockCase = { id: caseId, title: 'Test Case' };
            const existingAssignment = {
                id: 'assignment-id-123',
                caseId,
                userId,
                userName,
                assignedBy: 'previous-user',
                notes: 'Previous assignment',
                isActive: true
            };

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseAssignmentRepository.findActiveAssignment.mockResolvedValue(existingAssignment);

            // Act
            const result = await service.assignCase(
                caseId,
                assignCaseDto,
                userId,
                userName,
                mockRequest
            );

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAssignmentRepository.findActiveAssignment).toHaveBeenCalledWith(
                caseId,
                userId
            );
            expect(mockCaseAssignmentRepository.create).not.toHaveBeenCalled();
            expect(mockCaseAssignmentRepository.save).not.toHaveBeenCalled();
            expect(mockCaseAuditService.logAssignment).not.toHaveBeenCalled();
            expect(result).toEqual(existingAssignment);
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const caseId = 'non-existent-case-id';
            const userId = 'user-id-123';
            const userName = 'Test User';

            const assignCaseDto: AssignCaseDto = {
                userId,
                userName,
                notes: 'Test assignment'
            };

            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.assignCase(caseId, assignCaseDto, userId, userName, mockRequest)
            ).rejects.toThrow(NotFoundException);
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAssignmentRepository.findActiveAssignment).not.toHaveBeenCalled();
        });
    });

    describe('unassignCase', () => {
        it('should unassign a user from a case', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const userId = 'user-id-123';
            const userName = 'Test User';

            const mockCase = { id: caseId, title: 'Test Case' };
            const mockAssignment = {
                id: 'assignment-id-123',
                caseId,
                userId,
                userName,
                isActive: true
            };

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseAssignmentRepository.findActiveAssignment.mockResolvedValue(mockAssignment);
            mockCaseAssignmentRepository.save.mockResolvedValue({
                ...mockAssignment,
                isActive: false
            });

            // Act
            await service.unassignCase(caseId, userId, userId, userName, mockRequest);

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAssignmentRepository.findActiveAssignment).toHaveBeenCalledWith(
                caseId,
                userId
            );
            expect(mockCaseAssignmentRepository.save).toHaveBeenCalledWith({
                ...mockAssignment,
                isActive: false
            });
            expect(mockCaseAuditService.logUnassignment).toHaveBeenCalledWith(
                caseId,
                userId,
                userName,
                mockRequest,
                userId,
                mockAssignment.userName
            );
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const caseId = 'non-existent-case-id';
            const userId = 'user-id-123';
            const userName = 'Test User';

            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.unassignCase(caseId, userId, userId, userName, mockRequest)
            ).rejects.toThrow(NotFoundException);
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAssignmentRepository.findActiveAssignment).not.toHaveBeenCalled();
        });

        it('should throw NotFoundException if assignment not found', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const userId = 'user-id-123';
            const userName = 'Test User';

            const mockCase = { id: caseId, title: 'Test Case' };

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseAssignmentRepository.findActiveAssignment.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.unassignCase(caseId, userId, userId, userName, mockRequest)
            ).rejects.toThrow(NotFoundException);
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAssignmentRepository.findActiveAssignment).toHaveBeenCalledWith(
                caseId,
                userId
            );
        });
    });

    describe('getCaseAssignments', () => {
        it('should return all active assignments for a case', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const mockCase = { id: caseId, title: 'Test Case' };
            const mockAssignments = [
                {
                    id: 'assignment-1',
                    caseId,
                    userId: 'user-1',
                    userName: 'User 1',
                    isActive: true
                },
                { id: 'assignment-2', caseId, userId: 'user-2', userName: 'User 2', isActive: true }
            ];

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseAssignmentRepository.findByCaseId.mockResolvedValue(mockAssignments);

            // Act
            const result = await service.getCaseAssignments(caseId);

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAssignmentRepository.findByCaseId).toHaveBeenCalledWith(caseId);
            expect(result).toEqual(mockAssignments);
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const caseId = 'non-existent-case-id';
            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getCaseAssignments(caseId)).rejects.toThrow(NotFoundException);
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAssignmentRepository.findByCaseId).not.toHaveBeenCalled();
        });
    });

    // Test for case assignments by user ID
    describe('getCasesByAssignedUser', () => {
        it('should return all cases assigned to a user', async () => {
            // Arrange
            const userId = 'user-id-123';
            const mockCases = [
                { id: 'case-1', title: 'Case 1' },
                { id: 'case-2', title: 'Case 2' }
            ];

            mockCaseRepository.findByAssignedUser.mockResolvedValue(mockCases);

            // Act
            const result = await mockCaseRepository.findByAssignedUser(userId);

            // Assert
            expect(mockCaseRepository.findByAssignedUser).toHaveBeenCalledWith(userId);
            expect(result).toEqual(mockCases);
        });
    });
});
