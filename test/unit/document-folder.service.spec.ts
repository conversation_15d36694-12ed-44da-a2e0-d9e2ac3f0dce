import { Test, TestingModule } from '@nestjs/testing';
import { DocumentFolderService } from '../../apps/document-engine/src/document/services/document-folder.service';
import { DocumentFolderRepository } from '../../apps/document-engine/src/repositories/document-folder.repository';
import { DocumentFolder } from '@app/common/typeorm/entities';

describe('DocumentFolderService', () => {
    let service: DocumentFolderService;
    // let folderRepository: DocumentFolderRepository;

    // Mock repository
    const mockFolderRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOneBy: jest.fn(),
        findByCaseId: jest.fn(),
        findRootFoldersByCaseId: jest.fn(),
        findChildFolders: jest.fn(),
        removeById: jest.fn(),
        countByFolderId: jest.fn(),
        searchInCase: jest.fn()
    };

    // Mock folder data
    const mockFolder = {
        id: 'folder-123',
        name: 'Test Folder',
        description: 'Test description',
        caseId: 'case-123',
        parentFolderId: null,
        path: '/Test Folder',
        createdBy: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date()
    } as unknown as DocumentFolder;

    const mockChildFolder = {
        id: 'child-folder-456',
        name: 'Child Folder',
        description: 'Child description',
        caseId: 'case-123',
        parentFolderId: 'folder-123',
        path: '/Test Folder/Child Folder',
        createdBy: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date()
    } as unknown as DocumentFolder;

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                DocumentFolderService,
                { provide: DocumentFolderRepository, useValue: mockFolderRepository }
            ]
        }).compile();

        service = module.get<DocumentFolderService>(DocumentFolderService);
        // folderRepository = module.get<DocumentFolderRepository>(DocumentFolderRepository);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createFolder', () => {
        it('should create a root folder successfully', async () => {
            // Arrange
            const createData = {
                name: 'New Folder',
                description: 'New folder description',
                caseId: 'case-123',
                createdBy: 'user-123'
            };

            mockFolderRepository.create.mockReturnValue(mockFolder);
            mockFolderRepository.save.mockResolvedValue(mockFolder);

            // Act
            const result = await service.createFolder(createData);

            // Assert
            expect(mockFolderRepository.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    name: createData.name,
                    description: createData.description,
                    caseId: createData.caseId,
                    parentFolderId: undefined,
                    path: `/${createData.name}`,
                    createdBy: createData.createdBy
                })
            );
            expect(mockFolderRepository.save).toHaveBeenCalledWith(mockFolder);
            expect(result).toEqual(mockFolder);
        });

        it('should create a child folder with parent path', async () => {
            // Arrange
            const createData = {
                name: 'Child Folder',
                caseId: 'case-123',
                parentFolderId: 'parent-folder-123',
                createdBy: 'user-123'
            };

            const parentFolder = {
                ...mockFolder,
                path: '/Parent Folder'
            };

            const childFolder = {
                ...mockChildFolder,
                path: '/Parent Folder/Child Folder'
            };

            mockFolderRepository.findOneBy.mockResolvedValue(parentFolder);
            mockFolderRepository.create.mockReturnValue(childFolder);
            mockFolderRepository.save.mockResolvedValue(childFolder);

            // Act
            const result = await service.createFolder(createData);

            // Assert
            expect(mockFolderRepository.findOneBy).toHaveBeenCalledWith({
                id: createData.parentFolderId
            });
            expect(mockFolderRepository.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    path: '/Parent Folder/Child Folder'
                })
            );
            expect(result).toEqual(childFolder);
        });

        it('should create child folder with root path when parent not found', async () => {
            // Arrange
            const createData = {
                name: 'Child Folder',
                caseId: 'case-123',
                parentFolderId: 'non-existent-parent',
                createdBy: 'user-123'
            };

            mockFolderRepository.findOneBy.mockResolvedValue(null);
            mockFolderRepository.create.mockReturnValue(mockChildFolder);
            mockFolderRepository.save.mockResolvedValue(mockChildFolder);

            // Act
            const result = await service.createFolder(createData);

            // Assert
            expect(mockFolderRepository.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    path: '/Child Folder'
                })
            );
            expect(result).toEqual(mockChildFolder);
        });
    });

    describe('findAllByCase', () => {
        it('should return all folders for a case', async () => {
            // Arrange
            const caseId = 'case-123';
            const folders = [mockFolder, mockChildFolder];
            mockFolderRepository.findByCaseId.mockResolvedValue(folders);

            // Act
            const result = await service.findAllByCase(caseId);

            // Assert
            expect(mockFolderRepository.findByCaseId).toHaveBeenCalledWith(caseId);
            expect(result).toEqual(folders);
        });
    });

    describe('findRootFolders', () => {
        it('should return root folders for a case', async () => {
            // Arrange
            const caseId = 'case-123';
            const rootFolders = [mockFolder];
            mockFolderRepository.findRootFoldersByCaseId.mockResolvedValue(rootFolders);

            // Act
            const result = await service.findRootFolders(caseId);

            // Assert
            expect(mockFolderRepository.findRootFoldersByCaseId).toHaveBeenCalledWith(caseId);
            expect(result).toEqual(rootFolders);
        });
    });

    describe('findChildFolders', () => {
        it('should return child folders for a parent', async () => {
            // Arrange
            const parentFolderId = 'folder-123';
            const childFolders = [mockChildFolder];
            mockFolderRepository.findChildFolders.mockResolvedValue(childFolders);

            // Act
            const result = await service.findChildFolders(parentFolderId);

            // Assert
            expect(mockFolderRepository.findChildFolders).toHaveBeenCalledWith(parentFolderId);
            expect(result).toEqual(childFolders);
        });
    });

    describe('getFolderById', () => {
        it('should return folder when found', async () => {
            // Arrange
            const folderId = 'folder-123';
            mockFolderRepository.findOneBy.mockResolvedValue(mockFolder);

            // Act
            const result = await service.getFolderById(folderId);

            // Assert
            expect(mockFolderRepository.findOneBy).toHaveBeenCalledWith({ id: folderId });
            expect(result).toEqual(mockFolder);
        });

        it('should return null when folder not found', async () => {
            // Arrange
            const folderId = 'non-existent';
            mockFolderRepository.findOneBy.mockResolvedValue(null);

            // Act
            const result = await service.getFolderById(folderId);

            // Assert
            expect(mockFolderRepository.findOneBy).toHaveBeenCalledWith({ id: folderId });
            expect(result).toBeNull();
        });
    });

    describe('getFolderHierarchy', () => {
        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should build folder hierarchy correctly', async () => {
            // Arrange
            const caseId = 'case-123';
            const mockFolders = [mockFolder, mockChildFolder];
            mockFolderRepository.findByCaseId.mockResolvedValue(mockFolders);

            // Act
            const result = await service.getFolderHierarchy(caseId);

            // Assert
            expect(mockFolderRepository.findByCaseId).toHaveBeenCalledWith(caseId);
            expect(result).toHaveLength(1); // One root folder
            expect(result[0].id).toBe(mockFolder.id);
            expect((result[0] as any).children).toHaveLength(1); // One child folder
            expect((result[0] as any).children[0].id).toBe(mockChildFolder.id);
            expect((result[0] as any).children[0].children).toHaveLength(0); // No children (orphaned folder ignored)
        });

        it('should return empty array when no folders exist', async () => {
            // Arrange
            const caseId = 'case-123';
            mockFolderRepository.findByCaseId.mockResolvedValue([]);

            // Act
            const result = await service.getFolderHierarchy(caseId);

            // Assert
            expect(result).toEqual([]);
        });

        it('should handle orphaned folders (parent not found)', async () => {
            // Arrange
            const caseId = 'case-123';
            const orphanedFolder = { ...mockChildFolder, parentFolderId: 'non-existent-parent' };
            const mockFolders = [mockFolder, orphanedFolder];
            mockFolderRepository.findByCaseId.mockResolvedValue(mockFolders);

            // Act
            const result = await service.getFolderHierarchy(caseId);

            // Assert
            expect(mockFolderRepository.findByCaseId).toHaveBeenCalledWith(caseId);
            expect(result).toHaveLength(1); // Only the root folder
            expect((result[0] as any).children).toHaveLength(0); // No children (orphaned folder ignored)
        });
    });

    describe('updateFolder', () => {
        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should update folder name successfully', async () => {
            // Arrange
            const folderId = 'folder-123';
            const updateData = {
                name: 'Updated Folder Name',
                updatedBy: 'user-123'
            };

            const updatedFolder = { ...mockFolder, name: updateData.name };
            mockFolderRepository.findOneBy.mockResolvedValue(mockFolder);
            mockFolderRepository.save.mockResolvedValue(updatedFolder);

            // Act
            const result = await service.updateFolder(folderId, updateData);

            // Assert
            expect(mockFolderRepository.findOneBy).toHaveBeenCalledWith({ id: folderId });
            expect(mockFolderRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    name: updateData.name,
                    path: '/Updated Folder Name',
                    updatedAt: expect.any(Date)
                })
            );
            expect(result).toEqual(updatedFolder);
        });

        it('should update folder parent successfully', async () => {
            // Arrange
            const folderId = 'folder-123';
            const updateData = {
                parentFolderId: 'new-parent-123',
                updatedBy: 'user-456'
            };

            const updatedFolder = {
                ...mockFolder,
                parentFolderId: updateData.parentFolderId,
                path: '/New Parent/Updated Folder Name',
                updatedAt: new Date()
            };

            mockFolderRepository.findOneBy.mockResolvedValue(mockFolder);
            mockFolderRepository.findOneBy.mockResolvedValueOnce(mockFolder); // For getFolderById
            mockFolderRepository.findOneBy.mockResolvedValueOnce({
                // For parent folder lookup
                id: 'new-parent-123',
                name: 'New Parent',
                path: '/New Parent'
            } as any);
            mockFolderRepository.save.mockResolvedValue(updatedFolder);

            // Act
            const result = await service.updateFolder(folderId, updateData);

            // Assert
            expect(mockFolderRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    parentFolderId: updateData.parentFolderId,
                    path: expect.stringContaining('/New Parent/Updated Folder Name')
                })
            );
            expect(result).toEqual(updatedFolder);
        });

        it('should return null when folder not found', async () => {
            // Arrange
            const folderId = 'non-existent';
            const updateData = {
                name: 'Updated Name',
                updatedBy: 'user-123'
            };

            mockFolderRepository.findOneBy.mockResolvedValue(null);

            // Act
            const result = await service.updateFolder(folderId, updateData);

            // Assert
            expect(mockFolderRepository.findOneBy).toHaveBeenCalledWith({ id: folderId });
            expect(mockFolderRepository.save).not.toHaveBeenCalled();
            expect(result).toBeNull();
        });

        it('should not update path when name is the same', async () => {
            // Arrange
            const folderId = 'folder-123';
            const updateData = {
                name: 'Test Folder', // Same name
                updatedBy: 'user-123'
            };

            mockFolderRepository.findOneBy.mockResolvedValue(mockFolder);
            mockFolderRepository.save.mockResolvedValue(mockFolder);

            // Act
            await service.updateFolder(folderId, updateData);

            // Assert
            expect(mockFolderRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    path: expect.stringContaining('/Test Folder') // Should contain the folder name
                })
            );
        });
    });

    describe('deleteFolder', () => {
        it('should delete folder successfully when empty', async () => {
            // Arrange
            const folderId = 'folder-123';
            mockFolderRepository.findOneBy.mockResolvedValue(mockFolder);
            mockFolderRepository.findChildFolders.mockResolvedValue([]);
            mockFolderRepository.countByFolderId.mockResolvedValue(0);
            mockFolderRepository.removeById.mockResolvedValue(true);

            // Act
            const result = await service.deleteFolder(folderId);

            // Assert
            expect(mockFolderRepository.findOneBy).toHaveBeenCalledWith({ id: folderId });
            expect(mockFolderRepository.findChildFolders).toHaveBeenCalledWith(folderId);
            expect(mockFolderRepository.countByFolderId).toHaveBeenCalledWith(folderId);
            expect(mockFolderRepository.removeById).toHaveBeenCalledWith(folderId);
            expect(result).toBe(true);
        });

        it('should throw error when folder has child folders', async () => {
            // Arrange
            const folderId = 'folder-123';
            const childFolders = [mockChildFolder];
            mockFolderRepository.findOneBy.mockResolvedValue(mockFolder);
            mockFolderRepository.findChildFolders.mockResolvedValue(childFolders);

            // Act & Assert
            await expect(service.deleteFolder(folderId)).rejects.toThrow(
                'Cannot delete folder that contains subfolders'
            );
            expect(mockFolderRepository.removeById).not.toHaveBeenCalled();
        });

        it('should throw error when folder has documents', async () => {
            // Arrange
            const folderId = 'folder-123';
            mockFolderRepository.findOneBy.mockResolvedValue(mockFolder);
            mockFolderRepository.findChildFolders.mockResolvedValue([]);
            mockFolderRepository.countByFolderId.mockResolvedValue(5);

            // Act & Assert
            await expect(service.deleteFolder(folderId)).rejects.toThrow(
                'Cannot delete folder that contains documents'
            );
            expect(mockFolderRepository.removeById).not.toHaveBeenCalled();
        });

        it('should return false when folder not found', async () => {
            // Arrange
            const folderId = 'non-existent';
            mockFolderRepository.findOneBy.mockResolvedValue(null);

            // Act
            const result = await service.deleteFolder(folderId);

            // Assert
            expect(mockFolderRepository.findOneBy).toHaveBeenCalledWith({ id: folderId });
            expect(mockFolderRepository.findChildFolders).not.toHaveBeenCalled();
            expect(result).toBe(false);
        });
    });

    describe('searchInCase', () => {
        it('should search folders in a case', async () => {
            // Arrange
            const caseId = 'case-123';
            const searchTerm = 'test';
            const limit = 20;
            const folders = [mockFolder];
            mockFolderRepository.searchInCase.mockResolvedValue(folders);

            // Act
            const result = await service.searchInCase(caseId, searchTerm, limit);

            // Assert
            expect(mockFolderRepository.searchInCase).toHaveBeenCalledWith(
                caseId,
                searchTerm,
                limit
            );
            expect(result).toEqual(folders);
        });

        it('should use default limit when not provided', async () => {
            // Arrange
            const caseId = 'case-123';
            const searchTerm = 'test';
            const folders = [mockFolder];
            mockFolderRepository.searchInCase.mockResolvedValue(folders);

            // Act
            await service.searchInCase(caseId, searchTerm);

            // Assert
            expect(mockFolderRepository.searchInCase).toHaveBeenCalledWith(caseId, searchTerm, 10);
        });
    });
});
