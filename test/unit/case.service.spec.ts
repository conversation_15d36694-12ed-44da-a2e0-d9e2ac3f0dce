import { Test, TestingModule } from '@nestjs/testing';
import { CaseService } from '../../apps/case-management/src/services/case.service';
import { CaseRepository } from '../../apps/case-management/src/repositories/case.repository';
import { ClientRepository } from '../../apps/case-management/src/repositories/client.repository';
import { CaseNumberGenerator } from '../../apps/case-management/src/utils/case-number-generator';
import { PaginationService } from '../../apps/case-management/src/services/pagination.service';
import { CaseAuditService } from '../../apps/case-management/src/services/case-audit.service';
import { CaseNoteService } from '../../apps/case-management/src/services/case-note.service';
import { CaseAttachmentService } from '../../apps/case-management/src/services/case-attachment.service';
import { CaseAssignmentService } from '../../apps/case-management/src/services/case-assignment.service';
import { CaseContactService } from '../../apps/case-management/src/services/case-contact.service';
import { CaseEventService } from '../../apps/case-management/src/services/case-event.service';
import { CaseRelationService } from '../../apps/case-management/src/services/case-relation.service';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { CasePriority, CaseType } from '@app/common/typeorm/entities/tenant/case.entity';
import { CreateCaseDto } from '../../apps/case-management/src/dto/create-case.dto';
import { CaseStatus } from '@app/common/enums/case-status.enum';
import { Request } from 'express';

describe('CaseService', () => {
    let service: CaseService;

    // Mock repositories
    const mockCaseRepository = {
        findOne: jest.fn(),
        find: jest.fn(),
        save: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        count: jest.fn(),
        delete: jest.fn()
    };

    const mockClientRepository = {
        findOne: jest.fn(),
        find: jest.fn(),
        save: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
        count: jest.fn(),
        delete: jest.fn()
    };

    // Mock services
    const mockCaseNumberGenerator = {
        generateCaseNumber: jest.fn()
    };

    const mockPaginationService = {
        paginate: jest.fn()
    };

    const mockCaseAuditService = {
        logAction: jest.fn(),
        logCaseCreation: jest.fn()
    };

    const mockCaseNoteService = {
        createNote: jest.fn()
    };

    const mockCaseAttachmentService = {
        createAttachment: jest.fn()
    };

    const mockCaseAssignmentService = {
        assignCase: jest.fn()
    };

    const mockCaseContactService = {
        createContact: jest.fn(),
        getContactsForCase: jest.fn()
    };

    const mockCaseEventService = {
        createEvent: jest.fn(),
        getCaseEvents: jest.fn()
    };

    const mockCaseRelationService = {
        createRelation: jest.fn(),
        getCaseRelations: jest.fn()
    };

    // Mock request
    const mockRequest = {
        ip: '127.0.0.1',
        headers: {
            'x-forwarded-for': '********'
        }
    } as unknown as Request;

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                CaseService,
                { provide: CaseRepository, useValue: mockCaseRepository },
                { provide: ClientRepository, useValue: mockClientRepository },
                { provide: CaseNumberGenerator, useValue: mockCaseNumberGenerator },
                { provide: PaginationService, useValue: mockPaginationService },
                { provide: CaseAuditService, useValue: mockCaseAuditService },
                { provide: CaseNoteService, useValue: mockCaseNoteService },
                { provide: CaseAttachmentService, useValue: mockCaseAttachmentService },
                { provide: CaseAssignmentService, useValue: mockCaseAssignmentService },
                { provide: CaseContactService, useValue: mockCaseContactService },
                { provide: CaseEventService, useValue: mockCaseEventService },
                { provide: CaseRelationService, useValue: mockCaseRelationService }
            ]
        }).compile();

        service = module.get<CaseService>(CaseService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('findCaseById', () => {
        it('should return a case if found', async () => {
            // Arrange
            const id = 'case-id-123';
            const mockCase = { id, title: 'Test Case', status: CaseStatus.IN_PROGRESS };
            mockCaseRepository.findOne.mockResolvedValue(mockCase);

            // Act
            const result = await service.findCaseById(id);

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id },
                relations: ['client']
            });
            expect(result).toEqual(mockCase);
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const id = 'non-existent-id';
            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(service.findCaseById(id)).rejects.toThrow(NotFoundException);
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id },
                relations: ['client']
            });
        });
    });

    describe('createCase', () => {
        it('should create a new case with existing client', async () => {
            // Arrange
            const userId = 'user-id-123';
            const userName = 'Test User';
            const clientId = 'client-id-123';
            const caseNumber = 'CASE-2023-001';

            const createCaseDto: CreateCaseDto = {
                title: 'New Test Case',
                description: 'Test case description',
                clientId,
                priority: CasePriority.HIGH,
                type: CaseType.CORPORATE,
                status: CaseStatus.DRAFT
            };

            const mockClient = { id: clientId, name: 'Test Client' };
            const mockNewCase = {
                id: 'new-case-id',
                caseNumber,
                title: createCaseDto.title,
                description: createCaseDto.description,
                priority: createCaseDto.priority,
                type: createCaseDto.type,
                status: createCaseDto.status,
                clientId,
                createdBy: userId
            };

            mockClientRepository.findOne.mockResolvedValue(mockClient);
            mockCaseNumberGenerator.generateCaseNumber.mockResolvedValue(caseNumber);
            mockCaseRepository.create.mockReturnValue(mockNewCase);
            mockCaseRepository.save.mockResolvedValue(mockNewCase);

            // Act
            const result = await service.createCase(createCaseDto, userId, userName, mockRequest);

            // Assert
            expect(mockClientRepository.findOne).toHaveBeenCalledWith({
                where: { id: clientId }
            });
            expect(mockCaseNumberGenerator.generateCaseNumber).toHaveBeenCalled();
            expect(mockCaseRepository.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    caseNumber,
                    title: createCaseDto.title,
                    clientId,
                    createdBy: userId
                })
            );
            expect(mockCaseRepository.save).toHaveBeenCalledWith(mockNewCase);
            expect(mockCaseAuditService.logCaseCreation).toHaveBeenCalled();
            expect(result).toEqual(mockNewCase);
        });

        it('should throw BadRequestException if client not found', async () => {
            // Arrange
            const userId = 'user-id-123';
            const userName = 'Test User';
            const clientId = 'non-existent-client';

            const createCaseDto: CreateCaseDto = {
                title: 'New Test Case',
                description: 'Test case description',
                clientId,
                status: CaseStatus.DRAFT
            };

            mockClientRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.createCase(createCaseDto, userId, userName, mockRequest)
            ).rejects.toThrow(BadRequestException);
            expect(mockClientRepository.findOne).toHaveBeenCalledWith({
                where: { id: clientId }
            });
            expect(mockCaseRepository.create).not.toHaveBeenCalled();
        });
    });
});
