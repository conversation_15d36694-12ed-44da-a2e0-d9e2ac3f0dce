import { Test, TestingModule } from '@nestjs/testing';
import { TaskService } from '../../apps/task-management/src/services/task.service';
import { TaskRepository } from '../../apps/task-management/src/repositories/task.repository';
import { TaskHistoryRepository } from '../../apps/task-management/src/repositories/task-history.repository';
import { TaskDependencyService } from '../../apps/task-management/src/services/task-dependency.service';
import { TaskPrioritizationService } from '../../apps/task-management/src/services/task-prioritization.service';
import { TaskAssignmentService } from '../../apps/task-management/src/services/task-assignment.service';
import { PaginationService } from 'apps/case-management/src/services/pagination.service';
import { CaseService } from 'apps/case-management/src/services/case.service';

// Mock dependencies
const mockTaskRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    findWithFilters: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    removeById: jest.fn()
};
const mockTaskHistoryRepository = { save: jest.fn() };
const mockTaskDependencyService = { getDependentTasks: jest.fn() };
const mockTaskPrioritizationService = { suggestPriority: jest.fn() };
const mockTaskAssignmentService = { autoAssignTask: jest.fn() };
const mockPaginationService = { createPaginatedResponse: jest.fn() };
const mockCaseService = { findCaseById: jest.fn(), getCaseDetails: jest.fn() };

describe('TaskService', () => {
    let service: TaskService;

    beforeEach(async () => {
        jest.clearAllMocks();
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TaskService,
                { provide: TaskRepository, useValue: mockTaskRepository },
                { provide: TaskHistoryRepository, useValue: mockTaskHistoryRepository },
                { provide: TaskDependencyService, useValue: mockTaskDependencyService },
                { provide: TaskPrioritizationService, useValue: mockTaskPrioritizationService },
                { provide: TaskAssignmentService, useValue: mockTaskAssignmentService },
                { provide: PaginationService, useValue: mockPaginationService },
                { provide: CaseService, useValue: mockCaseService }
            ]
        }).compile();
        service = module.get<TaskService>(TaskService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createTask', () => {
        it('should create a new task', async () => {
            // Arrange
            const createTaskDto = { caseId: 'case1', title: 'Test Task' };
            const userId = 'user1';
            const userName = 'User One';
            const createdTask = {
                ...createTaskDto,
                id: 'task1',
                status: 'OPEN',
                priority: 'MEDIUM',
                createdBy: userId
            };
            const savedTask = { ...createdTask };
            mockCaseService.findCaseById.mockResolvedValue({ id: createTaskDto.caseId });
            mockTaskRepository.create.mockReturnValue(createdTask);
            mockTaskRepository.save.mockResolvedValue(savedTask);
            mockTaskHistoryRepository.save.mockResolvedValue({});
            mockTaskPrioritizationService.suggestPriority.mockReturnValue('MEDIUM');
            mockTaskAssignmentService.autoAssignTask.mockResolvedValue(savedTask);
            mockCaseService.getCaseDetails.mockResolvedValue({ assignments: [] });
            // Mock addDependencyInfoToTask
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...savedTask,
                dependentTasks: [],
                dependsOnTasks: [],
                hasDependents: false,
                hasDependencies: false
            });

            // Act
            const result = await service.createTask(createTaskDto as any, userId, userName);

            // Assert
            expect(mockCaseService.findCaseById).toHaveBeenCalledWith(createTaskDto.caseId);
            expect(mockTaskRepository.create).toHaveBeenCalled();
            expect(mockTaskRepository.save).toHaveBeenCalledWith(createdTask);
            expect(mockTaskHistoryRepository.save).toHaveBeenCalled();
            expect(result).toHaveProperty('id', 'task1');
            expect(result).toHaveProperty('dependentTasks');
        });

        it('should throw NotFoundException if case does not exist', async () => {
            // Arrange
            const createTaskDto = { caseId: 'case404', title: 'Test Task' };
            mockCaseService.findCaseById.mockRejectedValue(new Error('not found'));

            // Act & Assert
            await expect(
                service.createTask(createTaskDto as any, 'user1', 'User One')
            ).rejects.toThrow('Case with ID case404 not found');
        });
    });

    describe('getTaskById', () => {
        it('should return a task by id', async () => {
            // Arrange
            const id = 'task1';
            const task = { id, title: 'Test Task', dependencies: [], statusHistory: [] };
            mockTaskRepository.findOne.mockResolvedValue(task);
            mockTaskDependencyService.getDependentTasks.mockResolvedValue([]);
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...task,
                dependentTasks: [],
                dependsOnTasks: [],
                hasDependents: false,
                hasDependencies: false
            });

            // Act
            const result = await service.getTaskById(id);

            // Assert
            expect(mockTaskRepository.findOne).toHaveBeenCalledWith({
                where: { id },
                relations: ['dependencies', 'dependencies.dependsOn', 'statusHistory']
            });
            expect(result).toHaveProperty('id', id);
        });

        it('should throw NotFoundException if task does not exist', async () => {
            // Arrange
            mockTaskRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getTaskById('notask')).rejects.toThrow(
                'Task with ID notask not found'
            );
        });
    });

    describe('updateTask', () => {
        it('should update a task', async () => {
            // Arrange
            const id = 'task1';
            const userId = 'user1';
            const updateTaskDto = { title: 'Updated Task', status: 'IN_PROGRESS' };
            const oldTask = { id, title: 'Old Task', status: 'OPEN', dependencies: [] };
            const updatedTask = { ...oldTask, ...updateTaskDto, updatedAt: expect.any(Date) };
            mockTaskRepository.findOne.mockResolvedValue(oldTask);
            mockTaskDependencyService.getDependentTasks.mockResolvedValue([]);
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...updatedTask,
                dependentTasks: [],
                dependsOnTasks: [],
                hasDependents: false,
                hasDependencies: false
            });
            service['validateStatusTransition'] = jest.fn().mockResolvedValue(undefined);
            mockTaskRepository.save.mockResolvedValue(updatedTask);
            mockTaskHistoryRepository.save.mockResolvedValue({});

            // Act
            const result = await service.updateTask(id, updateTaskDto as any, userId);

            // Assert
            expect(mockTaskRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({ id, title: 'Updated Task', status: 'IN_PROGRESS' })
            );
            expect(result).toHaveProperty('title', 'Updated Task');
        });
    });

    describe('deleteTask', () => {
        it('should delete a task if no dependents', async () => {
            // Arrange
            const id = 'task1';
            const task = { id, dependencies: [] };
            mockTaskRepository.findOne.mockResolvedValue(task);
            mockTaskDependencyService.getDependentTasks.mockResolvedValue([]);
            mockTaskRepository.removeById = jest.fn().mockResolvedValue(true);

            // Act
            const result = await service.deleteTask(id);

            // Assert
            expect(mockTaskRepository.removeById).toHaveBeenCalledWith(id);
            expect(result).toBe(true);
        });

        it('should throw NotFoundException if task does not exist', async () => {
            // Arrange
            mockTaskRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(service.deleteTask('notask')).rejects.toThrow(
                'Task with ID notask not found'
            );
        });

        it('should throw BadRequestException if dependents exist and force is false', async () => {
            // Arrange
            const id = 'task1';
            const task = { id, dependencies: [] };
            mockTaskRepository.findOne.mockResolvedValue(task);
            mockTaskDependencyService.getDependentTasks.mockResolvedValue([{}]);

            // Act & Assert
            await expect(service.deleteTask(id)).rejects.toThrow(
                'Cannot delete task with 1 dependent tasks. Use force=true to override.'
            );
        });
    });

    describe('Assignment Rules', () => {
        it('should auto-assign task when no assignee specified and case has assignments', async () => {
            // Arrange
            const createTaskDto = { caseId: 'case1', title: 'Test Task' };
            const userId = 'user1';
            const userName = 'User One';
            const createdTask = {
                ...createTaskDto,
                id: 'task1',
                status: 'OPEN',
                priority: 'MEDIUM',
                createdBy: userId
            };
            const savedTask = { ...createdTask };
            const autoAssignedTask = { ...savedTask, assigneeId: 'assignee1' };

            mockCaseService.findCaseById.mockResolvedValue({ id: createTaskDto.caseId });
            mockTaskRepository.create.mockReturnValue(createdTask);
            mockTaskRepository.save.mockResolvedValue(savedTask);
            mockTaskHistoryRepository.save.mockResolvedValue({});
            mockCaseService.getCaseDetails.mockResolvedValue({
                assignments: [{ userId: 'assignee1' }, { userId: 'assignee2' }]
            });
            mockTaskAssignmentService.autoAssignTask.mockResolvedValue(autoAssignedTask);
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...autoAssignedTask,
                dependentTasks: [],
                dependsOnTasks: [],
                hasDependents: false,
                hasDependencies: false
            });

            // Act
            const result = await service.createTask(createTaskDto as any, userId, userName);

            // Assert
            expect(mockTaskAssignmentService.autoAssignTask).toHaveBeenCalledWith(savedTask, [
                'assignee1',
                'assignee2'
            ]);
            expect(result).toHaveProperty('assigneeId', 'assignee1');
        });

        it('should leave task unassigned when case has no assignments', async () => {
            // Arrange
            const createTaskDto = { caseId: 'case1', title: 'Test Task' };
            const userId = 'user1';
            const userName = 'User One';
            const createdTask = {
                ...createTaskDto,
                id: 'task1',
                status: 'OPEN',
                priority: 'MEDIUM',
                createdBy: userId
            };
            const savedTask = { ...createdTask };

            mockCaseService.findCaseById.mockResolvedValue({ id: createTaskDto.caseId });
            mockTaskRepository.create.mockReturnValue(createdTask);
            mockTaskRepository.save.mockResolvedValue(savedTask);
            mockTaskHistoryRepository.save.mockResolvedValue({});
            mockCaseService.getCaseDetails.mockResolvedValue({ assignments: [] });
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...savedTask,
                dependentTasks: [],
                dependsOnTasks: [],
                hasDependents: false,
                hasDependencies: false
            });

            // Act
            const result = await service.createTask(createTaskDto as any, userId, userName);

            // Assert
            expect(mockTaskAssignmentService.autoAssignTask).not.toHaveBeenCalled();
            expect(result).not.toHaveProperty('assigneeId');
        });

        it('should handle auto-assignment errors gracefully', async () => {
            // Arrange
            const createTaskDto = { caseId: 'case1', title: 'Test Task' };
            const userId = 'user1';
            const userName = 'User One';
            const createdTask = {
                ...createTaskDto,
                id: 'task1',
                status: 'OPEN',
                priority: 'MEDIUM',
                createdBy: userId
            };
            const savedTask = { ...createdTask };

            mockCaseService.findCaseById.mockResolvedValue({ id: createTaskDto.caseId });
            mockTaskRepository.create.mockReturnValue(createdTask);
            mockTaskRepository.save.mockResolvedValue(savedTask);
            mockTaskHistoryRepository.save.mockResolvedValue({});
            mockCaseService.getCaseDetails.mockRejectedValue(new Error('Service unavailable'));
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...savedTask,
                dependentTasks: [],
                dependsOnTasks: [],
                hasDependents: false,
                hasDependencies: false
            });

            // Act
            const result = await service.createTask(createTaskDto as any, userId, userName);

            // Assert
            expect(mockTaskAssignmentService.autoAssignTask).not.toHaveBeenCalled();
            expect(result).not.toHaveProperty('assigneeId');
        });
    });

    describe('Status Transitions', () => {
        it('should allow valid status transition from OPEN to IN_PROGRESS', async () => {
            // Arrange
            const id = 'task1';
            const userId = 'user1';
            const userName = 'User One';
            const statusChangeDto = { status: 'IN_PROGRESS', comment: 'Starting work' };
            const oldTask = { id, title: 'Test Task', status: 'OPEN', dependencies: [] };
            const updatedTask = { ...oldTask, status: 'IN_PROGRESS', updatedAt: expect.any(Date) };

            // Mock getTaskById to return the old task
            service.getTaskById = jest.fn().mockResolvedValue(oldTask);
            mockTaskDependencyService.getDependentTasks.mockResolvedValue([]);
            service['validateStatusTransition'] = jest.fn().mockResolvedValue(undefined);
            mockTaskRepository.save.mockResolvedValue(updatedTask);
            mockTaskHistoryRepository.save.mockResolvedValue({});
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...updatedTask,
                dependentTasks: [],
                dependsOnTasks: [],
                hasDependents: false,
                hasDependencies: false
            });

            // Act
            const result = await service.changeTaskStatus(
                id,
                statusChangeDto as any,
                userId,
                userName
            );

            // Assert
            expect(service['validateStatusTransition']).toHaveBeenCalledWith(
                id,
                'OPEN',
                'IN_PROGRESS'
            );
            expect(mockTaskRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({ status: 'IN_PROGRESS' })
            );
            expect(result).toHaveProperty('status', 'IN_PROGRESS');
        });

        it('should return task unchanged if status is not changing', async () => {
            // Arrange
            const id = 'task1';
            const userId = 'user1';
            const userName = 'User One';
            const statusChangeDto = { status: 'OPEN', comment: 'No change' };
            const task = { id, title: 'Test Task', status: 'OPEN', dependencies: [] };

            // Mock getTaskById to return the task
            service.getTaskById = jest.fn().mockResolvedValue(task);
            mockTaskDependencyService.getDependentTasks.mockResolvedValue([]);
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...task,
                dependentTasks: [],
                dependsOnTasks: [],
                hasDependents: false,
                hasDependencies: false
            });

            // Act
            const result = await service.changeTaskStatus(
                id,
                statusChangeDto as any,
                userId,
                userName
            );

            // Assert
            expect(mockTaskRepository.save).not.toHaveBeenCalled();
            expect(result).toEqual(task);
        });

        it('should throw BadRequestException for invalid status transition', async () => {
            // Arrange
            const id = 'task1';
            const userId = 'user1';
            const userName = 'User One';
            const statusChangeDto = { status: 'DONE', comment: 'Invalid transition' };
            const task = { id, title: 'Test Task', status: 'OPEN', dependencies: [] };

            mockTaskRepository.findOne.mockResolvedValue(task);
            service['validateStatusTransition'] = jest
                .fn()
                .mockRejectedValue(new Error('Invalid status transition from OPEN to DONE'));

            // Act & Assert
            await expect(
                service.changeTaskStatus(id, statusChangeDto as any, userId, userName)
            ).rejects.toThrow();
        });
    });

    describe('Dependency Management', () => {
        it('should return task with dependency information', async () => {
            // Arrange
            const id = 'task1';
            const task = {
                id,
                title: 'Test Task',
                dependencies: [{ dependsOnId: 'task2' }, { dependsOnId: 'task3' }],
                statusHistory: []
            };
            const dependentTasks = [{ task: { id: 'task4' } }, { task: { id: 'task5' } }];

            mockTaskRepository.findOne.mockResolvedValue(task);
            mockTaskDependencyService.getDependentTasks.mockResolvedValue(dependentTasks);
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...task,
                dependentTasks: ['task4', 'task5'],
                dependsOnTasks: ['task2', 'task3'],
                hasDependents: true,
                hasDependencies: true
            });

            // Act
            const result = await service.getTaskById(id);

            // Assert
            expect(result).toHaveProperty('dependentTasks', ['task4', 'task5']);
            expect(result).toHaveProperty('dependsOnTasks', ['task2', 'task3']);
            expect(result).toHaveProperty('hasDependents', true);
            expect(result).toHaveProperty('hasDependencies', true);
        });

        it('should handle tasks with no dependencies', async () => {
            // Arrange
            const id = 'task1';
            const task = { id, title: 'Test Task', dependencies: [], statusHistory: [] };

            mockTaskRepository.findOne.mockResolvedValue(task);
            mockTaskDependencyService.getDependentTasks.mockResolvedValue([]);
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...task,
                dependentTasks: [],
                dependsOnTasks: [],
                hasDependents: false,
                hasDependencies: false
            });

            // Act
            const result = await service.getTaskById(id);

            // Assert
            expect(result).toHaveProperty('hasDependents', false);
            expect(result).toHaveProperty('hasDependencies', false);
        });
    });

    describe('Prioritization', () => {
        it('should use suggested priority when due date is provided', async () => {
            // Arrange
            const createTaskDto = {
                caseId: 'case1',
                title: 'Test Task',
                dueDate: new Date('2024-12-31')
            };
            const userId = 'user1';
            const userName = 'User One';
            const createdTask = {
                ...createTaskDto,
                id: 'task1',
                status: 'OPEN',
                priority: 'HIGH',
                createdBy: userId
            };
            const savedTask = { ...createdTask };

            mockCaseService.findCaseById.mockResolvedValue({ id: createTaskDto.caseId });
            mockTaskRepository.create.mockReturnValue(createdTask);
            mockTaskRepository.save.mockResolvedValue(savedTask);
            mockTaskHistoryRepository.save.mockResolvedValue({});
            mockTaskPrioritizationService.suggestPriority.mockReturnValue('HIGH');
            mockCaseService.getCaseDetails.mockResolvedValue({ assignments: [] });
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...savedTask,
                dependentTasks: [],
                dependsOnTasks: [],
                hasDependents: false,
                hasDependencies: false
            });

            // Act
            const result = await service.createTask(createTaskDto as any, userId, userName);

            // Assert
            expect(mockTaskPrioritizationService.suggestPriority).toHaveBeenCalled();
            expect(result).toHaveProperty('priority', 'HIGH');
        });

        it('should use MEDIUM priority when no due date is provided', async () => {
            // Arrange
            const createTaskDto = { caseId: 'case1', title: 'Test Task' };
            const userId = 'user1';
            const userName = 'User One';
            const createdTask = {
                ...createTaskDto,
                id: 'task1',
                status: 'OPEN',
                priority: 'MEDIUM',
                createdBy: userId
            };
            const savedTask = { ...createdTask };

            mockCaseService.findCaseById.mockResolvedValue({ id: createTaskDto.caseId });
            mockTaskRepository.create.mockReturnValue(createdTask);
            mockTaskRepository.save.mockResolvedValue(savedTask);
            mockTaskHistoryRepository.save.mockResolvedValue({});
            mockCaseService.getCaseDetails.mockResolvedValue({ assignments: [] });
            service['addDependencyInfoToTask'] = jest.fn().mockReturnValue({
                ...savedTask,
                dependentTasks: [],
                dependsOnTasks: [],
                hasDependents: false,
                hasDependencies: false
            });

            // Act
            const result = await service.createTask(createTaskDto as any, userId, userName);

            // Assert
            expect(mockTaskPrioritizationService.suggestPriority).not.toHaveBeenCalled();
            expect(result).toHaveProperty('priority', 'MEDIUM');
        });
    });

    describe('Task Filtering and Pagination', () => {
        it('should return paginated tasks with filters', async () => {
            // Arrange
            const filterDto = {
                caseId: 'case1',
                status: 'OPEN',
                page: 1,
                limit: 10
            };
            const tasks = [
                { id: 'task1', title: 'Task 1', status: 'OPEN' },
                { id: 'task2', title: 'Task 2', status: 'OPEN' }
            ];
            const total = 2;
            const paginatedResponse = { data: tasks, total, page: 1, limit: 10 };

            mockTaskRepository.findWithFilters.mockResolvedValue([tasks, total]);
            mockPaginationService.createPaginatedResponse.mockReturnValue(paginatedResponse);

            // Act
            const result = await service.getTasks(filterDto as any);

            // Assert
            expect(mockTaskRepository.findWithFilters).toHaveBeenCalledWith(filterDto);
            expect(mockPaginationService.createPaginatedResponse).toHaveBeenCalledWith(
                tasks,
                total,
                1,
                10
            );
            expect(result).toEqual(paginatedResponse);
        });
    });

    describe('Force Delete with Dependencies', () => {
        it('should force delete task with dependencies when force=true', async () => {
            // Arrange
            const id = 'task1';
            const task = { id, dependencies: [{ id: 'dep1' }] };
            const dependentTasks = [{ id: 'dep2', task: { id: 'task2' } }];

            mockTaskRepository.findOne.mockResolvedValue(task);
            mockTaskDependencyService.getDependentTasks.mockResolvedValue(dependentTasks);
            service['removeDependenciesFromDependentTasks'] = jest
                .fn()
                .mockResolvedValue(undefined);
            service['removeTaskDependencies'] = jest.fn().mockResolvedValue(undefined);
            mockTaskRepository.removeById.mockResolvedValue(true);

            // Act
            const result = await service.deleteTask(id, true);

            // Assert
            expect(service['removeDependenciesFromDependentTasks']).toHaveBeenCalledWith(
                id,
                dependentTasks
            );
            expect(service['removeTaskDependencies']).toHaveBeenCalledWith(id, task.dependencies);
            expect(mockTaskRepository.removeById).toHaveBeenCalledWith(id);
            expect(result).toBe(true);
        });
    });
});
