import { Test, TestingModule } from '@nestjs/testing';
import { TaskNotificationService } from '../../apps/task-management/src/services/task-notification.service';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import { Task, TaskStatus, TaskPriority } from '@app/common/typeorm/entities/tenant/task.entity';

describe('TaskNotificationService', () => {
    let service: TaskNotificationService;
    let messageProducer: MessageProducerService;

    const mockTask = {
        id: 'task-123',
        title: 'Test Task',
        description: 'Test task description',
        priority: TaskPriority.HIGH,
        status: TaskStatus.OPEN,
        caseId: 'case-456',
        assigneeId: 'user-789',
        createdBy: 'creator-111',
        dueDate: new Date('2024-12-31'),
        createdAt: new Date(),
        updatedAt: new Date()
    } as Task;

    beforeEach(async () => {
        const mockMessageProducer = {
            enqueueMessage: jest.fn().mockResolvedValue({ jobId: 'test-job', status: 'queued' })
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TaskNotificationService,
                {
                    provide: MessageProducerService,
                    useValue: mockMessageProducer
                }
            ]
        }).compile();

        service = module.get<TaskNotificationService>(TaskNotificationService);
        messageProducer = module.get<MessageProducerService>(MessageProducerService);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('sendTaskCreatedNotification', () => {
        it('should send task creation notification with correct parameters', async () => {
            await service.sendTaskCreatedNotification(
                mockTask,
                'John Creator',
                '<EMAIL>',
                'Jane Assignee',
                'tenant-123',
                'Test Law Firm'
            );

            expect(messageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'generic-notification',
                    tenantName: 'Test Law Firm',
                    recipientName: 'Jane Assignee',
                    message: expect.stringContaining('John Creator')
                }),
                metadata: {
                    notificationType: 'task-created',
                    taskId: 'task-123',
                    caseId: 'case-456'
                }
            });
        });

        it('should handle errors gracefully', async () => {
            const error = new Error('Queue service error');
            (messageProducer.enqueueMessage as jest.Mock).mockRejectedValueOnce(error);

            const loggerErrorSpy = jest
                .spyOn((service as any).logger, 'error')
                .mockImplementation();

            await service.sendTaskCreatedNotification(
                mockTask,
                'John Creator',
                '<EMAIL>',
                'Jane Assignee',
                'tenant-123',
                'Test Law Firm'
            );

            expect(loggerErrorSpy).toHaveBeenCalledWith(
                expect.stringContaining('Failed to send task creation notification'),
                error.stack
            );

            loggerErrorSpy.mockRestore();
        });
    });

    describe('sendTaskAssignmentNotification', () => {
        it('should send assignment notification to new assignee', async () => {
            await service.sendTaskAssignmentNotification(
                mockTask,
                '<EMAIL>',
                'Old Assignee',
                '<EMAIL>',
                'New Assignee',
                'Admin User',
                'tenant-123',
                'Test Law Firm'
            );

            expect(messageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'generic-notification',
                    recipientName: 'New Assignee',
                    message: expect.stringContaining('Admin User')
                }),
                metadata: {
                    notificationType: 'task-reassigned',
                    taskId: 'task-123',
                    caseId: 'case-456'
                }
            });
        });

        it('should notify previous assignee about unassignment', async () => {
            await service.sendTaskAssignmentNotification(
                mockTask,
                '<EMAIL>',
                'Old Assignee',
                '<EMAIL>',
                'New Assignee',
                'Admin User',
                'tenant-123',
                'Test Law Firm'
            );

            // Should send two notifications
            expect(messageProducer.enqueueMessage).toHaveBeenCalledTimes(2);

            // Check the second call for unassignment notification
            const secondCall = (messageProducer.enqueueMessage as jest.Mock).mock.calls[1][0];
            expect(secondCall.recipients.email).toEqual(['<EMAIL>']);
            expect(secondCall.variables.message).toContain('Task Unassigned: Test Task');
            expect(secondCall.metadata.notificationType).toBe('task-unassigned');
        });

        it('should not send unassignment notification if no previous assignee', async () => {
            await service.sendTaskAssignmentNotification(
                mockTask,
                null,
                null,
                '<EMAIL>',
                'New Assignee',
                'Admin User',
                'tenant-123',
                'Test Law Firm'
            );

            // Should only send one notification (to the new assignee)
            expect(messageProducer.enqueueMessage).toHaveBeenCalledTimes(1);
        });
    });

    describe('sendTaskStatusUpdateNotification', () => {
        it('should send status update notification', async () => {
            await service.sendTaskStatusUpdateNotification(
                { ...mockTask, status: TaskStatus.IN_PROGRESS },
                TaskStatus.OPEN,
                'John Updater',
                '<EMAIL>',
                'Jane Assignee',
                'tenant-123',
                'Test Law Firm'
            );

            expect(messageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'generic-notification',
                    message: expect.stringContaining('John Updater'),
                    recipientName: 'Jane Assignee'
                }),
                metadata: {
                    notificationType: 'task-status-updated',
                    taskId: 'task-123',
                    caseId: 'case-456',
                    previousStatus: TaskStatus.OPEN,
                    newStatus: TaskStatus.IN_PROGRESS
                }
            });
        });

        it('should include completion message when task is completed', async () => {
            const completedTask = { ...mockTask, status: TaskStatus.DONE };

            await service.sendTaskStatusUpdateNotification(
                completedTask,
                TaskStatus.IN_PROGRESS,
                'John Updater',
                '<EMAIL>',
                'Jane Assignee',
                'tenant-123',
                'Test Law Firm'
            );

            const call = (messageProducer.enqueueMessage as jest.Mock).mock.calls[0][0];
            expect(call.variables.message).toContain('✅ This task has been marked as completed');
        });

        it('should include cancellation message when task is blocked', async () => {
            const cancelledTask = { ...mockTask, status: TaskStatus.BLOCKED };

            await service.sendTaskStatusUpdateNotification(
                cancelledTask,
                TaskStatus.IN_PROGRESS,
                'John Updater',
                '<EMAIL>',
                'Jane Assignee',
                'tenant-123',
                'Test Law Firm'
            );

            const call = (messageProducer.enqueueMessage as jest.Mock).mock.calls[0][0];
            expect(call.variables.message).toContain('⚠️ This task has been blocked');
        });

        it('should send to additional recipients when provided', async () => {
            await service.sendTaskStatusUpdateNotification(
                { ...mockTask, status: TaskStatus.DONE },
                TaskStatus.IN_PROGRESS,
                'John Updater',
                '<EMAIL>',
                'Jane Assignee',
                'tenant-123',
                'Test Law Firm',
                ['<EMAIL>', '<EMAIL>']
            );

            const call = (messageProducer.enqueueMessage as jest.Mock).mock.calls[0][0];
            expect(call.recipients.email).toEqual([
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ]);
        });
    });

    describe('sendTaskDueDateReminderNotification', () => {
        it('should send due date reminder for upcoming task', async () => {
            await service.sendTaskDueDateReminderNotification(
                mockTask,
                '<EMAIL>',
                'Jane Assignee',
                'tenant-123',
                'Test Law Firm',
                3 // Due in 3 days
            );

            expect(messageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'generic-notification',
                    message: expect.stringContaining('This task is due in 3 day(s)'),
                    recipientName: 'Jane Assignee'
                }),
                metadata: {
                    notificationType: 'task-due-reminder',
                    taskId: 'task-123',
                    caseId: 'case-456',
                    daysUntilDue: 3,
                    dueDate: mockTask.dueDate!.toISOString()
                }
            });
        });

        it('should send overdue notification for past due tasks', async () => {
            await service.sendTaskDueDateReminderNotification(
                mockTask,
                '<EMAIL>',
                'Jane Assignee',
                'tenant-123',
                'Test Law Firm',
                -2 // Overdue by 2 days
            );

            const call = (messageProducer.enqueueMessage as jest.Mock).mock.calls[0][0];
            expect(call.variables.message).toContain('⚠️ Overdue Task: Test Task');
            expect(call.variables.message).toContain('This task is 2 day(s) overdue');
            expect(call.metadata.notificationType).toBe('task-overdue');
        });
    });

    describe('sendTaskCommentNotification', () => {
        it('should send comment notification to team members', async () => {
            await service.sendTaskCommentNotification(
                mockTask,
                'John Commenter',
                'This is a test comment on the task',
                ['<EMAIL>', '<EMAIL>'],
                ['Member One', 'Member Two'],
                'tenant-123',
                'Test Law Firm'
            );

            expect(messageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'tenant-123',
                userId: 'system',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>', '<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'generic-notification',
                    message: expect.stringContaining('John Commenter added a comment')
                }),
                metadata: {
                    notificationType: 'task-comment-added',
                    taskId: 'task-123',
                    caseId: 'case-456',
                    commentAuthor: 'John Commenter'
                }
            });
        });
    });

    describe('formatPriority', () => {
        it('should format task priorities correctly', () => {
            const formatMethod = (service as any).formatPriority;

            expect(formatMethod(TaskPriority.LOW)).toBe('Low');
            expect(formatMethod(TaskPriority.MEDIUM)).toBe('Medium');
            expect(formatMethod(TaskPriority.HIGH)).toBe('High');
            expect(formatMethod(TaskPriority.HIGHEST)).toBe('Highest');
            expect(formatMethod(TaskPriority.LOWEST)).toBe('Lowest');
        });
    });

    describe('formatStatus', () => {
        it('should format task statuses correctly', () => {
            const formatMethod = (service as any).formatStatus;

            expect(formatMethod(TaskStatus.OPEN)).toBe('Open');
            expect(formatMethod(TaskStatus.IN_PROGRESS)).toBe('In Progress');
            expect(formatMethod(TaskStatus.DONE)).toBe('Done');
            expect(formatMethod(TaskStatus.BLOCKED)).toBe('Blocked');
        });
    });

    describe('error handling', () => {
        it('should handle messageProducer errors gracefully in all methods', async () => {
            const error = new Error('Queue service error');
            (messageProducer.enqueueMessage as jest.Mock).mockRejectedValue(error);

            const loggerErrorSpy = jest
                .spyOn((service as any).logger, 'error')
                .mockImplementation();

            // Test each method
            await service.sendTaskCreatedNotification(
                mockTask,
                'Creator',
                '<EMAIL>',
                'Assignee',
                'tenant',
                'Firm'
            );
            await service.sendTaskAssignmentNotification(
                mockTask,
                null,
                null,
                '<EMAIL>',
                'Assignee',
                'Admin',
                'tenant',
                'Firm'
            );
            await service.sendTaskStatusUpdateNotification(
                mockTask,
                TaskStatus.OPEN,
                'Updater',
                '<EMAIL>',
                'Assignee',
                'tenant',
                'Firm'
            );
            await service.sendTaskDueDateReminderNotification(
                mockTask,
                '<EMAIL>',
                'Assignee',
                'tenant',
                'Firm',
                3
            );
            await service.sendTaskCommentNotification(
                mockTask,
                'Commenter',
                'Comment',
                ['<EMAIL>'],
                ['Member'],
                'tenant',
                'Firm'
            );

            // Each method should log its specific error
            expect(loggerErrorSpy).toHaveBeenCalledTimes(5);

            loggerErrorSpy.mockRestore();
        });
    });
});
