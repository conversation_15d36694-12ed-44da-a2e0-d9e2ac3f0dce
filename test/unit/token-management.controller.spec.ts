import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';

import {
    CustomToken,
    TokenType,
    TokenStatus,
    TokenDataType
} from '@app/common/typeorm/entities/tenant/custom-token.entity';
import { ApiResponseUtil } from '@app/common/api-response';
import { Request } from 'express';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { TokenManagementController } from 'apps/document-engine/src/controllers/token-management.controller';
import { TokenManagementService } from 'apps/document-engine/src/document/services';
import { BulkCreateTokensDto } from 'apps/document-engine/src/dto/bulk-create-tokens.dto';
import { CreateCustomTokenDto } from 'apps/document-engine/src/dto/create-custom-token.dto';
import { UpdateCustomTokenDto } from 'apps/document-engine/src/dto/update-custom-token.dto';

// Mock ApiResponseUtil
jest.mock('@app/common/api-response', () => ({
    ApiResponseUtil: {
        created: jest
            .fn()
            .mockImplementation((data, message) => ({ data, message, statusCode: 201 })),
        ok: jest.fn().mockImplementation((data, message) => ({ data, message, statusCode: 200 })),
        notFound: jest.fn().mockImplementation((message) => ({ message, statusCode: 404 })),
        badRequest: jest
            .fn()
            .mockImplementation((message, data) => ({ message, data, statusCode: 400 })),
        internalServerError: jest
            .fn()
            .mockImplementation((message, data) => ({ message, data, statusCode: 500 }))
    }
}));

// Mock Guards
const mockJwtGuard = { canActivate: jest.fn(() => true) };
const mockTenantGuard = { canActivate: jest.fn(() => true) };
const mockRolesGuard = { canActivate: jest.fn(() => true) };

describe('TokenManagementController', () => {
    let controller: TokenManagementController;
    let tokenManagementService: jest.Mocked<TokenManagementService>;

    // Mock data
    const mockUser = {
        systemUserId: 'user-123',
        name: 'John Doe',
        email: '<EMAIL>'
    };

    const mockRequest = {
        user: mockUser
    } as unknown as Request;

    const mockCustomToken: CustomToken = {
        id: 'token-1',
        tokenName: 'clientName',
        description: 'Client full name',
        tokenType: TokenType.CUSTOM,
        dataType: TokenDataType.STRING,
        entityName: 'client',
        fieldPath: 'name',
        transformationConfig: {},
        validationConfig: { required: true },
        category: 'client',
        tags: ['client'],
        compatibleTemplateTypes: [],
        compatibleCaseTypes: [],
        status: TokenStatus.ACTIVE,
        isActive: true,
        usageCount: 0,
        isSystemToken: false,
        isCustomToken: true,
        fullTokenName: 'clientName',
        lastUsedAt: new Date(),
        metadata: {},
        createdBy: 'user-123',
        lastModifiedBy: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null
    } as CustomToken;

    const mockSystemToken: CustomToken = {
        id: 'system-currentDate',
        tokenName: 'currentDate',
        description: 'Current date in GB format',
        tokenType: TokenType.SYSTEM,
        dataType: TokenDataType.DATE,
        entityName: 'system',
        fieldPath: 'currentDate',
        transformationConfig: {},
        validationConfig: {},
        category: 'Date/Time',
        tags: ['system'],
        compatibleTemplateTypes: [],
        compatibleCaseTypes: [],
        status: TokenStatus.ACTIVE,
        isActive: true,
        usageCount: 0,
        isSystemToken: true,
        isCustomToken: false,
        fullTokenName: 'currentDate',
        lastUsedAt: new Date(),
        metadata: { source: 'hardcoded', location: 'system' },
        createdBy: 'system',
        lastModifiedBy: 'system',
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null
    } as CustomToken;

    beforeEach(async () => {
        const mockTokenManagementService = {
            createCustomToken: jest.fn(),
            updateCustomToken: jest.fn(),
            deleteCustomToken: jest.fn(),
            getTokenById: jest.fn(),
            getTokenByName: jest.fn(),
            getAllActiveTokens: jest.fn(),
            getSystemTokens: jest.fn(),
            getCustomTokens: jest.fn(),
            searchTokens: jest.fn(),
            getAvailableEntities: jest.fn(),
            resolveTokenValue: jest.fn(),
            bulkCreateTokens: jest.fn(),
            getTokenUsageStats: jest.fn()
        };

        const module: TestingModule = await Test.createTestingModule({
            controllers: [TokenManagementController],
            providers: [{ provide: TokenManagementService, useValue: mockTokenManagementService }]
        })
            .overrideGuard(JwtGuard)
            .useValue(mockJwtGuard)
            .overrideGuard(TenantGuard)
            .useValue(mockTenantGuard)
            .overrideGuard(RolesGuard)
            .useValue(mockRolesGuard)
            .compile();

        controller = module.get<TokenManagementController>(TokenManagementController);
        tokenManagementService = module.get(TokenManagementService);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    describe('createCustomToken', () => {
        const createTokenDto: CreateCustomTokenDto = {
            tokenName: 'testToken',
            description: 'Test token',
            dataType: TokenDataType.STRING,
            entityName: 'client',
            fieldPath: 'name',
            category: 'client',
            transformationConfig: {},
            validationConfig: { required: true }
        };

        it('should create a custom token successfully', async () => {
            tokenManagementService.createCustomToken.mockResolvedValue(mockCustomToken);

            const result = await controller.createCustomToken(createTokenDto, mockRequest);
            expect(result).toBeDefined();

            expect(tokenManagementService.createCustomToken).toHaveBeenCalledWith(
                createTokenDto,
                'user-123'
            );
            expect(ApiResponseUtil.created).toHaveBeenCalledWith(
                mockCustomToken,
                'Custom token created successfully'
            );
            expect(result).toBeDefined();
        });

        it('should handle creation errors', async () => {
            tokenManagementService.createCustomToken.mockRejectedValue(
                new BadRequestException('Token already exists')
            );

            await expect(controller.createCustomToken(createTokenDto, mockRequest)).rejects.toThrow(
                BadRequestException
            );
        });
    });

    describe('bulkCreateTokens', () => {
        const bulkCreateDto: BulkCreateTokensDto = {
            tokens: [
                {
                    tokenName: 'token1',
                    description: 'Test token 1',
                    dataType: TokenDataType.STRING,
                    entityName: 'client',
                    fieldPath: 'name',
                    category: 'client'
                },
                {
                    tokenName: 'token2',
                    description: 'Test token 2',
                    dataType: TokenDataType.STRING,
                    entityName: 'client',
                    fieldPath: 'email',
                    category: 'client'
                }
            ]
        };

        it('should bulk create tokens successfully', async () => {
            const bulkResult = {
                successful: 2,
                failed: 0,
                results: [
                    { tokenName: 'token1', success: true },
                    { tokenName: 'token2', success: true }
                ]
            };
            tokenManagementService.bulkCreateTokens.mockResolvedValue(bulkResult);

            const result = await controller.bulkCreateTokens(bulkCreateDto, mockRequest);
            expect(result).toBeDefined();

            expect(result).toBeDefined();
            expect(tokenManagementService.bulkCreateTokens).toHaveBeenCalledWith(
                bulkCreateDto.tokens,
                'user-123'
            );
            expect(ApiResponseUtil.created).toHaveBeenCalledWith(
                bulkResult,
                'Bulk token creation completed'
            );
        });
    });

    describe('getTokens', () => {
        it('should get tokens with default filters', async () => {
            const mockResult = {
                tokens: [mockCustomToken],
                total: 1
            };
            tokenManagementService.searchTokens.mockResolvedValue(mockResult);

            const result = await controller.getTokens();
            expect(result).toBeDefined();

            expect(result).toBeDefined();
            expect(tokenManagementService.searchTokens).toHaveBeenCalledWith({
                page: 1,
                limit: 10,
                search: undefined,
                tokenType: undefined,
                entityName: undefined,
                category: undefined,
                status: undefined,
                isActive: undefined,
                sortBy: 'tokenName',
                order: 'ASC'
            });
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                mockResult,
                'Tokens retrieved successfully'
            );
        });

        it('should get tokens with custom filters', async () => {
            const mockResult = {
                tokens: [mockCustomToken],
                total: 1
            };
            tokenManagementService.searchTokens.mockResolvedValue(mockResult);

            await controller.getTokens(
                '2', // page
                '20', // limit
                'client', // search
                TokenType.CUSTOM, // tokenType
                'client', // entityName
                'client', // category
                TokenStatus.ACTIVE, // status
                'true', // isActive
                'createdAt', // sortBy
                'DESC' // order
            );

            expect(tokenManagementService.searchTokens).toHaveBeenCalledWith({
                page: 2,
                limit: 20,
                search: 'client',
                tokenType: TokenType.CUSTOM,
                entityName: 'client',
                category: 'client',
                status: TokenStatus.ACTIVE,
                isActive: true,
                sortBy: 'createdAt',
                order: 'DESC'
            });
        });
    });

    describe('getSystemTokens', () => {
        it('should get system tokens successfully', async () => {
            const systemTokens = [mockSystemToken];
            tokenManagementService.getSystemTokens.mockResolvedValue(systemTokens);

            const result = await controller.getSystemTokens();
            expect(result).toBeDefined();

            expect(tokenManagementService.getSystemTokens).toHaveBeenCalled();
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                { tokens: systemTokens },
                'System tokens retrieved successfully'
            );
        });
    });

    describe('getCustomTokens', () => {
        it('should get custom tokens with grouping', async () => {
            const customTokens = [mockCustomToken];
            tokenManagementService.getCustomTokens.mockResolvedValue(customTokens);

            const result = await controller.getCustomTokens();
            expect(result).toBeDefined();

            expect(tokenManagementService.getCustomTokens).toHaveBeenCalled();
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                expect.objectContaining({
                    tokens: customTokens,
                    tokensByEntity: {
                        client: [mockCustomToken]
                    },
                    summary: {
                        total: 1,
                        byEntity: [{ entity: 'client', count: 1 }]
                    }
                }),
                'Custom tokens retrieved successfully'
            );
        });
    });

    describe('getTokenById', () => {
        it('should get token by id successfully', async () => {
            tokenManagementService.getTokenById.mockResolvedValue(mockCustomToken);

            const result = await controller.getTokenById('token-1');
            expect(result).toBeDefined();

            expect(tokenManagementService.getTokenById).toHaveBeenCalledWith('token-1');
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                mockCustomToken,
                'Token retrieved successfully'
            );
        });

        it('should return not found when token does not exist', async () => {
            tokenManagementService.getTokenById.mockResolvedValue(null);

            const result = await controller.getTokenById('non-existent');
            expect(result).toBeDefined();

            expect(ApiResponseUtil.notFound).toHaveBeenCalledWith('Token not found');
        });
    });

    describe('updateCustomToken', () => {
        const updateTokenDto: UpdateCustomTokenDto = {
            description: 'Updated description',
            isActive: false
        };

        it('should update token successfully', async () => {
            const updatedToken = {
                ...mockCustomToken,
                description: 'Updated description',
                fullTokenName: mockCustomToken.fullTokenName,
                isSystemToken: mockCustomToken.isSystemToken,
                isCustomToken: mockCustomToken.isCustomToken,
                lastUsedAt: mockCustomToken.lastUsedAt
            } as CustomToken;
            tokenManagementService.updateCustomToken.mockResolvedValue(updatedToken);

            const result = await controller.updateCustomToken(
                'token-1',
                updateTokenDto,
                mockRequest
            );

            expect(result).toBeDefined();
            expect(tokenManagementService.updateCustomToken).toHaveBeenCalledWith(
                'token-1',
                updateTokenDto,
                'user-123'
            );
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                updatedToken,
                'Token updated successfully'
            );
        });

        it('should handle not found error', async () => {
            tokenManagementService.updateCustomToken.mockRejectedValue(
                new Error('Token with ID token-1 not found')
            );

            const result = await controller.updateCustomToken(
                'token-1',
                updateTokenDto,
                mockRequest
            );

            expect(result).toBeDefined();
            expect(ApiResponseUtil.notFound).toHaveBeenCalledWith(
                'Token with ID token-1 not found'
            );
        });
    });

    describe('deleteCustomToken', () => {
        it('should delete token successfully', async () => {
            tokenManagementService.deleteCustomToken.mockResolvedValue(true);

            const result = await controller.deleteCustomToken('token-1', mockRequest);
            expect(result).toBeDefined();

            expect(tokenManagementService.deleteCustomToken).toHaveBeenCalledWith(
                'token-1',
                'user-123'
            );
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                { success: true },
                'Token deleted successfully'
            );
        });

        it('should return not found when deletion fails', async () => {
            tokenManagementService.deleteCustomToken.mockResolvedValue(false);

            const result = await controller.deleteCustomToken('token-1', mockRequest);
            expect(result).toBeDefined();

            expect(ApiResponseUtil.notFound).toHaveBeenCalledWith('Token not found');
        });
    });

    describe('getAvailableEntities', () => {
        it('should get available entities', () => {
            const mockEntities = [
                {
                    name: 'client',
                    displayName: 'Client',
                    description: 'Client information',
                    fields: [
                        {
                            name: 'name',
                            path: 'name',
                            displayName: 'Full Name',
                            dataType: 'string',
                            nullable: false,
                            description: 'Client name',
                            example: 'John Smith'
                        }
                    ],
                    relations: []
                }
            ];
            tokenManagementService.getAvailableEntities.mockReturnValue(mockEntities);

            const result = controller.getAvailableEntities();

            expect(result).toBeDefined();
            expect(tokenManagementService.getAvailableEntities).toHaveBeenCalled();
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                { entities: mockEntities },
                'Available entities retrieved successfully'
            );
        });
    });

    describe('getTokenizableFields', () => {
        it('should get tokenizable fields with availability status', async () => {
            const mockEntities = [
                {
                    name: 'client',
                    displayName: 'Client',
                    description: 'Client information',
                    fields: [
                        {
                            name: 'name',
                            path: 'name',
                            displayName: 'Full Name',
                            dataType: 'string',
                            nullable: false,
                            description: 'Client name',
                            example: 'John Smith'
                        }
                    ],
                    relations: []
                }
            ];
            const mockActiveTokens = [mockCustomToken];

            tokenManagementService.getAvailableEntities.mockReturnValue(mockEntities);
            tokenManagementService.getAllActiveTokens.mockResolvedValue(mockActiveTokens);

            const result = await controller.getTokenizableFields();
            expect(result).toBeDefined();

            expect(tokenManagementService.getAvailableEntities).toHaveBeenCalled();
            expect(tokenManagementService.getAllActiveTokens).toHaveBeenCalled();
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                expect.objectContaining({
                    tokenizableEntities: expect.any(Array),
                    summary: expect.objectContaining({
                        totalEntities: 1,
                        totalFields: 1
                    }),
                    instructions: expect.any(Object)
                }),
                'Tokenizable fields organized by entity retrieved successfully'
            );
        });
    });

    describe('resolveTokenValue', () => {
        it('should resolve token value successfully', async () => {
            const mockContext = {
                case: { id: 'case-1', caseNumber: 'CAS-001' },
                client: { id: 'client-1', name: 'John Doe' }
            };
            const mockResult = {
                value: 'John Doe',
                success: true,
                dataType: 'string',
                transformed: false
            };

            tokenManagementService.resolveTokenValue.mockResolvedValue(mockResult);

            const result = await controller.resolveTokenValue('clientName', mockContext);
            expect(result).toBeDefined();

            expect(tokenManagementService.resolveTokenValue).toHaveBeenCalledWith(
                'clientName',
                mockContext
            );
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(mockResult, 'Token value resolved');
        });
    });

    describe('getTokenByName', () => {
        it('should get token by name successfully', async () => {
            tokenManagementService.getTokenByName.mockResolvedValue(mockCustomToken);

            const result = await controller.getTokenByName('clientName');
            expect(result).toBeDefined();

            expect(tokenManagementService.getTokenByName).toHaveBeenCalledWith('clientName');
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                mockCustomToken,
                'Token retrieved successfully'
            );
        });

        it('should return not found when token does not exist', async () => {
            tokenManagementService.getTokenByName.mockResolvedValue(null);

            const result = await controller.getTokenByName('nonExistent');
            expect(result).toBeDefined();

            expect(ApiResponseUtil.notFound).toHaveBeenCalledWith('Token not found');
        });
    });

    describe('validateTokenName', () => {
        it('should validate available token name', async () => {
            tokenManagementService.getTokenByName.mockResolvedValue(null);

            const result = await controller.validateTokenName('newTokenName');
            expect(result).toBeDefined();

            expect(tokenManagementService.getTokenByName).toHaveBeenCalledWith('newTokenName');
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                {
                    tokenName: 'newTokenName',
                    isAvailable: true,
                    existingToken: null
                },
                'Token name is available'
            );
        });

        it('should validate taken token name', async () => {
            tokenManagementService.getTokenByName.mockResolvedValue(mockCustomToken);

            const result = await controller.validateTokenName('existingToken');
            expect(result).toBeDefined();

            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                {
                    tokenName: 'existingToken',
                    isAvailable: false,
                    existingToken: {
                        id: mockCustomToken.id,
                        tokenType: mockCustomToken.tokenType,
                        status: mockCustomToken.status
                    }
                },
                'Token name is already taken'
            );
        });

        it('should handle empty token name', async () => {
            await expect(controller.validateTokenName('')).rejects.toThrow(BadRequestException);
        });
    });

    describe('getTokenUsageStats', () => {
        it('should get token usage statistics', async () => {
            const mockStats = [
                {
                    tokenId: 'token-1',
                    tokenName: 'clientName',
                    usageCount: 5,
                    lastUsedAt: new Date(),
                    templatesUsing: [],
                    usageTrend: 'stable' as 'stable' | 'increasing' | 'decreasing'
                }
            ];
            tokenManagementService.getTokenUsageStats.mockResolvedValue(mockStats);

            const result = await controller.getTokenUsageStats();
            expect(result).toBeDefined();

            expect(tokenManagementService.getTokenUsageStats).toHaveBeenCalled();
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                { stats: mockStats },
                'Token usage statistics retrieved successfully'
            );
        });
    });

    describe('getDataTypes', () => {
        it('should return available data types', () => {
            const result = controller.getDataTypes();

            expect(result).toBeDefined();
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                {
                    dataTypes: expect.arrayContaining([
                        { value: 'STRING', label: 'Text' },
                        { value: 'NUMBER', label: 'Number' },
                        { value: 'DATE', label: 'Date' },
                        { value: 'CURRENCY', label: 'Currency' }
                    ])
                },
                'Data types retrieved successfully'
            );
        });
    });

    describe('getTokenCategories', () => {
        it('should return token categories', () => {
            const result = controller.getTokenCategories();

            expect(result).toBeDefined();
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                {
                    categories: expect.arrayContaining([
                        'case',
                        'client',
                        'property',
                        'financial',
                        'dates',
                        'contacts',
                        'legal',
                        'system',
                        'custom'
                    ])
                },
                'Token categories retrieved successfully'
            );
        });
    });

    describe('testTokenResolution', () => {
        it('should test token resolution successfully', async () => {
            const testRequest = {
                tokenName: 'clientName',
                context: {
                    case: { id: 'case-1' },
                    client: { id: 'client-1', name: 'John Doe' }
                }
            };
            const mockResult = {
                value: 'John Doe',
                success: true,
                dataType: 'string',
                transformed: false
            };

            tokenManagementService.resolveTokenValue.mockResolvedValue(mockResult);

            const result = await controller.testTokenResolution(testRequest);
            expect(result).toBeDefined();

            expect(tokenManagementService.resolveTokenValue).toHaveBeenCalledWith(
                'clientName',
                testRequest.context
            );
            expect(ApiResponseUtil.ok).toHaveBeenCalledWith(
                {
                    tokenName: 'clientName',
                    resolution: mockResult,
                    contextProvided: ['case', 'client']
                },
                'Token resolution test completed'
            );
        });
    });
});
