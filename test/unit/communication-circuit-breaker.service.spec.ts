import { Test, TestingModule } from '@nestjs/testing';
import {
    CircuitBreakerService,
    CircuitBreakerState,
    CircuitBreakerOptions
} from '@app/common/communication/services/circuit-breaker.service';

describe('CircuitBreakerService', () => {
    let service: CircuitBreakerService;

    const testCircuitName = 'test-circuit';
    const customOptions: Partial<CircuitBreakerOptions> = {
        failureThreshold: 3,
        recoveryTimeout: 30000, // 30 seconds
        monitoringPeriod: 60000, // 1 minute
        expectedErrors: ['ECONNREFUSED', 'TIMEOUT']
    };

    beforeEach(async () => {
        // Clear all mocks before each test
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [CircuitBreakerService]
        }).compile();

        service = module.get<CircuitBreakerService>(CircuitBreakerService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('method: registerCircuit', () => {
        it('should register a circuit with default options', () => {
            // Act
            service.registerCircuit(testCircuitName);

            // Assert
            const stats = service.getCircuitStats(testCircuitName);
            expect(stats).toEqual({
                state: CircuitBreakerState.CLOSED,
                failureCount: 0,
                successCount: 0
            });
        });

        it('should register a circuit with custom options', () => {
            // Act
            service.registerCircuit(testCircuitName, customOptions);

            // Assert
            const stats = service.getCircuitStats(testCircuitName);
            expect(stats).toEqual({
                state: CircuitBreakerState.CLOSED,
                failureCount: 0,
                successCount: 0
            });
        });

        it('should override existing circuit registration', () => {
            // Arrange
            service.registerCircuit(testCircuitName, { failureThreshold: 5 });
            const initialStats = service.getCircuitStats(testCircuitName);

            // Act
            service.registerCircuit(testCircuitName, customOptions);

            // Assert
            const updatedStats = service.getCircuitStats(testCircuitName);
            expect(updatedStats).toEqual({
                state: CircuitBreakerState.CLOSED,
                failureCount: 0,
                successCount: 0
            });
            expect(updatedStats).toEqual(initialStats); // Should reset stats
        });
    });

    describe('method: execute', () => {
        beforeEach(() => {
            service.registerCircuit(testCircuitName, customOptions);
        });

        it('should execute operation successfully when circuit is closed', async () => {
            // Arrange
            const mockOperation = jest.fn().mockResolvedValue('success');

            // Act
            const result = await service.execute(testCircuitName, mockOperation);

            // Assert
            expect(result).toBe('success');
            expect(mockOperation).toHaveBeenCalledTimes(1);

            const stats = service.getCircuitStats(testCircuitName);
            expect(stats?.successCount).toBe(1);
            expect(stats?.failureCount).toBe(0);
        });

        it('should handle operation failure and increment failure count', async () => {
            // Arrange
            const mockError = new Error('ECONNREFUSED');
            const mockOperation = jest.fn().mockRejectedValue(mockError);

            // Act & Assert
            await expect(service.execute(testCircuitName, mockOperation)).rejects.toThrow(
                'ECONNREFUSED'
            );

            const stats = service.getCircuitStats(testCircuitName);
            expect(stats?.failureCount).toBe(1);
            expect(stats?.state).toBe(CircuitBreakerState.CLOSED);
        });

        it('should open circuit after reaching failure threshold', async () => {
            // Arrange
            const mockError = new Error('ECONNREFUSED');
            const mockOperation = jest.fn().mockRejectedValue(mockError);

            // Act - Trigger failures up to threshold (3)
            for (let i = 0; i < 3; i++) {
                try {
                    await service.execute(testCircuitName, mockOperation);
                } catch {
                    // Expected to fail
                }
            }

            // Assert
            const stats = service.getCircuitStats(testCircuitName);
            expect(stats?.state).toBe(CircuitBreakerState.OPEN);
            expect(stats?.failureCount).toBe(3);
            expect(stats?.nextAttemptTime).toBeInstanceOf(Date);
        });

        it('should reject calls when circuit is open', async () => {
            // Arrange - Open the circuit
            const mockError = new Error('ECONNREFUSED');
            const failingOperation = jest.fn().mockRejectedValue(mockError);

            for (let i = 0; i < 3; i++) {
                try {
                    await service.execute(testCircuitName, failingOperation);
                } catch {
                    // Expected to fail
                }
            }

            const successOperation = jest.fn().mockResolvedValue('success');

            // Act & Assert
            await expect(service.execute(testCircuitName, successOperation)).rejects.toThrow(
                'Circuit breaker is open'
            );

            expect(successOperation).not.toHaveBeenCalled();
        });

        it('should execute fallback when circuit is open', async () => {
            // Arrange - Open the circuit
            const mockError = new Error('ECONNREFUSED');
            const failingOperation = jest.fn().mockRejectedValue(mockError);

            for (let i = 0; i < 3; i++) {
                try {
                    await service.execute(testCircuitName, failingOperation);
                } catch {
                    // Expected to fail
                }
            }

            const successOperation = jest.fn().mockResolvedValue('success');
            const fallbackOperation = jest.fn().mockResolvedValue('fallback-result');

            // Act
            const result = await service.execute(
                testCircuitName,
                successOperation,
                fallbackOperation
            );

            // Assert
            expect(result).toBe('fallback-result');
            expect(successOperation).not.toHaveBeenCalled();
            expect(fallbackOperation).toHaveBeenCalledTimes(1);
        });

        it('should transition to half-open after recovery timeout', async () => {
            // Arrange - Open the circuit
            const mockError = new Error('ECONNREFUSED');
            const failingOperation = jest.fn().mockRejectedValue(mockError);

            for (let i = 0; i < 3; i++) {
                try {
                    await service.execute(testCircuitName, failingOperation);
                } catch {
                    // Expected to fail
                }
            }

            // Manually set next attempt time to past to simulate timeout
            const stats = service.getCircuitStats(testCircuitName);
            if (stats) {
                stats.nextAttemptTime = new Date(Date.now() - 1000); // 1 second ago
            }

            const successOperation = jest.fn().mockResolvedValue('recovery-success');

            // Act
            const result = await service.execute(testCircuitName, successOperation);

            // Assert
            expect(result).toBe('recovery-success');
            expect(successOperation).toHaveBeenCalledTimes(1);

            const updatedStats = service.getCircuitStats(testCircuitName);
            expect(updatedStats?.state).toBe(CircuitBreakerState.CLOSED);
            expect(updatedStats?.failureCount).toBe(0);
            expect(updatedStats?.successCount).toBe(1);
        });

        it('should not trigger circuit breaker for unexpected errors', async () => {
            // Arrange
            const unexpectedError = new Error('UNEXPECTED_ERROR');
            const mockOperation = jest.fn().mockRejectedValue(unexpectedError);

            // Act & Assert
            await expect(service.execute(testCircuitName, mockOperation)).rejects.toThrow(
                'UNEXPECTED_ERROR'
            );

            const stats = service.getCircuitStats(testCircuitName);
            expect(stats?.failureCount).toBe(0); // Should not increment for unexpected errors
            expect(stats?.state).toBe(CircuitBreakerState.CLOSED);
        });

        it('should throw error for unregistered circuit', async () => {
            // Arrange
            const mockOperation = jest.fn().mockResolvedValue('success');

            // Act & Assert
            await expect(service.execute('unregistered-circuit', mockOperation)).rejects.toThrow(
                'Circuit breaker not registered: unregistered-circuit'
            );
        });

        it('should execute fallback when operation fails', async () => {
            // Arrange
            const mockError = new Error('ECONNREFUSED');
            const failingOperation = jest.fn().mockRejectedValue(mockError);
            const fallbackOperation = jest.fn().mockResolvedValue('fallback-success');

            // Act
            const result = await service.execute(
                testCircuitName,
                failingOperation,
                fallbackOperation
            );

            // Assert
            expect(result).toBe('fallback-success');
            expect(failingOperation).toHaveBeenCalledTimes(1);
            expect(fallbackOperation).toHaveBeenCalledTimes(1);
        });
    });

    describe('method: getCircuitStats', () => {
        it('should return circuit stats for registered circuit', () => {
            // Arrange
            service.registerCircuit(testCircuitName, customOptions);

            // Act
            const stats = service.getCircuitStats(testCircuitName);

            // Assert
            expect(stats).toEqual({
                state: CircuitBreakerState.CLOSED,
                failureCount: 0,
                successCount: 0
            });
        });

        it('should return undefined for unregistered circuit', () => {
            // Act
            const stats = service.getCircuitStats('unregistered-circuit');

            // Assert
            expect(stats).toBeUndefined();
        });
    });

    describe('method: getAllCircuitStats', () => {
        it('should return all registered circuit stats', () => {
            // Arrange
            service.registerCircuit('circuit1');
            service.registerCircuit('circuit2', customOptions);

            // Act
            const allStats = service.getAllCircuitStats();

            // Assert
            expect(allStats.size).toBe(2);
            expect(allStats.has('circuit1')).toBe(true);
            expect(allStats.has('circuit2')).toBe(true);

            const circuit1Stats = allStats.get('circuit1');
            expect(circuit1Stats?.state).toBe(CircuitBreakerState.CLOSED);
        });

        it('should return empty map when no circuits registered', () => {
            // Act
            const allStats = service.getAllCircuitStats();

            // Assert
            expect(allStats.size).toBe(0);
        });
    });

    describe('method: resetCircuit', () => {
        beforeEach(() => {
            service.registerCircuit(testCircuitName, customOptions);
        });

        it('should reset circuit stats to initial state', async () => {
            // Arrange - Create some failures
            const mockError = new Error('ECONNREFUSED');
            const failingOperation = jest.fn().mockRejectedValue(mockError);

            try {
                await service.execute(testCircuitName, failingOperation);
            } catch {
                // Expected to fail
            }

            // Act
            service.resetCircuit(testCircuitName);

            // Assert
            const stats = service.getCircuitStats(testCircuitName);
            expect(stats).toEqual({
                state: CircuitBreakerState.CLOSED,
                failureCount: 0,
                successCount: 0
            });
        });

        it('should reset open circuit to closed', async () => {
            // Arrange - Open the circuit
            const mockError = new Error('ECONNREFUSED');
            const failingOperation = jest.fn().mockRejectedValue(mockError);

            for (let i = 0; i < 3; i++) {
                try {
                    await service.execute(testCircuitName, failingOperation);
                } catch {
                    // Expected to fail
                }
            }

            // Verify circuit is open
            let stats = service.getCircuitStats(testCircuitName);
            expect(stats?.state).toBe(CircuitBreakerState.OPEN);

            // Act
            service.resetCircuit(testCircuitName);

            // Assert
            stats = service.getCircuitStats(testCircuitName);
            expect(stats?.state).toBe(CircuitBreakerState.CLOSED);
            expect(stats?.failureCount).toBe(0);
            expect(stats?.nextAttemptTime).toBeUndefined();
            expect(stats?.lastFailureTime).toBeUndefined();
        });

        it('should throw error for unregistered circuit', () => {
            // Act & Assert
            expect(() => service.resetCircuit('unregistered-circuit')).toThrow(
                'Circuit breaker not found: unregistered-circuit'
            );
        });
    });

    describe('method: cleanup', () => {
        beforeEach(() => {
            service.registerCircuit(testCircuitName, {
                ...customOptions,
                monitoringPeriod: 1000 // 1 second for testing
            });
        });

        it('should cleanup old failure data after monitoring period', async () => {
            // Arrange - Create a failure
            const mockError = new Error('ECONNREFUSED');
            const failingOperation = jest.fn().mockRejectedValue(mockError);

            try {
                await service.execute(testCircuitName, failingOperation);
            } catch {
                // Expected to fail
            }

            // Verify failure is recorded
            let stats = service.getCircuitStats(testCircuitName);
            expect(stats?.failureCount).toBe(1);
            expect(stats?.lastFailureTime).toBeInstanceOf(Date);

            // Manually set last failure time to past monitoring period
            if (stats?.lastFailureTime) {
                stats.lastFailureTime = new Date(Date.now() - 2000); // 2 seconds ago
            }

            // Act
            service.cleanup();

            // Assert
            stats = service.getCircuitStats(testCircuitName);
            expect(stats?.failureCount).toBe(0);
            expect(stats?.successCount).toBe(0);
            expect(stats?.lastFailureTime).toBeUndefined();
        });

        it('should reset open circuit to closed during cleanup', async () => {
            // Arrange - Open the circuit
            const mockError = new Error('ECONNREFUSED');
            const failingOperation = jest.fn().mockRejectedValue(mockError);

            for (let i = 0; i < 3; i++) {
                try {
                    await service.execute(testCircuitName, failingOperation);
                } catch {
                    // Expected to fail
                }
            }

            // Manually set last failure time to past monitoring period
            const stats = service.getCircuitStats(testCircuitName);
            if (stats?.lastFailureTime) {
                stats.lastFailureTime = new Date(Date.now() - 2000); // 2 seconds ago
            }

            // Verify circuit is open
            expect(stats?.state).toBe(CircuitBreakerState.OPEN);

            // Act
            service.cleanup();

            // Assert
            const cleanedStats = service.getCircuitStats(testCircuitName);
            expect(cleanedStats?.state).toBe(CircuitBreakerState.CLOSED);
            expect(cleanedStats?.nextAttemptTime).toBeUndefined();
        });

        it('should not cleanup recent failures', async () => {
            // Arrange - Create a recent failure
            const mockError = new Error('ECONNREFUSED');
            const failingOperation = jest.fn().mockRejectedValue(mockError);

            try {
                await service.execute(testCircuitName, failingOperation);
            } catch {
                // Expected to fail
            }

            // Act
            service.cleanup();

            // Assert - Recent failure should not be cleaned up
            const stats = service.getCircuitStats(testCircuitName);
            expect(stats?.failureCount).toBe(1);
            expect(stats?.lastFailureTime).toBeInstanceOf(Date);
        });
    });

    describe('error handling scenarios', () => {
        beforeEach(() => {
            service.registerCircuit(testCircuitName, {
                failureThreshold: 2,
                recoveryTimeout: 1000,
                expectedErrors: []
            });
        });

        it('should treat all errors as circuit-breaking when no expected errors defined', async () => {
            // Arrange
            const randomError = new Error('RANDOM_ERROR');
            const failingOperation = jest.fn().mockRejectedValue(randomError);

            // Act & Assert - First failure
            await expect(service.execute(testCircuitName, failingOperation)).rejects.toThrow(
                'RANDOM_ERROR'
            );

            let stats = service.getCircuitStats(testCircuitName);
            expect(stats?.failureCount).toBe(1);

            // Second failure should open circuit
            await expect(service.execute(testCircuitName, failingOperation)).rejects.toThrow(
                'RANDOM_ERROR'
            );

            stats = service.getCircuitStats(testCircuitName);
            expect(stats?.state).toBe(CircuitBreakerState.OPEN);
        });
    });
});
