import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { CustomTokenRepository } from '@app/common/repositories/custom-token.repository';
import {
    CustomToken,
    TokenType,
    TokenStatus,
    TokenDataType
} from '@app/common/typeorm/entities/tenant/custom-token.entity';
import {
    CreateTokenRequest,
    UpdateTokenRequest,
    TokenResolutionContext
} from '@app/common/interfaces/token.interfaces';
import { TokenManagementService } from 'apps/document-engine/src/document/services';

describe('TokenManagementService', () => {
    let service: TokenManagementService;
    let customTokenRepository: jest.Mocked<CustomTokenRepository>;

    // Mock token data
    const mockCustomToken: CustomToken = {
        id: 'token-1',
        tokenName: 'clientName',
        description: 'Client full name',
        tokenType: TokenType.CUSTOM,
        dataType: TokenDataType.STRING,
        entityName: 'client',
        fieldPath: 'name',
        transformationConfig: {},
        validationConfig: { required: true },
        category: 'client',
        tags: ['client', 'name'],
        compatibleTemplateTypes: [],
        compatibleCaseTypes: [],
        status: TokenStatus.ACTIVE,
        isActive: true,
        usageCount: 5,
        isSystemToken: false,
        isCustomToken: true,
        fullTokenName: 'clientName',
        lastUsedAt: new Date('2025-01-01'),
        metadata: {},
        createdBy: 'user-1',
        lastModifiedBy: 'user-1',
        createdAt: new Date('2025-01-01'),
        updatedAt: new Date('2025-01-01'),
        deletedAt: null
    } as CustomToken;

    const mockSystemToken: CustomToken = {
        id: 'system-currentDate',
        tokenName: 'currentDate',
        description: 'Current date in GB format (DD/MM/YYYY)',
        tokenType: TokenType.SYSTEM,
        dataType: TokenDataType.DATE,
        entityName: 'system',
        fieldPath: 'currentDate',
        transformationConfig: {},
        validationConfig: {},
        category: 'Date/Time',
        tags: ['system', 'hardcoded'],
        compatibleTemplateTypes: [],
        compatibleCaseTypes: [],
        status: TokenStatus.ACTIVE,
        isActive: true,
        usageCount: 0,
        isSystemToken: true,
        isCustomToken: false,
        fullTokenName: 'currentDate',
        lastUsedAt: new Date(),
        metadata: { source: 'hardcoded', location: 'system' },
        createdBy: 'system',
        lastModifiedBy: 'system',
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null
    } as CustomToken;

    const mockCreateTokenRequest: CreateTokenRequest = {
        tokenName: 'newToken',
        description: 'A new test token',
        dataType: 'string',
        entityName: 'client',
        fieldPath: 'email',
        category: 'client',
        tags: ['test'],
        transformationConfig: { uppercase: true },
        validationConfig: { required: true },
        compatibleTemplateTypes: [],
        compatibleCaseTypes: []
    };

    beforeEach(async () => {
        const mockCustomTokenRepository = {
            findByName: jest.fn(),
            findById: jest.fn(),
            findByEntityAndFieldPath: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            softDelete: jest.fn(),
            findAllActive: jest.fn(),
            findSystemTokens: jest.fn(),
            findCustomTokens: jest.fn(),
            findWithFilters: jest.fn(),
            incrementUsage: jest.fn(),
            getUsageStats: jest.fn()
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TokenManagementService,
                { provide: CustomTokenRepository, useValue: mockCustomTokenRepository }
            ]
        }).compile();

        service = module.get<TokenManagementService>(TokenManagementService);
        customTokenRepository = module.get(CustomTokenRepository);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createCustomToken', () => {
        it('should create a custom token successfully', async () => {
            customTokenRepository.findByName.mockResolvedValue(null);
            customTokenRepository.findByEntityAndFieldPath.mockResolvedValue(null);
            customTokenRepository.create.mockResolvedValue(mockCustomToken);

            const result = await service.createCustomToken(mockCreateTokenRequest, 'user-1');

            expect(customTokenRepository.findByName).toHaveBeenCalledWith('newToken');
            expect(customTokenRepository.findByEntityAndFieldPath).toHaveBeenCalledWith(
                'client',
                'email'
            );
            expect(customTokenRepository.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    tokenName: 'newToken',
                    description: 'A new test token',
                    tokenType: TokenType.CUSTOM,
                    entityName: 'client',
                    fieldPath: 'email',
                    createdBy: 'user-1'
                })
            );
            expect(result).toEqual(mockCustomToken);
        });

        it('should throw BadRequestException if token name already exists', async () => {
            customTokenRepository.findByName.mockResolvedValue(mockCustomToken);

            await expect(
                service.createCustomToken(mockCreateTokenRequest, 'user-1')
            ).rejects.toThrow(BadRequestException);
            expect(customTokenRepository.findByName).toHaveBeenCalledWith('newToken');
        });

        it('should throw BadRequestException if field path already tokenized', async () => {
            customTokenRepository.findByName.mockResolvedValue(null);
            customTokenRepository.findByEntityAndFieldPath.mockResolvedValue(mockCustomToken);

            await expect(
                service.createCustomToken(mockCreateTokenRequest, 'user-1')
            ).rejects.toThrow(BadRequestException);
            expect(customTokenRepository.findByEntityAndFieldPath).toHaveBeenCalledWith(
                'client',
                'email'
            );
        });

        it('should validate token name format', async () => {
            const invalidRequest = { ...mockCreateTokenRequest, tokenName: '123invalid' };

            await expect(service.createCustomToken(invalidRequest, 'user-1')).rejects.toThrow(
                BadRequestException
            );
        });
    });

    describe('updateCustomToken', () => {
        const updateRequest: UpdateTokenRequest = {
            description: 'Updated description',
            isActive: false
        };

        it('should update a custom token successfully', async () => {
            const updatedToken = {
                ...mockCustomToken,
                description: 'Updated description',
                fullTokenName: mockCustomToken.fullTokenName,
                isSystemToken: mockCustomToken.isSystemToken,
                isCustomToken: mockCustomToken.isCustomToken,
                lastUsedAt: mockCustomToken.lastUsedAt
            } as CustomToken;
            customTokenRepository.findById.mockResolvedValue(mockCustomToken);
            customTokenRepository.update.mockResolvedValue(updatedToken);

            const result = await service.updateCustomToken('token-1', updateRequest, 'user-1');

            expect(customTokenRepository.findById).toHaveBeenCalledWith('token-1');
            expect(customTokenRepository.update).toHaveBeenCalledWith(
                'token-1',
                expect.objectContaining({
                    description: 'Updated description',
                    isActive: false,
                    lastModifiedBy: 'user-1'
                })
            );
            expect(result).toEqual(updatedToken);
        });

        it('should throw NotFoundException if token not found', async () => {
            customTokenRepository.findById.mockResolvedValue(null);

            await expect(
                service.updateCustomToken('non-existent', updateRequest, 'user-1')
            ).rejects.toThrow(NotFoundException);
        });

        it('should throw BadRequestException for system tokens', async () => {
            const systemToken = {
                ...mockCustomToken,
                isSystemToken: true,
                isCustomToken: false,
                fullTokenName: mockCustomToken.fullTokenName,
                lastUsedAt: mockCustomToken.lastUsedAt
            } as CustomToken;
            customTokenRepository.findById.mockResolvedValue(systemToken);

            await expect(
                service.updateCustomToken('token-1', updateRequest, 'user-1')
            ).rejects.toThrow(BadRequestException);
        });
    });

    describe('deleteCustomToken', () => {
        it('should delete a custom token successfully', async () => {
            customTokenRepository.findById.mockResolvedValue(mockCustomToken);
            customTokenRepository.softDelete.mockResolvedValue(true);

            const result = await service.deleteCustomToken('token-1', 'user-1');

            expect(customTokenRepository.findById).toHaveBeenCalledWith('token-1');
            expect(customTokenRepository.softDelete).toHaveBeenCalledWith('token-1', 'user-1');
            expect(result).toBe(true);
        });

        it('should throw NotFoundException if token not found', async () => {
            customTokenRepository.findById.mockResolvedValue(null);

            await expect(service.deleteCustomToken('non-existent', 'user-1')).rejects.toThrow(
                NotFoundException
            );
        });

        it('should throw BadRequestException for system tokens', async () => {
            const systemToken = {
                ...mockCustomToken,
                isSystemToken: true,
                isCustomToken: false,
                fullTokenName: mockCustomToken.fullTokenName,
                lastUsedAt: mockCustomToken.lastUsedAt
            } as CustomToken;
            customTokenRepository.findById.mockResolvedValue(systemToken);

            await expect(service.deleteCustomToken('token-1', 'user-1')).rejects.toThrow(
                BadRequestException
            );
        });
    });

    describe('getSystemTokens', () => {
        it('should return both database and hardcoded system tokens', async () => {
            const databaseSystemTokens = [mockSystemToken];
            customTokenRepository.findSystemTokens.mockResolvedValue(databaseSystemTokens);

            const result = await service.getSystemTokens();

            expect(customTokenRepository.findSystemTokens).toHaveBeenCalled();
            expect(result.length).toBeGreaterThan(1); // Should include hardcoded tokens
            expect(result).toContain(mockSystemToken);

            // Check that hardcoded system tokens are included
            const hardcodedTokens = result.filter(
                (token) =>
                    token.metadata?.source === 'hardcoded' && token.metadata?.location === 'system'
            );
            expect(hardcodedTokens.length).toBeGreaterThan(0);
        });
    });

    describe('searchTokens', () => {
        it('should search tokens with filters', async () => {
            const mockTokens = [mockCustomToken];
            customTokenRepository.findWithFilters.mockResolvedValue([mockTokens, 1]);

            const filters = {
                page: 1,
                limit: 10,
                search: 'client',
                tokenType: TokenType.CUSTOM
            };

            const result = await service.searchTokens(filters);

            expect(customTokenRepository.findWithFilters).toHaveBeenCalledWith(filters);
            expect(result.tokens).toEqual(mockTokens);
            expect(result.total).toBe(1);
        });

        it('should include system tokens when no specific filter', async () => {
            customTokenRepository.findWithFilters.mockResolvedValue([[], 0]);

            const result = await service.searchTokens({});

            expect(result.tokens.length).toBeGreaterThan(0); // Should include hardcoded system tokens
        });

        it('should filter only system tokens when requested', async () => {
            customTokenRepository.findWithFilters.mockResolvedValue([[], 0]);

            const result = await service.searchTokens({ tokenType: TokenType.SYSTEM });

            expect(result.tokens.every((token) => token.tokenType === TokenType.SYSTEM)).toBe(true);
        });
    });

    describe('resolveTokenValue', () => {
        const mockContext: TokenResolutionContext = {
            case: { id: 'case-1', caseNumber: 'CAS-2025-001' },
            client: { id: 'client-1', name: 'John Doe', email: '<EMAIL>' },
            property: { id: 'prop-1', fullAddress: '123 Main St' },
            customEntities: {},
            timestamp: new Date()
        };

        it('should resolve token value successfully', async () => {
            customTokenRepository.findByName.mockResolvedValue(mockCustomToken);
            customTokenRepository.incrementUsage.mockResolvedValue(undefined);

            const result = await service.resolveTokenValue('clientName', mockContext);

            expect(customTokenRepository.findByName).toHaveBeenCalledWith('clientName');
            expect(customTokenRepository.incrementUsage).toHaveBeenCalledWith('token-1');
            expect(result.success).toBe(true);
            expect(result.value).toBe('John Doe');
        });

        it('should return error for non-existent token', async () => {
            customTokenRepository.findByName.mockResolvedValue(null);

            const result = await service.resolveTokenValue('nonExistent', mockContext);

            expect(result.success).toBe(false);
            expect(result.error).toContain('not found');
        });

        it('should return error for inactive token', async () => {
            const inactiveToken = {
                ...mockCustomToken,
                isActive: false,
                fullTokenName: mockCustomToken.fullTokenName,
                isSystemToken: mockCustomToken.isSystemToken,
                isCustomToken: mockCustomToken.isCustomToken,
                lastUsedAt: mockCustomToken.lastUsedAt
            } as CustomToken;
            customTokenRepository.findByName.mockResolvedValue(inactiveToken);

            const result = await service.resolveTokenValue('clientName', mockContext);

            expect(result.success).toBe(false);
            expect(result.error).toContain('not active');
        });

        it('should apply transformations', async () => {
            const tokenWithTransform = {
                ...mockCustomToken,
                transformationConfig: { uppercase: true },
                fullTokenName: mockCustomToken.fullTokenName,
                isSystemToken: mockCustomToken.isSystemToken,
                isCustomToken: mockCustomToken.isCustomToken,
                lastUsedAt: mockCustomToken.lastUsedAt
            } as CustomToken;
            customTokenRepository.findByName.mockResolvedValue(tokenWithTransform);
            customTokenRepository.incrementUsage.mockResolvedValue(undefined);

            const result = await service.resolveTokenValue('clientName', mockContext);

            expect(result.success).toBe(true);
            expect(result.value).toBe('JOHN DOE');
            expect(result.transformed).toBe(true);
        });
    });

    describe('getAvailableEntities', () => {
        it('should return available entities with fields', () => {
            const entities = service.getAvailableEntities();

            expect(entities.length).toBeGreaterThan(0);
            expect(entities[0]).toHaveProperty('name');
            expect(entities[0]).toHaveProperty('displayName');
            expect(entities[0]).toHaveProperty('fields');
            expect(entities[0].fields.length).toBeGreaterThan(0);

            // Check that system entity exists
            const systemEntity = entities.find((e) => e.name === 'system');
            expect(systemEntity).toBeDefined();
        });
    });

    describe('bulkCreateTokens', () => {
        it('should create multiple tokens successfully', async () => {
            const requests = [
                mockCreateTokenRequest,
                { ...mockCreateTokenRequest, tokenName: 'token2' }
            ];

            customTokenRepository.findByName.mockResolvedValue(null);
            customTokenRepository.findByEntityAndFieldPath.mockResolvedValue(null);
            customTokenRepository.create.mockResolvedValue(mockCustomToken);

            const result = await service.bulkCreateTokens(requests, 'user-1');

            expect(result.successful).toBe(2);
            expect(result.failed).toBe(0);
            expect(result.results.length).toBe(2);
            expect(result.results.every((r) => r.success)).toBe(true);
        });

        it('should handle partial failures in bulk creation', async () => {
            const requests = [
                mockCreateTokenRequest,
                { ...mockCreateTokenRequest, tokenName: 'existingToken' }
            ];

            customTokenRepository.findByName
                .mockResolvedValueOnce(null)
                .mockResolvedValueOnce(mockCustomToken); // Second token already exists
            customTokenRepository.findByEntityAndFieldPath.mockResolvedValue(null);
            customTokenRepository.create.mockResolvedValue(mockCustomToken);

            const result = await service.bulkCreateTokens(requests, 'user-1');

            expect(result.successful).toBe(1);
            expect(result.failed).toBe(1);
            expect(result.results.length).toBe(2);
            expect(result.results[0].success).toBe(true);
            expect(result.results[1].success).toBe(false);
        });
    });

    describe('getTokenUsageStats', () => {
        it('should return token usage statistics', async () => {
            const mockStats = [
                {
                    id: 'token-1',
                    tokenName: 'clientName',
                    usageCount: 5,
                    lastUsedAt: new Date()
                }
            ];
            customTokenRepository.getUsageStats.mockResolvedValue(mockStats);

            const result = await service.getTokenUsageStats();

            expect(customTokenRepository.getUsageStats).toHaveBeenCalled();
            expect(result.length).toBe(1);
            expect(result[0]).toMatchObject({
                tokenId: 'token-1',
                tokenName: 'clientName',
                usageCount: 5
            });
        });
    });

    describe('private helper methods', () => {
        it('should validate token names correctly', () => {
            // Access private method for testing
            const service_any = service as any;

            // Valid token names
            expect(() => service_any.validateTokenName('validToken', 'client')).not.toThrow();
            expect(() => service_any.validateTokenName('token123', 'client')).not.toThrow();
            expect(() => service_any.validateTokenName('token_name', 'client')).not.toThrow();

            // Invalid token names
            expect(() => service_any.validateTokenName('', 'client')).toThrow(BadRequestException);
            expect(() => service_any.validateTokenName('123token', 'client')).toThrow(
                BadRequestException
            );
            expect(() => service_any.validateTokenName('token.name', 'client')).toThrow(
                BadRequestException
            );
            expect(() => service_any.validateTokenName('a', 'client')).toThrow(BadRequestException);
            expect(() => service_any.validateTokenName('a'.repeat(51), 'client')).toThrow(
                BadRequestException
            );
        });

        it('should map string data types correctly', () => {
            const service_any = service as any;

            expect(service_any.mapStringToTokenDataType('string')).toBe(TokenDataType.STRING);
            expect(service_any.mapStringToTokenDataType('number')).toBe(TokenDataType.NUMBER);
            expect(service_any.mapStringToTokenDataType('date')).toBe(TokenDataType.DATE);
            expect(service_any.mapStringToTokenDataType('currency')).toBe(TokenDataType.CURRENCY);
            expect(service_any.mapStringToTokenDataType('invalid')).toBe(TokenDataType.STRING); // Default
        });

        it('should apply transformations correctly', () => {
            const service_any = service as any;

            // Test uppercase transformation
            const result1 = service_any.applyTransformations(
                'test value',
                { uppercase: true },
                TokenDataType.STRING
            );
            expect(result1).toBe('TEST VALUE');

            // Test prefix/suffix
            const result2 = service_any.applyTransformations(
                'value',
                { prefix: 'Pre-', suffix: '-Post' },
                TokenDataType.STRING
            );
            expect(result2).toBe('Pre-value-Post');

            // Test currency formatting
            const result3 = service_any.applyTransformations(12345.67, {}, TokenDataType.CURRENCY);
            expect(result3).toBe('£12,345.67');
        });

        it('should validate token values correctly', () => {
            const service_any = service as any;

            // Test required validation
            expect(service_any.validateTokenValue(null, { required: true })).toBe(false);
            expect(service_any.validateTokenValue('value', { required: true })).toBe(true);

            // Test length validation
            expect(service_any.validateTokenValue('ab', { minLength: 3 })).toBe(false);
            expect(service_any.validateTokenValue('abc', { minLength: 3 })).toBe(true);
            expect(service_any.validateTokenValue('toolong', { maxLength: 5 })).toBe(false);

            // Test allowed values
            expect(
                service_any.validateTokenValue('invalid', { allowedValues: ['valid', 'ok'] })
            ).toBe(false);
            expect(
                service_any.validateTokenValue('valid', { allowedValues: ['valid', 'ok'] })
            ).toBe(true);
        });
    });
});
