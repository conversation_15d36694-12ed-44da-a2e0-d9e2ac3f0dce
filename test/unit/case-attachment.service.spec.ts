import { Test, TestingModule } from '@nestjs/testing';
import { CaseAttachmentService } from '../../apps/case-management/src/services/case-attachment.service';
import { CaseAttachmentRepository } from '../../apps/case-management/src/repositories/case-attachment.repository';
import { CaseRepository } from '../../apps/case-management/src/repositories/case.repository';
import { CaseAuditService } from '../../apps/case-management/src/services/case-audit.service';
import { NotFoundException } from '@nestjs/common';
import { CreateAttachmentDto } from '../../apps/case-management/src/dto/create-attachment.dto';
import { DocumentType } from '@app/common/typeorm/entities/tenant/case-attachment.entity';
import { Request } from 'express';

describe('CaseAttachmentService', () => {
    let service: CaseAttachmentService;

    // Mock repositories
    const mockCaseRepository = {
        findOne: jest.fn()
    };

    const mockCaseAttachmentRepository = {
        findOne: jest.fn(),
        find: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        findByCaseId: jest.fn(),
        findByFilename: jest.fn(),
        findByDocumentType: jest.fn(),
        deleteAttachment: jest.fn()
    };

    // Mock services
    const mockCaseAuditService = {
        logAttachmentAdded: jest.fn(),
        logAttachmentRemoved: jest.fn(),
        logAction: jest.fn()
    };

    // Mock request
    const mockRequest = {
        ip: '127.0.0.1',
        headers: {
            'x-forwarded-for': '********'
        }
    } as unknown as Request;

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                CaseAttachmentService,
                { provide: CaseRepository, useValue: mockCaseRepository },
                { provide: CaseAttachmentRepository, useValue: mockCaseAttachmentRepository },
                { provide: CaseAuditService, useValue: mockCaseAuditService }
            ]
        }).compile();

        service = module.get<CaseAttachmentService>(CaseAttachmentService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createAttachment', () => {
        it('should create a new attachment for a case', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const uploadedBy = 'user-id-123';
            const uploadedByName = 'Test User';

            const createAttachmentDto: CreateAttachmentDto = {
                filename: 'test-document.pdf',
                url: 'https://storage.example.com/test-document.pdf',
                fileSize: 1024,
                mimeType: 'application/pdf',
                description: 'Test document',
                documentType: DocumentType.EVIDENCE
            };

            const mockCase = { id: caseId, title: 'Test Case' };
            const mockAttachment = {
                id: 'attachment-id-123',
                ...createAttachmentDto,
                caseId,
                uploadedBy,
                uploadedByName
            };

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseAttachmentRepository.create.mockReturnValue(mockAttachment);
            mockCaseAttachmentRepository.save.mockResolvedValue(mockAttachment);

            // Act
            const result = await service.createAttachment(
                caseId,
                createAttachmentDto,
                uploadedBy,
                uploadedByName,
                mockRequest
            );

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAttachmentRepository.create).toHaveBeenCalledWith({
                caseId,
                filename: createAttachmentDto.filename,
                url: createAttachmentDto.url,
                fileSize: createAttachmentDto.fileSize,
                mimeType: createAttachmentDto.mimeType,
                description: createAttachmentDto.description,
                documentType: createAttachmentDto.documentType,
                uploadedBy,
                uploadedByName
            });
            expect(mockCaseAttachmentRepository.save).toHaveBeenCalledWith(mockAttachment);
            expect(mockCaseAuditService.logAttachmentAdded).toHaveBeenCalledWith(
                caseId,
                uploadedBy,
                uploadedByName,
                mockRequest,
                mockAttachment.id,
                mockAttachment.filename
            );
            expect(result).toEqual(mockAttachment);
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const caseId = 'non-existent-case-id';
            const uploadedBy = 'user-id-123';
            const uploadedByName = 'Test User';

            const createAttachmentDto: CreateAttachmentDto = {
                filename: 'test-document.pdf',
                url: 'https://storage.example.com/test-document.pdf',
                fileSize: 1024,
                mimeType: 'application/pdf'
            };

            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.createAttachment(
                    caseId,
                    createAttachmentDto,
                    uploadedBy,
                    uploadedByName,
                    mockRequest
                )
            ).rejects.toThrow(NotFoundException);
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAttachmentRepository.create).not.toHaveBeenCalled();
        });
    });

    describe('getCaseAttachments', () => {
        it('should return all attachments for a case', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const mockCase = { id: caseId, title: 'Test Case' };
            const mockAttachments = [
                { id: 'attachment-1', caseId, filename: 'document1.pdf' },
                { id: 'attachment-2', caseId, filename: 'document2.pdf' }
            ];

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseAttachmentRepository.findByCaseId.mockResolvedValue(mockAttachments);

            // Act
            const result = await service.getCaseAttachments(caseId);

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAttachmentRepository.findByCaseId).toHaveBeenCalledWith(caseId);
            expect(result).toEqual(mockAttachments);
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const caseId = 'non-existent-case-id';
            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getCaseAttachments(caseId)).rejects.toThrow(NotFoundException);
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAttachmentRepository.findByCaseId).not.toHaveBeenCalled();
        });
    });

    describe('getAttachmentById', () => {
        it('should return a specific attachment', async () => {
            // Arrange
            const attachmentId = 'attachment-id-123';
            const mockAttachment = {
                id: attachmentId,
                caseId: 'case-id-123',
                filename: 'document.pdf'
            };

            mockCaseAttachmentRepository.findOne.mockResolvedValue(mockAttachment);

            // Act
            const result = await service.getAttachmentById(attachmentId);

            // Assert
            expect(mockCaseAttachmentRepository.findOne).toHaveBeenCalledWith({
                where: { id: attachmentId }
            });
            expect(result).toEqual(mockAttachment);
        });

        it('should throw NotFoundException if attachment not found', async () => {
            // Arrange
            const attachmentId = 'non-existent-attachment-id';
            mockCaseAttachmentRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getAttachmentById(attachmentId)).rejects.toThrow(
                NotFoundException
            );
            expect(mockCaseAttachmentRepository.findOne).toHaveBeenCalledWith({
                where: { id: attachmentId }
            });
        });
    });

    describe('deleteAttachment', () => {
        it('should delete an attachment', async () => {
            // Arrange
            const attachmentId = 'attachment-id-123';
            const userId = 'user-id-123';
            const userName = 'Test User';

            const mockAttachment = {
                id: attachmentId,
                caseId: 'case-id-123',
                filename: 'document.pdf'
            };

            mockCaseAttachmentRepository.findOne.mockResolvedValue(mockAttachment);
            mockCaseAttachmentRepository.deleteAttachment = jest.fn().mockResolvedValue(undefined);

            // Act
            await service.deleteAttachment(attachmentId, userId, userName, mockRequest);

            // Assert
            expect(mockCaseAttachmentRepository.findOne).toHaveBeenCalledWith({
                where: { id: attachmentId }
            });
            expect(mockCaseAttachmentRepository.deleteAttachment).toHaveBeenCalledWith(
                attachmentId
            );
            expect(mockCaseAuditService.logAttachmentRemoved).toHaveBeenCalledWith(
                mockAttachment.caseId,
                userId,
                userName,
                mockRequest,
                attachmentId,
                mockAttachment.filename
            );
        });

        it('should throw NotFoundException if attachment not found', async () => {
            // Arrange
            const attachmentId = 'non-existent-attachment-id';
            const userId = 'user-id-123';
            const userName = 'Test User';

            mockCaseAttachmentRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.deleteAttachment(attachmentId, userId, userName, mockRequest)
            ).rejects.toThrow(NotFoundException);
            expect(mockCaseAttachmentRepository.findOne).toHaveBeenCalledWith({
                where: { id: attachmentId }
            });
            expect(mockCaseAttachmentRepository.delete).not.toHaveBeenCalled();
        });
    });

    describe('searchAttachmentsByFilename', () => {
        it('should return attachments matching the filename search', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const filename = 'document';
            const mockCase = { id: caseId, title: 'Test Case' };
            const mockAttachments = [
                { id: 'attachment-1', caseId, filename: 'document1.pdf' },
                { id: 'attachment-2', caseId, filename: 'document2.pdf' }
            ];

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseAttachmentRepository.findByFilename.mockResolvedValue(mockAttachments);

            // Act
            const result = await service.searchAttachmentsByFilename(caseId, filename);

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAttachmentRepository.findByFilename).toHaveBeenCalledWith(
                caseId,
                filename
            );
            expect(result).toEqual(mockAttachments);
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const caseId = 'non-existent-case-id';
            const filename = 'document';
            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(service.searchAttachmentsByFilename(caseId, filename)).rejects.toThrow(
                NotFoundException
            );
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAttachmentRepository.findByFilename).not.toHaveBeenCalled();
        });
    });

    describe('getAttachmentsByDocumentType', () => {
        it('should return attachments of the specified document type', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const documentType = DocumentType.EVIDENCE;
            const mockCase = { id: caseId, title: 'Test Case' };
            const mockAttachments = [
                { id: 'attachment-1', caseId, documentType: DocumentType.EVIDENCE },
                { id: 'attachment-2', caseId, documentType: DocumentType.EVIDENCE }
            ];

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseAttachmentRepository.findByDocumentType.mockResolvedValue(mockAttachments);

            // Act
            const result = await service.getAttachmentsByDocumentType(caseId, documentType);

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAttachmentRepository.findByDocumentType).toHaveBeenCalledWith(
                caseId,
                documentType
            );
            expect(result).toEqual(mockAttachments);
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const caseId = 'non-existent-case-id';
            const documentType = DocumentType.EVIDENCE;
            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.getAttachmentsByDocumentType(caseId, documentType)
            ).rejects.toThrow(NotFoundException);
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseAttachmentRepository.findByDocumentType).not.toHaveBeenCalled();
        });
    });
});
