import { Test, TestingModule } from '@nestjs/testing';
import { DocumentFolderController } from '../../apps/document-engine/src/controllers/document-folder.controller';
import { DocumentFolderService } from '../../apps/document-engine/src/document/services/document-folder.service';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { IsLawyer } from '@app/common/roles/decorators';
import { DocumentFolder } from '@app/common/typeorm/entities';
import { Request } from 'express';

// Mock request with user information
interface RequestWithUser extends Request {
    user: {
        id: string;
        username: string;
        email: string;
        roles: string[];
        systemUserId: string;
        preferred_username: string;
    };
}

describe('DocumentFolderController', () => {
    let controller: DocumentFolderController;
    let folderService: DocumentFolderService;

    // Mock DocumentFolderService
    const mockFolderService = {
        createFolder: jest.fn(),
        getFolderById: jest.fn(),
        updateFolder: jest.fn(),
        deleteFolder: jest.fn(),
        folderRepo: {
            findChildFolders: jest.fn(),
            findRootFoldersByCaseId: jest.fn(),
            searchInCase: jest.fn(),
            findByCaseIdAndId: jest.fn(),
            getFolderPath: jest.fn()
        }
    };

    // Mock request
    const mockRequest = {
        ip: '127.0.0.1',
        headers: {
            'x-forwarded-for': '********'
        },
        user: {
            id: 'auth0|12345',
            systemUserId: 'user-123',
            username: 'testuser',
            preferred_username: 'testuser',
            email: '<EMAIL>',
            roles: ['lawyer']
        }
    } as unknown as RequestWithUser;

    // Mock folder
    const mockFolder = {
        id: 'folder-123',
        name: 'Test Folder',
        description: 'Test description',
        caseId: 'case-123',
        parentFolderId: null,
        path: '/Test Folder',
        createdBy: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date()
    } as unknown as DocumentFolder;

    // Mock guards
    const mockJwtGuard = { canActivate: jest.fn().mockReturnValue(true) };
    const mockTenantGuard = { canActivate: jest.fn().mockReturnValue(true) };
    const mockRolesGuard = { canActivate: jest.fn().mockReturnValue(true) };
    const mockIsLawyer = { canActivate: jest.fn().mockReturnValue(true) };

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            controllers: [DocumentFolderController],
            providers: [
                {
                    provide: DocumentFolderService,
                    useValue: mockFolderService
                }
            ]
        })
            .overrideGuard(JwtGuard)
            .useValue(mockJwtGuard)
            .overrideGuard(TenantGuard)
            .useValue(mockTenantGuard)
            .overrideGuard(RolesGuard)
            .useValue(mockRolesGuard)
            .overrideGuard(IsLawyer)
            .useValue(mockIsLawyer)
            .compile();

        controller = module.get<DocumentFolderController>(DocumentFolderController);
        folderService = module.get<DocumentFolderService>(DocumentFolderService);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
        expect(folderService).toBeDefined();
    });

    describe('createFolder', () => {
        it('should create a new folder', async () => {
            // Arrange
            const createData = {
                name: 'New Folder',
                description: 'New folder description',
                caseId: 'case-123',
                parentFolderId: 'parent-folder-123'
            };

            mockFolderService.createFolder.mockResolvedValue(mockFolder);

            // Act
            const result = await controller.createFolder(createData, mockRequest);

            // Assert
            expect(mockFolderService.createFolder).toHaveBeenCalledWith({
                name: createData.name,
                description: createData.description,
                caseId: createData.caseId,
                parentFolderId: createData.parentFolderId,
                createdBy: mockRequest.user.systemUserId
            });
            expect(result).toEqual({
                code: 201,
                status: 'Created',
                message: 'Folder created successfully',
                data: mockFolder
            });
        });

        it('should create folder without optional fields', async () => {
            // Arrange
            const createData = {
                name: 'New Folder',
                caseId: 'case-123'
            };

            mockFolderService.createFolder.mockResolvedValue(mockFolder);

            // Act
            await controller.createFolder(createData, mockRequest);

            // Assert
            expect(mockFolderService.createFolder).toHaveBeenCalledWith({
                name: createData.name,
                description: undefined,
                caseId: createData.caseId,
                parentFolderId: undefined,
                createdBy: mockRequest.user.systemUserId
            });
        });
    });

    describe('findFolders', () => {
        it('should return root folders when caseId provided without parentFolderId', async () => {
            // Arrange
            const caseId = 'case-123';
            const folders = [mockFolder];
            mockFolderService.folderRepo.findRootFoldersByCaseId.mockResolvedValue(folders);

            // Act
            const result = await controller.findFolders(caseId, undefined);

            // Assert
            expect(mockFolderService.folderRepo.findRootFoldersByCaseId).toHaveBeenCalledWith(
                caseId
            );
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Folders retrieved successfully',
                data: folders
            });
        });

        it('should return child folders when parentFolderId provided', async () => {
            // Arrange
            const caseId = 'case-123';
            const parentFolderId = 'parent-folder-123';
            const folders = [mockFolder];
            mockFolderService.folderRepo.findChildFolders.mockResolvedValue(folders);

            // Act
            const result = await controller.findFolders(caseId, parentFolderId);

            // Assert
            expect(mockFolderService.folderRepo.findChildFolders).toHaveBeenCalledWith(
                parentFolderId
            );
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Folders retrieved successfully',
                data: folders
            });
        });

        it('should return bad request when caseId not provided', async () => {
            // Act
            const result = await controller.findFolders('' as any, undefined);

            // Assert
            expect(result).toEqual({
                code: 400,
                status: 'Bad Request',
                message: 'Case ID is required',
                data: {}
            });
        });
    });

    describe('searchFolders', () => {
        it('should search folders with all parameters', async () => {
            // Arrange
            const caseId = 'case-123';
            const searchTerm = 'test';
            const limit = '20';
            const folders = [mockFolder];

            mockFolderService.folderRepo.searchInCase.mockResolvedValue(folders);

            // Act
            const result = await controller.searchFolders(caseId, searchTerm, limit);

            // Assert
            expect(mockFolderService.folderRepo.searchInCase).toHaveBeenCalledWith(
                caseId,
                searchTerm,
                20
            );
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Folders searched successfully',
                data: folders
            });
        });

        it('should search folders without limit', async () => {
            // Arrange
            const caseId = 'case-123';
            const searchTerm = 'test';
            const folders = [mockFolder];

            mockFolderService.folderRepo.searchInCase.mockResolvedValue(folders);

            // Act
            const result = await controller.searchFolders(caseId, searchTerm, undefined);

            // Assert
            expect(mockFolderService.folderRepo.searchInCase).toHaveBeenCalledWith(
                caseId,
                searchTerm,
                undefined
            );
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Folders searched successfully',
                data: folders
            });
        });

        it('should return bad request when caseId not provided', async () => {
            // Act
            const result = await controller.searchFolders('' as any, 'test', undefined);

            // Assert
            expect(result).toEqual({
                code: 400,
                status: 'Bad Request',
                message: 'Case ID is required',
                data: {}
            });
        });

        it('should return bad request when searchTerm not provided', async () => {
            // Act
            const result = await controller.searchFolders('case-123', '' as any, undefined);

            // Assert
            expect(result).toEqual({
                code: 400,
                status: 'Bad Request',
                message: 'Search term is required',
                data: {}
            });
        });
    });

    describe('findOne', () => {
        it('should return folder by ID when caseId not provided', async () => {
            // Arrange
            const folderId = 'folder-123';
            mockFolderService.getFolderById.mockResolvedValue(mockFolder);

            // Act
            const result = await controller.findOne(folderId, undefined);

            // Assert
            expect(mockFolderService.getFolderById).toHaveBeenCalledWith(folderId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Folder retrieved successfully',
                data: mockFolder
            });
        });

        it('should return folder by ID and caseId for security check', async () => {
            // Arrange
            const folderId = 'folder-123';
            const caseId = 'case-123';
            mockFolderService.folderRepo.findByCaseIdAndId.mockResolvedValue(mockFolder);

            // Act
            const result = await controller.findOne(folderId, caseId);

            // Assert
            expect(mockFolderService.folderRepo.findByCaseIdAndId).toHaveBeenCalledWith(
                caseId,
                folderId
            );
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Folder retrieved successfully',
                data: mockFolder
            });
        });

        it('should return not found when folder does not exist', async () => {
            // Arrange
            const folderId = 'non-existent';
            mockFolderService.getFolderById.mockResolvedValue(null);

            // Act
            const result = await controller.findOne(folderId);

            // Assert
            expect(mockFolderService.getFolderById).toHaveBeenCalledWith(folderId);
            expect(result).toEqual({
                code: 404,
                status: 'Not Found',
                message: 'Folder not found',
                data: {}
            });
        });
    });

    describe('getFolderPath', () => {
        it('should return folder path', async () => {
            // Arrange
            const folderId = 'folder-123';
            const path = '/Test Folder/Subfolder';
            mockFolderService.folderRepo.getFolderPath.mockResolvedValue(path);

            // Act
            const result = await controller.getFolderPath(folderId);

            // Assert
            expect(mockFolderService.folderRepo.getFolderPath).toHaveBeenCalledWith(folderId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Folder path retrieved successfully',
                data: path
            });
        });
    });

    describe('updateFolder', () => {
        it('should update folder when it exists', async () => {
            // Arrange
            const folderId = 'folder-123';
            const updateData = {
                name: 'Updated Folder Name',
                description: 'Updated description',
                parentFolderId: 'new-parent-123'
            };

            const updatedFolder = { ...mockFolder, ...updateData };
            mockFolderService.updateFolder.mockResolvedValue(updatedFolder);

            // Act
            const result = await controller.updateFolder(folderId, updateData, mockRequest);

            // Assert
            expect(mockFolderService.updateFolder).toHaveBeenCalledWith(folderId, {
                ...updateData,
                updatedBy: mockRequest.user.systemUserId
            });
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Folder updated successfully',
                data: updatedFolder
            });
        });

        it('should return not found when folder does not exist', async () => {
            // Arrange
            const folderId = 'non-existent';
            const updateData = {
                name: 'Updated Folder Name'
            };

            mockFolderService.updateFolder.mockResolvedValue(null);

            // Act
            const result = await controller.updateFolder(folderId, updateData, mockRequest);

            // Assert
            expect(mockFolderService.updateFolder).toHaveBeenCalledWith(folderId, {
                ...updateData,
                updatedBy: mockRequest.user.systemUserId
            });
            expect(result).toEqual({
                code: 404,
                status: 'Not Found',
                message: 'Folder not found',
                data: {}
            });
        });
    });

    describe('removeFolder', () => {
        it('should delete folder successfully', async () => {
            // Arrange
            const folderId = 'folder-123';
            mockFolderService.deleteFolder.mockResolvedValue(true);

            // Act
            const result = await controller.removeFolder(folderId);

            // Assert
            expect(mockFolderService.deleteFolder).toHaveBeenCalledWith(folderId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Folder deleted successfully',
                data: { success: true }
            });
        });

        it('should return success false when folder not found', async () => {
            // Arrange
            const folderId = 'non-existent';
            mockFolderService.deleteFolder.mockResolvedValue(false);

            // Act
            const result = await controller.removeFolder(folderId);

            // Assert
            expect(mockFolderService.deleteFolder).toHaveBeenCalledWith(folderId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Folder deleted successfully',
                data: { success: false }
            });
        });
    });
});
