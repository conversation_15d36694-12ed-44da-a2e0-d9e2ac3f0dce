import { Test, TestingModule } from '@nestjs/testing';
import { DocumentService } from '../../apps/document-engine/src/document/services/document.service';
import { DocumentRepository } from '../../apps/document-engine/src/repositories/document.repository';
import { S3StorageService } from '../../apps/document-engine/src/document/services/s3-storage.service';
import { NotFoundException } from '@nestjs/common';
import { Document } from '@app/common/typeorm/entities';

describe('DocumentService', () => {
    let service: DocumentService;

    // Mock repositories
    const mockDocumentRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findOneBy: jest.fn(),
        findByCaseId: jest.fn(),
        findByFolderId: jest.fn(),
        removeById: jest.fn(),
        search: jest.fn(),
        findRootDocumentsByCaseId: jest.fn()
    };

    // Mock services
    const mockS3StorageService = {
        uploadFile: jest.fn(),
        getDownloadUrl: jest.fn(),
        deleteFile: jest.fn()
    };

    // Mock document data
    const mockDocument: Document = {
        id: 'doc-123',
        name: 'Test Document',
        description: 'Test description',
        caseId: 'case-123',
        folderId: 'folder-123',
        s3Key: 'test-key',
        s3Bucket: 'test-bucket',
        fileName: 'test.pdf',
        fileExtension: 'pdf',
        mimeType: 'application/pdf',
        sizeInBytes: '1024',
        checksum: 'test-checksum',
        createdBy: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
        lastModifiedBy: 'user-123'
    } as Document;

    const mockFileBuffer = Buffer.from('test file content');

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                DocumentService,
                { provide: DocumentRepository, useValue: mockDocumentRepository },
                { provide: S3StorageService, useValue: mockS3StorageService }
            ]
        }).compile();

        service = module.get<DocumentService>(DocumentService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createDocument', () => {
        it('should create a new document successfully', async () => {
            // Arrange
            const createData = {
                name: 'Test Document',
                description: 'Test description',
                caseId: 'case-123',
                folderId: 'folder-123',
                fileBuffer: mockFileBuffer,
                filename: 'test.pdf',
                mimeType: 'application/pdf',
                createdBy: 'user-123'
            };

            const uploadResult = {
                s3Key: 'test-key',
                s3Bucket: 'test-bucket',
                checksum: 'test-checksum'
            };

            mockS3StorageService.uploadFile.mockResolvedValue(uploadResult);
            mockDocumentRepository.create.mockReturnValue(mockDocument);
            mockDocumentRepository.save.mockResolvedValue(mockDocument);

            // Act
            const result = await service.createDocument(createData);

            // Assert
            expect(mockS3StorageService.uploadFile).toHaveBeenCalledWith(
                createData.caseId,
                createData.folderId,
                createData.filename,
                createData.fileBuffer,
                createData.mimeType
            );
            expect(mockDocumentRepository.create).toHaveBeenCalledWith({
                name: createData.name,
                description: createData.description,
                caseId: createData.caseId,
                folderId: createData.folderId,
                s3Key: uploadResult.s3Key,
                s3Bucket: uploadResult.s3Bucket,
                fileName: createData.filename,
                fileExtension: 'pdf',
                mimeType: createData.mimeType,
                sizeInBytes: '17',
                checksum: uploadResult.checksum,
                createdBy: createData.createdBy,
                updatedAt: expect.any(Date),
                createdAt: expect.any(Date),
                lastModifiedBy: createData.createdBy
            });
            expect(mockDocumentRepository.save).toHaveBeenCalledWith(mockDocument);
            expect(result).toEqual(mockDocument);
        });

        it('should create document without folderId when not provided', async () => {
            // Arrange
            const createData = {
                name: 'Test Document',
                caseId: 'case-123',
                fileBuffer: mockFileBuffer,
                filename: 'test.pdf',
                mimeType: 'application/pdf',
                createdBy: 'user-123'
            };

            const uploadResult = {
                s3Key: 'test-key',
                s3Bucket: 'test-bucket',
                checksum: 'test-checksum'
            };

            mockS3StorageService.uploadFile.mockResolvedValue(uploadResult);
            mockDocumentRepository.create.mockReturnValue(mockDocument);
            mockDocumentRepository.save.mockResolvedValue(mockDocument);

            // Act
            await service.createDocument(createData);

            // Assert
            expect(mockS3StorageService.uploadFile).toHaveBeenCalledWith(
                createData.caseId,
                null,
                createData.filename,
                createData.fileBuffer,
                createData.mimeType
            );
        });

        it('should extract file extension from filename when not provided', async () => {
            // Arrange
            const createData = {
                name: 'Test Document',
                caseId: 'case-123',
                fileBuffer: mockFileBuffer,
                filename: 'test-document.docx',
                mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                createdBy: 'user-123'
            };

            const uploadResult = {
                s3Key: 'test-key',
                s3Bucket: 'test-bucket',
                checksum: 'test-checksum'
            };

            mockS3StorageService.uploadFile.mockResolvedValue(uploadResult);
            mockDocumentRepository.create.mockReturnValue(mockDocument);
            mockDocumentRepository.save.mockResolvedValue(mockDocument);

            // Act
            await service.createDocument(createData);

            // Assert
            expect(mockDocumentRepository.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    fileExtension: 'docx'
                })
            );
        });
    });

    describe('getDocumentById', () => {
        it('should return document when found', async () => {
            // Arrange
            const documentId = 'doc-123';
            mockDocumentRepository.findOneBy.mockResolvedValue(mockDocument);

            // Act
            const result = await service.getDocumentById(documentId);

            // Assert
            expect(mockDocumentRepository.findOneBy).toHaveBeenCalledWith({ id: documentId });
            expect(result).toEqual(mockDocument);
        });

        it('should return null when document not found', async () => {
            // Arrange
            const documentId = 'non-existent';
            mockDocumentRepository.findOneBy.mockResolvedValue(null);

            // Act
            const result = await service.getDocumentById(documentId);

            // Assert
            expect(mockDocumentRepository.findOneBy).toHaveBeenCalledWith({ id: documentId });
            expect(result).toBeNull();
        });
    });

    describe('getDocumentsByCaseId', () => {
        it('should return documents for a case', async () => {
            // Arrange
            const caseId = 'case-123';
            const documents = [mockDocument];
            mockDocumentRepository.findByCaseId.mockResolvedValue(documents);

            // Act
            const result = await service.getDocumentsByCaseId(caseId);

            // Assert
            expect(mockDocumentRepository.findByCaseId).toHaveBeenCalledWith(caseId);
            expect(result).toEqual(documents);
        });
    });

    describe('getDocumentsByFolderId', () => {
        it('should return documents for a folder', async () => {
            // Arrange
            const folderId = 'folder-123';
            const documents = [mockDocument];
            mockDocumentRepository.findByFolderId.mockResolvedValue(documents);

            // Act
            const result = await service.getDocumentsByFolderId(folderId);

            // Assert
            expect(mockDocumentRepository.findByFolderId).toHaveBeenCalledWith(folderId);
            expect(result).toEqual(documents);
        });
    });

    describe('getDocumentDownloadUrl', () => {
        it('should return download URL when document exists', async () => {
            // Arrange
            const documentId = 'doc-123';
            const downloadUrl = 'https://s3.amazonaws.com/test-bucket/test-key';
            mockDocumentRepository.findOneBy.mockResolvedValue(mockDocument);
            mockS3StorageService.getDownloadUrl.mockResolvedValue(downloadUrl);

            // Act
            const result = await service.getDocumentDownloadUrl(documentId);

            // Assert
            expect(mockDocumentRepository.findOneBy).toHaveBeenCalledWith({ id: documentId });
            expect(mockS3StorageService.getDownloadUrl).toHaveBeenCalledWith(
                mockDocument.s3Key,
                mockDocument.fileName
            );
            expect(result).toEqual({
                url: downloadUrl,
                filename: mockDocument.fileName
            });
        });

        it('should throw NotFoundException when document not found', async () => {
            // Arrange
            const documentId = 'non-existent';
            mockDocumentRepository.findOneBy.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getDocumentDownloadUrl(documentId)).rejects.toThrow(
                NotFoundException
            );
            expect(mockDocumentRepository.findOneBy).toHaveBeenCalledWith({ id: documentId });
        });
    });

    describe('updateDocument', () => {
        it('should update document when it exists', async () => {
            // Arrange
            const documentId = 'doc-123';
            const updateData = {
                name: 'Updated Document Name',
                description: 'Updated description',
                updatedBy: 'user-123'
            };

            const updatedDocument = { ...mockDocument, ...updateData };
            mockDocumentRepository.findOneBy.mockResolvedValue(mockDocument);
            mockDocumentRepository.save.mockResolvedValue(updatedDocument);

            // Act
            const result = await service.updateDocument(documentId, updateData);

            // Assert
            expect(mockDocumentRepository.findOneBy).toHaveBeenCalledWith({ id: documentId });
            expect(mockDocumentRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    ...mockDocument,
                    name: updateData.name,
                    description: updateData.description,
                    updatedAt: expect.any(Date),
                    lastModifiedBy: updateData.updatedBy
                })
            );
            expect(result).toEqual(updatedDocument);
        });

        it('should return null when document not found', async () => {
            // Arrange
            const documentId = 'non-existent';
            const updateData = {
                name: 'Updated Document Name',
                updatedBy: 'user-123'
            };

            mockDocumentRepository.findOneBy.mockResolvedValue(null);

            // Act
            const result = await service.updateDocument(documentId, updateData);

            // Assert
            expect(mockDocumentRepository.findOneBy).toHaveBeenCalledWith({ id: documentId });
            expect(mockDocumentRepository.save).not.toHaveBeenCalled();
            expect(result).toBeNull();
        });

        it('should only update provided fields', async () => {
            // Arrange
            const documentId = 'doc-123';
            const updateData = {
                name: 'Updated Document Name',
                updatedBy: 'user-123'
            };

            mockDocumentRepository.findOneBy.mockResolvedValue(mockDocument);
            mockDocumentRepository.save.mockResolvedValue(mockDocument);

            // Act
            await service.updateDocument(documentId, updateData);

            // Assert
            expect(mockDocumentRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    name: updateData.name,
                    description: mockDocument.description, // Should remain unchanged
                    updatedAt: expect.any(Date),
                    lastModifiedBy: updateData.updatedBy
                })
            );
        });
    });

    describe('deleteDocument', () => {
        it('should delete document successfully', async () => {
            // Arrange
            const documentId = 'doc-123';
            mockDocumentRepository.findOneBy.mockResolvedValue(mockDocument);
            mockS3StorageService.deleteFile.mockResolvedValue(undefined);
            mockDocumentRepository.removeById.mockResolvedValue(undefined);

            // Act
            const result = await service.deleteDocument(documentId);

            // Assert
            expect(mockDocumentRepository.findOneBy).toHaveBeenCalledWith({ id: documentId });
            expect(mockS3StorageService.deleteFile).toHaveBeenCalledWith(mockDocument.s3Key);
            expect(mockDocumentRepository.removeById).toHaveBeenCalledWith(documentId);
            expect(result).toBe(true);
        });

        it('should return false when document not found', async () => {
            // Arrange
            const documentId = 'non-existent';
            mockDocumentRepository.findOneBy.mockResolvedValue(null);

            // Act
            const result = await service.deleteDocument(documentId);

            // Assert
            expect(mockDocumentRepository.findOneBy).toHaveBeenCalledWith({ id: documentId });
            expect(mockS3StorageService.deleteFile).not.toHaveBeenCalled();
            expect(mockDocumentRepository.removeById).not.toHaveBeenCalled();
            expect(result).toBe(false);
        });

        it('should throw error when S3 deletion fails', async () => {
            // Arrange
            const documentId = 'doc-123';
            const error = new Error('S3 deletion failed');
            mockDocumentRepository.findOneBy.mockResolvedValue(mockDocument);
            mockS3StorageService.deleteFile.mockRejectedValue(error);

            // Act & Assert
            await expect(service.deleteDocument(documentId)).rejects.toThrow(
                'Failed to delete document: S3 deletion failed'
            );
            expect(mockDocumentRepository.removeById).not.toHaveBeenCalled();
        });
    });

    describe('searchDocuments', () => {
        it('should search documents with default limit', async () => {
            // Arrange
            const searchTerm = 'test';
            const documents = [mockDocument];
            mockDocumentRepository.search.mockResolvedValue(documents);

            // Act
            const result = await service.searchDocuments(searchTerm);

            // Assert
            expect(mockDocumentRepository.search).toHaveBeenCalledWith(
                searchTerm,
                undefined,
                undefined,
                10
            );
            expect(result).toEqual(documents);
        });

        it('should search documents with custom options', async () => {
            // Arrange
            const searchTerm = 'test';
            const options = {
                folderId: 'folder-123',
                caseId: 'case-123',
                limit: 20
            };
            const documents = [mockDocument];
            mockDocumentRepository.search.mockResolvedValue(documents);

            // Act
            const result = await service.searchDocuments(searchTerm, options);

            // Assert
            expect(mockDocumentRepository.search).toHaveBeenCalledWith(
                searchTerm,
                options.folderId,
                options.caseId,
                options.limit
            );
            expect(result).toEqual(documents);
        });
    });

    describe('replaceDocumentFile', () => {
        it('should replace document file successfully', async () => {
            // Arrange
            const documentId = 'doc-123';
            const replaceData = {
                fileBuffer: mockFileBuffer,
                filename: 'new-file.pdf',
                mimeType: 'application/pdf',
                updatedBy: 'user-123'
            };

            const uploadResult = {
                s3Key: 'test-key',
                s3Bucket: 'test-bucket',
                checksum: 'new-checksum'
            };

            const updatedDocument = {
                ...mockDocument,
                s3Key: uploadResult.s3Key,
                fileName: replaceData.filename,
                fileExtension: 'pdf',
                mimeType: replaceData.mimeType,
                sizeInBytes: '18',
                checksum: uploadResult.checksum,
                updatedAt: new Date(),
                lastModifiedBy: replaceData.updatedBy
            };

            mockDocumentRepository.findOneBy.mockResolvedValue(mockDocument);
            mockS3StorageService.deleteFile.mockResolvedValue(undefined);
            mockS3StorageService.uploadFile.mockResolvedValue(uploadResult);
            mockDocumentRepository.save.mockResolvedValue(updatedDocument);

            // Act
            const result = await service.replaceDocumentFile(documentId, replaceData);

            // Assert
            expect(mockDocumentRepository.findOneBy).toHaveBeenCalledWith({ id: documentId });
            expect(mockS3StorageService.deleteFile).toHaveBeenCalledWith(mockDocument.s3Key);
            expect(mockS3StorageService.uploadFile).toHaveBeenCalledWith(
                mockDocument.caseId,
                mockDocument.folderId,
                replaceData.filename,
                replaceData.fileBuffer,
                replaceData.mimeType
            );
            expect(mockDocumentRepository.save).toHaveBeenCalledWith({
                id: mockDocument.id,
                s3Bucket: mockDocument.s3Bucket,
                caseId: mockDocument.caseId,
                folderId: mockDocument.folderId,
                name: mockDocument.name,
                description: mockDocument.description,
                s3Key: uploadResult.s3Key,
                fileName: replaceData.filename,
                fileExtension: 'pdf',
                mimeType: replaceData.mimeType,
                sizeInBytes: '17',
                checksum: uploadResult.checksum,
                createdBy: replaceData.updatedBy,
                updatedAt: expect.any(Date),
                createdAt: expect.any(Date),
                lastModifiedBy: replaceData.updatedBy
            });
            expect(result).toEqual(updatedDocument);
        });

        it('should throw NotFoundException when document not found', async () => {
            // Arrange
            const documentId = 'non-existent';
            const replaceData = {
                fileBuffer: mockFileBuffer,
                filename: 'new-file.pdf',
                updatedBy: 'user-123'
            };

            mockDocumentRepository.findOneBy.mockResolvedValue(null);

            // Act & Assert
            await expect(service.replaceDocumentFile(documentId, replaceData)).rejects.toThrow(
                NotFoundException
            );
            expect(mockS3StorageService.deleteFile).not.toHaveBeenCalled();
            expect(mockS3StorageService.uploadFile).not.toHaveBeenCalled();
        });

        it('should use existing mimeType when not provided', async () => {
            // Arrange
            const documentId = 'doc-123';
            const replaceData = {
                fileBuffer: mockFileBuffer,
                filename: 'new-file.pdf',
                updatedBy: 'user-123'
            };

            const uploadResult = {
                s3Key: 'new-key',
                s3Bucket: 'test-bucket',
                checksum: 'new-checksum'
            };

            mockDocumentRepository.findOneBy.mockResolvedValue(mockDocument);
            mockS3StorageService.deleteFile.mockResolvedValue(undefined);
            mockS3StorageService.uploadFile.mockResolvedValue(uploadResult);
            mockDocumentRepository.save.mockResolvedValue(mockDocument);

            // Act
            await service.replaceDocumentFile(documentId, replaceData);

            // Assert
            expect(mockS3StorageService.uploadFile).toHaveBeenCalledWith(
                mockDocument.caseId,
                mockDocument.folderId,
                replaceData.filename,
                replaceData.fileBuffer,
                mockDocument.mimeType // Should use existing mimeType
            );
        });
    });

    describe('getFileExtension', () => {
        it('should extract file extension from filename', async () => {
            // This is a private method, but we can test it indirectly through createDocument
            const createData = {
                name: 'Test Document',
                caseId: 'case-123',
                fileBuffer: mockFileBuffer,
                filename: 'test-document.xlsx',
                mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                createdBy: 'user-123'
            };

            const uploadResult = {
                s3Key: 'test-key',
                s3Bucket: 'test-bucket',
                checksum: 'test-checksum'
            };

            mockS3StorageService.uploadFile.mockResolvedValue(uploadResult);
            mockDocumentRepository.create.mockReturnValue(mockDocument);
            mockDocumentRepository.save.mockResolvedValue(mockDocument);

            // Act
            await service.createDocument(createData);
            // Assert
            expect(mockDocumentRepository.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    fileExtension: 'xlsx'
                })
            );
        });

        it('should handle filename without extension', async () => {
            const createData = {
                name: 'Test Document',
                caseId: 'case-123',
                fileBuffer: mockFileBuffer,
                filename: 'test-document',
                mimeType: 'text/plain',
                createdBy: 'user-123'
            };

            const uploadResult = {
                s3Key: 'test-key',
                s3Bucket: 'test-bucket',
                checksum: 'test-checksum'
            };

            mockS3StorageService.uploadFile.mockResolvedValue(uploadResult);
            mockDocumentRepository.create.mockReturnValue(mockDocument);
            mockDocumentRepository.save.mockResolvedValue(mockDocument);

            // Act
            await service.createDocument(createData);

            // Assert
            expect(mockDocumentRepository.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    fileExtension: ''
                })
            );
        });
    });
});
