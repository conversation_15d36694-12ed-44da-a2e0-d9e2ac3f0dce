import { Test, TestingModule } from '@nestjs/testing';
import { DocumentAuditService } from '../../apps/document-engine/src/document/services/document-audit.service';
import { DocumentAuditRepository } from '../../apps/document-engine/src/repositories/document-audit.repository';
import { DocumentAudit } from '@app/common/typeorm/entities';

describe('DocumentAuditService', () => {
    let service: DocumentAuditService;

    // Mock repository
    const mockDocumentAuditRepository = {
        create: jest.fn(),
        save: jest.fn(),
        findByDocumentId: jest.fn(),
        findAuditEntries: jest.fn()
    };

    // Mock audit entry
    const mockAuditEntry = {
        id: 'audit-123',
        documentId: 'doc-123',
        documentVersionId: 'version-123',
        actionType: 'CREATE',
        actionDetails: { operation: 'document_created' },
        performedBy: 'user-123',
        performedAt: new Date(),
        ipAddress: '127.0.0.1',
        userAgent: 'Mozilla/5.0'
    } as unknown as DocumentAudit;

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                DocumentAuditService,
                { provide: DocumentAuditRepository, useValue: mockDocumentAuditRepository }
            ]
        }).compile();

        service = module.get<DocumentAuditService>(DocumentAuditService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createAuditEntry', () => {
        it('should create audit entry successfully', async () => {
            // Arrange
            const auditData = {
                documentId: 'doc-123',
                actionType: 'CREATE',
                actionDetails: { operation: 'document_created' },
                performedBy: 'user-123',
                ipAddress: '127.0.0.1',
                userAgent: 'Mozilla/5.0'
            };

            mockDocumentAuditRepository.create.mockReturnValue(mockAuditEntry);
            mockDocumentAuditRepository.save.mockResolvedValue(mockAuditEntry);

            // Act
            const result = await service.createAuditEntry(auditData);

            // Assert
            expect(mockDocumentAuditRepository.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    documentId: auditData.documentId,
                    actionType: auditData.actionType,
                    actionDetails: auditData.actionDetails,
                    performedBy: auditData.performedBy,
                    performedAt: expect.any(Date),
                    ipAddress: auditData.ipAddress,
                    userAgent: auditData.userAgent
                })
            );
            expect(mockDocumentAuditRepository.save).toHaveBeenCalledWith(mockAuditEntry);
            expect(result).toEqual(mockAuditEntry);
        });

        it('should create audit entry with default values for optional fields', async () => {
            // Arrange
            const auditData = {
                actionType: 'VIEW',
                performedBy: 'user-123'
            };

            mockDocumentAuditRepository.create.mockReturnValue(mockAuditEntry);
            mockDocumentAuditRepository.save.mockResolvedValue(mockAuditEntry);

            // Act
            await service.createAuditEntry(auditData);

            // Assert
            expect(mockDocumentAuditRepository.create).toHaveBeenCalledWith(
                expect.objectContaining({
                    documentId: undefined,
                    actionType: auditData.actionType,
                    actionDetails: {},
                    performedBy: auditData.performedBy,
                    ipAddress: '',
                    userAgent: ''
                })
            );
        });

        it('should handle repository errors', async () => {
            // Arrange
            const auditData = {
                actionType: 'CREATE',
                performedBy: 'user-123'
            };

            const error = new Error('Database connection failed');
            mockDocumentAuditRepository.create.mockRejectedValue(error);

            // Act & Assert
            await expect(service.createAuditEntry(auditData)).rejects.toThrow(
                'Failed to create audit entry: Database connection failed'
            );
        });
    });

    describe('findByDocumentId', () => {
        it('should return audit entries for a document', async () => {
            // Arrange
            const documentId = 'doc-123';
            const auditEntries = [mockAuditEntry];
            mockDocumentAuditRepository.findByDocumentId.mockResolvedValue(auditEntries);

            // Act
            const result = await service.findByDocumentId(documentId);

            // Assert
            expect(mockDocumentAuditRepository.findByDocumentId).toHaveBeenCalledWith(documentId);
            expect(result).toEqual(auditEntries);
        });

        it('should handle repository errors', async () => {
            // Arrange
            const documentId = 'doc-123';
            const error = new Error('Database connection failed');
            mockDocumentAuditRepository.findByDocumentId.mockRejectedValue(error);

            // Act & Assert
            await expect(service.findByDocumentId(documentId)).rejects.toThrow(
                'Database connection failed'
            );
        });
    });

    describe('findAuditEntries', () => {
        it('should return audit entries with all criteria', async () => {
            // Arrange
            const options = {
                documentId: 'doc-123',
                performedBy: 'user-123',
                actionType: 'CREATE',
                fromDate: new Date('2023-01-01'),
                toDate: new Date('2023-12-31'),
                limit: 20,
                offset: 10
            };

            const result = {
                entries: [mockAuditEntry],
                total: 1
            };

            mockDocumentAuditRepository.findAuditEntries.mockResolvedValue(result);

            // Act
            const auditResult = await service.findAuditEntries(options);

            // Assert
            expect(mockDocumentAuditRepository.findAuditEntries).toHaveBeenCalledWith(options);
            expect(auditResult).toEqual(result);
        });

        it('should return audit entries with partial criteria', async () => {
            // Arrange
            const options = {
                performedBy: 'user-123',
                actionType: 'VIEW'
            };

            const result = {
                entries: [mockAuditEntry],
                total: 1
            };

            mockDocumentAuditRepository.findAuditEntries.mockResolvedValue(result);

            // Act
            const auditResult = await service.findAuditEntries(options);

            // Assert
            expect(mockDocumentAuditRepository.findAuditEntries).toHaveBeenCalledWith(options);
            expect(auditResult).toEqual(result);
        });

        it('should handle repository errors', async () => {
            // Arrange
            const options = {
                documentId: 'doc-123'
            };

            const error = new Error('Database connection failed');
            mockDocumentAuditRepository.findAuditEntries.mockRejectedValue(error);

            // Act & Assert
            await expect(service.findAuditEntries(options)).rejects.toThrow(
                'Database connection failed'
            );
        });
    });
});
