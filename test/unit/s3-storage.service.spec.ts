import { Test, TestingModule } from '@nestjs/testing';
import { S3StorageService } from '../../apps/document-engine/src/document/services/s3-storage.service';
import { AwsConfigService } from '@app/common/config/aws.config';
import { TenantContextService } from '@app/common/multi-tenancy';

// Mock AWS SDK before importing the service
const mockS3Client = {
    upload: jest.fn(),
    getSignedUrlPromise: jest.fn(),
    deleteObject: jest.fn(),
    headObject: jest.fn(),
    getObject: jest.fn(),
    copyObject: jest.fn(),
    createBucket: jest.fn(),
    headBucket: jest.fn()
};

jest.mock('aws-sdk', () => ({
    S3: jest.fn().mockImplementation(() => mockS3Client)
}));

describe('S3StorageService', () => {
    let service: S3StorageService;

    // Mock services
    const mockAwsConfigService = {
        region: 'us-east-1',
        accessKeyId: 'test-access-key',
        secretAccessKey: 'test-secret-key',
        documentBucketName: 'test-bucket',
        presignedUrlExpirationSeconds: 3600,
        s3Endpoint: null,
        s3ForcePathStyle: false,
        s3UseSSL: true
    };

    const mockTenantContextService = {
        hasTenant: jest.fn().mockReturnValue(true),
        getTenantId: jest.fn().mockReturnValue('tenant-123')
    };

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                S3StorageService,
                { provide: AwsConfigService, useValue: mockAwsConfigService },
                { provide: TenantContextService, useValue: mockTenantContextService }
            ]
        }).compile();

        service = module.get<S3StorageService>(S3StorageService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('uploadFile', () => {
        it('should upload file successfully', async () => {
            // Arrange
            const caseId = 'case-123';
            const folderId = 'folder-123';
            const filename = 'test-document.pdf';
            const fileBuffer = Buffer.from('test content');
            const mimeType = 'application/pdf';

            const expectedS3Key = `tenant-tenant-123/case/${caseId}/${folderId}/${filename}`;
            const expectedChecksum = 'test-checksum';

            mockS3Client.upload.mockReturnValue({
                promise: jest.fn().mockResolvedValue({
                    ETag: expectedChecksum,
                    Location: `https://test-bucket.s3.amazonaws.com/${expectedS3Key}`
                })
            });

            // Act
            const result = await service.uploadFile(
                caseId,
                folderId,
                filename,
                fileBuffer,
                mimeType
            );

            // Assert
            expect(mockS3Client.upload).toHaveBeenCalledWith({
                Bucket: 'test-bucket',
                Key: expectedS3Key,
                Body: fileBuffer,
                ContentType: mimeType,
                Metadata: {
                    'case-id': caseId,
                    'folder-id': folderId,
                    'original-filename': filename,
                    'upload-date': expect.any(String),
                    checksum: expect.any(String)
                }
            });
            expect(result).toEqual({
                s3Key: expectedS3Key,
                s3Bucket: 'test-bucket',
                eTag: expectedChecksum,
                checksum: expect.any(String)
            });
        });

        it('should upload file without folderId', async () => {
            // Arrange
            const caseId = 'case-123';
            const filename = 'test-document.pdf';
            const fileBuffer = Buffer.from('test content');
            const mimeType = 'application/pdf';

            const expectedS3Key = `tenant-tenant-123/case/${caseId}/${filename}`;

            mockS3Client.upload.mockReturnValue({
                promise: jest.fn().mockResolvedValue({
                    ETag: 'test-checksum',
                    Location: `https://test-bucket.s3.amazonaws.com/${expectedS3Key}`
                })
            });

            // Act
            const result = await service.uploadFile(caseId, null, filename, fileBuffer, mimeType);

            // Assert
            expect(mockS3Client.upload).toHaveBeenCalledWith({
                Bucket: 'test-bucket',
                Key: expectedS3Key,
                Body: fileBuffer,
                ContentType: mimeType,
                Metadata: {
                    'case-id': caseId,
                    'folder-id': '',
                    'original-filename': filename,
                    'upload-date': expect.any(String),
                    checksum: expect.any(String)
                }
            });
            expect(result.s3Key).toBe(expectedS3Key);
        });

        it('should handle upload errors', async () => {
            // Arrange
            const caseId = 'case-123';
            const filename = 'test-document.pdf';
            const fileBuffer = Buffer.from('test content');
            const mimeType = 'application/pdf';

            const error = new Error('S3 upload failed');
            mockS3Client.upload.mockReturnValue({
                promise: jest.fn().mockRejectedValue(error)
            });

            // Act & Assert
            await expect(
                service.uploadFile(caseId, null, filename, fileBuffer, mimeType)
            ).rejects.toThrow('Failed to upload file: S3 upload failed');
        });

        it('should throw error when tenant context not available', async () => {
            // Arrange
            mockTenantContextService.hasTenant.mockReturnValue(false);

            // Act & Assert
            await expect(
                service.uploadFile(
                    'case-123',
                    null,
                    'test.pdf',
                    Buffer.from('test'),
                    'application/pdf'
                )
            ).rejects.toThrow('Tenant context not available');
        });
    });

    describe('getDownloadUrl', () => {
        it('should generate presigned download URL', async () => {
            // Arrange
            const s3Key = 'tenant-tenant-123/case/case-123/test-document.pdf';
            const filename = 'test-document.pdf';
            const expectedUrl = 'https://test-bucket.s3.amazonaws.com/test-key?signature=abc123';

            mockS3Client.getSignedUrlPromise.mockResolvedValue(expectedUrl);

            // Act
            const result = await service.getDownloadUrl(s3Key, filename);

            // Assert
            expect(mockS3Client.getSignedUrlPromise).toHaveBeenCalledWith('getObject', {
                Bucket: 'test-bucket',
                Key: s3Key,
                ResponseContentDisposition: `attachment; filename="${encodeURIComponent(filename)}"`,
                Expires: 3600
            });
            expect(result).toBe(expectedUrl);
        });

        it('should handle URL generation errors', async () => {
            // Arrange
            const s3Key = 'tenant-tenant-123/case/case-123/test-document.pdf';
            const filename = 'test-document.pdf';
            const error = new Error('URL generation failed');

            mockS3Client.getSignedUrlPromise.mockRejectedValue(error);

            // Act & Assert
            await expect(service.getDownloadUrl(s3Key, filename)).rejects.toThrow(
                'Failed to generate download URL: URL generation failed'
            );
        });
    });

    describe('deleteFile', () => {
        it('should delete file successfully', async () => {
            // Arrange
            const s3Key = 'tenant-tenant-123/case/case-123/test-document.pdf';

            mockS3Client.deleteObject.mockReturnValue({
                promise: jest.fn().mockResolvedValue({})
            });

            // Act
            const result = await service.deleteFile(s3Key);

            // Assert
            expect(mockS3Client.deleteObject).toHaveBeenCalledWith({
                Bucket: 'test-bucket',
                Key: s3Key
            });
            expect(result).toBe(true);
        });

        it('should handle delete errors', async () => {
            // Arrange
            const s3Key = 'tenant-tenant-123/case/case-123/test-document.pdf';
            const error = new Error('S3 delete failed');

            mockS3Client.deleteObject.mockReturnValue({
                promise: jest.fn().mockRejectedValue(error)
            });

            // Act & Assert
            await expect(service.deleteFile(s3Key)).rejects.toThrow(
                'Failed to delete file: S3 delete failed'
            );
        });
    });

    describe('fileExists', () => {
        it('should return true when file exists', async () => {
            // Arrange
            const s3Key = 'tenant-tenant-123/case/case-123/test-document.pdf';

            mockS3Client.headObject.mockReturnValue({
                promise: jest.fn().mockResolvedValue({})
            });

            // Act
            const result = await service.fileExists(s3Key);

            // Assert
            expect(mockS3Client.headObject).toHaveBeenCalledWith({
                Bucket: 'test-bucket',
                Key: s3Key
            });
            expect(result).toBe(true);
        });

        it('should return false when file does not exist', async () => {
            // Arrange
            const s3Key = 'tenant-tenant-123/case/case-123/test-document.pdf';
            const error = { code: 'NotFound' };

            mockS3Client.headObject.mockReturnValue({
                promise: jest.fn().mockRejectedValue(error)
            });

            // Act
            const result = await service.fileExists(s3Key);

            // Assert
            expect(result).toBe(false);
        });
    });

    describe('getFileMetadata', () => {
        it('should return file metadata', async () => {
            // Arrange
            const s3Key = 'tenant-tenant-123/case/case-123/test-document.pdf';
            const metadata = {
                ETag: 'test-etag',
                ContentType: 'application/pdf',
                ContentLength: 1024,
                LastModified: new Date()
            };

            mockS3Client.headObject.mockReturnValue({
                promise: jest.fn().mockResolvedValue(metadata)
            });

            // Act
            const result = await service.getFileMetadata(s3Key);

            // Assert
            expect(mockS3Client.headObject).toHaveBeenCalledWith({
                Bucket: 'test-bucket',
                Key: s3Key
            });
            expect(result).toEqual(metadata);
        });
    });

    describe('copyFile', () => {
        it('should copy file successfully', async () => {
            // Arrange
            const sourceKey = 'tenant-tenant-123/case/case-123/source.pdf';
            const destinationKey = 'tenant-tenant-123/case/case-123/destination.pdf';
            const copyResult = { CopyObjectResult: { ETag: 'test-etag' } };

            mockS3Client.copyObject.mockReturnValue({
                promise: jest.fn().mockResolvedValue(copyResult)
            });

            // Act
            const result = await service.copyFile(sourceKey, destinationKey);

            // Assert
            expect(mockS3Client.copyObject).toHaveBeenCalledWith({
                Bucket: 'test-bucket',
                CopySource: `${mockAwsConfigService.documentBucketName}/${sourceKey}`,
                Key: destinationKey
            });
            expect(result).toEqual(copyResult);
        });
    });

    describe('ensureBucketExists', () => {
        it('should return true when bucket exists', async () => {
            // Arrange
            mockS3Client.headBucket.mockReturnValue({
                promise: jest.fn().mockResolvedValue({})
            });

            // Act
            const result = await service.ensureBucketExists();

            // Assert
            expect(mockS3Client.headBucket).toHaveBeenCalledWith({
                Bucket: 'test-bucket'
            });
            expect(result).toBe(true);
        });

        it('should create bucket when it does not exist', async () => {
            // Arrange
            const notFoundError = { code: 'NotFound' };
            mockS3Client.headBucket.mockReturnValue({
                promise: jest.fn().mockRejectedValue(notFoundError)
            });
            mockS3Client.createBucket.mockReturnValue({
                promise: jest.fn().mockResolvedValue({})
            });

            // Act
            const result = await service.ensureBucketExists();

            // Assert
            expect(mockS3Client.createBucket).toHaveBeenCalledWith({
                Bucket: 'test-bucket',
                CreateBucketConfiguration: {
                    LocationConstraint: 'us-east-1'
                }
            });
            expect(result).toBe(true);
        });
    });
});
