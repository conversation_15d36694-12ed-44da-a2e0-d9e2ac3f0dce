import { Test, TestingModule } from '@nestjs/testing';
import { DocumentController } from '../../apps/document-engine/src/controllers/document.controller';
import { DocumentService } from '../../apps/document-engine/src/document/services/document.service';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { IsLawyer } from '@app/common/roles/decorators';
import { Document } from '@app/common/typeorm/entities';
import { Request } from 'express';

// Mock request with user information
interface RequestWithUser extends Request {
    user: {
        id: string;
        username: string;
        email: string;
        roles: string[];
        systemUserId: string;
        preferred_username: string;
    };
}

describe('DocumentController', () => {
    let controller: DocumentController;
    let documentService: DocumentService;

    // Mock DocumentService
    const mockDocumentService = {
        createDocument: jest.fn(),
        getDocumentById: jest.fn(),
        getDocumentsByCaseId: jest.fn(),
        getDocumentsByFolderId: jest.fn(),
        updateDocument: jest.fn(),
        deleteDocument: jest.fn(),
        searchDocuments: jest.fn(),
        getDocumentDownloadUrl: jest.fn(),
        replaceDocumentFile: jest.fn(),
        documentRepo: {
            findRootDocumentsByCaseId: jest.fn()
        }
    };

    // Mock request
    const mockRequest = {
        ip: '127.0.0.1',
        headers: {
            'x-forwarded-for': '********'
        },
        user: {
            id: 'auth0|12345',
            systemUserId: 'user-123',
            username: 'testuser',
            preferred_username: 'testuser',
            email: '<EMAIL>',
            roles: ['lawyer']
        }
    } as unknown as RequestWithUser;

    // Mock file
    const mockFile = {
        fieldname: 'file',
        originalname: 'test-document.pdf',
        encoding: '7bit',
        mimetype: 'application/pdf',
        size: 1024,
        destination: '/tmp',
        filename: 'test-file.pdf',
        path: '/tmp/test-file.pdf',
        buffer: Buffer.from('test file content')
    };

    // Mock document
    const mockDocument: Document = {
        id: 'doc-123',
        name: 'Test Document',
        description: 'Test description',
        caseId: 'case-123',
        folderId: 'folder-123',
        s3Key: 'test-key',
        s3Bucket: 'test-bucket',
        fileName: 'test-document.pdf',
        fileExtension: 'pdf',
        mimeType: 'application/pdf',
        sizeInBytes: '1024',
        checksum: 'test-checksum',
        createdBy: 'user-123',
        createdAt: new Date(),
        updatedAt: new Date(),
        lastModifiedBy: 'user-123'
    } as Document;

    // Mock guards
    const mockJwtGuard = { canActivate: jest.fn().mockReturnValue(true) };
    const mockTenantGuard = { canActivate: jest.fn().mockReturnValue(true) };
    const mockRolesGuard = { canActivate: jest.fn().mockReturnValue(true) };
    const mockIsLawyer = { canActivate: jest.fn().mockReturnValue(true) };

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            controllers: [DocumentController],
            providers: [
                {
                    provide: DocumentService,
                    useValue: mockDocumentService
                }
            ]
        })
            .overrideGuard(JwtGuard)
            .useValue(mockJwtGuard)
            .overrideGuard(TenantGuard)
            .useValue(mockTenantGuard)
            .overrideGuard(RolesGuard)
            .useValue(mockRolesGuard)
            .overrideGuard(IsLawyer)
            .useValue(mockIsLawyer)
            .compile();

        controller = module.get<DocumentController>(DocumentController);
        documentService = module.get<DocumentService>(DocumentService);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
        expect(documentService).toBeDefined();
    });

    describe('createDocument', () => {
        it('should create a new document', async () => {
            // Arrange
            const createData = {
                name: 'Test Document',
                description: 'Test description',
                caseId: 'case-123',
                folderId: 'folder-123'
            };

            mockDocumentService.createDocument.mockResolvedValue(mockDocument);

            // Act
            const result = await controller.createDocument(mockFile, createData, mockRequest);

            // Assert
            expect(mockDocumentService.createDocument).toHaveBeenCalledWith({
                name: createData.name,
                description: createData.description,
                caseId: createData.caseId,
                folderId: createData.folderId,
                fileBuffer: mockFile.buffer,
                filename: mockFile.originalname,
                mimeType: mockFile.mimetype,
                createdBy: mockRequest.user.systemUserId
            });
            expect(result).toEqual({
                code: 201,
                status: 'Created',
                message: 'Document created successfully',
                data: mockDocument
            });
        });

        it('should create document without optional fields', async () => {
            // Arrange
            const createData = {
                name: 'Test Document',
                caseId: 'case-123'
            };

            mockDocumentService.createDocument.mockResolvedValue(mockDocument);

            // Act
            await controller.createDocument(mockFile, createData, mockRequest);

            // Assert
            expect(mockDocumentService.createDocument).toHaveBeenCalledWith({
                name: createData.name,
                description: undefined,
                caseId: createData.caseId,
                folderId: undefined,
                fileBuffer: mockFile.buffer,
                filename: mockFile.originalname,
                mimeType: mockFile.mimetype,
                createdBy: mockRequest.user.systemUserId
            });
        });
    });

    describe('findAll', () => {
        it('should return root documents when caseId provided without folderId', async () => {
            // Arrange
            const caseId = 'case-123';
            const documents = [mockDocument];
            mockDocumentService.documentRepo.findRootDocumentsByCaseId.mockResolvedValue(documents);

            // Act
            const result = await controller.findAll(caseId, undefined);

            // Assert
            expect(mockDocumentService.documentRepo.findRootDocumentsByCaseId).toHaveBeenCalledWith(
                caseId
            );
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Documents retrieved successfully',
                data: documents
            });
        });

        it('should return documents by folderId when provided', async () => {
            // Arrange
            const folderId = 'folder-123';
            const documents = [mockDocument];
            mockDocumentService.getDocumentsByFolderId.mockResolvedValue(documents);

            // Act
            const result = await controller.findAll(undefined, folderId);

            // Assert
            expect(mockDocumentService.getDocumentsByFolderId).toHaveBeenCalledWith(folderId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Documents retrieved successfully',
                data: documents
            });
        });

        it('should return documents by caseId when only caseId provided', async () => {
            // Arrange
            const caseId = 'case-123';
            const documents = [mockDocument];
            mockDocumentService.documentRepo.findRootDocumentsByCaseId.mockResolvedValue(documents);

            // Act
            const result = await controller.findAll(caseId, undefined);

            // Assert
            expect(mockDocumentService.documentRepo.findRootDocumentsByCaseId).toHaveBeenCalledWith(
                caseId
            );
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Documents retrieved successfully',
                data: documents
            });
        });

        it('should return empty array when no parameters provided', async () => {
            // Act
            const result = await controller.findAll(undefined, undefined);

            // Assert
            expect(mockDocumentService.getDocumentsByCaseId).not.toHaveBeenCalled();
            expect(mockDocumentService.getDocumentsByFolderId).not.toHaveBeenCalled();
            expect(
                mockDocumentService.documentRepo.findRootDocumentsByCaseId
            ).not.toHaveBeenCalled();
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Documents retrieved successfully',
                data: []
            });
        });
    });

    describe('search', () => {
        it('should search documents with all parameters', async () => {
            // Arrange
            const searchTerm = 'test';
            const caseId = 'case-123';
            const folderId = 'folder-123';
            const limit = '20';
            const documents = [mockDocument];

            mockDocumentService.searchDocuments.mockResolvedValue(documents);

            // Act
            const result = await controller.search(searchTerm, caseId, folderId, limit);

            // Assert
            expect(mockDocumentService.searchDocuments).toHaveBeenCalledWith(searchTerm, {
                folderId,
                caseId,
                limit: 20
            });
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Documents searched successfully',
                data: documents
            });
        });

        it('should search documents with only search term', async () => {
            // Arrange
            const searchTerm = 'test';
            const documents = [mockDocument];

            mockDocumentService.searchDocuments.mockResolvedValue(documents);

            // Act
            const result = await controller.search(searchTerm, undefined, undefined, undefined);

            // Assert
            expect(mockDocumentService.searchDocuments).toHaveBeenCalledWith(searchTerm, {
                folderId: undefined,
                caseId: undefined,
                limit: undefined
            });
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Documents searched successfully',
                data: documents
            });
        });
    });

    describe('findOne', () => {
        it('should return document when found', async () => {
            // Arrange
            const documentId = 'doc-123';
            mockDocumentService.getDocumentById.mockResolvedValue(mockDocument);

            // Act
            const result = await controller.findOne(documentId);

            // Assert
            expect(mockDocumentService.getDocumentById).toHaveBeenCalledWith(documentId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Document retrieved successfully',
                data: mockDocument
            });
        });

        it('should return not found when document does not exist', async () => {
            // Arrange
            const documentId = 'non-existent';
            mockDocumentService.getDocumentById.mockResolvedValue(null);

            // Act
            const result = await controller.findOne(documentId);

            // Assert
            expect(mockDocumentService.getDocumentById).toHaveBeenCalledWith(documentId);
            expect(result).toEqual({
                code: 404,
                status: 'Not Found',
                message: 'Document not found',
                data: {}
            });
        });
    });

    describe('downloadDocument', () => {
        it('should return download URL when document exists', async () => {
            // Arrange
            const documentId = 'doc-123';
            const downloadInfo = {
                url: 'https://s3.amazonaws.com/test-bucket/test-key',
                filename: 'test-document.pdf'
            };

            mockDocumentService.getDocumentDownloadUrl.mockResolvedValue(downloadInfo);

            // Act
            const result = await controller.downloadDocument(documentId);

            // Assert
            expect(mockDocumentService.getDocumentDownloadUrl).toHaveBeenCalledWith(documentId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Download URL generated successfully',
                data: downloadInfo
            });
        });
    });

    describe('updateDocument', () => {
        it('should update document when it exists', async () => {
            // Arrange
            const documentId = 'doc-123';
            const updateData = {
                name: 'Updated Document Name',
                description: 'Updated description',
                folderId: 'new-folder-123',
                caseId: 'new-case-123'
            };

            const updatedDocument = { ...mockDocument, ...updateData };
            mockDocumentService.updateDocument.mockResolvedValue(updatedDocument);

            // Act
            const result = await controller.updateDocument(documentId, updateData, mockRequest);

            // Assert
            expect(mockDocumentService.updateDocument).toHaveBeenCalledWith(documentId, {
                ...updateData,
                updatedBy: mockRequest.user.systemUserId
            });
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Document updated successfully',
                data: updatedDocument
            });
        });

        it('should return not found when document does not exist', async () => {
            // Arrange
            const documentId = 'non-existent';
            const updateData = {
                name: 'Updated Document Name'
            };

            mockDocumentService.updateDocument.mockResolvedValue(null);

            // Act
            const result = await controller.updateDocument(documentId, updateData, mockRequest);

            // Assert
            expect(mockDocumentService.updateDocument).toHaveBeenCalledWith(documentId, {
                ...updateData,
                updatedBy: mockRequest.user.systemUserId
            });
            expect(result).toEqual({
                code: 404,
                status: 'Not Found',
                message: 'Document not found',
                data: {}
            });
        });
    });

    describe('replaceDocumentFile', () => {
        it('should replace document file successfully', async () => {
            // Arrange
            const documentId = 'doc-123';
            const newFile = {
                ...mockFile,
                originalname: 'new-document.pdf',
                mimetype: 'application/pdf',
                buffer: Buffer.from('new file content')
            };

            const updatedDocument = {
                ...mockDocument,
                fileName: newFile.originalname,
                mimeType: newFile.mimetype
            };

            mockDocumentService.replaceDocumentFile.mockResolvedValue(updatedDocument);

            // Act
            const result = await controller.replaceDocumentFile(documentId, newFile, mockRequest);

            // Assert
            expect(mockDocumentService.replaceDocumentFile).toHaveBeenCalledWith(documentId, {
                fileBuffer: newFile.buffer,
                filename: newFile.originalname,
                mimeType: newFile.mimetype,
                updatedBy: mockRequest.user.systemUserId
            });
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Document file replaced successfully',
                data: updatedDocument
            });
        });
    });

    describe('removeDocument', () => {
        it('should delete document successfully', async () => {
            // Arrange
            const documentId = 'doc-123';
            mockDocumentService.deleteDocument.mockResolvedValue(true);

            // Act
            const result = await controller.removeDocument(documentId);

            // Assert
            expect(mockDocumentService.deleteDocument).toHaveBeenCalledWith(documentId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Document deleted successfully',
                data: { success: true }
            });
        });

        it('should return success false when document not found', async () => {
            // Arrange
            const documentId = 'non-existent';
            mockDocumentService.deleteDocument.mockResolvedValue(false);

            // Act
            const result = await controller.removeDocument(documentId);

            // Assert
            expect(mockDocumentService.deleteDocument).toHaveBeenCalledWith(documentId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Document deleted successfully',
                data: { success: false }
            });
        });
    });
});
