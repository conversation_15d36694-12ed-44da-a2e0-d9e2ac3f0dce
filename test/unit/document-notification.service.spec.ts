import { Test, TestingModule } from '@nestjs/testing';
import { DocumentNotificationService } from '../../apps/document-engine/src/document/services/document-notification.service';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { COMMUNICATION_CHANNELS } from '@app/common/constants/channel.constant';
import { QUEUE_PRIORITIES } from '@app/common/constants/queue.constants';
import { DocumentNotificationType } from '@app/common/enums/document-notification-types.enum';
import { DocumentEventType } from '@app/common/enums/document-event-types.enum';
import { DocumentOperationType } from '@app/common/enums/document-action-types.enum';

describe('DocumentNotificationService', () => {
    let service: DocumentNotificationService;
    let mockMessageProducer: jest.Mocked<MessageProducerService>;

    beforeEach(async () => {
        const mockProducer = {
            enqueueMessage: jest.fn()
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                DocumentNotificationService,
                {
                    provide: MessageProducerService,
                    useValue: mockProducer
                }
            ]
        }).compile();

        service = module.get<DocumentNotificationService>(DocumentNotificationService);
        mockMessageProducer = module.get(MessageProducerService);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('notifyDocumentGenerated', () => {
        it('should send document generation notification', async () => {
            const context = {
                tenantId: 'test-tenant',
                tenantName: 'Test Legal Firm',
                userId: 'user-123',
                userEmail: '<EMAIL>',
                userName: 'Test User',
                document: {
                    id: 'doc-123',
                    name: 'Test Document'
                } as any,
                template: {
                    id: 'template-123',
                    name: 'Test Template'
                } as any,
                caseId: 'case-123',
                caseNumber: 'CASE-001'
            };

            mockMessageProducer.enqueueMessage.mockResolvedValue({
                jobId: 'job-123',
                queueName: 'email',
                status: 'queued',
                channels: [COMMUNICATION_CHANNELS.EMAIL]
            });

            await service.notifyDocumentGenerated(context);

            expect(mockMessageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'test-tenant',
                userId: 'user-123',
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: ['<EMAIL>'],
                    notification: ['user-123']
                },
                variables: expect.objectContaining({
                    type: DocumentNotificationType.DOCUMENT_GENERATED,
                    tenantName: 'Test Legal Firm',
                    recipientName: 'Test User',
                    documentName: 'Test Document',
                    templateName: 'Test Template',
                    caseNumber: 'CASE-001',
                    caseId: 'case-123'
                }),
                priority: QUEUE_PRIORITIES.NORMAL,
                caseId: 'case-123',
                metadata: expect.objectContaining({
                    documentId: 'doc-123',
                    templateId: 'template-123',
                    eventType: DocumentEventType.DOCUMENT_GENERATED
                })
            });
        });

        it('should handle notification errors gracefully', async () => {
            const context = {
                tenantId: 'test-tenant',
                tenantName: 'Test Legal Firm',
                userId: 'user-123',
                userEmail: '<EMAIL>',
                userName: 'Test User'
            };

            mockMessageProducer.enqueueMessage.mockRejectedValue(new Error('Queue error'));

            // Should not throw
            await expect(service.notifyDocumentGenerated(context)).resolves.toBeUndefined();
        });
    });

    describe('notifyDocumentUploaded', () => {
        it('should send document upload notification', async () => {
            const context = {
                tenantId: 'test-tenant',
                tenantName: 'Test Legal Firm',
                userId: 'user-123',
                userEmail: '<EMAIL>',
                userName: 'Test User',
                document: {
                    id: 'doc-123',
                    name: 'Uploaded Document'
                } as any,
                caseId: 'case-123',
                caseNumber: 'CASE-001'
            };

            mockMessageProducer.enqueueMessage.mockResolvedValue({
                jobId: 'job-123',
                queueName: 'email',
                status: 'queued',
                channels: [COMMUNICATION_CHANNELS.EMAIL]
            });

            await service.notifyDocumentUploaded(context);

            expect(mockMessageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'test-tenant',
                userId: 'user-123',
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: ['<EMAIL>'],
                    notification: ['user-123']
                },
                variables: expect.objectContaining({
                    type: DocumentNotificationType.DOCUMENT_UPLOADED,
                    documentName: 'Uploaded Document',
                    caseNumber: 'CASE-001'
                }),
                priority: QUEUE_PRIORITIES.NORMAL,
                caseId: 'case-123',
                metadata: expect.objectContaining({
                    documentId: 'doc-123',
                    eventType: DocumentEventType.DOCUMENT_UPLOADED
                })
            });
        });
    });

    describe('notifyDocumentShared', () => {
        it('should send document share notification', async () => {
            const context = {
                tenantId: 'test-tenant',
                tenantName: 'Test Legal Firm',
                userId: 'user-123',
                userEmail: '<EMAIL>',
                userName: 'Test User',
                document: {
                    id: 'doc-123',
                    name: 'Shared Document'
                } as any,
                caseId: 'case-123',
                caseNumber: 'CASE-001',
                sharedWithEmails: ['<EMAIL>'],
                sharedWithUserIds: ['user-456'],
                shareMessage: 'Please review this document'
            };

            mockMessageProducer.enqueueMessage.mockResolvedValue({
                jobId: 'job-123',
                queueName: 'email',
                status: 'queued',
                channels: [COMMUNICATION_CHANNELS.EMAIL]
            });

            await service.notifyDocumentShared(context);

            expect(mockMessageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'test-tenant',
                userId: 'user-123',
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: ['<EMAIL>'],
                    notification: ['user-456']
                },
                variables: expect.objectContaining({
                    type: DocumentNotificationType.DOCUMENT_SHARED,
                    documentName: 'Shared Document',
                    sharedBy: 'Test User',
                    shareMessage: 'Please review this document'
                }),
                priority: QUEUE_PRIORITIES.NORMAL,
                caseId: 'case-123',
                metadata: expect.objectContaining({
                    documentId: 'doc-123',
                    sharedBy: 'user-123',
                    eventType: DocumentEventType.DOCUMENT_SHARED
                })
            });
        });
    });

    describe('notifyDocumentGenerationFailed', () => {
        it('should send document generation failure notification', async () => {
            const context = {
                tenantId: 'test-tenant',
                tenantName: 'Test Legal Firm',
                userId: 'user-123',
                userEmail: '<EMAIL>',
                userName: 'Test User',
                templateName: 'Failed Template',
                caseId: 'case-123',
                caseNumber: 'CASE-001',
                errorMessage: 'Token resolution failed'
            };

            mockMessageProducer.enqueueMessage.mockResolvedValue({
                jobId: 'job-123',
                queueName: 'email',
                status: 'queued',
                channels: [COMMUNICATION_CHANNELS.EMAIL]
            });

            await service.notifyDocumentGenerationFailed(context);

            expect(mockMessageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'test-tenant',
                userId: 'user-123',
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: ['<EMAIL>'],
                    notification: ['user-123']
                },
                variables: expect.objectContaining({
                    type: DocumentNotificationType.DOCUMENT_GENERATION_FAILED,
                    templateName: 'Failed Template',
                    errorMessage: 'Token resolution failed'
                }),
                priority: QUEUE_PRIORITIES.HIGH,
                caseId: 'case-123',
                metadata: expect.objectContaining({
                    eventType: DocumentEventType.DOCUMENT_GENERATION_FAILED
                })
            });
        });
    });

    describe('notifyBatchDocumentOperation', () => {
        it('should send batch document operation notification', async () => {
            const context = {
                tenantId: 'test-tenant',
                tenantName: 'Test Legal Firm',
                userId: 'user-123',
                userEmail: '<EMAIL>',
                userName: 'Test User',
                operation: DocumentOperationType.UPLOADED,
                documentNames: ['Doc1.pdf', 'Doc2.docx'],
                documentCount: 2,
                caseId: 'case-123',
                caseNumber: 'CASE-001'
            };

            mockMessageProducer.enqueueMessage.mockResolvedValue({
                jobId: 'job-123',
                queueName: 'email',
                status: 'queued',
                channels: [COMMUNICATION_CHANNELS.EMAIL]
            });

            await service.notifyBatchDocumentOperation(context);

            expect(mockMessageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'test-tenant',
                userId: 'user-123',
                channels: [COMMUNICATION_CHANNELS.EMAIL, COMMUNICATION_CHANNELS.NOTIFICATION],
                recipients: {
                    email: ['<EMAIL>'],
                    notification: ['user-123']
                },
                variables: expect.objectContaining({
                    type: DocumentNotificationType.BATCH_DOCUMENTS_UPLOADED,
                    documentCount: 2,
                    operation: DocumentOperationType.UPLOADED,
                    documentNames: ['Doc1.pdf', 'Doc2.docx']
                }),
                priority: QUEUE_PRIORITIES.NORMAL,
                caseId: 'case-123',
                metadata: expect.objectContaining({
                    operation: DocumentOperationType.UPLOADED,
                    documentCount: 2,
                    eventType: DocumentEventType.DOCUMENT_BATCH_UPLOADED
                })
            });
        });
    });

    describe('notifyDocumentSentToClient', () => {
        it('should send document to client notification', async () => {
            const context = {
                tenantId: 'test-tenant',
                tenantName: 'Test Legal Firm',
                userId: 'user-123',
                userEmail: '<EMAIL>',
                userName: 'Test User',
                document: {
                    id: 'doc-123',
                    name: 'Contract.pdf'
                } as any,
                caseId: 'case-123',
                caseNumber: 'CASE-001',
                clientEmail: '<EMAIL>',
                clientName: 'John Doe',
                documentUrl: 'https://example.com/doc.pdf',
                clientMessage: 'Please review and sign this contract'
            };

            mockMessageProducer.enqueueMessage.mockResolvedValue({
                jobId: 'job-123',
                queueName: 'email',
                status: 'queued',
                channels: [COMMUNICATION_CHANNELS.EMAIL]
            });

            await service.notifyDocumentSentToClient(context);

            expect(mockMessageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'test-tenant',
                userId: 'user-123',
                channels: [COMMUNICATION_CHANNELS.EMAIL],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: DocumentNotificationType.DOCUMENT_SHARED,
                    tenantName: 'Test Legal Firm',
                    recipientName: 'John Doe',
                    documentName: 'Contract.pdf',
                    sharedBy: 'Test User',
                    shareMessage: 'Please review and sign this contract',
                    documentUrl: 'https://example.com/doc.pdf'
                }),
                priority: QUEUE_PRIORITIES.NORMAL,
                caseId: 'case-123',
                metadata: expect.objectContaining({
                    documentId: 'doc-123',
                    clientEmail: '<EMAIL>',
                    eventType: DocumentEventType.DOCUMENT_SHARED
                })
            });
        });
    });
});
