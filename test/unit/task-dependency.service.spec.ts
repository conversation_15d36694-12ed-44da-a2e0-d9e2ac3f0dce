import { Test, TestingModule } from '@nestjs/testing';
import { TaskDependencyService } from '../../apps/task-management/src/services/task-dependency.service';
import { TaskDependencyRepository } from '../../apps/task-management/src/repositories/task-dependency.repository';
import { TaskRepository } from '../../apps/task-management/src/repositories/task.repository';
import { TaskStatus } from '@app/common/typeorm/entities/tenant/task.entity';

// Mock dependencies
const mockTaskDependencyRepository = {
    findOne: jest.fn(),
    findOneById: jest.fn(),
    findByTaskId: jest.fn(),
    findDependentTasks: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    removeById: jest.fn(),
    wouldCreateCircularDependency: jest.fn()
};

const mockTaskRepository = {
    findOneById: jest.fn()
};

describe('TaskDependencyService', () => {
    let service: TaskDependencyService;

    beforeEach(async () => {
        jest.clearAllMocks();
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TaskDependencyService,
                { provide: TaskDependencyRepository, useValue: mockTaskDependencyRepository },
                { provide: TaskRepository, useValue: mockTaskRepository }
            ]
        }).compile();
        service = module.get<TaskDependencyService>(TaskDependencyService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('addDependency', () => {
        it('should add a dependency successfully', async () => {
            // Arrange
            const taskId = 'task1';
            const addDependencyDto = { dependsOnId: 'task2' };
            const createdBy = 'user1';
            const task = { id: taskId, title: 'Task 1' };
            const dependsOnTask = { id: 'task2', title: 'Task 2' };
            const dependency = { id: 'dep1', taskId, dependsOnId: 'task2', createdBy };

            mockTaskRepository.findOneById
                .mockResolvedValueOnce(task)
                .mockResolvedValueOnce(dependsOnTask);
            mockTaskDependencyRepository.findOne.mockResolvedValue(null);
            mockTaskDependencyRepository.wouldCreateCircularDependency.mockResolvedValue(false);
            mockTaskDependencyRepository.create.mockReturnValue(dependency);
            mockTaskDependencyRepository.save.mockResolvedValue(dependency);

            // Act
            const result = await service.addDependency(taskId, addDependencyDto as any, createdBy);

            // Assert
            expect(mockTaskRepository.findOneById).toHaveBeenCalledWith(taskId);
            expect(mockTaskRepository.findOneById).toHaveBeenCalledWith('task2');
            expect(mockTaskDependencyRepository.findOne).toHaveBeenCalledWith({
                where: { taskId, dependsOnId: 'task2' }
            });
            expect(mockTaskDependencyRepository.wouldCreateCircularDependency).toHaveBeenCalledWith(
                taskId,
                'task2'
            );
            expect(mockTaskDependencyRepository.create).toHaveBeenCalledWith({
                taskId,
                dependsOnId: 'task2',
                createdBy
            });
            expect(result).toEqual(dependency);
        });

        it('should throw NotFoundException if task does not exist', async () => {
            // Arrange
            const taskId = 'notask';
            const addDependencyDto = { dependsOnId: 'task2' };
            const createdBy = 'user1';
            mockTaskRepository.findOneById.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.addDependency(taskId, addDependencyDto as any, createdBy)
            ).rejects.toThrow('Task with ID notask not found');
        });

        it('should throw NotFoundException if dependency task does not exist', async () => {
            // Arrange
            const taskId = 'task1';
            const addDependencyDto = { dependsOnId: 'notask' };
            const createdBy = 'user1';
            const task = { id: taskId, title: 'Task 1' };

            mockTaskRepository.findOneById.mockResolvedValueOnce(task).mockResolvedValueOnce(null);

            // Act & Assert
            await expect(
                service.addDependency(taskId, addDependencyDto as any, createdBy)
            ).rejects.toThrow('Dependency task with ID notask not found');
        });

        it('should throw BadRequestException for self-dependency', async () => {
            // Arrange
            const taskId = 'task1';
            const addDependencyDto = { dependsOnId: 'task1' };
            const createdBy = 'user1';
            const task = { id: taskId, title: 'Task 1' };

            mockTaskRepository.findOneById.mockResolvedValueOnce(task).mockResolvedValueOnce(task);

            // Act & Assert
            await expect(
                service.addDependency(taskId, addDependencyDto as any, createdBy)
            ).rejects.toThrow('A task cannot depend on itself');
        });

        it('should throw ConflictException if dependency already exists', async () => {
            // Arrange
            const taskId = 'task1';
            const addDependencyDto = { dependsOnId: 'task2' };
            const createdBy = 'user1';
            const task = { id: taskId, title: 'Task 1' };
            const dependsOnTask = { id: 'task2', title: 'Task 2' };
            const existingDependency = { id: 'dep1', taskId, dependsOnId: 'task2' };

            mockTaskRepository.findOneById
                .mockResolvedValueOnce(task)
                .mockResolvedValueOnce(dependsOnTask);
            mockTaskDependencyRepository.findOne.mockResolvedValue(existingDependency);

            // Act & Assert
            await expect(
                service.addDependency(taskId, addDependencyDto as any, createdBy)
            ).rejects.toThrow('This dependency already exists');
        });

        it('should throw BadRequestException for circular dependency', async () => {
            // Arrange
            const taskId = 'task1';
            const addDependencyDto = { dependsOnId: 'task2' };
            const createdBy = 'user1';
            const task = { id: taskId, title: 'Task 1' };
            const dependsOnTask = { id: 'task2', title: 'Task 2' };

            mockTaskRepository.findOneById
                .mockResolvedValueOnce(task)
                .mockResolvedValueOnce(dependsOnTask);
            mockTaskDependencyRepository.findOne.mockResolvedValue(null);
            mockTaskDependencyRepository.wouldCreateCircularDependency.mockResolvedValue(true);

            // Act & Assert
            await expect(
                service.addDependency(taskId, addDependencyDto as any, createdBy)
            ).rejects.toThrow('Adding this dependency would create a circular reference');
        });
    });

    describe('getDependencies', () => {
        it('should return dependencies for a task', async () => {
            // Arrange
            const taskId = 'task1';
            const task = { id: taskId, title: 'Task 1' };
            const dependencies = [
                { id: 'dep1', taskId, dependsOnId: 'task2' },
                { id: 'dep2', taskId, dependsOnId: 'task3' }
            ];

            mockTaskRepository.findOneById.mockResolvedValue(task);
            mockTaskDependencyRepository.findByTaskId.mockResolvedValue(dependencies);

            // Act
            const result = await service.getDependencies(taskId);

            // Assert
            expect(mockTaskRepository.findOneById).toHaveBeenCalledWith(taskId);
            expect(mockTaskDependencyRepository.findByTaskId).toHaveBeenCalledWith(taskId);
            expect(result).toEqual(dependencies);
        });

        it('should throw NotFoundException if task does not exist', async () => {
            // Arrange
            const taskId = 'notask';
            mockTaskRepository.findOneById.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getDependencies(taskId)).rejects.toThrow(
                'Task with ID notask not found'
            );
        });
    });

    describe('getDependencyByTaskAndDependsOn', () => {
        it('should return dependency when found', async () => {
            // Arrange
            const taskId = 'task1';
            const dependsOnId = 'task2';
            const dependency = { id: 'dep1', taskId, dependsOnId };

            mockTaskDependencyRepository.findOne.mockResolvedValue(dependency);

            // Act
            const result = await service.getDependencyByTaskAndDependsOn(taskId, dependsOnId);

            // Assert
            expect(mockTaskDependencyRepository.findOne).toHaveBeenCalledWith({
                where: { taskId, dependsOnId }
            });
            expect(result).toEqual(dependency);
        });

        it('should return null when dependency not found', async () => {
            // Arrange
            const taskId = 'task1';
            const dependsOnId = 'task2';

            mockTaskDependencyRepository.findOne.mockResolvedValue(null);

            // Act
            const result = await service.getDependencyByTaskAndDependsOn(taskId, dependsOnId);

            // Assert
            expect(result).toBeNull();
        });
    });

    describe('getDependentTasks', () => {
        it('should return dependent tasks', async () => {
            // Arrange
            const taskId = 'task1';
            const task = { id: taskId, title: 'Task 1' };
            const dependentTasks = [
                { id: 'dep1', taskId: 'task2', dependsOnId: taskId },
                { id: 'dep2', taskId: 'task3', dependsOnId: taskId }
            ];

            mockTaskRepository.findOneById.mockResolvedValue(task);
            mockTaskDependencyRepository.findDependentTasks.mockResolvedValue(dependentTasks);

            // Act
            const result = await service.getDependentTasks(taskId);

            // Assert
            expect(mockTaskRepository.findOneById).toHaveBeenCalledWith(taskId);
            expect(mockTaskDependencyRepository.findDependentTasks).toHaveBeenCalledWith(taskId);
            expect(result).toEqual(dependentTasks);
        });

        it('should throw NotFoundException if task does not exist', async () => {
            // Arrange
            const taskId = 'notask';
            mockTaskRepository.findOneById.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getDependentTasks(taskId)).rejects.toThrow(
                'Task with ID notask not found'
            );
        });
    });

    describe('removeDependency', () => {
        it('should remove a dependency successfully', async () => {
            // Arrange
            const taskId = 'task1';
            const dependencyId = 'dep1';
            const userId = 'user1';
            const task = { id: taskId, title: 'Task 1' };
            const dependency = { id: dependencyId, taskId, dependsOnId: 'task2' };

            mockTaskRepository.findOneById.mockResolvedValue(task);
            mockTaskDependencyRepository.findOneById.mockResolvedValue(dependency);
            mockTaskDependencyRepository.removeById.mockResolvedValue(true);

            // Act
            const result = await service.removeDependency(taskId, dependencyId, userId);

            // Assert
            expect(mockTaskRepository.findOneById).toHaveBeenCalledWith(taskId);
            expect(mockTaskDependencyRepository.findOneById).toHaveBeenCalledWith(dependencyId);
            expect(mockTaskDependencyRepository.removeById).toHaveBeenCalledWith(dependencyId);
            expect(result).toBe(true);
        });

        it('should throw NotFoundException if task does not exist', async () => {
            // Arrange
            const taskId = 'notask';
            const dependencyId = 'dep1';
            const userId = 'user1';
            mockTaskRepository.findOneById.mockResolvedValue(null);

            // Act & Assert
            await expect(service.removeDependency(taskId, dependencyId, userId)).rejects.toThrow(
                'Task with ID notask not found'
            );
        });

        it('should throw NotFoundException if dependency does not exist', async () => {
            // Arrange
            const taskId = 'task1';
            const dependencyId = 'notdep';
            const userId = 'user1';
            const task = { id: taskId, title: 'Task 1' };

            mockTaskRepository.findOneById.mockResolvedValue(task);
            mockTaskDependencyRepository.findOneById.mockResolvedValue(null);

            // Act & Assert
            await expect(service.removeDependency(taskId, dependencyId, userId)).rejects.toThrow(
                'Dependency with ID notdep not found'
            );
        });

        it('should throw BadRequestException if dependency does not belong to task', async () => {
            // Arrange
            const taskId = 'task1';
            const dependencyId = 'dep1';
            const userId = 'user1';
            const task = { id: taskId, title: 'Task 1' };
            const dependency = { id: dependencyId, taskId: 'different-task', dependsOnId: 'task2' };

            mockTaskRepository.findOneById.mockResolvedValue(task);
            mockTaskDependencyRepository.findOneById.mockResolvedValue(dependency);

            // Act & Assert
            await expect(service.removeDependency(taskId, dependencyId, userId)).rejects.toThrow(
                'Dependency with ID dep1 does not belong to task task1'
            );
        });
    });

    describe('areAllDependenciesCompleted', () => {
        it('should return true when no dependencies exist', async () => {
            // Arrange
            const taskId = 'task1';
            mockTaskDependencyRepository.findByTaskId.mockResolvedValue([]);

            // Act
            const result = await service.areAllDependenciesCompleted(taskId);

            // Assert
            expect(mockTaskDependencyRepository.findByTaskId).toHaveBeenCalledWith(taskId);
            expect(result).toBe(true);
        });

        it('should return true when all dependencies are completed', async () => {
            // Arrange
            const taskId = 'task1';
            const dependencies = [
                { dependsOn: { status: TaskStatus.DONE } },
                { dependsOn: { status: TaskStatus.DONE } }
            ];

            mockTaskDependencyRepository.findByTaskId.mockResolvedValue(dependencies);

            // Act
            const result = await service.areAllDependenciesCompleted(taskId);

            // Assert
            expect(result).toBe(true);
        });

        it('should return false when any dependency is not completed', async () => {
            // Arrange
            const taskId = 'task1';
            const dependencies = [
                { dependsOn: { status: TaskStatus.DONE } },
                { dependsOn: { status: TaskStatus.IN_PROGRESS } }
            ];

            mockTaskDependencyRepository.findByTaskId.mockResolvedValue(dependencies);

            // Act
            const result = await service.areAllDependenciesCompleted(taskId);

            // Assert
            expect(result).toBe(false);
        });
    });

    describe('hasIncompleteDependentTasks', () => {
        it('should return false when no dependent tasks exist', async () => {
            // Arrange
            const taskId = 'task1';
            mockTaskDependencyRepository.findDependentTasks.mockResolvedValue([]);

            // Act
            const result = await service.hasIncompleteDependentTasks(taskId);

            // Assert
            expect(mockTaskDependencyRepository.findDependentTasks).toHaveBeenCalledWith(taskId);
            expect(result).toBe(false);
        });

        it('should return false when all dependent tasks are completed', async () => {
            // Arrange
            const taskId = 'task1';
            const dependentTasks = [
                { task: { status: TaskStatus.DONE } },
                { task: { status: TaskStatus.DONE } }
            ];

            mockTaskDependencyRepository.findDependentTasks.mockResolvedValue(dependentTasks);

            // Act
            const result = await service.hasIncompleteDependentTasks(taskId);

            // Assert
            expect(result).toBe(false);
        });

        it('should return true when any dependent task is incomplete', async () => {
            // Arrange
            const taskId = 'task1';
            const dependentTasks = [
                { task: { status: TaskStatus.DONE } },
                { task: { status: TaskStatus.IN_PROGRESS } }
            ];

            mockTaskDependencyRepository.findDependentTasks.mockResolvedValue(dependentTasks);

            // Act
            const result = await service.hasIncompleteDependentTasks(taskId);

            // Assert
            expect(result).toBe(true);
        });
    });
});
