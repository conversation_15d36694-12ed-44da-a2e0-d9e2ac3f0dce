import { Test, TestingModule } from '@nestjs/testing';
import { CaseNotificationService } from '../../apps/case-management/src/services/case-notification.service';
import { MessageProducerService } from '@app/common/communication/producers/communication.producer';
import { TenantContextService } from '@app/common/multi-tenancy/tenant-context.service';
import { CaseRepository } from '../../apps/case-management/src/repositories/case.repository';
import { CaseAssignmentRepository } from '../../apps/case-management/src/repositories/case-assignment.repository';
import { CaseAuditService } from '../../apps/case-management/src/services/case-audit.service';

describe('CaseNotificationService', () => {
    let service: CaseNotificationService;
    let messageProducer: MessageProducerService;

    beforeEach(async () => {
        const mockMessageProducer = {
            enqueueMessage: jest.fn().mockResolvedValue({ jobId: 'test-job', status: 'queued' })
        };

        const mockTenantContext = {
            getTenantId: jest.fn().mockReturnValue('test-tenant-123')
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                CaseNotificationService,
                {
                    provide: MessageProducerService,
                    useValue: mockMessageProducer
                },
                {
                    provide: TenantContextService,
                    useValue: mockTenantContext
                },
                {
                    provide: CaseRepository,
                    useValue: {}
                },
                {
                    provide: CaseAssignmentRepository,
                    useValue: {}
                },
                {
                    provide: CaseAuditService,
                    useValue: {}
                }
            ]
        }).compile();

        service = module.get<CaseNotificationService>(CaseNotificationService);
        messageProducer = module.get<MessageProducerService>(MessageProducerService);
        tenantContext = module.get<TenantContextService>(TenantContextService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('sendCaseCreatedNotification', () => {
        it('should send case creation notification with correct parameters', async () => {
            const mockCase = {
                id: 'case-123',
                caseNumber: 'TEST-2024-001',
                status: 'DRAFT',
                title: 'Test Case',
                description: 'Test case description',
                type: 'LITIGATION',
                deadline: new Date('2024-12-31'),
                client: {
                    name: 'Test Client',
                    email: '<EMAIL>'
                }
            };

            await service.sendCaseCreatedNotification(mockCase, 'user-123', ['<EMAIL>']);

            expect(messageProducer.enqueueMessage).toHaveBeenCalledWith({
                tenantId: 'test-tenant-123',
                userId: 'user-123',
                channels: ['email'],
                recipients: {
                    email: ['<EMAIL>']
                },
                variables: expect.objectContaining({
                    type: 'case-created',
                    caseId: 'case-123',
                    caseNumber: 'TEST-2024-001',
                    status: 'DRAFT',
                    recipientName: 'Test Client'
                }),
                caseId: 'case-123',
                metadata: {
                    notificationType: 'case-created'
                }
            });
        });
    });

    describe('urgency determination', () => {
        it('should determine urgency correctly based on deadline and priority', () => {
            // Access private method for testing
            const determineUrgency = (service as any).determineUrgency;

            // Test critical urgency for overdue case
            const overdueCase = {
                deadline: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
                priority: 'MEDIUM'
            };
            expect(determineUrgency.call(service, overdueCase)).toBe('critical');

            // Test high urgency for high priority
            const highPriorityCase = {
                deadline: new Date(Date.now() + 48 * 60 * 60 * 1000), // 2 days from now
                priority: 'HIGH'
            };
            expect(determineUrgency.call(service, highPriorityCase)).toBe('high');

            // Test normal urgency
            const normalCase = {
                deadline: new Date(Date.now() + 48 * 60 * 60 * 1000), // 2 days from now
                priority: 'MEDIUM'
            };
            expect(determineUrgency.call(service, normalCase)).toBe('normal');
        });
    });
});
