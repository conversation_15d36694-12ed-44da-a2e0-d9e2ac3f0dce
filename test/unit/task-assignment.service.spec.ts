import { Test, TestingModule } from '@nestjs/testing';
import { TaskAssignmentService } from '../../apps/task-management/src/services/task-assignment.service';
import { TaskRepository } from '../../apps/task-management/src/repositories/task.repository';

// Mock dependencies
const mockTaskRepository = {
    findOneById: jest.fn(),
    save: jest.fn(),
    countOpenTasksByAssigneeId: jest.fn()
};

describe('TaskAssignmentService', () => {
    let service: TaskAssignmentService;

    beforeEach(async () => {
        jest.clearAllMocks();
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TaskAssignmentService,
                { provide: TaskRepository, useValue: mockTaskRepository }
            ]
        }).compile();
        service = module.get<TaskAssignmentService>(TaskAssignmentService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('assignTask', () => {
        it('should assign a task to a user', async () => {
            // Arrange
            const taskId = 'task1';
            const assignTaskDto = { assigneeId: 'user1' };
            const existingTask = { id: taskId, title: 'Test Task', assigneeId: null };
            const updatedTask = {
                ...existingTask,
                assigneeId: 'user1',
                updatedAt: expect.any(Date)
            };

            mockTaskRepository.findOneById.mockResolvedValue(existingTask);
            mockTaskRepository.save.mockResolvedValue(updatedTask);

            // Act
            const result = await service.assignTask(taskId, assignTaskDto as any);

            // Assert
            expect(mockTaskRepository.findOneById).toHaveBeenCalledWith(taskId);
            expect(mockTaskRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    id: taskId,
                    assigneeId: 'user1',
                    updatedAt: expect.any(Date)
                })
            );
            expect(result).toEqual(updatedTask);
        });

        it('should throw NotFoundException if task does not exist', async () => {
            // Arrange
            const taskId = 'notask';
            const assignTaskDto = { assigneeId: 'user1' };
            mockTaskRepository.findOneById.mockResolvedValue(null);

            // Act & Assert
            await expect(service.assignTask(taskId, assignTaskDto as any)).rejects.toThrow(
                'Task with ID notask not found'
            );
        });
    });

    describe('autoAssignTask', () => {
        it('should auto-assign task to user with lowest workload', async () => {
            // Arrange
            const task = { id: 'task1', caseId: 'case1', title: 'Test Task' };
            const caseAssignees = ['user1', 'user2', 'user3'];
            const updatedTask = { ...task, assigneeId: 'user2' };

            mockTaskRepository.countOpenTasksByAssigneeId
                .mockResolvedValueOnce(5) // user1 workload
                .mockResolvedValueOnce(2) // user2 workload (lowest)
                .mockResolvedValueOnce(8); // user3 workload
            mockTaskRepository.save.mockResolvedValue(updatedTask);

            // Act
            const result = await service.autoAssignTask(task as any, caseAssignees);

            // Assert
            expect(mockTaskRepository.countOpenTasksByAssigneeId).toHaveBeenCalledTimes(3);
            expect(mockTaskRepository.countOpenTasksByAssigneeId).toHaveBeenCalledWith('user1');
            expect(mockTaskRepository.countOpenTasksByAssigneeId).toHaveBeenCalledWith('user2');
            expect(mockTaskRepository.countOpenTasksByAssigneeId).toHaveBeenCalledWith('user3');
            expect(mockTaskRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    id: 'task1',
                    assigneeId: 'user2'
                })
            );
            expect(result).toEqual(updatedTask);
        });

        it('should leave task unassigned when no case assignees available', async () => {
            // Arrange
            const task = { id: 'task1', caseId: 'case1', title: 'Test Task' };
            const caseAssignees: string[] = [];

            // Act
            const result = await service.autoAssignTask(task as any, caseAssignees);

            // Assert
            expect(mockTaskRepository.countOpenTasksByAssigneeId).not.toHaveBeenCalled();
            expect(mockTaskRepository.save).not.toHaveBeenCalled();
            expect(result).toEqual(task);
        });

        it('should handle single assignee case', async () => {
            // Arrange
            const task = { id: 'task1', caseId: 'case1', title: 'Test Task' };
            const caseAssignees = ['user1'];
            const updatedTask = { ...task, assigneeId: 'user1' };

            mockTaskRepository.countOpenTasksByAssigneeId.mockResolvedValue(3);
            mockTaskRepository.save.mockResolvedValue(updatedTask);

            // Act
            const result = await service.autoAssignTask(task as any, caseAssignees);

            // Assert
            expect(mockTaskRepository.countOpenTasksByAssigneeId).toHaveBeenCalledWith('user1');
            expect(mockTaskRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    id: 'task1',
                    assigneeId: 'user1'
                })
            );
            expect(result).toEqual(updatedTask);
        });

        it('should handle equal workloads by assigning to first user', async () => {
            // Arrange
            const task = { id: 'task1', caseId: 'case1', title: 'Test Task' };
            const caseAssignees = ['user1', 'user2'];
            const updatedTask = { ...task, assigneeId: 'user1' };

            mockTaskRepository.countOpenTasksByAssigneeId
                .mockResolvedValueOnce(5) // user1 workload
                .mockResolvedValueOnce(5); // user2 workload (same)
            mockTaskRepository.save.mockResolvedValue(updatedTask);

            // Act
            const result = await service.autoAssignTask(task as any, caseAssignees);

            // Assert
            expect(mockTaskRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    id: 'task1',
                    assigneeId: 'user1' // First user gets assigned when workloads are equal
                })
            );
            expect(result).toEqual(updatedTask);
        });
    });

    describe('unassignTask', () => {
        it('should unassign a task', async () => {
            // Arrange
            const taskId = 'task1';
            const existingTask = { id: taskId, title: 'Test Task', assigneeId: 'user1' };
            const updatedTask = { ...existingTask, assigneeId: null, updatedAt: expect.any(Date) };

            mockTaskRepository.findOneById.mockResolvedValue(existingTask);
            mockTaskRepository.save.mockResolvedValue(updatedTask);

            // Act
            const result = await service.unassignTask(taskId);

            // Assert
            expect(mockTaskRepository.findOneById).toHaveBeenCalledWith(taskId);
            expect(mockTaskRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    id: taskId,
                    assigneeId: null,
                    updatedAt: expect.any(Date)
                })
            );
            expect(result).toEqual(updatedTask);
        });

        it('should throw NotFoundException if task does not exist', async () => {
            // Arrange
            const taskId = 'notask';
            mockTaskRepository.findOneById.mockResolvedValue(null);

            // Act & Assert
            await expect(service.unassignTask(taskId)).rejects.toThrow(
                'Task with ID notask not found'
            );
        });

        it('should handle unassigning already unassigned task', async () => {
            // Arrange
            const taskId = 'task1';
            const existingTask = { id: taskId, title: 'Test Task', assigneeId: null };
            const updatedTask = { ...existingTask, updatedAt: expect.any(Date) };

            mockTaskRepository.findOneById.mockResolvedValue(existingTask);
            mockTaskRepository.save.mockResolvedValue(updatedTask);

            // Act
            const result = await service.unassignTask(taskId);

            // Assert
            expect(mockTaskRepository.save).toHaveBeenCalledWith(
                expect.objectContaining({
                    id: taskId,
                    assigneeId: null
                })
            );
            expect(result).toEqual(updatedTask);
        });
    });
});
