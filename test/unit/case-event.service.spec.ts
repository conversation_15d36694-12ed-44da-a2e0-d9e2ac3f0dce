import { Test, TestingModule } from '@nestjs/testing';
import { CaseEventService } from '../../apps/case-management/src/services/case-event.service';
import { CaseEventRepository } from '../../apps/case-management/src/repositories/case-event.repository';
import { CaseRepository } from '../../apps/case-management/src/repositories/case.repository';
import { NotFoundException } from '@nestjs/common';
import { CreateCaseEventDto } from '../../apps/case-management/src/dto/create-case-event.dto';
import {
    CaseEventCategory,
    CaseEventType
} from '@app/common/typeorm/entities/tenant/case-event.entity';
import { CaseAuditService } from '../../apps/case-management/src/services/case-audit.service';
import { PaginationService } from '../../apps/case-management/src/services/pagination.service';
import { Request } from 'express';

describe('CaseEventService', () => {
    let service: CaseEventService;

    // Mock repositories
    const mockCaseRepository = {
        findOne: jest.fn()
    };

    const mockCaseEventRepository = {
        findOne: jest.fn(),
        find: jest.fn(),
        create: jest.fn(),
        save: jest.fn(),
        findByCaseId: jest.fn(),
        findByCaseIdAndCategory: jest.fn(),
        findByCaseIdAndType: jest.fn()
    };

    // Mock services
    const mockCaseAuditService = {
        logAction: jest.fn()
    };

    const mockPaginationService = {
        createPaginatedResponse: jest.fn()
    };

    // Mock request
    const mockRequest = {
        ip: '127.0.0.1',
        headers: {
            'x-forwarded-for': '********'
        }
    } as unknown as Request;

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                CaseEventService,
                { provide: CaseRepository, useValue: mockCaseRepository },
                { provide: CaseEventRepository, useValue: mockCaseEventRepository },
                { provide: CaseAuditService, useValue: mockCaseAuditService },
                { provide: PaginationService, useValue: mockPaginationService }
            ]
        }).compile();

        service = module.get<CaseEventService>(CaseEventService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createEvent', () => {
        it('should create a new event for a case', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const userId = 'user-id-123';
            const userName = 'Test User';

            const createEventDto: CreateCaseEventDto = {
                title: 'Test Event',
                description: 'This is a test event',
                category: CaseEventCategory.OTHER,
                type: CaseEventType.OTHER,
                eventDate: new Date(),
                metadata: { status: 'APPROVED' }
            };

            const mockCase = { id: caseId, title: 'Test Case' };
            const mockEvent = {
                id: 'event-id-123',
                caseId,
                ...createEventDto,
                createdBy: userId,
                createdByName: userName
            };

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseEventRepository.create.mockReturnValue(mockEvent);
            mockCaseEventRepository.save.mockResolvedValue(mockEvent);

            // Act
            const result = await service.createEvent(
                caseId,
                createEventDto,
                userId,
                userName,
                mockRequest
            );

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseEventRepository.create).toHaveBeenCalledWith({
                caseId,
                title: createEventDto.title,
                description: createEventDto.description,
                category: createEventDto.category,
                type: createEventDto.type,
                eventDate: createEventDto.eventDate,
                metadata: createEventDto.metadata,
                createdBy: userId,
                createdByName: userName
            });
            expect(mockCaseEventRepository.save).toHaveBeenCalledWith(mockEvent);
            expect(result).toEqual(mockEvent);
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const caseId = 'non-existent-case-id';
            const userId = 'user-id-123';
            const userName = 'Test User';

            const createEventDto: CreateCaseEventDto = {
                title: 'Test Event',
                description: 'This is a test event',
                category: CaseEventCategory.OTHER,
                type: CaseEventType.OTHER,
                eventDate: new Date()
            };

            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                service.createEvent(caseId, createEventDto, userId, userName, mockRequest)
            ).rejects.toThrow(NotFoundException);
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseEventRepository.create).not.toHaveBeenCalled();
        });
    });

    describe('getCaseEvents', () => {
        it('should return all events for a case', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const mockCase = { id: caseId, title: 'Test Case' };
            const mockEvents = [
                { id: 'event-1', caseId, title: 'Event 1' },
                { id: 'event-2', caseId, title: 'Event 2' }
            ];

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseEventRepository.findByCaseId.mockResolvedValue(mockEvents);

            // Act
            const result = await service.getCaseEvents(caseId);

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseEventRepository.findByCaseId).toHaveBeenCalledWith(caseId);
            expect(result).toEqual(mockEvents);
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const caseId = 'non-existent-case-id';
            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getCaseEvents(caseId)).rejects.toThrow(NotFoundException);
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseEventRepository.findByCaseId).not.toHaveBeenCalled();
        });
    });

    describe('getEventsByCategory', () => {
        it('should return events of a specific category', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const category = CaseEventCategory.OTHER;
            const mockCase = { id: caseId, title: 'Test Case' };
            const mockEvents = [
                { id: 'event-1', caseId, category: CaseEventCategory.OTHER },
                { id: 'event-2', caseId, category: CaseEventCategory.OTHER }
            ];

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseEventRepository.findByCaseIdAndCategory.mockResolvedValue(mockEvents);

            // Act
            const result = await service.getEventsByCategory(caseId, category);

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseEventRepository.findByCaseIdAndCategory).toHaveBeenCalledWith(
                caseId,
                category
            );
            expect(result).toEqual(mockEvents);
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const caseId = 'non-existent-case-id';
            const category = CaseEventCategory.OTHER;
            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getEventsByCategory(caseId, category)).rejects.toThrow(
                NotFoundException
            );
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseEventRepository.findByCaseIdAndCategory).not.toHaveBeenCalled();
        });
    });

    describe('getEventsByType', () => {
        it('should return events of a specific type', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const type = CaseEventType.OTHER;
            const mockCase = { id: caseId, title: 'Test Case' };
            const mockEvents = [
                { id: 'event-1', caseId, type: CaseEventType.OTHER },
                { id: 'event-2', caseId, type: CaseEventType.OTHER }
            ];

            mockCaseRepository.findOne.mockResolvedValue(mockCase);
            mockCaseEventRepository.findByCaseIdAndType.mockResolvedValue(mockEvents);

            // Act
            const result = await service.getEventsByType(caseId, type);

            // Assert
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseEventRepository.findByCaseIdAndType).toHaveBeenCalledWith(caseId, type);
            expect(result).toEqual(mockEvents);
        });

        it('should throw NotFoundException if case not found', async () => {
            // Arrange
            const caseId = 'non-existent-case-id';
            const type = CaseEventType.OTHER;
            mockCaseRepository.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(service.getEventsByType(caseId, type)).rejects.toThrow(NotFoundException);
            expect(mockCaseRepository.findOne).toHaveBeenCalledWith({
                where: { id: caseId }
            });
            expect(mockCaseEventRepository.findByCaseIdAndType).not.toHaveBeenCalled();
        });
    });
});
