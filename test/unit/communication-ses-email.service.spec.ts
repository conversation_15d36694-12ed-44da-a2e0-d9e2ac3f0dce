import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { SESEmailService } from '@app/common/communication/services/ses-email.service';
import {
    createMockConfigService,
    createMockEmailTemplateData
} from '@app/common/testing/mock-factories';
import { SESEmailData, SESEmailResult } from '@app/common/communication/services/ses-email.service';
import { UnifiedTemplateConfig } from '@app/common/communication/services/template.service';

// Mock AWS SES SDK
const mockSESClient = {
    send: jest.fn()
};

jest.mock('@aws-sdk/client-ses', () => ({
    SESClient: jest.fn(() => mockSESClient),
    SendTemplatedEmailCommand: jest.fn((params) => ({
        ...params,
        _mockCommand: 'SendTemplatedEmailCommand'
    }))
}));

describe('SESEmailService', () => {
    let service: SESEmailService;
    let mockConfigService: any;

    const testTemplateData = createMockEmailTemplateData();
    const mockTemplateConfig: UnifiedTemplateConfig = {
        sendGridTemplateId: 'd-test-template-id',
        sesTemplateName: 'test-ses-template',
        mailgunTemplateName: 'test-mailgun-template',
        defaultSubject: 'Test Subject - {{caseNumber}}',
        categories: ['test', 'case', 'legal'],
        requiredVariables: ['tenantName', 'recipientName', 'caseNumber'],
        optionalVariables: ['caseSummary', 'handlerName']
    };

    const mockSESEmailData: SESEmailData = {
        to: '<EMAIL>',
        templateType: 'case-update',
        templateData: testTemplateData.caseUpdate,
        tenantId: 'test-tenant-123'
    };

    beforeEach(async () => {
        // Clear all mocks before each test
        jest.clearAllMocks();

        // Create mock services
        mockConfigService = createMockConfigService({
            AWS_ACCESS_KEY_ID: 'test-access-key',
            AWS_SECRET_ACCESS_KEY: 'test-secret-key',
            AWS_SES_REGION: 'us-east-1',
            FROM_EMAIL: '<EMAIL>'
        });

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                SESEmailService,
                {
                    provide: ConfigService,
                    useValue: mockConfigService
                }
            ]
        }).compile();

        service = module.get<SESEmailService>(SESEmailService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('method: sendTemplatedEmail', () => {
        beforeEach(() => {
            mockSESClient.send.mockResolvedValue({
                MessageId: 'ses-test-message-id',
                $metadata: {
                    httpStatusCode: 200,
                    requestId: 'test-request-id'
                }
            });
        });

        it('should send templated email successfully', async () => {
            // Arrange
            const emailData = mockSESEmailData;
            const template = mockTemplateConfig;

            // Act
            const result: SESEmailResult = await service.sendTemplatedEmail(emailData, template);

            // Assert
            expect(result).toEqual({
                messageId: 'ses-test-message-id',
                provider: 'ses',
                status: 'sent',
                templateType: 'case-update'
            });
            expect(mockSESClient.send).toHaveBeenCalledTimes(1);
        });

        it('should validate email data before sending', async () => {
            // Arrange
            const invalidEmailData = {
                ...mockSESEmailData,
                to: 'invalid-email'
            };
            const template = mockTemplateConfig;

            // Act & Assert
            await expect(service.sendTemplatedEmail(invalidEmailData, template)).rejects.toThrow(
                'Invalid email address'
            );
            expect(mockSESClient.send).not.toHaveBeenCalled();
        });

        it('should throw error when SES is not configured', async () => {
            // Arrange
            mockConfigService.get.mockImplementation((key) => {
                if (key === 'AWS_ACCESS_KEY_ID') return undefined;
                if (key === 'AWS_SECRET_ACCESS_KEY') return undefined;
                return 'us-east-1';
            });

            // Recreate service with unconfigured SES
            const module = await Test.createTestingModule({
                providers: [
                    SESEmailService,
                    { provide: ConfigService, useValue: mockConfigService }
                ]
            }).compile();
            const unconfiguredService = module.get<SESEmailService>(SESEmailService);

            const emailData = mockSESEmailData;
            const template = mockTemplateConfig;

            // Act & Assert
            await expect(
                unconfiguredService.sendTemplatedEmail(emailData, template)
            ).rejects.toThrow('SES is not properly configured');
        });

        it('should handle SES template not found error', async () => {
            // Arrange
            const sesError = new Error('Template not found');
            sesError.name = 'TemplateDoesNotExistException';
            mockSESClient.send.mockRejectedValue(sesError);

            const emailData = mockSESEmailData;
            const template = mockTemplateConfig;

            // Act & Assert
            await expect(service.sendTemplatedEmail(emailData, template)).rejects.toThrow(
                'SES templated email failed'
            );
        });

        it('should respect AWS tag limits', async () => {
            // Arrange
            const templateWithManyCategories = {
                ...mockTemplateConfig,
                categories: [
                    'cat1',
                    'cat2',
                    'cat3',
                    'cat4',
                    'cat5',
                    'cat6',
                    'cat7',
                    'cat8',
                    'cat9',
                    'cat10',
                    'cat11',
                    'cat12'
                ]
            };

            // Act
            await service.sendTemplatedEmail(mockSESEmailData, templateWithManyCategories);

            // Assert
            const sentCommand = mockSESClient.send.mock.calls[0][0];
            // AWS SES has a limit of 10 tags per message (2 standard + 8 category tags max)
            expect(sentCommand.Tags.length).toBeLessThanOrEqual(10);
        });
    });

    describe('method: isEnabled', () => {
        it('should return true when SES is properly configured', () => {
            // Act
            const result = service.isEnabled();

            // Assert
            expect(result).toBe(true);
        });

        it('should return false when SES credentials are missing', async () => {
            // Arrange
            mockConfigService.get.mockImplementation((key) => {
                if (key === 'AWS_ACCESS_KEY_ID') return undefined;
                if (key === 'AWS_SECRET_ACCESS_KEY') return undefined;
                return 'us-east-1';
            });

            // Recreate service with unconfigured SES
            const module = await Test.createTestingModule({
                providers: [
                    SESEmailService,
                    { provide: ConfigService, useValue: mockConfigService }
                ]
            }).compile();
            const unconfiguredService = module.get<SESEmailService>(SESEmailService);

            // Act
            const result = unconfiguredService.isEnabled();

            // Assert
            expect(result).toBe(false);
        });
    });

    describe('method: getStatus', () => {
        it('should return correct SES service status', () => {
            // Act
            const result = service.getStatus();

            // Assert
            expect(result).toEqual({
                configured: true,
                region: 'us-east-1',
                status: 'active'
            });
        });

        it('should return inactive status when not configured', async () => {
            // Arrange
            mockConfigService.get.mockImplementation((key) => {
                if (key === 'AWS_ACCESS_KEY_ID') return undefined;
                return 'us-east-1';
            });

            // Recreate service with unconfigured SES
            const module = await Test.createTestingModule({
                providers: [
                    SESEmailService,
                    { provide: ConfigService, useValue: mockConfigService }
                ]
            }).compile();
            const unconfiguredService = module.get<SESEmailService>(SESEmailService);

            // Act
            const result = unconfiguredService.getStatus();

            // Assert
            expect(result).toEqual({
                configured: false,
                region: 'us-east-1',
                status: 'not configured'
            });
        });
    });

    describe('error handling', () => {
        it('should handle SES service errors gracefully', async () => {
            // Arrange
            const sesError = new Error('SES service temporarily unavailable');
            sesError.name = 'ServiceUnavailableException';
            mockSESClient.send.mockRejectedValue(sesError);

            const emailData = mockSESEmailData;
            const template = mockTemplateConfig;

            // Act & Assert
            await expect(service.sendTemplatedEmail(emailData, template)).rejects.toThrow(
                'SES templated email failed'
            );
        });

        it('should handle network errors', async () => {
            // Arrange
            const networkError = new Error('Network error');
            networkError.name = 'NetworkError';
            mockSESClient.send.mockRejectedValue(networkError);

            const emailData = mockSESEmailData;
            const template = mockTemplateConfig;

            // Act & Assert
            await expect(service.sendTemplatedEmail(emailData, template)).rejects.toThrow(
                'SES templated email failed'
            );
        });

        it('should handle quota exceeded errors', async () => {
            // Arrange
            const quotaError = new Error('Sending quota exceeded');
            quotaError.name = 'SendingPausedException';
            mockSESClient.send.mockRejectedValue(quotaError);

            const emailData = mockSESEmailData;
            const template = mockTemplateConfig;

            // Act & Assert
            await expect(service.sendTemplatedEmail(emailData, template)).rejects.toThrow(
                'SES templated email failed'
            );
        });
    });
});
