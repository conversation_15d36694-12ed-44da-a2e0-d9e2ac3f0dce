import { Test, TestingModule } from '@nestjs/testing';
import { CaseAttachmentController } from '../../apps/case-management/src/controllers/case-attachment.controller';
import { CaseAttachmentService } from '../../apps/case-management/src/services/case-attachment.service';
import { CreateAttachmentDto } from '../../apps/case-management/src/dto/create-attachment.dto';
import { Request } from 'express';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { DocumentType } from '@app/common/typeorm/entities/tenant/case-attachment.entity';

// Extend Express Request to include user property
interface RequestWithUser extends Request {
    user: {
        id: string;
        username: string;
        email: string;
        roles: string[];
        systemUserId: string;
        preferred_username: string;
    };
}

describe('CaseAttachmentController', () => {
    let controller: CaseAttachmentController;

    // Mock request with user information
    const mockRequest = {
        ip: '127.0.0.1',
        headers: {
            'x-forwarded-for': '********'
        },
        user: {
            id: 'user-id-123',
            systemUserId: 'user-id-123',
            username: 'testuser',
            preferred_username: 'testuser',
            email: '<EMAIL>',
            roles: ['user']
        }
    } as unknown as RequestWithUser;

    // Mock CaseAttachmentService
    const mockCaseAttachmentService = {
        createAttachment: jest.fn(),
        getCaseAttachments: jest.fn(),
        getAttachment: jest.fn(),
        deleteAttachment: jest.fn(),
        searchAttachmentsByFilename: jest.fn(),
        getAttachmentsByDocumentType: jest.fn(),
        getAttachmentById: jest.fn()
    };

    // Mock guards
    const mockJwtGuard = { canActivate: jest.fn().mockReturnValue(true) };
    const mockTenantGuard = { canActivate: jest.fn().mockReturnValue(true) };
    const mockRolesGuard = { canActivate: jest.fn().mockReturnValue(true) };

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            controllers: [CaseAttachmentController],
            providers: [
                {
                    provide: CaseAttachmentService,
                    useValue: mockCaseAttachmentService
                }
            ]
        })
            .overrideGuard(JwtGuard)
            .useValue(mockJwtGuard)
            .overrideGuard(TenantGuard)
            .useValue(mockTenantGuard)
            .overrideGuard(RolesGuard)
            .useValue(mockRolesGuard)
            .compile();

        controller = module.get<CaseAttachmentController>(CaseAttachmentController);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    describe('createAttachment', () => {
        it('should create a new attachment for a case', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const createAttachmentDto: CreateAttachmentDto = {
                filename: 'test-document.pdf',
                url: 'https://storage.example.com/test-document.pdf',
                fileSize: 1024,
                mimeType: 'application/pdf',
                description: 'Test document',
                documentType: DocumentType.EVIDENCE
            };

            const attachmentData = {
                id: 'attachment-id-123',
                caseId,
                ...createAttachmentDto,
                uploadedBy: mockRequest.user.systemUserId,
                uploadedByName: mockRequest.user.preferred_username,
                createdAt: new Date()
            };

            mockCaseAttachmentService.createAttachment.mockResolvedValue(attachmentData);

            // Act
            const result = await controller.createAttachment(
                caseId,
                createAttachmentDto,
                mockRequest
            );

            // Assert
            expect(mockCaseAttachmentService.createAttachment).toHaveBeenCalledWith(
                caseId,
                createAttachmentDto,
                mockRequest.user.systemUserId,
                mockRequest.user.preferred_username || mockRequest.user.email,
                mockRequest
            );
            expect(result).toEqual({
                code: 201,
                status: 'Created',
                message: 'Attachment created successfully',
                data: attachmentData
            });
        });
    });

    describe('getCaseAttachments', () => {
        it('should return all attachments for a case', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const attachmentsData = [
                { id: 'attachment-1', caseId, filename: 'document1.pdf' },
                { id: 'attachment-2', caseId, filename: 'document2.pdf' }
            ];

            mockCaseAttachmentService.getCaseAttachments.mockResolvedValue(attachmentsData);

            // Act
            const result = await controller.getCaseAttachments(caseId);

            // Assert
            expect(mockCaseAttachmentService.getCaseAttachments).toHaveBeenCalledWith(caseId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Attachments retrieved successfully',
                data: attachmentsData
            });
        });
    });

    describe('getAttachmentById', () => {
        it('should return a specific attachment', async () => {
            // Arrange
            const attachmentId = 'attachment-id-123';
            const attachmentData = {
                id: attachmentId,
                caseId: 'case-id-123',
                filename: 'document.pdf',
                url: 'https://storage.example.com/document.pdf'
            };

            mockCaseAttachmentService.getAttachmentById.mockResolvedValue(attachmentData);

            // Act
            const result = await controller.getAttachmentById(attachmentId);

            // Assert
            expect(mockCaseAttachmentService.getAttachmentById).toHaveBeenCalledWith(attachmentId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Attachment retrieved successfully',
                data: attachmentData
            });
        });
    });

    describe('deleteAttachment', () => {
        it('should delete an attachment', async () => {
            // Arrange
            const attachmentId = 'attachment-id-123';

            mockCaseAttachmentService.deleteAttachment.mockResolvedValue(undefined);

            // Act
            const result = await controller.deleteAttachment(attachmentId, mockRequest);

            // Assert
            expect(mockCaseAttachmentService.deleteAttachment).toHaveBeenCalledWith(
                attachmentId,
                mockRequest.user.systemUserId,
                mockRequest.user.preferred_username || mockRequest.user.email,
                mockRequest
            );
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Attachment deleted successfully',
                data: null
            });
        });
    });

    describe('searchAttachmentsByFilename', () => {
        it('should return attachments matching the filename search', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const filename = 'document';
            const attachmentsData = [
                { id: 'attachment-1', caseId, filename: 'document1.pdf' },
                { id: 'attachment-2', caseId, filename: 'document2.pdf' }
            ];

            mockCaseAttachmentService.searchAttachmentsByFilename.mockResolvedValue(
                attachmentsData
            );

            // Act
            const result = await controller.searchAttachmentsByFilename(caseId, filename);

            // Assert
            expect(mockCaseAttachmentService.searchAttachmentsByFilename).toHaveBeenCalledWith(
                caseId,
                filename
            );
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Attachments retrieved successfully',
                data: attachmentsData
            });
        });
    });

    describe('getAttachmentsByDocumentType', () => {
        it('should return attachments of a specific document type', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const documentType = DocumentType.EVIDENCE;
            const attachments = [
                { id: 'attachment-1', caseId, documentType: DocumentType.EVIDENCE },
                { id: 'attachment-2', caseId, documentType: DocumentType.EVIDENCE }
            ];

            mockCaseAttachmentService.getAttachmentsByDocumentType.mockResolvedValue(attachments);

            // Act
            const result = await controller.getAttachmentsByDocumentType(caseId, documentType);

            // Assert
            expect(mockCaseAttachmentService.getAttachmentsByDocumentType).toHaveBeenCalledWith(
                caseId,
                documentType
            );
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: `Attachments of type ${documentType} retrieved successfully`,
                data: attachments
            });
        });
    });
});
