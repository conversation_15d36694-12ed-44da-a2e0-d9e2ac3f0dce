import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { TemplateService } from '@app/common/communication/services/template.service';
import { Tenant } from '@app/common/typeorm/entities';
import {
    createMockConfigService,
    createMockTenant,
    createMockEmailTemplateData
} from '@app/common/testing/mock-factories';

describe('TemplateService', () => {
    let service: TemplateService;
    let mockConfigService: any;
    let mockTenantRepository: jest.Mocked<Repository<Tenant>>;

    const testTemplateData = createMockEmailTemplateData();
    const mockTenant = createMockTenant();

    beforeEach(async () => {
        // Clear all mocks before each test
        jest.clearAllMocks();

        // Create mock services
        mockConfigService = createMockConfigService({
            SUPPORT_EMAIL: '<EMAIL>',
            CLIENT_PORTAL_URL: 'https://portal.testfirm.com',
            APP_URL: 'https://app.testfirm.com'
        });

        mockTenantRepository = {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            delete: jest.fn(),
            create: jest.fn(),
            merge: jest.fn(),
            remove: jest.fn()
        } as any;

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TemplateService,
                {
                    provide: ConfigService,
                    useValue: mockConfigService
                },
                {
                    provide: getRepositoryToken(Tenant),
                    useValue: mockTenantRepository
                }
            ]
        }).compile();

        service = module.get<TemplateService>(TemplateService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('method: determineTemplateType', () => {
        it('should return case-update for case with status', () => {
            // Arrange
            const variables = {
                caseId: 'case-123',
                status: 'in_progress'
            };

            // Act
            const result = service.determineTemplateType(variables);

            // Assert
            expect(result).toBe('case-update');
        });

        it('should return case-urgent for high urgency cases', () => {
            // Arrange
            const variables = {
                caseId: 'case-123',
                urgency: 'high'
            };

            // Act
            const result = service.determineTemplateType(variables);

            // Assert
            expect(result).toBe('case-urgent');
        });

        it('should return case-created for case without status', () => {
            // Arrange
            const variables = {
                caseId: 'case-123'
                // No status field
            };

            // Act
            const result = service.determineTemplateType(variables);

            // Assert
            expect(result).toBe('case-created');
        });

        it('should return welcome for welcome type', () => {
            // Arrange
            const variables = {
                type: 'welcome',
                isWelcome: true
            };

            // Act
            const result = service.determineTemplateType(variables);

            // Assert
            expect(result).toBe('welcome');
        });

        it('should return password-reset for password reset token', () => {
            // Arrange
            const variables = {
                passwordResetToken: 'reset-token-123'
            };

            // Act
            const result = service.determineTemplateType(variables);

            // Assert
            expect(result).toBe('password-reset');
        });

        it('should return generic-notification for unknown types', () => {
            // Arrange
            const variables = {
                randomField: 'random-value'
            };

            // Act
            const result = service.determineTemplateType(variables);

            // Assert
            expect(result).toBe('generic-notification');
        });

        it('should return specific type when type is explicitly provided', () => {
            // Arrange
            const variables = {
                type: 'billing-statement',
                caseId: 'case-123' // This would normally trigger case-created
            };

            // Act
            const result = service.determineTemplateType(variables);

            // Assert
            expect(result).toBe('billing-statement');
        });
    });

    describe('method: validateTemplateData', () => {
        it('should pass validation for complete required variables', () => {
            // Arrange
            const templateType = 'case-update';
            const templateData = {
                tenantName: 'Test Legal Firm',
                recipientName: 'John Test',
                caseNumber: 'TEST-2024-001',
                status: 'in_progress'
            };

            // Act & Assert
            expect(() => service.validateTemplateData(templateType, templateData)).not.toThrow();
        });

        it('should throw error for missing required variables', () => {
            // Arrange
            const templateType = 'case-update';
            const templateData = {
                tenantName: 'Test Legal Firm',
                recipientName: 'John Test'
                // Missing caseNumber and status
            };

            // Act & Assert
            expect(() => service.validateTemplateData(templateType, templateData)).toThrow(
                'Missing required template variables for case-update: caseNumber, status'
            );
        });

        it('should throw error for empty string required variables', () => {
            // Arrange
            const templateType = 'case-update';
            const templateData = {
                tenantName: 'Test Legal Firm',
                recipientName: '', // Empty string
                caseNumber: 'TEST-2024-001',
                status: 'in_progress'
            };

            // Act & Assert
            expect(() => service.validateTemplateData(templateType, templateData)).toThrow(
                'Missing required template variables for case-update: recipientName'
            );
        });

        it('should throw error for unknown template type', () => {
            // Arrange
            const templateType = 'unknown-template';
            const templateData = {};

            // Act & Assert
            expect(() => service.validateTemplateData(templateType, templateData)).toThrow(
                'Template configuration not found for type: unknown-template'
            );
        });

        it('should handle optional variables correctly', () => {
            // Arrange
            const templateType = 'case-update';
            const templateData = {
                tenantName: 'Test Legal Firm',
                recipientName: 'John Test',
                caseNumber: 'TEST-2024-001',
                status: 'in_progress',
                // Optional variables
                caseSummary: 'Case summary',
                handlerName: 'Jane Attorney'
            };

            // Act & Assert
            expect(() => service.validateTemplateData(templateType, templateData)).not.toThrow();
        });
    });

    describe('method: processTemplate', () => {
        beforeEach(() => {
            const completeMockTenant = {
                ...mockTenant,
                realm: 'test-realm',
                adminUsername: 'admin',
                adminEmail: '<EMAIL>',
                adminFirstName: 'Admin',
                adminLastName: 'User',
                registrationAllowed: true,
                verifyEmail: true,
                rememberMe: true,
                dedicatedRealmAdmin: true,
                clientId: 'test-client',
                clientSecret: 'test-secret',
                enabled: true,
                users: [] // Add the missing required property
            };
            mockTenantRepository.findOne.mockResolvedValue(completeMockTenant);
        });

        it('should process case-update template correctly', async () => {
            // Arrange
            const variables = { ...testTemplateData.caseUpdate, caseId: 'case-123' };
            const tenantId = 'test-tenant-123';

            // Act
            const result = await service.processTemplate(variables, tenantId);

            // Assert
            expect(result).toEqual({
                templateType: 'case-update',
                templateData: expect.objectContaining({
                    tenantName: 'Test Legal Firm',
                    recipientName: 'John Test',
                    caseNumber: 'case-123',
                    status: 'in_progress'
                }),
                subject: undefined // Subject is set in templateData, not at top level
            });
            expect(mockTenantRepository.findOne).toHaveBeenCalledWith({ where: { id: tenantId } });
        });

        it('should process welcome template correctly', async () => {
            // Arrange
            const variables = {
                ...testTemplateData.welcome
                // Welcome template doesn't need caseId
            };
            const tenantId = 'test-tenant-123';

            // Act
            const result = await service.processTemplate(variables, tenantId);

            // Assert
            expect(result).toEqual({
                templateType: 'welcome',
                templateData: expect.objectContaining({
                    tenantName: 'Test Legal Firm',
                    recipientName: 'John Test',
                    role: 'Client'
                }),
                subject: undefined // Subject is set in templateData, not at top level
            });
        });

        it('should enrich template data with tenant information', async () => {
            // Arrange
            const variables = { ...testTemplateData.caseCreated };
            const { ...variablesWithoutTenantName } = variables; // Remove tenant name to test enrichment
            const tenantId = 'test-tenant-123';

            // Act
            const result = await service.processTemplate(variablesWithoutTenantName, tenantId);

            // Assert
            expect(result.templateData.tenantName).toBe(mockTenant.displayName);
            expect(result.templateData.supportEmail).toBe('<EMAIL>');
            expect(result.templateData.loginUrl).toBe('https://portal.testfirm.com');
        });

        it('should handle missing tenant gracefully', async () => {
            // Arrange
            const variables = testTemplateData.caseCreated;
            const tenantId = 'non-existent-tenant';
            mockTenantRepository.findOne.mockResolvedValue(null);

            // Act
            const result = await service.processTemplate(variables, tenantId);

            // Assert
            expect(result.templateData.tenantName).toBe('Test Legal Firm'); // Uses the tenant name from variables
        });

        it('should throw error when tenant repository fails', async () => {
            // Arrange
            const variables = testTemplateData.caseCreated;
            const tenantId = 'test-tenant-123';
            const error = new Error('Database connection failed');
            mockTenantRepository.findOne.mockRejectedValue(error);

            // Act & Assert
            await expect(service.processTemplate(variables, tenantId)).rejects.toThrow(
                'Unable to fetch tenant information for test-tenant-123'
            );
        });
    });

    describe('method: getTemplateConfig', () => {
        it('should return template configuration for valid type', () => {
            // Arrange
            const templateType = 'case-update';

            // Act
            const result = service.getTemplateConfig(templateType);

            // Assert
            expect(result).toHaveProperty('sendGridTemplateId');
            expect(result).toHaveProperty('sesTemplateName');
            expect(result).toHaveProperty('mailgunTemplateName');
            expect(result).toHaveProperty('requiredVariables');
            expect(result).toHaveProperty('optionalVariables');
        });

        it('should throw error for unknown template type', () => {
            // Arrange
            const templateType = 'unknown-template';

            // Act & Assert
            expect(() => service.getTemplateConfig(templateType)).toThrow(
                'Template configuration not found for type: unknown-template'
            );
        });
    });

    describe('method: getTemplateVariables', () => {
        it('should return required and optional variables for template', () => {
            // Arrange
            const templateType = 'case-update';

            // Act
            const result = service.getTemplateVariables(templateType);

            // Assert
            expect(result).toHaveProperty('required');
            expect(result).toHaveProperty('optional');
            expect(Array.isArray(result.required)).toBe(true);
            expect(Array.isArray(result.optional)).toBe(true);
            expect(result.required).toContain('tenantName');
            expect(result.required).toContain('recipientName');
        });

        it('should throw error for unknown template type', () => {
            // Arrange
            const templateType = 'unknown-template';

            // Act & Assert
            expect(() => service.getTemplateVariables(templateType)).toThrow(
                'Template not found: unknown-template'
            );
        });
    });

    describe('method: getUnifiedTemplates', () => {
        it('should return all available templates', () => {
            // Act
            const result = service.getUnifiedTemplates();

            // Assert
            expect(typeof result).toBe('object');
            expect(result).toHaveProperty('case-update');
            expect(result).toHaveProperty('welcome');
            expect(result).toHaveProperty('password-reset');
            expect(Object.keys(result).length).toBeGreaterThan(0);
        });
    });
});
