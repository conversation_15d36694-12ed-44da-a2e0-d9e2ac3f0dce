# Test Fixes

This document explains recent fixes to the testing infrastructure.

## Unit Test Fixes

### Issue: Module Path Resolution

The unit tests were failing with an error stating that the module `../libs/common/src/config/typeorm/typeorm.config.public` could not be found. This was occurring in the `jest-setup.ts` file.

**Fix:**
- Updated the import path to use the path alias defined in the Jest moduleNameMapper configuration. 
- Changed from `../libs/common/src/config/typeorm/typeorm.config.public` to `@app/common/config/typeorm/typeorm.config.public`

This ensures that all imports are consistent and follow the project's path alias pattern.

## Integration Test Fixes

### Issue: Missing wait-for-schema.js File

The integration tests were failing with an error stating that the module `./helpers/wait-for-schema` could not be found from `test/helpers/integration-jest-setup.js`.

**Fixes:**
1. Created the `wait-for-schema.js` file in the correct location (`test/helpers/wait-for-schema.js`)
2. Implemented a more robust schema readiness check using direct TypeORM connections
3. Corrected the import path in `integration-jest-setup.js` to use `./wait-for-schema` instead of `./helpers/wait-for-schema`

### Issue: Database Connection Error

Some integration tests were still showing errors about connecting to database at `postgres:5432` instead of `localhost:5434`.

This is a non-critical issue related to a leftover configuration in `integration-migrations-setup.js`. The tests are still passing because they're using the simplified test app which uses the correct connection details.

## Testing Best Practices

1. Always use path aliases for imports (e.g., `@app/common/...`) to ensure consistency across the codebase
2. Avoid direct dependencies in mocks to prevent circular references
3. Use the test app factory for creating test instances that are properly isolated
4. Reset the database between tests using `resetTestDatabase()` to ensure test isolation

## Running Tests

- Unit tests: `yarn test:unit`
- Integration tests: `yarn test:integration:sequential` (recommended for most reliable results)
- All tests: `yarn test`
- Docker-based tests: `yarn test:docker`

## Troubleshooting

If you still encounter issues with the database not being ready:
- Run the database setup script: `yarn test:db:setup`
- Check the database health: `yarn test:db:health`
- Make sure environment variables are properly set 