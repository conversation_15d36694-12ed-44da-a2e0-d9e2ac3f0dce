-- Initialize the test database with all the tables needed for integration tests
-- This file is mounted in the PostgreSQL container's /docker-entrypoint-initdb.d directory

-- Connect to the test database
\c tk_lpm_test;

-- Create public schema
CREATE SCHEMA IF NOT EXISTS public;

-- Create schemas for tenant tables
CREATE SCHEMA IF NOT EXISTS tenant_test;

-- Create typeorm_migrations table for tracking migrations
CREATE TABLE IF NOT EXISTS public.typeorm_migrations (
  id SERIAL PRIMARY KEY,
  timestamp BIGINT NOT NULL,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create tenant table for multi-tenancy
CREATE TABLE IF NOT EXISTS public.tenant (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  realm VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user table for authentication
CREATE TABLE IF NOT EXISTS public.user (
  id SERIAL PRIMARY KEY,
  username VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  keycloak_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create system_roles table for role-based access control
CREATE TABLE IF NOT EXISTS public.system_roles (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  permissions JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default roles
INSERT INTO public.system_roles (name, description, permissions)
VALUES 
  ('admin', 'System administrator with all permissions', '{"*": true}'),
  ('user', 'Regular user with limited permissions', '{"read": true}')
ON CONFLICT (name) DO NOTHING;

-- Create tenant_typeorm_migrations table for tracking tenant migrations
CREATE TABLE IF NOT EXISTS public.tenant_typeorm_migrations (
  id SERIAL PRIMARY KEY,
  timestamp BIGINT NOT NULL,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_roles table for mapping users to roles
CREATE TABLE IF NOT EXISTS public.user_roles (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES public.user(id),
  role_id INTEGER NOT NULL REFERENCES public.system_roles(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, role_id)
);

-- Create tenant schema tables for case management
CREATE TABLE IF NOT EXISTS tenant_test.client (
  id SERIAL PRIMARY KEY,
  first_name VARCHAR(255) NOT NULL,
  last_name VARCHAR(255) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(50),
  address TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS tenant_test.case (
  id SERIAL PRIMARY KEY,
  case_number VARCHAR(50) UNIQUE NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  status VARCHAR(50) NOT NULL DEFAULT 'DRAFT',
  priority VARCHAR(50) NOT NULL DEFAULT 'MEDIUM',
  client_id INTEGER REFERENCES tenant_test.client(id),
  assigned_to INTEGER,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS tenant_test.case_note (
  id SERIAL PRIMARY KEY,
  case_id INTEGER NOT NULL REFERENCES tenant_test.case(id),
  content TEXT NOT NULL,
  created_by INTEGER NOT NULL,
  is_pinned BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Grant permissions
GRANT ALL PRIVILEGES ON SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON SCHEMA tenant_test TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA tenant_test TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA tenant_test TO postgres;

-- Set search path
SET search_path TO public, tenant_test; 