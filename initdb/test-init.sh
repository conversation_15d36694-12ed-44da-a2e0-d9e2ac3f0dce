#!/bin/bash
set -e

# This script is used to initialize the test database environment
echo "Running test environment initialization script..."

# Define variables
TEST_DB="tk_lpm_test"
KEYCLOAK_DB="keycloak"

# Check if Keycloak database exists
if psql -U "$POSTGRES_USER" -lqt | cut -d \| -f 1 | grep -qw "$KEYCLOAK_DB"; then
    echo "Database $KEYCLOAK_DB already exists"
else
    # Create Keycloak database
    echo "Creating Keycloak database..."
    psql -v ON_ERROR_STOP=1 -U "$POSTGRES_USER" -c "CREATE DATABASE $KEYCLOAK_DB"
    echo "Keycloak database created."
    
    # Grant privileges on Keycloak database
    psql -v ON_ERROR_STOP=1 -U "$POSTGRES_USER" -c "GRANT ALL PRIVILEGES ON DATABASE $KEYCLOAK_DB TO $POSTGRES_USER"
    echo "Privileges granted for Keycloak database."
    
    # Set owner of the keycloak DB
    psql -v ON_ERROR_STOP=1 -U "$POSTGRES_USER" -c "ALTER DATABASE $KEYCLOAK_DB OWNER TO $POSTGRES_USER"
    echo "Database ownership set."
fi

# Initialize test database with schema
echo "Initializing test database schema..."
psql -v ON_ERROR_STOP=1 -U "$POSTGRES_USER" -d "$TEST_DB" <<-EOSQL
  -- Create public schema
  CREATE SCHEMA IF NOT EXISTS public;
  
  -- Basic tables needed for tests
  CREATE TABLE IF NOT EXISTS public.tenant (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    realm VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
  
  CREATE TABLE IF NOT EXISTS public.user (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    keycloak_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
EOSQL

echo "Test database schema initialized successfully."
echo "Test environment initialization completed successfully!" 