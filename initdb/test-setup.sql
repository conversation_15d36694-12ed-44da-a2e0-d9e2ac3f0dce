-- Create keycloak database if it doesn't exist
DO $$
BEGIN
   IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'keycloak') THEN
      CREATE DATABASE keycloak;
   END IF;
END
$$;

-- Grant privileges to postgres user
GRANT ALL PRIVILEGES ON DATABASE keycloak TO postgres;

-- Connect to the test database to set up schema
\c tk_lpm_test

-- Create the public schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS public;

-- Basic tables needed for testing
CREATE TABLE IF NOT EXISTS public.tenant (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  realm VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS public.user (
  id SERIAL PRIMARY KEY,
  username VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  keycloak_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
); 