#!/bin/bash
set -e

# Define the Keycloak database name
KEYCLOAK_DATABASE="keycloak"

echo "Creating Keycloak database: $KEYCLOAK_DATABASE"

# Check if database exists
if psql -U "$POSTGRES_USER" -lqt | cut -d \| -f 1 | grep -qw "$KEYCLOAK_DATABASE"; then
    echo "Database $KEYCLOAK_DATABASE already exists"
else
    # Create the database
    createdb -U "$POSTGRES_USER" "$KEYCLOAK_DATABASE"
    echo "Database $KEYCLOAK_DATABASE created"
fi

# Grant privileges to the PostgreSQL user on the Keycloak database
psql -v ON_ERROR_STOP=1 -U "$POSTGRES_USER" -d "$POSTGRES_DB" <<-EOSQL
  GRANT ALL PRIVILEGES ON DATABASE $KEYCLOAK_DATABASE TO $POSTGRES_USER;
EOSQL

echo "Keycloak database initialization completed" 