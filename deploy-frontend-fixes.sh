#!/usr/bin/env bash
set -e

echo "🚀 Deploying CORS and <PERSON><PERSON>r Fixes for Frontend Teams"
echo "=" | head -c 80
echo ""
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

echo "📋 Changes Made:"
echo ""
echo "✅ Added CORS origins to Core task definition:"
echo "   • http://localhost:3000 (development)"
echo "   • https://project-uq6hi.vercel.app (production)"
echo "   • https://api.tklpm.com (API endpoint)"
echo ""
echo "✅ Added cookie-parser middleware to Core gateway"
echo "✅ Created comprehensive frontend integration guide"
echo ""

# Commit only the specific files we changed
echo "📝 Committing specific changes..."
git add infrastructure/ecs/core-task-definition.json
git add apps/core/src/app.module.ts
git add FRONTEND-INTEGRATION-GUIDE.md
git add deploy-frontend-fixes.sh
git commit -m "Fix CORS and cookie parser for frontend teams

- Add frontend URLs to CORS_ORIGIN in Core task definition
- Enable cookie-parser middleware in Core gateway
- Create comprehensive frontend integration guide
- Resolve HttpOnly cookie access issues"

echo "✅ Changes committed"
echo ""

# Push to staging
echo "🚀 Pushing to staging branch..."
git push origin staging

echo "✅ Pushed to staging"
echo ""

echo "⏳ Next Steps:"
echo ""
echo "1. Wait for GitHub Actions build to complete (~10 minutes)"
echo "   Check: https://github.com/Tec-Juice/tk-lpm-backend/actions"
echo ""
echo "2. Deploy Core service:"
echo "   aws ecs update-service \\"
echo "     --cluster tk-lpm-staging-cluster \\"
echo "     --service core \\"
echo "     --force-new-deployment \\"
echo "     --region us-east-1"
echo ""
echo "3. Verify CORS is working:"
echo "   curl -H \"Origin: http://localhost:3000\" \\"
echo "        -H \"Access-Control-Request-Method: POST\" \\"
echo "        -X OPTIONS \\"
echo "        https://api.tklpm.com/api/auth/login"
echo ""
echo "4. Share FRONTEND-INTEGRATION-GUIDE.md with frontend teams"
echo ""
echo "🎉 Frontend teams should now be able to:"
echo "   ✅ Access API from localhost:3000"
echo "   ✅ Access API from Vercel production"
echo "   ✅ Use authentication cookies properly"
echo "   ✅ Make authenticated requests with credentials: 'include'"
echo ""

